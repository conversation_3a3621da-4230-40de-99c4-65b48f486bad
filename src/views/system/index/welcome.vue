<template>
  <div class="home-wrapper">
    <div class="wrokbench-wrapper" style="padding-top: 16px;">
      <div class="top-con">
        <div class="top-con-left card">
          <div class="handle-count">
            <h3 class="user-name">{{ timeFixText() }}， {{ $store.getters.userInfo.name }}</h3>

            <div class="welcome-text">
              欢迎使用<br />
              浙江省社区矫正数据协同系统
            </div>
            <!-- <p class="sub-text">您在社区矫正随行智控系统已处理了 <b> -->
            <!-- <countTo :start-val="0" :end-val="statisticsNum.yjsxNum || 0" :duration="3000" /> -->
            <!-- </b> 件预警事项，抽查了 <b> -->
            <!-- <countTo :start-val="0" :end-val="statisticsNum.ccdxNum || 0" :duration="3000" /> -->
            <!-- </b> 名社区矫正对象</p> -->
            <StatisticalList :statisticsNum="statisticsNum" />
          </div>
        </div>
        <div class="top-con-right flex-col">
          <div class="top-con-right-t card">
            <div class="c-title">待办事项</div>
            <!-- <TodoList /> -->

            <div class="linse-charts" style="height: 100%;">
              <ToDoList :dataSource="todoData" />
            </div>
          </div>
          <div style="height: 445px;" class="top-con-right-b card">
            <div class="top-con-right-b-item flex-col">
              <div class="c-title flex-space">
                <div class="flex-align">协同近况</div>
              </div>
              <div class="b-item-con" style="overflow: hidden;">
                <TodoListTable :list="todoData.noticeList" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-con flex-space">
        <!-- width:40%;min-width: 480px -->
        <div class="bottom-con-item card flex-col" style="width: 480px;flex:inherit;height: 318px;">
          <div class="c-title flex-space">
            <div class="flex-align">协同排名</div>
            <sh-select
              style="width: 160px;"
              labelKey="label"
              valueKey="value"
              @change="getRank()"
              :options="[
                { label: '法院', value: 20 },
                { label: '监狱', value: 61 },
                { label: '看守所', value: 41 }
              ]"
              placeholder="请选择"
              v-model="selectVal"
            />
          </div>
          <div class="" style="flex:1;overflow: hidden;">
            <CollaborativeRanking :list="rankList" />
          </div>
        </div>
        <div class="bottom-con-item card flex-col" style="height: 318px;">
          <div class="c-title flex-space">
            <div class="flex-align">协同数量</div>
            <a-radio-group class="screening" v-model="selectVal2" @change="getTrend()" button-style="solid">
              <a-radio-button value="month">
                本月
                <!--  -->
              </a-radio-button>

              <a-radio-button value="year">
                本年
              </a-radio-button>
              <a-radio-button value="">
                总计
              </a-radio-button>
            </a-radio-group>
          </div>
          <div style="flex:1">
            <CollaborativeTrend :data="trendList" style="height: 100%;" />
          </div>
        </div>
      </div>
    </div>
    <!-- <remindMessage ref="remindMessage"></remindMessage> -->
  </div>
</template>

<script>
import { timeFix } from '@/utils/util' /// es/notification
// 统计
import StatisticalList from './components/StatisticalList.vue'
// 协同通知
import TodoListTable from '@/views/system/index/components/TodoListTable'
// 协同排名
import CollaborativeRanking from './components/CollaborativeRanking.vue'
// 协同趋势
import CollaborativeTrend from './components/CollaborativeTrend.vue'
// 待办事项
import ToDoList from './components/ToDoList.vue'
import remindMessage from './components/remindMessage.vue'
export default {
  components: { StatisticalList, TodoListTable, CollaborativeTrend, CollaborativeRanking, ToDoList, remindMessage },
  data() {
    return {
      selectVal: 20,
      selectVal2: '',
      todoData: {},
      rankList: [],
      trendList: [],
      statisticsNum: {} //
    }
  },
  provide() {
    return {
      parentData: this.$data
    }
  },
  created() {
    // this.$store.dispatch('ToggleLayoutMode', 'topMenu')
    // // 因为顶部菜单不能固定左侧菜单栏，所以强制关闭
    // this.handleFixSiderbar(false)
    // // 触发窗口resize事件
    // triggerWindowResizeEvent()
  },
  async mounted() {
    this.getTodo()
    this.getRank()
    this.getTrend()
    this.$nextTick(() => {
      this.$refs.remindMessage.open()
    })
  },
  methods: {
    async getTrend() {
      const { data } = await this.$http({
        url: '/homePage/trend',
        params: {
          type: this.selectVal2
        }
      })
      this.trendList = data
    },
    async getRank() {
      this.$nextTick(async () => {
        const { data } = await this.$http({
          url: '/homePage/rank',
          params: {
            type: this.selectVal
          }
        })
        this.rankList = data
      })
    },
    async getTodo() {
      const { data } = await this.$http({
        url: '/homePage/todo'
      })
      this.todoData = data
    },
    timeFixText() {
      return timeFix()
    }
  }
}
</script>
<style lang="less" scoped>
//
// @media (max-width: 1420px) {
//     .wrokbench-wrapper {
//         .top-con {
//             flex-flow: column;

//             .top-con-left {
//                 flex: 1;
//                 width: 100%;
//                 margin: 0;
//                 margin-bottom: 18px;
//             }

//             /deep/.list-wrapper {
//                 display: flex;
//                 box-sizing: border-box;
//                 // gap: 20px; /* 在项目之间添加10px的间距 */
//                 flex-wrap: wrap;

//                 .list-item {
//                     // flex:1;
//                     overflow: hidden;
//                     width: calc(50% - 20px);
//                     margin-right: 20px;
//                     margin-top: 20px;
//                     margin-bottom: 0;

//                     &:last-child {
//                         margin-right: 0;
//                     }

//                     // margin: 0 18px;
//                 }
//             }
//         }

//         .bottom-con {
//             flex-flow: column;

//             .bottom-con-item {
//                 margin: 0;
//                 width: 100%;
//                 margin-bottom: 18px;
//                 flex: inherit;

//             }
//         }
//     }
// }

// 要创建一个媒体查询以针对大于 1440 像素的屏幕宽度应用样式，您可以使用 CSS3 中的 @media 查询。以下是一个示例：

/* 大于 1440px 的媒体查询 */
@media (min-width: 1440) {
  /* 在此处添加您的样式 */
  .top-con-left {
    /deep/.ant-divider {
      margin: 0 60px;
    }
  }
}
</style>

<style lang="less" scoped>
@gutterPx: 18px;

.as-box {
  margin-bottom: @gutterPx;

  .as-box-left {
    width: 40%;
    margin-right: @gutterPx;
  }

  .as-box-right {
    flex: 1;
  }
}

.time-box {
  font-size: 20px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  margin-top: 10px;

  span.num {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    background: linear-gradient(1deg, rgba(109, 159, 255, 0) 0%, rgba(231, 234, 254, 0.29) 100%);
    box-shadow: inset 0px 2px 4px 1px rgba(255, 255, 255, 0.16);
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid rgba(255, 255, 255, 0.87);
  }
}

.top-notice {
  padding: 16px 40px 20px 40px;

  img {
    width: 291px;
  }

  .notice-sub {
    font-size: 24px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
    line-height: 41px;
    // letter-spacing: 38px;
  }
}

.flex-align {
  display: flex;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-flow: column;
}

.smart-box {
  display: flex;
  width: 100%;
  height: 100%;

  .smart-box-left {
    // width: 40%;
  }

  .smart-box-right {
    flex: 1;
    overflow: hidden;
  }
}

.flex-space {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.handle-count {
  min-height: 334px;
  padding-top: 28px;
  // margin-bottom: 55px;

  .welcome-text {
    font-size: 26px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #2d8fff;
    line-height: 41px;
    margin-bottom: 20px;
  }

  .user-name {
    font-size: 30px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #439bff;
    // margin-top: 52px;
    margin-bottom: 28px;
  }

  .sub-text {
    font-size: 20px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 35px;
    margin-top: 25px;

    b {
      font-size: 23px;
      font-family: Arial-Bold, Arial;
      font-weight: bold;
      color: #439bff;
    }
  }
}
.home-wrapper {
  min-height: calc(100vh - 53px);
  background: url('~@/assets/home-bg.png') no-repeat top center;
  background-color: #eaeff6;
  background-size: 100%;
  padding: 16px;
  // min-width: 1366px;
}
.wrokbench-wrapper {
  margin-top: 0;
  display: flex;
  flex-flow: column;
  // height: 100%;
}

.top-con {
  width: 100%;
  // margin-bottom: @gutterPx;
  display: flex;

  justify-content: center;

  .top-con-left {
    // width: 40%;
    // min-width: 480px;
    width: 480px;
    margin-right: @gutterPx;
    background: url('~@/assets/home-slide-bg.png') no-repeat top center;
    background-color: #fff;
    background-size: 100%;
  }

  .top-con-right {
    flex: 1;
    overflow: hidden;

    .top-con-right-b {
      margin-top: @gutterPx;
      display: flex;

      .top-con-right-b-item {
        flex: 1;

        &:last-child {
          padding-left: @gutterPx;
        }

        .b-item-con {
          flex: 1;
        }
      }
    }
  }
}

.card {
  background: #ffffff;
  border-radius: 9px 9px 9px 9px;
  padding: 22px;
}

.c-title {
  font-size: 20px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  line-height: 1.5em;
  height: 32px;
}

.sufixx-text {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #7c7c7c;
}

.c-sub-title {
  font-size: 17px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.bottom-con {
  margin-top: @gutterPx;
  .bottom-con-item {
    flex: 1;
    margin-right: @gutterPx;
    overflow: hidden;

    &:last-child {
      margin: 0;
    }
  }
}
</style>

<template>
  <div v-bind="$attrs" style="width: 100%;height: 100%;overflow: auto;">
    <a-list :data-source="list">
      <a-list-item slot="renderItem" slot-scope="item">
        <div class="flex-space" @click="open(item.noticeType)">
          <div class="flex-space-item">
            <img v-if="item.isNew" style="width: 38px;" src="~@/assets/work/new.png" alt="">   {{ item.msg }}
          </div>
          <span>{{ moment(item.noticeTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
      </a-list-item>
    </a-list>
  </div>
</template>

<script>
    import moment from 'moment';
    export default {
      props: {
        list: {
          default: () => [],
          type: Array
        }
      },
      data () {
        return {
          cc: [
            { type: 'add_correction_01', path: '/jzdxxj?zt=0' },
            { type: 'arrest_01', path: '/sendApplyArrest' },
            { type: 'arrest_02', path: '/sendApplyArrest' },
            { type: 'invest_01', path: '/acceptInvestInfo?zt=0' },
            { type: 'recommit_01', path: '/acceptRecommit' },
            { type: 'revocation_parole_01', path: '/tqcxjs' },
            { type: 'revocation_parole_02', path: '/tqcxjs' },
            { type: 'revocation_probation_01', path: '/tqcxhx' },
            { type: 'revocation_probation_02', path: '/tqcxhx' },
            { type: 'terminate_01', path: '/terminate?date=' },
            { type: 'irs_01', path: '/wbxt/main/wbxtmz?key=irssubsistenceassistanceinformation' },
            { type: 'irs_02', path: '/wbxt/main/wbxtmz?key=irsextremepoorbasicinfo' },
            { type: 'irs_03', path: '/wbxt/main/wbxtmz?key=irsedgeassistancerecipients' },
            { type: 'irs_04', path: '/wbxt/main/wbxtmz?key=irsmarriageregistrationinformation' },
            { type: 'irs_05', path: '/wbxt/main/wbxtfy' },
            { type: 'irs_06', path: '/wbxt/main/wbxtcl' },
            { type: 'irs_07', path: '/wbxt/main/wbxtrlsb?key=sjzxdm029biz029sjjhdsjjac60deltagtold' },
            { type: 'irs_08', path: '/wbxt/main/wbxtrlsb?key=sjzxdm029cachelabourerdeltagtold' },
            { type: 'irs_09', path: '/wbxt/main/wbxtrlsb?key=sjzxdm029biz029sjjhdsjjac02deltagtold' },
            { type: 'irs_10', path: '/wbxt/main/wbxtfzggw' },
            { type: 'irs_11', path: '/wbxt/main/wbxtylbzj' },
            { type: 'irs_12', path: '' },
            { type: 'irs_13', path: '' },
            { type: 'irs_14', path: '' },
            { type: 'irs_15', path: '' }
          ]
        }
      },
      methods: {
        moment,
        open(type) {
          const values = this.cc.filter(i => i.type === type)
          this.$router.push({ path: values[0].path })
        }
      }
    }
</script>

<style lang="less" scoped>
.flex-space{
  display: flex;
  justify-content: space-between;
  width: 100%;
  overflow: hidden;
  .flex-space-item{
    padding-right: 20px;
    overflow: hidden;
    flex:1;
    text-overflow: ellipsis;
    word-break: nowrap;
    white-space: nowrap;
  }
  span{
    opacity: .8;
  }
}

</style>

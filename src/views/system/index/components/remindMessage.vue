<template>
  <div class="msg-box" :style="dialogStyle" v-if="visible">
    <div>
      <img src="~@/assets/icons/close.png" class="close-icon" @click="handleCancel" />
    </div>
    <div class="content">
      <p class="title">跨部门办案协同意见征集</p>
      <p>
        如您在操作跨部门大数据办案中有什么意见或问题，可点击下方按钮在跳转的页面中进行填写（该页面由浙江省政法一体化办案系统提供。）
      </p>
      <p class="zfb-tag" @click="toReport">去填写</p>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      dialogStyle: {
        bottom: 0,
        right: '40px'
      }
    }
  },
  created() {
    console.log(document.documentElement.clientHeight, document.documentElement.clientWidth)
  },
  methods: {
    open() {
      this.visible = true
      this.dialogStyle.bottom = '10px'
      this.dialogStyle.right = 40 + 'px'
    },
    handleCancel() {
      this.visible = false
    },
    toReport() {
      window.open('http://************:8019', '_blank')
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  overflow: hidden !important;
  padding: 0;
}
/deep/.ant-modal-content {
  border-radius: 14px;
}
/deep/.ant-modal-close {
  right: -30px;
  top: -30px;
}
.msg-box {
  width: 400px;
  border-radius: 14px;
  position: fixed;
  z-index: 999;
}
.close-icon {
  position: absolute;
  right: -15px;
  top: -15px;
  width: 28px;
  height: 28px;
  cursor: pointer;
}
.content {
  background: url('~@/assets/icons/content-bg.png') no-repeat;
  background-size: 100% 100%;
  text-align: center;
  padding: 40px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.zfb-tag {
  background: #4b7aff;
  border-radius: 27px 27px 27px 27px;
  color: #fff;
  width: 30%;
  margin: 0 auto;
  padding: 6px 10px;
  cursor: pointer;
}
.title {
  font-weight: bold;
  font-size: 20px;
}
</style>

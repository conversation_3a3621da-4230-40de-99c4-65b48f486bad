<template>
  <div class="list-wrapper" v-bind="$attrs">
    <div class="list-item">
      <div>
        <a-radio-group class="screening" v-model="typeOne" button-style="solid" @change="getBaseInfoCount()">
          <a-radio-button value="month">
            本月
          </a-radio-button>

          <a-radio-button value="year">
            本年
          </a-radio-button>
          <a-radio-button value="">
            总计
          </a-radio-button>
        </a-radio-group>
        <div class="list-title"><img src="~@/assets/work/icon7.png" alt="">
          人员接收衔接

        </div>

        <div class="sub-count">
          <div>
            法院:<b>
              <countTo :start-val="0" :end-val="baseInfoCount[20]" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            监狱:<b>
              <countTo :start-val="0" :end-val="baseInfoCount[61]" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            看守所:<b>
              <countTo :start-val="0" :end-val="baseInfoCount[41]" :duration="3000" />
            </b>
          </div>
        </div>
      </div>
    </div>
    <div class="list-item">
      <div>
        <a-radio-group class="screening" v-model="typeTwo" button-style="solid" @change="getInvestInfoCount()">
          <a-radio-button value="month">
            本月
          </a-radio-button>

          <a-radio-button value="year">
            本年
          </a-radio-button>
          <a-radio-button value="">
            总计
          </a-radio-button>
        </a-radio-group>
        <div class="list-title" style="color: #4F78C5;"><img src="~@/assets/work/icon8.png" alt="">
          调查评估协同
        </div>
        <div class="sub-count">
          <div>
            法院:<b>
              <countTo :start-val="0" :end-val="investInfoCount[20]" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            监狱:<b>
              <countTo :start-val="0" :end-val="investInfoCount[61]" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            看守所:<b>
              <countTo :start-val="0" :end-val="investInfoCount[41]" :duration="3000" />
            </b>
          </div>
        </div>
      </div>
    </div>
    <div class="list-item">

      <div>
        <a-radio-group class="screening" v-model="typeThree" button-style="solid" @change="getOtherCount()">
          <a-radio-button value="month">
            本月
          </a-radio-button>

          <a-radio-button value="year">
            本年
          </a-radio-button>
          <a-radio-button value="">
            总计
          </a-radio-button>
        </a-radio-group>
        <div class="list-title" style="color: #5D6997;"><img src="~@/assets/work/icon9.png" alt="">
          其它协同
        </div>
        <div class="sub-count">
          <div>
            公安处罚:<b>
              <countTo :start-val="0" :end-val="otherCount.acceptRecommit" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            奖惩提请:<b>
              <countTo :start-val="0" :end-val="otherCount.punishment" :duration="3000" />
            </b>
            <!-- <a-divider type="vertical" /> -->

          </div>
          <div>
            走访任务:<b>
              <countTo :start-val="0" :end-val="otherCount.interviewJobDistribute" :duration="3000" />
            </b>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  props: {
    statisticsNum: {
      default: () => { },
      type: Object
    }
  },
  data() {
    return {
      typeOne: '',
      baseInfoCount: {
        20: 0,
        41: 0,
        61: 0
      },
      typeTwo: '',
      investInfoCount: {
        20: 0,
        41: 0,
        61: 0
      },
      typeThree: '',
      otherCount: {
        acceptRecommit: 0,
        interviewJobDistribute: 0,
        punishment: 0
      }
      // statisticsNum:{}
    }
  },
  mounted() {
    // this.getStatisticsNum()
    this.getBaseInfoCount()
    this.getInvestInfoCount()
    this.getOtherCount()
  },
  methods: {
    async getOtherCount() {
      const { data } = await this.$http({
        url: '/homePage/otherCount',
        params: {
          type: this.typeThree
        }
      })
      this.otherCount = data
    },
    async getInvestInfoCount() {
      const { data } = await this.$http({
        url: '/homePage/investInfoCount',
        params: {
          type: this.typeTwo
        }
      })
      this.investInfoCount = data
    },
    // 人员接收衔接
    async getBaseInfoCount() {
      const { data } = await this.$http({
        url: '/homePage/baseInfoCount',
        params: {
          type: this.typeOne
        }
      })
      this.baseInfoCount = data
    },
    /**
     *
     * @param {*} text  2%
     * @returns 2
     */
    getPcum(text) {
      debugger
      if (!text) {
        return 0
      }
      const n = text.split('%')
      return Number(n[0])
    }

  }
}
</script>

<style lang="less" scoped>
@gutterPx: 18px;

.list-wrapper {
  padding-top: 30px;
}

.flex-col {
  display: flex;
  flex-flow: column;
}

.flex-space {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item {
  height: 166px;
  display: flex;
  align-items: center;
  width: 100%;

  padding: @gutterPx 10px @gutterPx 30px;
  padding-right: 2px;
  background: #FFFFFF;
  box-shadow: 0px 0px 4px 1px rgba(91, 91, 91, 0.18);
  border-radius: 11px 11px 11px 11px;
  margin-bottom: 30px;
  position: relative;

  .screening {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  &>div {
    width: 100%;
  }

  .list-title {
    font-size: 24px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #38ABE3;
    display: flex;
    align-items: center;
  }

  .sub-count {
    font-size: 18px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    margin-top: 30px;
    display: flex;
justify-content: space-between;
    &>div {
      // flex: 1;
      position: relative;
      overflow: hidden;
      // white-space: nowrap;
      margin-right: 10px;
      &:last-child{
        // margin: 0
      }

      /deep/.ant-divider {
        margin: 0 30px;
      }
    }

    b {
      font-size: 20px;
      font-family: Arial-Bold, Arial;
      font-weight: bold;
    }
  }

  img {
    width: 44px;
    margin-right: 10px;
  }

  &:last-child {
    margin: 0;
  }
}</style>

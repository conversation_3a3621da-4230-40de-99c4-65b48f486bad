<template>
  <a-table
    style="margin-top: 14px;"
    size="small"
    :bordered="false"
    :pagination="false"
    :scroll="{y:184}"
    :columns="columns"
    :data-source="list">
    <template slot="sort" slot-scope="text,record,index">
      <span :class="{first:index===0,section:index===1,three:index===2}">{{ index+1 }}</span>
    </template>
    <a slot="per" slot-scope="text">{{ text/100 }}%</a>
  </a-table>
</template>
<script>
const columns = [
    {
        title: '排行',
        dataIndex: '',
        width: 46,
        key: 'rowIndex',
        align: 'center',
        scopedSlots: { customRender: 'sort' }
        // customRender: function (t, r, index) {
        //     return parseInt(index) + 1
        // }
    },
    {
        title: '单位名称',
        dataIndex: 'extOrgName',
        key: 'extOrgName',
        scopedSlots: { customRender: 'extOrgName' }
    },
    {
        title: '协同数量(次)',
        dataIndex: 'sum',
        align: 'center',
        key: 'sum',
        ellipsis: true

    },
    {
        title: '接收(次)',
        dataIndex: 'accepted',
        align: 'center',
        key: 'accepted',
        ellipsis: true
    },
    {
        title: '退回(次)',
        dataIndex: 'returned',
        align: 'center',
        key: 'returned',
        ellipsis: true
    },
    {
        title: '有效协同',
        dataIndex: 'per',
        align: 'center',
        key: 'per',
        scopedSlots: { customRender: 'per' },
        ellipsis: true
    }
];

const data = [
    {
        key: '1',
        name: 'John',
        age: 32,
        address: 'New York No. 1 Lake Park, New York No. 1 Lake Park',
        tags: ['nice', 'developer']
    },
    {
        key: '2',
        name: 'Jim',
        age: 42,
        address: 'London No. 2 Lake Park, London No. 2 Lake Park',
        tags: ['loser']
    },
    {
        key: '3',
        name: 'Joe',
        age: 32,
        address: 'Sidney No. 1 Lake Park, Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher']
    },
    {
        key: '4',
        name: 'Joe',
        age: 32,
        address: 'Sidney No. 1 Lake Park, Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher']
    },
    {
        key: '5',
        name: 'Joe',
        age: 32,
        address: 'Sidney No. 1 Lake Park, Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher']
    },
     {
        key: '6',
        name: 'Joe',
        age: 32,
        address: 'Sidney No. 1 Lake Park, Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher']
    },
     {
        key: '7',
        name: 'Joe2',
        age: 332,
        address: 'Sidney No. 1 Lake Park, Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher']
    }
];

export default {
    props: {
        list: {
            default: () => [],
            type: Array
        }
    },
    data() {
        return {
            data,
            columns
        };
    }
};
</script>
<style lang="less" scoped>
.first{
    color: rgba(255, 85, 21, 1);

}
.section{
color: rgba(255, 168, 20, 1);
}.three{
color: rgba(22, 144, 255, 1);
}
/deep/.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
    background: rgba(245, 245, 245, 1) !important;
}

/deep/.ant-table-small {
    border: none;
}

/deep/.ant-table-small>.ant-table-content>.ant-table-body {
    margin: 0;
}</style>

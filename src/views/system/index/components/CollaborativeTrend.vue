<template>
  <div ref="chart" style="width:100%;height:100%" />
</template>

<script>
import resizeEcharts from '@/mixins/resizeEcharts'
export default {
    mixins: [resizeEcharts],
    props: {
        data: {
            default: () => [],
            type: Array

        }
    },
    data() {
        return {

        }
    },
    watch: {
        data(val) {
            this.init()
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            const echarts = this.$echarts
            !this.myChart && (this.myChart = echarts.init(this.$refs.chart))
            console.log(echarts)
            this.option = {
                grid: {
                    top: '8%',
                    left: '0%',
                    right: '2%',
                    bottom: 0, // 也可设置left和right设置距离来控制图表的大小
                    containLabel: true
                },
                tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'line'
            } },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLabel: {
                        interval: 0
                    },

                    data: this.data.map(item => item.y)
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: this.data.map(item => item.amount),
                        type: 'line',
                        smooth: true,
                        color: 'rgba(22, 144, 255, 1)',
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                                [{
                                    offset: 0,
                                    color: 'rgba(105, 176, 255, 0.13)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(105, 176, 255, 0.13)'
                                }]
                            ),
                            shadowColor: 'rgba(22, 144, 255, 1)',
                            shadowBlur: 10
                        }
                    }
                ]
            };
            // 使用更新后的 option，渲染图表
            this.myChart.setOption(this.option, true)
        }
    }

}
</script>

<style lang="less" scoped></style>

<template>
  <section class="s-row" :gutter="15">
    <div class="s-col" :span="6" v-for="(item, index) in list" :key="index">
      <div class="todo-item" @click="$router.push({ path: item.path })">
        <div class="con">
          <b>
            <countTo :start-val="0" :end-val="item.num || 0" :duration="3000" />
          </b>
          <div>{{ item.name }}</div>
        </div>
        <img :src="item.img" alt="" />
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    dataSource: {
      default: () => {},
      type: Object
    }
  },
  data() {
    return {
      list: [
        { name: '矫正对象待接收', num: 0, path: '/jzdxxj?zt=0', img: require('@/assets/work/i1.png') },
        { name: '衔接回执待反馈', num: 0, path: '/jzdxxj?zt=1', img: require('@/assets/work/i2.png') },
        { name: '调查评估待指派', num: 0, path: '/acceptInvestInfo?zt=0', img: require('@/assets/work/i3.png') },
        { name: '调查结果待反馈', num: 0, path: '/acceptInvestInfo?zt=1', img: require('@/assets/work/i4.png') },
        { name: '公安处罚待反馈', num: 0, path: '/acceptRecommit?zt=0', img: require('@/assets/work/i9.png') },
        { name: '撤销假释待处理', num: 0, path: '/revocationparole', img: require('@/assets/work/i5.png') },
        { name: '撤销缓刑待处理', num: 0, path: '/revocationprobation', img: require('@/assets/work/i6.png') },
        { name: '提请逮捕待处理', num: 0, path: '/sendApplyArrest?zt=0', img: require('@/assets/work/i7.png') },
        { name: '变更执行地待通知', num: 0, path: '/bgzxd?zt=0', img: require('@/assets/work/i8.png') }
      ]
    }
  },
  watch: {
    dataSource: {
      handler() {
        this.list[0].num = this.dataSource.acceptBaseInfo[0]
        this.list[1].num = this.dataSource.acceptBaseInfo[1]
        this.list[2].num = this.dataSource.acceptInvestInfo[0]
        this.list[3].num = this.dataSource.acceptInvestInfo[1]
        this.list[4].num = this.dataSource.acceptRecommit
        this.list[5].num = this.dataSource.revocationParole
        this.list[6].num = this.dataSource.revocationProbation
        // this.list[7].num = this.dataSource.sendApplyArrest
        this.list[8].num = this.dataSource.sendPlaceChange
      },
      deep: true
    }
  }
}
</script>

<style lang="less" scoped>
.s-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -10px; /* 补偿行间距 */
  .s-col {
    width: calc(20% - 10px); /* 每行5个，所以宽度为20%减去右间距 */
    margin-right: 10px; /* 设置右间距 */
    box-sizing: border-box; /* 让宽度包含内边距和边框 */
  }
}
.todo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 130px;
  user-select: none;
  cursor: pointer;
  margin-top: 18px;
  .con {
    position: relative;
    z-index: 1;
    text-align: center;
    font-size: 22px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    b {
      font-size: 28px;
      font-family: Arial, Arial;
      font-weight: bold;
      color: #333333;
    }
  }
  img {
    position: absolute;
    user-select: none;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}
</style>

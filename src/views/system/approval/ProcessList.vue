<template>
  <div>
    <a-card style="margin-top: 24px" :bordered="false">
      <div slot="title">
        <div class="operator">
          <a-upload
            name="file"
            accept=".zip,.ZIP,.xml"
            action="/api/approval/deployments"
            :headers="headers"
            @change="handleUpload"
            :showUploadList="false">
            <a-button type="primary"> <a-icon type="plus" />&nbsp;部署/修改流程 </a-button>
          </a-upload>
        </div>
      </div>
      <div slot="extra">
        <a-input style="margin-left: 16px; width: 272px;" v-model="search.keyString" />
        <a-button style="margin-left: 16px;" @click="handleSearch"> <a-icon type="search" />&nbsp;查询</a-button>
      </div>

      <div>
        <a-table
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="pagination"
          :loading="loading"
          size="middle"
          @change="onChange">
          <template slot="operation" slot-scope="text, record">
            <!--            <a @click="handleShow(record.id)">-->
            <!--              <a-icon type="edit" />&nbsp;查看</a>-->
            <!--            <a-divider type="vertical" />-->
            <a-popconfirm title="是否要删除此行？删除后无法恢复" @confirm="handleRemove(record.deploymentId)">
              <a> <a-icon type="delete" />&nbsp;删除</a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
import approvalService from '@/api/modular/system/approvalService'
import { mapState } from 'vuex'
import Vue from 'vue';
import { ACCESS_TOKEN } from '@/store/mutation-types';
const columns = [
  {
    title: '审批流程ID',
    dataIndex: 'id',
    scopedSlots: { customRender: 'id' }
  },
  {
    title: '流程名称',
    dataIndex: 'key',
    scopedSlots: { customRender: 'key' }
  },
  {
    title: '部署ID',
    dataIndex: 'deploymentId',
    scopedSlots: { customRender: 'deploymentId' },
    width: 120
  },
  {
    title: '当前版本',
    dataIndex: 'version',
    scopedSlots: { customRender: 'version' },
    width: 80
  },
  {
    title: '操作',
    dataIndex: 'operation',
    scopedSlots: { customRender: 'operation' },
    width: 150
  }
]

export default {
  name: 'ProcessList',
  // components: {
  //   ProcessList
  // },
  data() {
    return {
      columns,
      dataSource: [],
      search: {
        keyString: '',
        state: 0
      },
      loading: true,
      visible: false,
      switchLoading: false,
      purchaseOrderId: '',
      isEdit: false,
      defaultFileList: [],
      headers: {
        'Authorization': 'Bearer ' + Vue.ls.get(ACCESS_TOKEN)
      },
      pagination: {},
      BASE_URL: process.env.VUE_APP_API_BASE_URL,
      URL: process.env.VUE_APP_API_BASE_URL
    }
  },
  computed: {
    ...mapState({
      token: state => state.user.access_token
    })
  },
  created: function () {
    this.fetch({})
  },
  methods: {
    fetch() {
      this.loading = true
      return new Promise((resolve, reject) => {
        approvalService
          .listProcess({
            lasted: true
          })
          .then(response => {
            console.info('response', response)
            const result = response
            // if (result.code === 200) {
            console.info('result.result', result)
            this.dataSource = result.data
            // }
            this.loading = false
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    onRefreshList() {
      this.fetch()
      this.drawerClose()
    },
    handleSearch() {
      this.loading = true
      this.pagination.current = 1
      this.fetch()
    },

    onShowSizeChange(current, pageSize) {
      this.pagination.pageSize = pageSize
      this.fetch()
    },
    handleChange(pageNo) {
      this.pagination.current = pageNo
      this.fetch()
    },
    handleEdit(id) {
      this.purchaseOrderId = id
      this.isEdit = true
      this.visible = true
    },
    drawerClose() {
      this.visible = false
    },
    onChange(pagination, filters, sorter) {
      debugger
      if (sorter !== null && sorter.columnKey !== undefined) {
        this.pagination.sort = sorter.columnKey
        this.pagination.order = sorter.order.replace('end', '')
        this.fetch()
      }
    },
    handleRemove(id) {
      this.loading = true
      return new Promise((resolve, reject) => {
        approvalService
          .deleteDeployments(id)
          .then(response => {
            const result = response
            if (result.code === 200) {
              this.$message.success('删除成功')
            }
            this.loading = false
            resolve()
          })
          .catch(error => {
            reject(error)
          })
          .then(() => {
            this.loading = false
            this.fetch()
          })
      })
    },
    handleShow(id) {
      this.purchaseOrderId = id
      this.isEdit = false
      this.visible = true
    },
    handleUpload(info) {
      console.info('info', info)
      if (info.file.status === 'done') {
        if (info.file.response.code === 200) {
          this.$success({
            title: '流程部署成功',
            // JSX support
            content: (
              <div>
                <p>流程ID为：{info.file.response.data.id} </p>
              </div>
            )
          })
          this.fetch()
        } else {
          this.$error({
            title: '流程部署失败',
            // JSX support
            content: (
              <div>
                <p>原因为：{info.file.response.message}，请检查上传的zip文件内容是否正确！ </p>
              </div>
            )
          })
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 部署失败.`)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ant-avatar-lg {
  width: 96px;
  height: 64px;
  line-height: 48px;
}

.ant-table td {
  white-space: nowrap;
}

.list-content-item {
  color: rgba(0, 0, 0, 0.45);
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  margin-left: 40px;

  span {
    line-height: 20px;
  }

  p {
    margin-top: 4px;
    margin-bottom: 0;
    line-height: 22px;
  }
}
</style>

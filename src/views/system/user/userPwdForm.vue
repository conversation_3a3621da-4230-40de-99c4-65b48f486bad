<template>
  <a-modal
    title="修改密码"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >

    <a-card :bordered="false">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :md="24" :sm="24">
              <a-form-item
                label="输入密码"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input-password placeholder="请输入密码" v-decorator="['password', {rules: [{required: true, message: '请输入密码！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="24" :sm="24">
              <a-form-item
                label="确认密码"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input-password placeholder="请再次输入密码" v-decorator="['repeatPassword', {rules: [{required: true, message: '请再次输入密码！'},{ validator: this.handlePasswordCheck }]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
    </a-card>

  </a-modal>
</template>

<script>
import { sysUserOwnRole, sysUserResetPassword } from '@/api/modular/system/userManage'
const columns = [
  {
    title: '角色名称',
    dataIndex: 'name'
  },
  {
    title: '唯一编码',
    dataIndex: 'code'
  }
]

export default {
  name: 'UserRoleIndex',

  data () {
    return {
      columns,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 }
      },
      loadData: [],
      selectedRowKeys: [], // Check here to configure the default column
      loading: true,
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      recordEntity: []
    }
  },
  computed: {
    hasSelected () {
      return this.selectedRowKeys.length > 0
    }
  },
  methods: {
    // 初始化方法
    userPwd (record) {
      this.recordEntity = record
      this.visible = true
    },

    /**
     * 获取用户已有角色
     */
    sysUserOwnRole () {
      this.loading = true
      sysUserOwnRole({ id: this.recordEntity.id }).then((res) => {
        // 选中多选框
        this.selectedRowKeys = res.data
        this.loading = false
      })
    },

    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    handlePasswordCheck (rule, value, callback) {
      const password = this.form.getFieldValue('password')
      console.log('value', value)
      if (value === undefined) {
        callback(new Error('请输入密码'))
      }
      if (value && password && value.trim() !== password.trim()) {
        callback(new Error('两次密码不一致'))
      }
      callback()
    },
    handleSubmit () {
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          this.confirmLoading = false
          this.visible = false
          sysUserResetPassword({ id: this.recordEntity.id, password: values.password }).then((res) => {
            if (res.success) {
              this.$message.success('修改成功')
              this.confirmLoading = false
              this.$emit('ok', this.recordEntity)
              this.handleCancel()
            } else {
              this.$message.error('修改失败：' + res.message)
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        }
      })
    },
    handleCancel () {
      this.recordEntity = []
      this.selectedRowKeys = []
      this.visible = false
    }
  }
}
</script>

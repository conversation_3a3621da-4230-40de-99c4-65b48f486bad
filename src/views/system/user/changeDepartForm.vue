<template>
  <a-modal
    title="请选择登录机构"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >

    <a-card :bordered="false">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :md="24" :sm="24">
              <a-form-item
                label="登录机构"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-select v-decorator="['orgId', {rules: [{required: true, message: '请选择机构！'}]}]" allow-clear placeholder="请选择机构">
                  <a-select-option v-for="(item, index) in orgList" :key="index" :value="item.orgId">{{
                    item.orgName
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
    </a-card>

  </a-modal>
</template>

<script>
import Vue from 'vue'
import { changeUserDepart } from '@/api/modular/system/loginManage'
import { ACCESS_TOKEN, ALL_APPS_MENU, DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
const columns = [
  {
    title: '角色名称',
    dataIndex: 'name'
  },
  {
    title: '唯一编码',
    dataIndex: 'code'
  }
]

export default {
  name: 'UserRoleIndex',

  data () {
    return {
      columns,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 }
      },
      loadData: [],
      orgId: '',
      orgList: [],
      selectedRowKeys: [], // Check here to configure the default column
      loading: true,
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      recordEntity: []
    }
  },
  computed: {
    hasSelected () {
      return this.selectedRowKeys.length > 0
    }
  },
  methods: {
    // 初始化方法
    userPwd (record) {
      this.recordEntity = record
      this.visible = true
    },
    open (record) {
      this.orgList = record
      this.visible = true
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    handleSubmit () {
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          this.confirmLoading = false
          this.visible = false
          changeUserDepart(values).then((res) => {
            if (res.success) {
              this.$message.success('切换成功')
              this.confirmLoading = false
              this.$emit('ok', this.recordEntity)
            } else {
              this.$message.error('切换失败：' + res.message)
              this.$emit('stepCaptchaCancel', this.recordEntity)
            }
          }).finally((res) => {
            this.confirmLoading = false
            this.$emit('stepCaptchaCancel', this.recordEntity)
          })
        }
      })
    },
    handleCancel () {
      this.recordEntity = []
      this.selectedRowKeys = []
      this.$message.error('取消选择，退出登录中！')
      Vue.ls.remove(ACCESS_TOKEN)
      Vue.ls.remove(ALL_APPS_MENU)
      Vue.ls.remove(DICT_TYPE_TREE_DATA)
      this.visible = false
    }
  }
}
</script>

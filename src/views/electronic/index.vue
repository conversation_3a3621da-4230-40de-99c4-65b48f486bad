<template>
  <div class="electronic-page">
    <!-- 头部 -->
    <div class="electronic-header">
      <div class="header-left">
        <img src="@/assets/logo.png" class="logo" alt="logo" />
        <span class="title">浙里社区矫正</span>
        <span class="sub-title">社区矫正电子卷宗（2025）余杭区073521</span>
      </div>
    </div>

    <div class="electronic-container">
      <!-- 左侧区域：人员信息和文件树 -->
      <div class="left-panel">
        <!-- 人员信息 -->
        <div class="person-info">
          <div class="status-tag" :class="personStatus">{{ statusText }}</div>
          <div class="section-title" style="margin-bottom: 16px;font-weight: bold;">人员信息</div>
          <div class="person-content">
            <div class="person-avatar">
              <img src="@/assets/user.png" alt="用户头像" />
              <span class="value">{{ baseInfo.correctionObjName }}</span>
            </div>

            <div class="info-item">
              <span class="label">证件号码：</span>
              <span class="value">{{ baseInfo.certNum }}</span>
            </div>
            <div class="info-item" v-if="personStatus !== 'pending-review'">
              <span class="label">矫正单位：</span>
              <span class="value">{{ baseInfo.deptName }}</span>
            </div>
            <div class="info-item" v-if="personStatus !== 'pending-review'">
              <span class="label">入矫时间：</span>
              <span class="value">{{ baseInfo.correctionStartAt }}</span>
            </div>
            <div class="info-item" v-if="personStatus !== 'pending-review'">
              <span class="label">罪名：</span>
              <span class="value">{{ baseInfo.criminalCharge }}</span>
            </div>
            <div class="info-item" v-if="personStatus !== 'pending-review'">
              <span class="label">矫正类型：</span>
              <span class="value">{{ baseInfo.correctionType }}</span>
            </div>
            <div class="info-item" v-if="personStatus === 'pending-review'">
              <span class="label">委托单位：</span>
              <span class="value">{{ baseInfo.entrustmentDeptName }}</span>
            </div>
            <div class="info-item" v-if="personStatus === 'pending-review'">
              <span class="label">调查单位：</span>
              <span class="value">{{ baseInfo.deptName }}</span>
            </div>
            <div class="info-item" v-if="personStatus === 'pending-review'">
              <span class="label">调查时间：</span>
              <span class="value">{{ baseInfo.entrustmentReceiveTime }} ~ {{ baseInfo.inveTime }}</span>
            </div>
            <div class="info-item" v-if="personStatus === 'pending-review'">
              <span class="label">调查结论：</span>
              <span class="value">{{ baseInfo.conclusion }}</span>
            </div>
          </div>
        </div>

        <!-- 文件树 -->
        <div class="file-tree-wrapper">
          <FileTree
            :treeData="fileList"
            v-model="selectedFiles"
            @rename="handleFileRename"
            @delete="handleFileDelete">
            <template #operations>
              <a-button v-if="false" type="primary" @click="showFileDrawer" :disabled="!selectedFiles.length">组卷</a-button>
              <a-button type="primary" @click="showAddMaterialDrawer" :disabled="!selectedFiles.length">补充材料</a-button>
              <a-button type="danger" @click="handleBatchDelete" :disabled="!selectedFiles.length">批量删除</a-button>
            </template>
          </FileTree>
        </div>
      </div>

      <!-- 中间区域：PDF预览 -->
      <div class="center-panel">
        <div class="pdf-container">
          <div v-if="!fileId" class="empty-state">
            <div class="empty-content">
              <div class="empty-icon">
                <svg viewBox="0 0 64 64" width="64" height="64">
                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  <rect
                    x="12"
                    y="8"
                    width="40"
                    height="48"
                    rx="2"
                    fill="url(#gradient)"
                    opacity="0.1"/>
                  <rect
                    x="12"
                    y="8"
                    width="40"
                    height="8"
                    rx="2"
                    fill="url(#gradient)"
                    opacity="0.3"/>
                  <rect
                    x="16"
                    y="20"
                    width="32"
                    height="2"
                    rx="1"
                    fill="url(#gradient)"
                    opacity="0.2"/>
                  <rect
                    x="16"
                    y="26"
                    width="24"
                    height="2"
                    rx="1"
                    fill="url(#gradient)"
                    opacity="0.2"/>
                  <rect
                    x="16"
                    y="32"
                    width="28"
                    height="2"
                    rx="1"
                    fill="url(#gradient)"
                    opacity="0.2"/>
                </svg>
              </div>
              <h3 class="empty-title">未选择文件</h3>
              <p class="empty-desc">请从左侧文件树中选择要预览的文件</p>
              <!-- <div class="empty-features">
                <div class="feature-item">
                  <span class="feature-icon">👁️</span>
                  <span>实时预览</span>
                </div>
                <div class="feature-item">
                  <span class="feature-icon">🔍</span>
                  <span>在线查看</span>
                </div>
              </div> -->
            </div>
          </div>
          <div v-else class="pdf-viewer-wrapper">
            <div class="pdf-header">
              <div class="file-info">
                <div class="file-icon">
                  <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="#ff4757"/>
                    <text
                      x="12"
                      y="16"
                      text-anchor="middle"
                      font-size="6"
                      font-weight="bold"
                      fill="#fff">PDF</text>
                  </svg>
                </div>
                <div class="file-details">
                  <span class="file-name">{{ getCurrentFileName() }}</span>
                  <span class="file-type">PDF 文档</span>
                </div>
              </div>
              <div class="pdf-actions">
                <div class="action-button delete-btn" @click="handleDeleteCurrentFile">
                  <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM8 9h8v10H8V9zm7.5-5l-1-1h-5l-1 1H5v2h14V4h-3.5z"/>
                  </svg>
                  <span>删除</span>
                </div>
              </div>
            </div>
            <iframe
              :key="fileId"
              :src="`/pdfJsLib/web/viewer.html?file=/api/sysFileInfo/download?id=${fileId}&fileType=.pdf`"
              style="width: 100%;height: calc(100% - 56px);" />
          </div>
          <!-- <sh-cloud-sign v-model="fileId" :showUpload="false" :showCloudSign="false" /> -->
        </div>
      </div>

      <!-- 右侧区域：文书属性 -->

      <doc-properties :is-expanded.sync="isPropertiesExpanded" :doc-data="docData" @save="handleSaveDoc" />
    </div>
    <file-drawer
      :visible="fileDrawerVisible"
      :selectedFiles="selectedFiles"
      @ok="handleFileDrawerOk"
      @cancel="handleFileDrawerCancel" />
    <add-material-drawer
      :visible="addMaterialDrawerVisible"
      @ok="handleAddMaterialOk"
      @cancel="handleAddMaterialCancel" />
  </div>
</template>

<script>
import FileTree from '@/components/FileTree.vue'
import DocProperties from './components/DocProperties.vue'
import FileDrawer from './components/FileDrawer.vue'
import AddMaterialDrawer from './components/AddMaterialDrawer.vue'
import {
  correctionDocTree,
  correctionBaseInfo,
  correctionDocDetail,
  correctionDocDelete, correctionDocEdit, correctionDocAdd
} from '@/api/modular/main/correctiondoc/correctionDocManage';

export default {
  name: 'Electronic',
  components: {
    FileTree,
    DocProperties,
    FileDrawer,
    AddMaterialDrawer
  },
  computed: {
    statusText() {
      const statusMap = {
        'in-correction': '在矫',
        'terminated': '解矫',
        'pending-review': '调查评估'
      }
      return statusMap[this.personStatus] || ''
    },
    docData() {
      return {
        docName: this.docProperties.docName,
        docSource: this.docProperties.docSource,
        pid: this.docProperties.pid,
        docScope: this.docProperties.docScope,
        docCatalog: this.docProperties.docCatalog,
        deptName: this.baseInfo.deptName,
        correctionObjName: this.baseInfo.correctionObjName,
        createTime: this.docProperties.createTime
      }
    }
  },
  data() {
    return {
      selectedFiles: [],
      isPropertiesExpanded: true,
      personStatus: 'in-correction',
      baseInfo: {
        correctionObjName: '张某某',
        certNum: 'xxxx',
        deptId: '1',
        deptName: 'xxxx',
        correctionStartAt: 'xxxx',
        criminalCharge: 'xxxx',
        correctionType: 'xxxx',
        entrustmentDeptName: 'xxxx',
        entrustmentReceiveTime: 'xxxx',
        inveTime: 'xxxx',
        conclusion: 'xxxx'
      },
      fileId: '',
      fileList: [],
      correctionObjId: '',
      docProperties: {
        id: '',
        correctionObjId: '',
        contactId: '',
        docName: '',
        docSource: '',
        docCatalog: '',
        docScope: '',
        createTime: '',
        pid: ''
      },
      fileDrawerVisible: false,
      addMaterialDrawerVisible: false
    }
  },
  watch: {
    selectedFiles: {
      handler(newFiles) {
        // 当选择了文件时更新docProperties
        if (newFiles && newFiles.length > 0) {
          // 获取第一个选中的文件（如果需要处理多选，可以修改此逻辑）
          const selectedFile = newFiles[newFiles.length - 1];

          // 确保选中的是文件而不是文件夹
          if (selectedFile.isFile) {
            // 更新PDF预览的fileId
            this.fileId = selectedFile.id;
            console.log(this.fileId)

            correctionDocDetail({ id: selectedFile.id }).then(res => {
              if (res.success && res.data) {
                console.log(res.data, 'res.data')
                // 整体替换对象以确保响应式
                this.docProperties = {
                  id: res.data.id,
                  correctionObjId: res.data.correctionObjId,
                  contactId: res.data.contactId,
                  docName: res.data.docName,
                  docSource: res.data.docSource,
                  docCatalog: res.data.docCatalog,
                  docScope: res.data.docScope,
                  createTime: res.data.createTime,
                  pid: res.data.pid || res.data.id
                }
              }
            })
          }
        }
      },
      immediate: true // 组件创建时立即执行一次
    }
  },
  created() {
    // 获取路由参数中的correctionObjId，如果没有则使用默认值
    this.correctionObjId = this.$route.query.correctionObjId || 'd1e0f1f1ae324f3ea143d0bed2a5641a'
    // this.correctionObjId = 'd1e0f1f1ae324f3ea143d0bed2a5641a'
    this.fetchData(this.correctionObjId)
  },
  methods: {
    fetchData(id) {
      correctionBaseInfo({ id: id }).then(res => {
        if (res.success && res.data) {
          this.baseInfo = res.data
          this.personStatus = res.data.personStatus
        }
      })
      this.fetchFileList()
    },
    handleSaveDoc(values) {
      correctionDocEdit(Object.assign(this.docProperties, values)).then(res => {
        if (res.success) {
          this.$message.success('保存成功')
          // 重新获取详情确保数据最新
          this.refreshCurrentFileDetail()
        } else {
          this.$message.error(res.message || '保存失败')
        }
      }).catch(error => {
        this.$message.error('保存失败：' + (error.message || error))
      })
    },
    handleBatchDelete() {
      if (!this.selectedFiles.length) {
        this.$message.warning('请先选择需要删除的文件')
        return
      }
      this.$confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedFiles.length} 个文件吗？删除后文书将从档案中去除是否继续执行删除操作`,
        okType: 'danger',
        onOk: () => {
          // 从文件树中删除选中的文件
          const removeFiles = (list) => {
            return list.filter(item => {
              if (item.children) {
                item.children = removeFiles(item.children)
              }
              return !this.selectedFiles.some(selected => selected.id === item.id)
            })
          }
          correctionDocDelete({ ids: this.selectedFiles.map(file => file.id).join(',') }).then(res => {
            if (res.success) {
              this.fileList = removeFiles(this.fileList)
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
          this.selectedFiles = [] // 清空选中
        }
      })
    },
    getCurrentFileName() {
      if (!this.fileId) return ''

      const findCurrentFile = (list) => {
        for (const item of list) {
          if (item.id === this.fileId) {
            return item
          }
          if (item.children) {
            const found = findCurrentFile(item.children)
            if (found) return found
          }
        }
        return null
      }

      const currentFile = findCurrentFile(this.fileList)
      return currentFile ? currentFile.catalogName || '未知文件' : ''
    },
    handleDeleteCurrentFile() {
      if (!this.fileId) {
        this.$message.warning('没有可删除的文件')
        return
      }

      // 找到当前预览的文件信息
      const findCurrentFile = (list) => {
        for (const item of list) {
          if (item.id === this.fileId) {
            return item
          }
          if (item.children) {
            const found = findCurrentFile(item.children)
            if (found) return found
          }
        }
        return null
      }

      const currentFile = findCurrentFile(this.fileList)
      console.log(currentFile)
      const fileName = currentFile ? currentFile.catalogName : '当前文件'

      this.$confirm({
        title: '确认删除',
        content: `确定要删除文件"${fileName}"吗？删除后文书将从档案中去除是否继续执行删除操作`,
        okType: 'danger',
        onOk: () => {
          correctionDocDelete({ ids: this.fileId }).then(res => {
            if (res.success) {
              // 从文件树中删除当前文件
              const removeCurrentFile = (list) => {
                return list.filter(item => {
                  if (item.children) {
                    item.children = removeCurrentFile(item.children)
                  }
                  return item.id !== this.fileId
                })
              }

              this.fileList = removeCurrentFile(this.fileList)

              // 清空当前预览
              this.fileId = ''
              this.selectedFiles = this.selectedFiles.filter(file => file.id !== this.fileId)

              this.$message.success('删除成功')
            } else {
              this.$message.error(res.message || '删除失败')
            }
          }).catch(error => {
            this.$message.error('删除失败：' + (error.message || error))
          })
        }
      })
    },
    showFileDrawer() {
      if (!this.selectedFiles || this.selectedFiles.length === 0) {
        this.$message.warning('请先选择需要组卷的文件')
        return
      }
      this.fileDrawerVisible = true
    },
    handleFileDrawerOk() {
      this.fileDrawerVisible = false
      // 处理组卷确认后的逻辑
      this.$message.success('组卷成功')
      this.selectedFiles = [] // 清空选择
    },
    handleFileDrawerCancel() {
      this.fileDrawerVisible = false
    },
    showAddMaterialDrawer() {
      this.addMaterialDrawerVisible = true
    },
    handleAddMaterialOk(values) {
      console.log('补充材料表单数据：', values)
      correctionDocAdd(Object.assign({
        correctionObjId: this.docProperties.correctionObjId,
        contactId: this.docProperties.correctionObjId,
        creator: this.docProperties.deptId
      }, values)).then(res => {
        if (res.success) {
          this.$message.success('补充材料添加成功')
        } else {
          this.$message.error(res.message || '补充材料添加失败')
        }
      })
      this.addMaterialDrawerVisible = false
    },
    handleAddMaterialCancel() {
      this.addMaterialDrawerVisible = false
    },
    handleFileRename(data) {
      // 先获取原始文件的详细信息
      correctionDocDetail({ id: data.id }).then(res => {
        if (res.success && res.data) {
          // 使用原始数据，只修改docName
          const editData = {
            ...res.data,
            docName: data.newName
          }

                    // 调用重命名API
          correctionDocEdit(editData).then(editRes => {
            if (editRes.success) {
              this.$message.success('重命名成功')
              // 重新获取文件树数据
              this.fetchFileList()

              // 如果重命名的是当前预览的文件，重新获取详情更新docData
              if (this.fileId === data.id) {
                this.refreshCurrentFileDetail()
              }
            } else {
              this.$message.error(editRes.message || '重命名失败')
            }
          }).catch(error => {
            this.$message.error('重命名失败：' + (error.message || error))
          })
        } else {
          this.$message.error(res.message || '获取文件信息失败')
        }
      }).catch(error => {
        this.$message.error('获取文件信息失败：' + (error.message || error))
      })
    },
    handleFileDelete(data) {
      // 调用删除API
      correctionDocDelete({ ids: data.id }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')

          // 如果删除的是当前预览的文件，清空预览
          if (this.fileId === data.id) {
            this.fileId = ''
          }

          // 从选中列表中移除已删除的文件
          this.selectedFiles = this.selectedFiles.filter(file => file.id !== data.id)

          // 重新获取文件树数据
          this.fetchFileList()
        } else {
          this.$message.error(res.message || '删除失败')
        }
      }).catch(error => {
        this.$message.error('删除失败：' + (error.message || error))
      })
    },
    fetchFileList() {
      // 重新获取文件列表的方法
      correctionDocTree({ correctionObjId: this.correctionObjId }).then(res => {
        if (res.success && res.data) {
          this.fileList = res.data
          // 默认选择第一个文档
          this.selectFirstFile()
        } else {
          this.$message.error(res.message || '获取文件目录失败')
        }
      }).catch(error => {
        this.$message.error('获取文件目录失败：' + (error.message || error))
      })
    },
    refreshCurrentFileDetail() {
      // 重新获取当前文件的详情
      if (!this.fileId) return

      correctionDocDetail({ id: this.fileId }).then(res => {
        if (res.success && res.data) {
          // 整体替换对象以确保响应式
          this.docProperties = {
            id: res.data.id,
            correctionObjId: res.data.correctionObjId,
            contactId: res.data.contactId,
            docName: res.data.docName,
            docSource: res.data.docSource,
            docCatalog: res.data.docCatalog,
            docScope: res.data.docScope,
            createTime: res.data.createTime,
            pid: res.data.pid || res.data.id
          }
        }
      }).catch(error => {
        console.error('获取文件详情失败：', error)
      })
    },
    selectFirstFile() {
      // 递归查找第一个文件（非文件夹）
      const findFirstFile = (list) => {
        for (const item of list) {
          // 只有当 isFile 为 true 时才认为是文件
          if (item.isFile === true) {
            return item
          }
          // 如果有子节点，递归查找
          if (item.children && item.children.length > 0) {
            const found = findFirstFile(item.children)
            if (found) return found
          }
        }
        return null
      }

      const firstFile = findFirstFile(this.fileList)
      if (firstFile) {
        // 设置为选中状态
        this.selectedFiles = [firstFile]
      }
    }
  }
}
</script>

<style lang="less" scoped>
.section-title{
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
}
.electronic-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: rgba(217, 232, 245, 1);
}

.electronic-header {
  height: 62px;
  background: rgba(22, 144, 255, 1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .logo {
      height: 36px;
      width: 36px;
    }

    .title {
      font-weight: bold;
      font-size: 32px;
      color: #FFFFFF;
    }

    .sub-title {
      font-size: 14px;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      margin-left: 38px;

      //   border-left: 1px solid #e8e8e8;
    }
  }
}

.electronic-container {
  flex: 1;
  display: flex;
  padding: 12px;
  gap: 12px;
  overflow: hidden;

  .left-panel {
    flex: 0 0 27.60%;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;

    .person-info {
      position: relative;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.64);
      background: #fff;
      padding: 16px;

      .status-tag {
        position: absolute;
        right: -60px;
        top: 6px;
        width: 160px;
        line-height: 30px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        // clip-path: polygon(30% 0%, 70% 0%, 100% 100%, 0% 100%);
        z-index: 1;
        transform: rotate(45deg);

        &.in-correction {
          background: linear-gradient(183deg, #81C3FF 0%, #1690FF 100%);
        }

        &.terminated {
          background: linear-gradient(183deg, #ADBFCF 0%, #778FA4 100%);
        }

        &.pending-review {
          background: linear-gradient(179deg, #FFD275 0%, #FF9C0A 100%);
        }
      }

      .person-content {

        .person-avatar {
          display: flex;
          padding-left: 8px;
          align-items: center;
          margin-bottom: 16px;
          font-weight: bold;
          font-size: 24px;
          color: #1690FF;

          img {
            width: 36px;
            height: 36px;
            margin-right: 10px;
            border-radius: 50%;
            border: 2px solid #1890ff;
            padding: 2px;

          }
        }

        .info-item {
          margin-bottom: 8px;
          display: flex;

          .label {
            color: #666;
            // color: rgba(170, 170, 170, 1);

            width: 80px;
            flex-shrink: 0;
            text-align: right;
          }

          .value {
            color: #333;
            flex: 1;
          }
        }
      }
    }

    .file-tree-wrapper {
      flex: 1;
      background: #fff;
      padding: 16px;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.64);
      overflow: hidden;
    }
  }

  .center-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.64);

        .pdf-container {
      flex: 1;
      background: #fff;
      overflow: hidden;
      position: relative;

            .empty-state {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f8fafe 0%, #e8f2ff 50%, #f0f7ff 100%);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(22, 144, 255, 0.03) 0%, transparent 50%);
          animation: pulse 4s ease-in-out infinite;
        }

        .empty-content {
          text-align: center;
          padding: 60px 40px;
          border-radius: 20px;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          box-shadow:
            0 20px 40px rgba(22, 144, 255, 0.08),
            0 8px 16px rgba(22, 144, 255, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
          border: 1px solid rgba(22, 144, 255, 0.1);
          max-width: 420px;
          position: relative;
          z-index: 1;

          .empty-icon {
            margin-bottom: 32px;
            opacity: 0.9;
            animation: float 3s ease-in-out infinite;
          }

          .empty-title {
            font-size: 28px;
            font-weight: 700;
            color: #1690ff;
            margin: 0 0 16px 0;
            letter-spacing: -0.5px;
          }

          .empty-desc {
            font-size: 16px;
            color: #64748b;
            margin: 0 0 40px 0;
            line-height: 1.6;
            font-weight: 400;
          }

          .empty-features {
            display: flex;
            justify-content: center;
            gap: 20px;

            .feature-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 12px;
              padding: 20px 16px;
              border-radius: 12px;
              background: rgba(22, 144, 255, 0.06);
              border: 1px solid rgba(22, 144, 255, 0.1);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              min-width: 100px;

              &:hover {
                transform: translateY(-4px);
                background: rgba(22, 144, 255, 0.1);
                border-color: rgba(22, 144, 255, 0.2);
                box-shadow: 0 8px 20px rgba(22, 144, 255, 0.15);
              }

              .feature-icon {
                font-size: 24px;
                filter: drop-shadow(0 2px 4px rgba(22, 144, 255, 0.1));
              }

              span:not(.feature-icon) {
                font-size: 13px;
                color: #1690ff;
                font-weight: 600;
                text-align: center;
                letter-spacing: 0.3px;
              }
            }
          }
        }
      }

      @keyframes pulse {
        0%, 100% { opacity: 0.3; transform: scale(1); }
        50% { opacity: 0.6; transform: scale(1.05); }
      }

      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-8px); }
      }

      .pdf-viewer-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;

                                            .pdf-header {
           display: flex;
           justify-content: space-between;
           align-items: center;
           padding: 16px 20px;
           background: #1690ff;
           border-bottom: none;
           height: 56px;
           box-shadow: 0 2px 8px rgba(22, 144, 255, 0.2);

           .file-info {
             flex: 1;
             display: flex;
             align-items: center;
             gap: 12px;

             .file-icon {
               flex-shrink: 0;
               display: flex;
               align-items: center;
               justify-content: center;
               width: 36px;
               height: 36px;
               background: rgba(255, 255, 255, 0.15);
               border-radius: 8px;
               border: 1px solid rgba(255, 255, 255, 0.2);

               svg {
                 opacity: 0.9;
               }
             }

             .file-details {
               display: flex;
               flex-direction: column;
               gap: 2px;
               min-width: 0;
               flex:1;

               .file-name {
                 font-size: 15px;
                 font-weight: 600;
                 color: #ffffff;
                 display: block;
                 max-width: 90%;
                 overflow: hidden;
                 text-overflow: ellipsis;
                 white-space: nowrap;
                 line-height: 1.2;
               }

               .file-type {
                 font-size: 12px;
                 font-weight: 500;
                 color: rgba(255, 255, 255, 0.8);
                 text-transform: uppercase;
                 letter-spacing: 0.5px;
               }
             }
           }

           .pdf-actions {
             .action-button {
               display: flex;
               align-items: center;
               gap: 6px;
               padding: 8px 16px;
               background: rgba(255, 255, 255, 0.1);
               border: 1px solid rgba(255, 255, 255, 0.2);
               border-radius: 6px;
               color: #ffffff;
               font-size: 13px;
               font-weight: 500;
               cursor: pointer;
               transition: all 0.2s ease;

               &:hover {
                 background: rgba(255, 255, 255, 0.2);
                 border-color: rgba(255, 255, 255, 0.3);
                 box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
               }

               &:active {
                 transform: translateY(1px);
                 box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
               }

               svg {
                 fill: currentColor;
                 opacity: 0.9;
               }

               span {
                 font-weight: 500;
               }
             }
           }
         }
      }
    }
  }
}

.property-item {
  margin-bottom: 16px;

  .label {
    display: block;
    color: #666;
    margin-bottom: 8px;
  }

  .value {
    color: #333;
  }

  /deep/ .ant-form-item {
    margin-bottom: 0;
  }
}
</style>

<template>
  <sh-drawer
    title="请确认组卷信息"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="file-drawer">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 卷宗信息 -->
        <div class="section-header">
          <div class="blue-line"></div>
          <span class="title">卷宗信息</span>
        </div>
        <div class="info-form">
          <div class="form-row">
            <div class="form-item">
              <span class="label required">卷宗名称：</span>
              <a-select v-model="formData.name" placeholder="调查评估卷宗" style="width: 100%">
                <a-select-option value="1">调查评估卷宗</a-select-option>
              </a-select>
            </div>
            <div class="form-item">
              <span class="label required">具体名称：</span>
              <a-input v-model="formData.specificName" placeholder="请填写具体卷宗名称" />
            </div>
            <div class="form-item">
              <span class="label required">组卷单位：</span>
              <a-input v-model="formData.unit" :value="'某某某司法局'" disabled />
            </div>
            <div class="form-item">
              <span class="label required">组卷日期：</span>
              <a-input v-model="formData.date" :value="'2025年2月8日'" disabled />
            </div>
          </div>
        </div>

        <!-- 组卷区域 -->
        <div class="section-header" style="margin-top: 24px;">
          <div class="blue-line"></div>
          <span class="title">组卷区域</span>
        </div>
        <div class="file-section">
          <!-- 左侧文件树 -->
          <div class="file-tree-section">
            <div class="section-title">请选择档案中所需组卷的文书：</div>
            <div class="file-tree-content">
              <FileTree
                :treeData="treeData"
                v-model="selectedTreeFiles"
                :editable="false"
                @input="handleTreeSelect"
              />
            </div>
          </div>

          <!-- 箭头1 -->
          <div class="arrow-section">
            <a-icon type="right" class="arrow-icon" />
          </div>

          <!-- 中间已选文件列表 -->
          <div class="selected-files-section">
            <div class="section-title">已选择的组卷的文书：</div>

            <div class="selected-files-content">
              <div class="selected-files-header">已选择 <b style="color: rgba(22, 144, 255, 1);">8份</b> 文书进行组卷，可拖动文书排序，确定后点击生成预览</div>
              <div class="selected-files-list">
                <sortable-table
                  :dataSource.sync="fileList"
                  @delete="handleDelete"
                />
              </div>
            </div>
          </div>

          <!-- 箭头2 -->
          <div class="arrow-section">
            <a-icon type="right" class="arrow-icon" />
          </div>

          <!-- 右侧预览 -->
          <div class="preview-section">
            <div class="section-title">卷宗预览：</div>
            <div class="preview-content">
              <iframe
                src="http://180.76.227.71:2999/pdf/web/viewer.html?file=http://118.31.34.211:6016/api/sysFileInfo/preview?id=1888799329387327489"
                style="width: 100%;height: 100%;" />            </div>
            <a-button type="primary" class="preview-btn" @click="handlePreview">生成预览卷宗</a-button>
          </div>
        </div>
      </div>

    </div>
    <!-- 底部说明和按钮 -->
    <template #footer>
      <a-space>
        <span class="footer-text">说明：组卷后所有文书会形成一份统一卷宗，自动生成封面和目录，页码保证连续性</span>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleOk">
          提交
        </a-button>
      </a-space>
    </template>
  </sh-drawer>
</template>

<script>
import FileTree from '@/components/FileTree.vue'
import SortableTable from './SortableTable.vue'
import { correctionDocTree } from '@/api/modular/main/correctiondoc/correctionDocManage'

export default {
  name: 'FileDrawer',
  components: {
    FileTree,
    SortableTable
  },
  data() {
    return {
      loading: false,
      formData: {
        name: undefined,
        specificName: '',
        unit: '某某某司法局',
        date: '2025年2月8日'
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 60,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '文书名称',
          dataIndex: 'name',
          ellipsis: true
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          width: 60,
          scopedSlots: { customRender: 'operation' }
        }
      ],
      selectedTreeFiles: [],
      fileList: [],
      treeData: []
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedFiles: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 抽屉打开时重置状态
        this.resetState()
        // 获取树形数据
        this.fetchTreeData()
        // 设置外部传入的选中状态
        if (this.selectedFiles.length > 0) {
          this.fileList = this.selectedFiles.map(file => ({
            ...file,
            children: file.children.length ? file.children : null,

            key: file.id
          }))
          this.selectedTreeFiles = this.selectedFiles
        }
      }
    },
    selectedFiles: {
      immediate: true,
      handler(val) {
        if (this.visible && val.length > 0) {
          this.fileList = val.map(file => ({
            ...file,
            children: file.children.length ? file.children : null,

            key: file.id
          }))
          this.selectedTreeFiles = val
        }
      }
    }
  },
  methods: {
    resetState() {
      // 重置表单
      this.formData = {
        name: undefined,
        specificName: '',
        unit: '某某某司法局',
        date: '2025年2月8日'
      }
      // 重置文件列表和选中状态
      this.fileList = []
      this.selectedTreeFiles = []
      this.treeData = []
    },
    fetchTreeData() {
      // 获取文件树形数据
      correctionDocTree({ correctionObjId: 'd1e0f1f1ae324f3ea143d0bed2a5641a' }).then(res => {
        if (res.success && res.data) {
          this.treeData = res.data
        } else {
          this.$message.error(res.message || '获取文件目录失败')
        }
      }).catch(error => {
        this.$message.error('获取文件目录失败：' + (error.message || error))
      })
    },
    handleOk() {
      if (!this.formData.name) {
        this.$message.warning('请选择卷宗名称')
        return
      }
      if (!this.formData.specificName) {
        this.$message.warning('请填写具体名称')
        return
      }
      if (this.fileList.length === 0) {
        this.$message.warning('请至少选择一个文件')
        return
      }

      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$emit('ok')
      }, 1500)
    },
    handleCancel() {
      this.resetState()
      this.$emit('cancel')
    },
    handleDelete(record) {
      this.fileList = this.fileList.filter(item => item.id !== record.id)
      this.selectedTreeFiles = this.selectedTreeFiles.filter(item => item.id !== record.id)
    },
    handlePreview() {
      this.$message.info('正在生成预览...')
    },
    handleTreeSelect(files) {
      this.fileList = files.map(file => ({
        ...file,
        key: file.id
      }))
    }
  }
}
</script>

<style lang="less" scoped>
.file-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .main-content {
    flex: 1;
    overflow: auto;
    padding: 24px;
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .blue-line {
      width: 4px;
      height: 16px;
      background: #1890ff;
      border-radius: 2px;
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }

  .info-form {
    background: #f8f8f8;
    padding: 16px;
    border-radius: 8px;

    .form-row {
      display: grid;
      // grid-template-columns: repeat(2, 1fr);
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
    }

    .form-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        color: #666;
        white-space: nowrap;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }
    }
  }

  .file-section {
    display: flex;
    align-items: stretch;
    gap: 12px;
    height: calc(100vh - 320px);
    min-height: 400px;
    padding-top: 32px;

    .section-box {
      position: relative;
      flex: 1;
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      padding: 16px;

    }

    .file-tree-section {
      position: relative;
      .section-box();
      display: flex;
      flex-direction: column;
      gap: 16px;

      .file-tree-content {
        flex: 1;
        overflow: hidden;
      }
    }

    .arrow-section {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      padding-top: 32px;

      .arrow-icon {
        font-size: 24px;
        color: #1890ff;
        // background: #f0f7ff;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px 4px 4px 4px;
border: 1px solid #1690FF;
        // border-radius: 50%;
      }
    }

    .selected-files-section {
      position: relative;
      .section-box();
      display: flex;
      flex-direction: column;
      gap: 16px;

      .selected-files-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      .selected-files-header {
        color: #666666;
        font-size: 14px;
        padding: 12px 0;
        padding-top: 0;
        border-bottom: 1px solid #e8e8e8;
      }

      .selected-files-list {
        flex: 1;
        overflow: auto;
        padding-top: 12px;
      }
    }

    .preview-section {
      position: relative;
      .section-box();
      display: flex;
      flex-direction: column;
      gap: 16px;

      .preview-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        .preview-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }

      .preview-btn {
        width: 100%;
      }
    }
  }

  .sub-title {
    font-size: 14px;
    color: #666;
  }

  .drawer-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .footer-text {
        color: #666;
      }

      .footer-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }
}

/deep/ .ant-drawer-body {
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.delete-link {
  color: #ff4d4f;

  &:hover {
    color: #ff7875;
  }
}

.footer-text {
  color: #666;
  margin-right: 20px;
}
</style>

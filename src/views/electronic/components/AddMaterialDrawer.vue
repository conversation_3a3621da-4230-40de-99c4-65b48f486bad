<template>
  <sh-drawer
    title="补充材料"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="add-material-drawer">
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 材料上传单独占一行 -->
        <div class="form-row">
          <a-form-item
            label="材料上传"
            class="full-width"
            :label-col="singleRowCol.labelCol"
            :wrapper-col="singleRowCol.wrapperCol"
          >
            <sh-file-uploader
              v-decorator="[ 'fileUpload', { rules: [{ required: true, message: '请上传材料文件' }] }]"
              uploadUrl="/api/sysFileInfo/uploadOss"
              :maxFileSize="5 * 1024 * 1024"
              acceptedFormats=".pdf"
              :allowMultiple="false"
            >
              <a class="upload-link">文书上传（根据接管规范，只允许上传清晰PDF文档）</a>
            </sh-file-uploader></a-form-item>
        </div>

        <!-- 文件名称和矫正对象一行 -->
        <div class="form-row">
          <a-form-item label="文件名称">
            <a-input v-decorator="[ 'docName',{ rules: [{ required: true, message: '请输入文件名称' }]}]" placeholder="请输入文件名称" />
          </a-form-item>
          <a-form-item label="矫正对象">
            <a-input v-decorator="[ 'personName', { initialValue: '张三' } ]" disabled />
          </a-form-item>
        </div>

        <!-- 材料来源和文书目录一行 -->
        <div class="form-row">
          <a-form-item label="材料来源">
            <sh-select dictType="doc_source" v-decorator="[ 'docSource', { initialValue: '1' } ]" disabled/>
          </a-form-item>
          <a-form-item label="文书目录">
            <sh-tree-select
              v-decorator="['docCatalog', { rules: [{ required: true, message: '请选择文书目录！' }]}]"
              apiURL="/correctionDocCatalog/tree"
              labelKey="catalogName"
              valueKey="id"
              style="width: 100%"
              :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
              placeholder="请选择文书目录"
              treeDefaultExpandAll
            />
          </a-form-item>
        </div>

        <!-- 查看限制和所属单位一行 -->
        <div class="form-row">
          <a-form-item label="查看限制">
            <a-select v-decorator="[ 'docScope', { rules: [{ required: true, message: '请选择查看权限' }] } ]">
              <a-select-option value="1">默认权限（本级及下级）</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="所属单位">
            <a-input v-decorator="[ 'deptName', { initialValue: 'deptName' } ]" disabled/>
          </a-form-item>
        </div>

        <!-- 归档日期单独占一行 -->
        <div class="form-row">
          <a-form-item
            label="归档日期"
            class="full-width"
            :label-col="singleRowCol.labelCol"
            :wrapper-col="singleRowCol.wrapperCol"
          >
            <a-input v-decorator="[ 'createTime', { initialValue: moment().format('YYYY-MM-DD HH:mm:ss') } ]" disabled />
          </a-form-item>
        </div>
      </a-form>
    </div>
  </sh-drawer>
</template>

<script>
import moment from 'moment';

export default {
  name: 'AddMaterialDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: this.$form.createForm(this),
      singleRowCol: {
        labelCol: { span: 3 },
        wrapperCol: { span: 21 }
      }
    }
  },
  beforeCreate() {
    this.form = this.$form.createForm(this)
  },
  methods: {
    moment,
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loading = true
          // 模拟提交
          setTimeout(() => {
            this.loading = false
            this.$emit('ok', values)
            this.form.resetFields()
          }, 1500)
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
.add-material-drawer {
  padding: 24px;

  .form-row {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .ant-form-item {
      flex: 1;
      margin-bottom: 0;

      &.full-width {
        flex: 0 0 100%;
      }
    }
  }

  .upload-link {
    color: #1890ff;
    cursor: pointer;
    &:hover {
      color: #40a9ff;
    }
  }
}

/deep/ .ant-form-item-label {
  text-align: right;
  line-height: 32px;
  label {
    color: #666;
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }
}

/deep/ .ant-form-item-control {
  // line-height: 32px;
}
/deep/.upload-area p{
    margin:0px ;
}
</style>

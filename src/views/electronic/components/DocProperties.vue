<template>
  <div class="right-panel-wrapper" :class="{ collapsed: !isExpanded }">
    <a-button
      class="collapse-btn"
      type="link"
      @click="$emit('update:isExpanded', !isExpanded)"
    >
      <a-icon :type="isExpanded ? 'right' : 'left'"/>
    </a-button>
    <div class="right-panel" v-show="isExpanded">
      <div class="doc-properties">
        <div class="section-title" style="margin-bottom: 16px;font-weight: bold;">
          文书属性
          <!-- <a-button type="primary" size="small" @click="showDrawer">
            选择文件
          </a-button> -->
        </div>
        <a-form layout="vertical" :form="form" :colon="true" class="properties-content">
          <div class="property-item">
            <a-form-item label="文件名称">
              <a-input
                v-decorator="[ 'docName', { initialValue: docData.docName, rules: [{ required: true, message: '请输入文件名称' }] } ]"
                placeholder="请输入文件名称"/>
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="矫正对象">
              <a-input v-decorator="[ 'correctionObjName', { initialValue: docData.correctionObjName } ]" disabled/>
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="材料来源">
              <sh-select dictType="doc_source" v-decorator="[ 'docSource', { initialValue: docData.docSource, rules: [{ required: true, message: '请选择材料来源' }] } ]" />
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="文书目录">
              <sh-tree-select
                v-decorator="['docCatalog', { initialValue: docData.docCatalog, rules: [{ required: true, message: '请选择文书目录！' }]}]"
                apiURL="/correctionDocCatalog/tree"
                labelKey="catalogName"
                valueKey="id"
                style="width: 100%"
                :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                placeholder="请选择文书目录"
                treeDefaultExpandAll
              >
              </sh-tree-select>
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="查看权限">
              <a-select v-decorator="[ 'docScope', { initialValue: docData.docScope||undefined, rules: [{ required: true, message: '请选择查看权限' }] } ]">
                <a-select-option value="1">默认权限（本级及下级）</a-select-option>
              </a-select>
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="所属单位">
              <a-input v-decorator="[ 'deptName', { initialValue: docData.deptName } ]" disabled/>
            </a-form-item>
          </div>
          <div class="property-item">
            <a-form-item label="归档日期">
              <a-input v-decorator="[ 'createTime', { initialValue: docData.createTime } ]" disabled/>
            </a-form-item>
          </div>
        </a-form>
        <div class="properties-footer">
          <a-button type="primary" block @click="handleSubmit">保存</a-button>
        </div>
      </div>
    </div>
    <file-drawer
      :visible="drawerVisible"
      :selectedFiles="selectedFiles"
      @ok="handleDrawerOk"
      @cancel="handleDrawerCancel"
    />
  </div>
</template>

<script>
import FileDrawer from './FileDrawer.vue'

export default {
  name: 'DocProperties',
  components: {
    FileDrawer
  },
  props: {
    isExpanded: {
      type: Boolean,
      default: true
    },
    docData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      drawerVisible: false,
      selectedFiles: []
    }
  },
  beforeCreate() {
    this.form = this.$form.createForm(this)
  },
  watch: {
    docData: {
      handler(newVal) {
        if (newVal && this.form) {
          this.$nextTick(() => {
            console.log(newVal)
            this.form.setFieldsValue({
              docName: newVal.docName,
              correctionObjName: newVal.correctionObjName,
              docSource: newVal.docSource,
              docCatalog: newVal.docCatalog,
              docScope: newVal.docScope,
              deptName: newVal.deptName,
              createTime: newVal.createTime
            })
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$emit('save', values)
        }
      })
    },
    showDrawer() {
      this.drawerVisible = true
    },
    handleDrawerOk() {
      this.drawerVisible = false
      // 处理选中文件的逻辑
    },
    handleDrawerCancel() {
      this.drawerVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form-item-label{
  padding-bottom: 0;
  height: 24px !important;
  line-height: 24px !important;
  min-height: 24px !important;
}
.section-title{
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
}
.right-panel-wrapper /deep/ .ant-form-item-label {
  height: inherit !important;
}

.right-panel-wrapper {
  position: relative;
  display: flex;
  flex: 0 0 25.65%;
  min-width: 0;
  transition: flex-basis 0.3s;

  &.collapsed {
    flex-basis: 0;
  }

  .collapse-btn {
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 64px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(125, 125, 125, 1);
    border: 1px solid rgba(255, 255, 255, 0.64);
    border-right: none;
    border-radius: 12px 0 0 12px;
    z-index: 1;
    color: #fff;
    opacity: 0.8;

    &:hover {
      opacity: 1;
    }
  }

  .right-panel {
    flex: 1;
    background: #fff;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.64);
    padding: 16px;
    overflow: hidden;

    .doc-properties {
      height: 100%;
      display: flex;
      flex-direction: column;

      .properties-content {
        flex: 1;
        overflow: auto;
      }

      .properties-footer {
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}

.property-item {
  margin-bottom: 6px;

  .label {
    color: #666;
    margin-bottom: 8px;
  }

  .value {
    color: #333;
  }

  /deep/ .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-label {
      line-height: 1;
      margin-bottom: 8px;

      label {
        color: #666;
      }
    }
  }
}
</style>

<template>
  <div class="sortable-table">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="false"

    >
      <template slot="order" slot-scope="text, record, index">
        {{ index + 1 }}
        <a-icon class="my-handle" type="unordered-list" />
      </template>

      <template slot="operation" slot-scope="text, record">
        <a @click="handleDelete(record)" class="delete-link">
          <a-icon type="delete" />
        </a>
      </template>
    </a-table>
  </div>
</template>

<script>
import Sortable from 'sortablejs'

export default {
  name: 'SortableTable',
  props: {
    dataSource: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 60,
          scopedSlots: { customRender: 'order' }
        },
        {
          title: '文书名称',
          dataIndex: 'name'
          // ellipsis: true
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          width: 60,
          scopedSlots: { customRender: 'operation' }
        }
      ]
    }
  },
  mounted() {
    const el = this.$el.querySelector('.ant-table-tbody')
    this.sortable = new Sortable(el, {
      animation: 150,
      handle: '.ant-table-row',
      ghostClass: 'sortable-ghost',
      dragClass: 'sortable-drag',
      forceFallback: true,
      onStart: (evt) => {

      },
      onEnd: ({ oldIndex, newIndex, item }) => {
        if (oldIndex !== newIndex) {
          const data = [...this.dataSource]
          const item = data.splice(oldIndex, 1)[0]
          data.splice(newIndex, 0, item)
          this.$emit('update:dataSource', data)
        }
      }
    })
  },
  methods: {
    handleDelete(record) {
      this.$emit('delete', record)
    }
  }
}
</script>

<style lang="less" scoped>
.sortable-table {
  /deep/ .ant-table-tbody {
    tr {
      cursor: move;
      &.sortable-ghost {
        background-color: #f5f5f5;
        width: 100%;
        opacity: 0.8;
      }
      &.sortable-drag {
        background-color: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.delete-link {
  color: #ff4d4f;
  &:hover {
    color: #ff7875;
  }
}
</style>

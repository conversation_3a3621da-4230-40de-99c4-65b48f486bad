<template>
  <a-modal
    title="编辑出圈预警信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正对象姓名" v-decorator="['name', {rules: [{required: true, message: '请输入矫正对象姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正单位名称" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="预警时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择预警时间" v-decorator="['warningTime',{rules: [{ required: true, message: '请选择预警时间！' }]}]" @change="onChangewarningTime"/>
        </a-form-item>
        <a-form-item
          label="是否出界"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入是否出界" v-decorator="['outOfBound', {rules: [{required: true, message: '请输入是否出界！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { outOfCircleWarningEdit } from '@/api/modular/main/outofcirclewarning/outOfCircleWarningManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        warningTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              jzdxId: record.jzdxId,
              name: record.name,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              sfzh: record.sfzh,
              outOfBound: record.outOfBound
            }
          )
        }, 100)
        // 时间单独处理
        if (record.warningTime != null) {
            this.form.getFieldDecorator('warningTime', { initialValue: moment(record.warningTime, 'YYYY-MM-DD') })
        }
        this.warningTimeDateString = moment(record.warningTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.warningTime = this.warningTimeDateString
            outOfCircleWarningEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangewarningTime(date, dateString) {
        this.warningTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

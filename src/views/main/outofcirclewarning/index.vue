<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.name" allow-clear placeholder="请输入矫正对象姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.sfzh" allow-clear placeholder="请输入身份证号"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="预警时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  format="YYYY-MM-DD"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <x-down
            ref="batchExport"
            @batchExport="batchExport"
          />
        </template>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
  import moment from 'moment'
import {
  outOfCircleWarningExport,
  outOfCircleWarningPage
} from '@/api/modular/main/outofcirclewarning/outOfCircleWarningManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      XDown,
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
          ellipsis: true,
          title: '姓名',
            align: 'center',
            dataIndex: 'name'
          },
          {
          ellipsis: true,
          title: '监管等级',
            align: 'center',
            dataIndex: 'jzdxId'
          },
          {
          ellipsis: true,
          title: '矫正单位id',
            align: 'center',
            dataIndex: 'jzjg'
          },
          {
          ellipsis: true,
          title: '身份证号',
            align: 'center',
            dataIndex: 'sfzh'
          },
          {
          ellipsis: true,
          title: '预警时间',
            align: 'center',
            dataIndex: 'warningTime'
          },
          {
          ellipsis: true,
          title: '是否出界',
            align: 'center',
            dataIndex: 'outOfBound'
          },
          {
          ellipsis: true,
          title: '是否出界',
            align: 'center',
            dataIndex: 'outOfBound'
          },
          {
          ellipsis: true,
          title: '出界类型',
            align: 'center',
            dataIndex: 'outOfBound'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return outOfCircleWarningPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */

      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      batchExport () {
        outOfCircleWarningExport().then((res) => {
          this.$refs.batchExport.downloadfile(res)
        })
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jiedaoId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据状态">
                <a-select style="width: 100%" v-model="queryParam.zt" placeholder="请选择数据状态">
                  <a-select-option v-for="(item,index) in ztData" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.pname" allow-clear placeholder="请输入社区矫正对象姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="10" :sm="24">
              <a-form-item label="任务开始时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  :show-time="{
                    hideDisabledOptions: true,
                    defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
                  }"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.recordId"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('interviewJobDistribute:add')" >
        </template>
        <!--        <span slot="ztscopedSlots" slot-scope="text">-->
        <!--          {{ '${column.dictTypeCode}' | dictType(text) }}-->
        <!--        </span>-->
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">事件详情</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { interviewJobDistributePage, interviewJobDistributeDelete } from '@/api/modular/main/interviewjobdistribute/interviewJobDistributeManage'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage'
  export default {
    components: {
      STable,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            ellipsis: true,
            title: '数据状态',
            align: 'center',
            dataIndex: 'zt'
          },
          {
            ellipsis: true,
            title: '姓名',
            align: 'center',
            dataIndex: 'pname'
          },
          {
            ellipsis: true,
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jiedaoName'
          },
          {
            ellipsis: true,
            title: '任务类型',
            align: 'center',
            dataIndex: 'rwlx'
          },
          {
            ellipsis: true,
            title: '任务开始时间',
            align: 'center',
            dataIndex: 'rwkssj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD')
            }
          },
          {
            ellipsis: true,
            title: '任务结束时间',
            align: 'center',
            dataIndex: 'rwjssj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD')
            }
          },
          {
            ellipsis: true,
            title: '走访人员',
            align: 'center',
            dataIndex: 'zfry'
          },
          {
            title: '操作',
            width: '150px',
            dataIndex: 'action',
            scopedSlots: { customRender: 'action' }
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return interviewJobDistributePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        orgTree: [],
        ztData: [
          { code: '待处理', name: '待处理' },
          { code: '已退回', name: '已退回' },
          { code: '待反馈', name: '待反馈' },
          { code: '已办结', name: '已办结' },
          { code: '超时未处理', name: '超时未处理' },
          { code: '超时办结', name: '超时办结' }
        ]
      }
    },
    created () {
      this.getOrgTree()
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD HH:mm:ss')
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD HH:mm:ss')
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      interviewJobDistributeDelete (record) {
        interviewJobDistributeDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangezfsj(date, dateString) {
        this.zfsjDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

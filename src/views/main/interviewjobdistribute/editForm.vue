<template>
  <a-modal
    title="走访任务下派"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-divider orientation="left">任务下发信息</a-divider>
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['recordId']" /></a-form-item>
        <a-form-item v-show="false"><a-input v-decorator="['pid']" /></a-form-item>
        <a-row :gutter="24">
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="任务标题"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入任务标题" :disabled="linkDisabled" v-decorator="['rwbt', {rules: [{required: true, message: '请输入任务标题！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="任务开始时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-date-picker style="width: 100%" placeholder="请选择任务开始时间" :disabled="linkDisabled" v-decorator="['rwkssj',{rules: [{ required: true, message: '请选择任务开始时间！' }]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="任务结束时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-date-picker style="width: 100%" placeholder="请选择任务结束时间" :disabled="linkDisabled" v-decorator="['rwjssj',{rules: [{ required: true, message: '请选择任务结束时间！' }]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="任务类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入任务类型" :disabled="linkDisabled" v-decorator="['rwlx']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="任务执行人类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入任务执行人类型" :disabled="linkDisabled" v-decorator="['rwzxrlx']" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="任务说明"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea :rows="4" placeholder="请输入任务说明" :disabled="linkDisabled" v-decorator="['rwsm', {rules: [{required: true, message: '请输入任务说明！'}]}]"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

    <a-spin :spinning="confirmLoading">
      <a-divider orientation="left">社区矫正对象</a-divider>
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item
              label="社区矫正对象"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入社区矫正对象姓名" :disabled="linkDisabled" v-decorator="['pname', {rules: [{required: true, message: '请输入社区矫正对象姓名！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="具体罪名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入具体罪名" :disabled="linkDisabled" v-decorator="['jtzm', {rules: [{required: true, message: '请输入具体罪名！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="联系电话"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入联系电话" :disabled="linkDisabled" v-decorator="['lxdh', {rules: [{required: true, message: '请输入联系电话！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入身份证号" :disabled="linkDisabled" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="机构名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入机构名称" :disabled="linkDisabled" v-decorator="['jiedaoName', {rules: [{required: true, message: '请输入机构名称！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

    <a-spin :spinning="confirmLoading">
      <a-divider orientation="left">走访反馈信息</a-divider>
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item
              label="任务编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入任务编号" :disabled="linkDisabled" v-decorator="['rwbh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="走访时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-date-picker style="width: 100%" placeholder="请选择走访时间" :disabled="linkDisabled" v-decorator="['zfsj']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="走访区县"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入走访区县" :disabled="linkDisabled" v-decorator="['zfqx']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="走访人员"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入走访人员" :disabled="linkDisabled" v-decorator="['zfry']" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="走访场所"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入走访场所" :disabled="linkDisabled" v-decorator="['zfcs']" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="是否有家庭重大变故"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group :disabled="linkDisabled" v-decorator="['sfyjtzdbg']" >
                <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="是否存在行为异常"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group :disabled="linkDisabled" v-decorator="['sfxwyc']" >
                <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="是否有固定收入来源"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group :disabled="linkDisabled" v-decorator="['sfgdsrly']" >
                <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="是否需要帮扶"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-radio-group :disabled="linkDisabled" v-decorator="['sfxybf']" >
                <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" pull="3">
            <a-form-item
              label="备注"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea :rows="4" placeholder="请输入备注" :disabled="linkDisabled" v-decorator="['beizhu']"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        linkRequired: true,
        linkDisabled: true,
        yesOrNoData: [
          { code: '是', name: '是' },
          { code: '否', name: '否' }
        ]
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              recordId: record.recordId,
              zt: record.zt,
              dishiId: record.dishiId,
              dishiName: record.dishiName,
              jiedaoId: record.jiedaoId,
              jiedaoName: record.jiedaoName,
              rwbt: record.rwbt,
              rwlx: record.rwlx,
              rwzxrlx: record.rwzxrlx,
              rwsm: record.rwsm,
              pid: record.pid,
              pname: record.pname,
              jtzm: record.jtzm,
              lxdh: record.lxdh,
              sfzh: record.sfzh,
              rwbh: record.rwbh,
              zfqx: record.zfqx,
              zfqxbm: record.zfqxbm,
              zfry: record.zfry,
              zfcs: record.zfcs,
              sfyjtzdbg: record.sfyjtzdbg,
              sfxwyc: record.sfxwyc,
              sfgdsrly: record.sfgdsrly,
              sfxybf: record.sfxybf,
              beizhu: record.beizhu,
              sfysyyc: record.sfysyyc,
              sfjiejiao: record.sfjiejiao
            }
          )
        }, 100)
        // 时间单独处理
        if (record.rwkssj != null) {
            this.form.getFieldDecorator('rwkssj', { initialValue: moment(record.rwkssj, 'YYYY-MM-DD') })
        }
        // 时间单独处理
        if (record.rwjssj != null) {
            this.form.getFieldDecorator('rwjssj', { initialValue: moment(record.rwjssj, 'YYYY-MM-DD') })
        }
        // 时间单独处理
        if (record.zfsj != null) {
            this.form.getFieldDecorator('zfsj', { initialValue: moment(record.zfsj, 'YYYY-MM-DD') })
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

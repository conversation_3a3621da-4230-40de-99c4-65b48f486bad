<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.name" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.orgIds"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="信访类型">
                <a-select v-model="queryParam.noticeType" placeholder="请选择信访类型">
                  <a-select-option v-for="(item, index) in letterTypeDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="更新时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="在矫状态">
                <a-select v-model="queryParam.zhuangtai" placeholder="请选择">
                  <a-select-option value="200" >在矫</a-select-option>
                  <a-select-option value="0" >解矫</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {};$refs.table.refresh(true)">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <x-down
            ref="batchExport"
            @batchExport="batchExport"/>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">查看详情</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
  import { petitionLetterInfoPage, petitionLetterInfoExport } from '@/api/modular/main/petitionletterinfo/petitionLetterInfoManage'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  import moment from 'moment';
  export default {
    components: {
      STable,
      XDown,
      editForm
    },
    data () {
      return {
        // 查询参数
        queryParam: { zhuangtai: '200' },
        // 表头
        columns: [
          { title: '姓名', align: 'center', dataIndex: 'name' },
          { title: '矫正单位', align: 'center', dataIndex: 'deptName' },
          { title: '信访类型', align: 'center', dataIndex: 'letterTypeName' },
          { title: '信访渠道', align: 'center', dataIndex: 'letterChannelName' },
          { title: '信访时间', align: 'center', dataIndex: 'letterTime' },
          { title: '是否异地信访', align: 'center', dataIndex: 'offsite' },
          { title: '更新时间', align: 'center', dataIndex: 'updateTime' },
          {
            ellipsis: true,
            title: '在矫状态',
            align: 'center',
            dataIndex: 'zhuangtai',
            customRender: (val) => {
              if (!val) {
                return '';
              }
              return val === '200' ? '在矫' : '解矫'
            }
          },
          { title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        orgTree: [],
        letterTypeDropDown: [],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return petitionLetterInfoPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.$route.query.date) {
        this.queryParam.dates = [moment(this.$route.query.date), moment(this.$route.query.date)]
      }
      if (this.$route.query.name) {
        this.queryParam.name = this.$route.query.name
      }
      this.getOrgTree()
      this.letterTypeDropDown = this.$options.filters['dictData']('letter_type')
    },
    methods: {
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD')
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD')
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      batchExport () {
        petitionLetterInfoExport(this.switchingDate()).then((res) => {
          this.$refs.batchExport.downloadfile(res)
        })
      },
      getOrgTree() {
        return getOrgTree().then(res => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

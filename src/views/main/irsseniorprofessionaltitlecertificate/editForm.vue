<template>
  <a-modal
    title="编辑人社厅高级职称证书"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="出生年月"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生年月" v-decorator="['csny', {rules: [{required: true, message: '请输入出生年月！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证照数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="电子证照编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照编码" v-decorator="['elcLicenceCode', {rules: [{required: true, message: '请输入电子证照编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照来源部门"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照来源部门" v-decorator="['elcLicenceDept', {rules: [{required: true, message: '请输入电子证照来源部门！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照文件数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="电子证照文件地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照文件地址" v-decorator="['elcLicenceFileUrl', {rules: [{required: true, message: '请输入电子证照文件地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照名称" v-decorator="['elcLicenceName', {rules: [{required: true, message: '请输入电子证照名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照结构化数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照结构化数据" v-decorator="['elcLicenceStruct', {rules: [{required: true, message: '请输入电子证照结构化数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证单位" v-decorator="['fzdw', {rules: [{required: true, message: '请输入发证单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证时间" v-decorator="['fzsj', {rules: [{required: true, message: '请输入发证时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评委会名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评委会名称" v-decorator="['pwhmc', {rules: [{required: true, message: '请输入评委会名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="取得资格时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入取得资格时间" v-decorator="['qdzgsj', {rules: [{required: true, message: '请输入取得资格时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签名证书"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="时间戳签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="资格名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入资格名称" v-decorator="['zgmc', {rules: [{required: true, message: '请输入资格名称！'}]}]" />
        </a-form-item>
        <a-form-item v-show="false"><a-input v-decorator="['zjhm']" /></a-form-item>
        <a-form-item
          label="证书编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书编号" v-decorator="['zsbh', {rules: [{required: true, message: '请输入证书编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书查询"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书查询" v-decorator="['zscx', {rules: [{required: true, message: '请输入证书查询！'}]}]" />
        </a-form-item>
        <a-form-item
          label="在线验证码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入在线验证码" v-decorator="['zxyzm', {rules: [{required: true, message: '请输入在线验证码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="专业名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入专业名称" v-decorator="['zymc', {rules: [{required: true, message: '请输入专业名称！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsSeniorProfessionalTitleCertificateEdit } from '@/api/modular/main/irsseniorprofessionaltitlecertificate/irsSeniorProfessionalTitleCertificateManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              csny: record.csny,
              data: record.data,
              elcLicenceCode: record.elcLicenceCode,
              elcLicenceDept: record.elcLicenceDept,
              elcLicenceFile: record.elcLicenceFile,
              elcLicenceFileUrl: record.elcLicenceFileUrl,
              elcLicenceName: record.elcLicenceName,
              elcLicenceStruct: record.elcLicenceStruct,
              fzdw: record.fzdw,
              fzsj: record.fzsj,
              pwhmc: record.pwhmc,
              qdzgsj: record.qdzgsj,
              signCert: record.signCert,
              signValue: record.signValue,
              tsa: record.tsa,
              xb: record.xb,
              xm: record.xm,
              zgmc: record.zgmc,
              zjhm: record.zjhm,
              zsbh: record.zsbh,
              zscx: record.zscx,
              zxyzm: record.zxyzm,
              zymc: record.zymc
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsSeniorProfessionalTitleCertificateEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

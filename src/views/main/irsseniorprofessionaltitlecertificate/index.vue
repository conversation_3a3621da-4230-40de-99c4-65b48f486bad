<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('irsSeniorProfessionalTitleCertificate:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照编码">
                  <a-input v-model="queryParam.elcLicenceCode" allow-clear placeholder="请输入电子证照编码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照来源部门">
                  <a-input v-model="queryParam.elcLicenceDept" allow-clear placeholder="请输入电子证照来源部门"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照文件地址">
                  <a-input v-model="queryParam.elcLicenceFileUrl" allow-clear placeholder="请输入电子证照文件地址"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照名称">
                  <a-input v-model="queryParam.elcLicenceName" allow-clear placeholder="请输入电子证照名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照结构化数据">
                  <a-input v-model="queryParam.elcLicenceStruct" allow-clear placeholder="请输入电子证照结构化数据"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证单位">
                  <a-input v-model="queryParam.fzdw" allow-clear placeholder="请输入发证单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证时间">
                  <a-input v-model="queryParam.fzsj" allow-clear placeholder="请输入发证时间"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评委会名称">
                  <a-input v-model="queryParam.pwhmc" allow-clear placeholder="请输入评委会名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="取得资格时间">
                  <a-input v-model="queryParam.qdzgsj" allow-clear placeholder="请输入取得资格时间"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="资格名称">
                  <a-input v-model="queryParam.zgmc" allow-clear placeholder="请输入资格名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书编号">
                  <a-input v-model="queryParam.zsbh" allow-clear placeholder="请输入证书编号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书查询">
                  <a-input v-model="queryParam.zscx" allow-clear placeholder="请输入证书查询"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="在线验证码">
                  <a-input v-model="queryParam.zxyzm" allow-clear placeholder="请输入在线验证码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="专业名称">
                  <a-input v-model="queryParam.zymc" allow-clear placeholder="请输入专业名称"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.zjhm"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('irsSeniorProfessionalTitleCertificate:add')" >
          <a-button type="primary" v-if="hasPerm('irsSeniorProfessionalTitleCertificate:add')" icon="plus" @click="$refs.addForm.add()">新增人社厅高级职称证书</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('irsSeniorProfessionalTitleCertificate:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('irsSeniorProfessionalTitleCertificate:edit') & hasPerm('irsSeniorProfessionalTitleCertificate:delete')"/>
          <a-popconfirm v-if="hasPerm('irsSeniorProfessionalTitleCertificate:delete')" placement="topRight" title="确认删除？" @confirm="() => irsSeniorProfessionalTitleCertificateDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { irsSeniorProfessionalTitleCertificatePage, irsSeniorProfessionalTitleCertificateDelete } from '@/api/modular/main/irsseniorprofessionaltitlecertificate/irsSeniorProfessionalTitleCertificateManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import {getOrgTree} from "@/api/modular/system/orgManage";
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '出生年月',
            align: 'center',
            dataIndex: 'csny'
          },
          {
          ellipsis: true,
          title: '证照数据',
            align: 'center',
            dataIndex: 'data'
          },
          {
          ellipsis: true,
          title: '电子证照编码',
            align: 'center',
            dataIndex: 'elcLicenceCode'
          },
          {
          ellipsis: true,
          title: '电子证照来源部门',
            align: 'center',
            dataIndex: 'elcLicenceDept'
          },
          {
          ellipsis: true,
          title: '电子证照文件数据',
            align: 'center',
            dataIndex: 'elcLicenceFile'
          },
          {
          ellipsis: true,
          title: '电子证照文件地址',
            align: 'center',
            dataIndex: 'elcLicenceFileUrl'
          },
          {
          ellipsis: true,
          title: '电子证照名称',
            align: 'center',
            dataIndex: 'elcLicenceName'
          },
          {
          ellipsis: true,
          title: '电子证照结构化数据',
            align: 'center',
            dataIndex: 'elcLicenceStruct'
          },
          {
          ellipsis: true,
          title: '发证单位',
            align: 'center',
            dataIndex: 'fzdw'
          },
          {
          ellipsis: true,
          title: '发证时间',
            align: 'center',
            dataIndex: 'fzsj'
          },
          {
          ellipsis: true,
          title: '评委会名称',
            align: 'center',
            dataIndex: 'pwhmc'
          },
          {
          ellipsis: true,
          title: '取得资格时间',
            align: 'center',
            dataIndex: 'qdzgsj'
          },
          {
          ellipsis: true,
          title: '签名证书',
            align: 'center',
            dataIndex: 'signCert'
          },
          {
          ellipsis: true,
          title: '签名值',
            align: 'center',
            dataIndex: 'signValue'
          },
          {
          ellipsis: true,
          title: '时间戳签名值',
            align: 'center',
            dataIndex: 'tsa'
          },
          {
          ellipsis: true,
          title: '性别',
            align: 'center',
            dataIndex: 'xb'
          },
          {
          ellipsis: true,
          title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '资格名称',
            align: 'center',
            dataIndex: 'zgmc'
          },
          {
          ellipsis: true,
          title: '证书编号',
            align: 'center',
            dataIndex: 'zsbh'
          },
          {
          ellipsis: true,
          title: '证书查询',
            align: 'center',
            dataIndex: 'zscx'
          },
          {
          ellipsis: true,
          title: '在线验证码',
            align: 'center',
            dataIndex: 'zxyzm'
          },
          {
          ellipsis: true,
          title: '专业名称',
            align: 'center',
            dataIndex: 'zymc'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return irsSeniorProfessionalTitleCertificatePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      if (this.hasPerm('irsSeniorProfessionalTitleCertificate:edit') || this.hasPerm('irsSeniorProfessionalTitleCertificate:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      irsSeniorProfessionalTitleCertificateDelete (record) {
        irsSeniorProfessionalTitleCertificateDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

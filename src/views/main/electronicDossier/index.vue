<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="目录名称">
                <a-input v-model="queryParam.catalogName" allow-clear placeholder="请输入"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="false"
        :rowKey="(record) => record.id"
        :defaultExpandAllRows="true"
        :expandedRowKeys="expandedRowKeys"
        @expand="handleExpand"
        childrenColumnName="children"
        :showPagination="false"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
          <a-button type="primary" @click="handleExpandAll">展开全部</a-button>
          <a-button type="primary" @click="handleCollapseAll">折叠全部</a-button>
        </template>
        <template slot="sort" slot-scope="text">
          {{ text }}
        </template>
        <template slot="mlcc" slot-scope="text">
          {{ text }}
        </template>
        <template slot="catalogName" slot-scope="text, record">
          <div v-if="editingKey === record.id && editingField === 'catalogName'" class="editable-cell-input-wrapper">
            <a-input
              v-model="record.catalogName"
              @pressEnter="handleSave(record)"
              @blur="handleSave(record)"
              class="editable-input"
            >
              <a-icon
                slot="suffix"
                type="check"
                class="editable-cell-icon-check"
                @click="handleSave(record)"
              />
            </a-input>
          </div>
          <div v-else class="editable-cell" @click="handleEdit(record.id, 'catalogName')">
            <a-input
              :value="record.catalogName"
              disabled
              class="editable-input-disabled"
            >
              <a-icon slot="suffix" type="edit" class="editable-cell-icon"/>
            </a-input>
          </div>
        </template>
        <template slot="wsmc" slot-scope="text, record">
          <div v-if="editingKey === record.id && editingField === 'wsmc'" class="editable-cell-input-wrapper">
            <a-input
              v-model="record.wsmc"
              @pressEnter="handleSave(record)"
              @blur="handleSave(record)"
              class="editable-input"
              :placeholder="'请输入文书名称'"
            >
              <a-icon
                slot="suffix"
                type="check"
                class="editable-cell-icon-check"
                @click="handleSave(record)"
              />
            </a-input>
          </div>
          <div v-else class="editable-cell" @click="handleEdit(record.id, 'wsmc')">
            <a-input
              :value="record.wsmc || '点击输入文书名称'"
              disabled
              class="editable-input-disabled"
            >
              <a-icon slot="suffix" type="edit" class="editable-cell-icon"/>
            </a-input>
          </div>
        </template>
        <template slot="action" slot-scope="text, record">
          <a-button type="link" @click="handleAddLevel(record)">添加本级</a-button>

          <a-button type="link" @click="handleAddNextLevel(record)">添加下级</a-button>
          <a-popconfirm
            title="确定要删除此条目吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" style="color: #ff4d4f">删除</a-button>
          </a-popconfirm>
        </template>
      </s-table>
    </a-card>

    <!-- 新增弹框 -->
    <add-form ref="addForm" @success="handleSuccess" @cancel="handleCancel"/>
  </div>
</template>

<script>
import { STable } from '@/components'
import AddForm from './components/AddForm'
import { correctionDocCatalogTree } from '@/api/modular/main/correctiondoc/correctionDocCatalogManage';

export default {
  components: {
    STable,
    AddForm
  },
  data() {
    return {
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      advanced: false,
      queryParam: {},
      editingKey: '',
      editingField: '',
      expandedRowKeys: [],
      columns: [
        { title: '序号', dataIndex: 'sort', width: 150, align: 'center', scopedSlots: { customRender: 'sort' } },
        { title: '目录名称', dataIndex: 'catalogName', align: 'left', scopedSlots: { customRender: 'catalogName' } },
        { title: '操作', dataIndex: 'action', width: 300, align: 'center', scopedSlots: { customRender: 'action' } }
      ],
      loadData: parameter => {
        return correctionDocCatalogTree(parameter).then(res => {
          return res.data
        })
      }
    }
  },
  methods: {
    handleAdd() {
      this.$refs.addForm.show()
    },
    handleExpandAll() {
      // 获取所有可展开的行的id
      const getAllIds = (data) => {
        let ids = []
        data.forEach(item => {
          ids.push(item.id)
          if (item.children && item.children.length) {
            ids = ids.concat(getAllIds(item.children))
          }
        })
        return ids
      }

      // 获取当前表格数据
      const tableData = this.$refs.table.localDataSource
      // 展开所有行
      this.expandedRowKeys = getAllIds(tableData)
      this.$message.success('已展开全部')
    },
    handleCollapseAll() {
      // 折叠所有行
      this.expandedRowKeys = []
      this.$message.success('已折叠全部')
    },
    handleEdit(key, field) {
      this.editingKey = key
      this.editingField = field
    },
    handleSave(record) {
      // 这里可以调用后端API保存修改
      console.log('保存的数据:', record)
      this.editingKey = ''
      this.editingField = ''
      this.$message.success('保存成功')
    },
    handleAddLevel(record) {
      // 处理添加本级
      this.$message.info(`点击了添加本级: ${record.catalogName}`)
    },
    handleAddNextLevel(record) {
      // 处理添加下级
      this.$message.info(`点击了添加下级: ${record.catalogName}`)
    },
    handleDelete(record) {
      // 处理删除
      this.confirmLoading = true
      // 这里可以调用后端API删除数据
      console.log('删除的数据:', record)

      setTimeout(() => {
        this.confirmLoading = false
        this.$refs.table.refresh(true)
        this.$message.success('删除成功')
      }, 1000)
    },
    handleExpand(expanded, record) {
      if (expanded) {
        // 展开时添加到 expandedRowKeys
        this.expandedRowKeys = [...this.expandedRowKeys, record.id]
      } else {
        // 折叠时从 expandedRowKeys 中移除
        this.expandedRowKeys = this.expandedRowKeys.filter(key => key !== record.id)
      }
    },
    handleSuccess() {
      this.$refs.table.refresh(true)
    },
    handleCancel() {
      // 可以添加取消时的处理逻辑
    }
  }
}
</script>

<style lang="less" scoped>
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}

.editable-cell {
  position: relative;
  width: 100%;

  .editable-input-disabled {
    cursor: pointer;
    background-color: transparent;

    &:hover {
      background-color: #f5f5f5;
    }

    input {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.65);
    }

    .editable-cell-icon {
      color: #1890ff;
      display: none;
    }

    &:hover .editable-cell-icon {
      display: inline-block;
    }
  }
}

.editable-cell-input-wrapper {
  position: relative;
  width: 100%;

  .editable-input {
    width: 100%;

    &:hover, &:focus {
      border-color: #40a9ff;
    }
  }

  .editable-cell-icon-check {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    cursor: pointer;
    color: #52c41a;
    z-index: 2;

    &:hover {
      color: #389e0d;
    }
  }
}
</style>

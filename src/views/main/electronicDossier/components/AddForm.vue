<template>
  <a-modal
    title="新增"
    :visible="visible"
    @ok="handleOk"
    width="880px"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <a-form :form="form" v-if="visible">
      <a-form-item label="目录名称" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
        <a-input v-decorator="['catalogName',{rules: [{ required: true, message: '请输入目录名称' }]}]" placeholder="请输入目录名称"/>
      </a-form-item>
      <a-form-item label="上级目录" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
        <sh-tree-select
          v-decorator="['pid', {rules: [{ required: true, message: '请选择上级目录！' }]}]"
          apiURL="/correctionDocCatalog/tree"
          labelKey="catalogName"
          valueKey="id"
          style="width: 100%"
          :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
          placeholder="请选择上级目录"
          treeDefaultExpandAll
        >
          <span slot="title" slot-scope="{ id }">{{ id }}</span>
        </sh-tree-select>
      </a-form-item>
      <a-form-item label="排序" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
        <a-input-number v-decorator="[ 'sort', { rules: [{ required: true, message: '请输入排序号' }] } ]" :min="1" placeholder="请输入排序号"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { correctionDocCatalogAdd } from '@/api/modular/main/correctiondoc/correctionDocCatalogManage'

export default {
  name: 'AddForm',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          // 调用后端API保存数据
          correctionDocCatalogAdd(values).then(res => {
            if (res.success) {
              this.visible = false
              this.confirmLoading = false
              this.form.resetFields()
              this.$emit('success')
              this.$message.success('保存成功')
            } else {
              this.confirmLoading = false
              this.$message.error(res.message || '保存失败')
            }
          }).catch(error => {
            this.confirmLoading = false
            this.$message.error('操作失败：' + (error.message || error))
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
      this.form.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>

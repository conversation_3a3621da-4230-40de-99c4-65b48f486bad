<template>
  <a-drawer
    title="交付衔接协同"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer custom-wrapper"
    @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-divider orientation="left" orientation-margin="0px">案件信息</a-divider>
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item
              label="社区矫正对象姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['xm']" :disabled="disabled" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="性别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['xb']">
                <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['zjhm']" :disabled="disabled" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="统一赋号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['tyfh']" :disabled="disabled"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="社区矫正案件编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['sqjzajbh']" :disabled="disabled"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="罪犯编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['zfbh']" :disabled="disabled"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="证件类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['zjlx']">
                <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="出生日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['csrq']"/>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" >
            <a-form-item
              label="现住地"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              class="moveleft">
              <VDistpicker
                :disabled="disabled"
                :province="jzdareaCode.province"
                :city="jzdareaCode.city"
                :area="jzdareaCode.area"
                @selected="changeChoseCityjzd"
                style="display: inline-block"></VDistpicker>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item
              label="现住地详址"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2">
              <a-input :disabled="disabled" v-decorator="['xzdxz']" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" >
            <a-form-item
              label="户籍地"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              class="moveleft">
              <VDistpicker
                :disabled="disabled"
                :province="hjareaCode.province"
                :city="hjareaCode.city"
                :area="hjareaCode.area"
                @selected="changeChoseCityhj"
                style="display: inline-block"></VDistpicker>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="户籍地详址"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['hjdxz']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="生效判决书字号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['sxpjszh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="判决文书生效日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['pjwssxrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="犯罪类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['fzlx']">
                <a-select-option v-for="(item,index) in fzlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="具体罪名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="true" v-decorator="['jtzm']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="管制期限"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['gzqx']">
                <a-select-option v-for="(item,index) in gzqxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="缓刑考验期限"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="true" v-decorator="['hxkyqx']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="是否数罪并罚"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['sfszbf']">
                <a-select-option v-for="(item,index) in yesOrNoData" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="判决刑种"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['pjxz']">
                <a-select-option v-for="(item,index) in pjxzDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="判决刑期开始日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['pjxqksrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="判决刑期结束日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['pjxqjsrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="有期徒刑期限"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['yqtxqx']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="附加刑"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['fjx']">
                <a-select-option v-for="(item,index) in fjxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="审判机关名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['spjgmc']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="剥夺政治权利年限"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['bdzzqlnx']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="具体罚款金额"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jtfkje']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="驱逐出境"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['qzcj']">
                <a-select-option v-for="(item,index) in yesOrNoData" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="没收财产"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['mscc']">
                <a-select-option v-for="(item,index) in yesOrNoData" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="是否“五独”"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['sfwd']">
                <a-select-option v-for="(item,index) in sfwdDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="是否“五涉”"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['sfws']">
                <a-select-option v-for="(item,index) in sfwsDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="是否有“四史”"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['sfyss']">
                <a-select-option v-for="(item,index) in sfyssDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释裁定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['jscdrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释考验期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jskyq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释考验期起日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['jskyqqr']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释考验期止日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['jskyqzr']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释裁定书文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jscdswh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="假释裁定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jscdjg']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="矫正机构"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jzjgName']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="交付执行日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['jfzxrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="执行通知书日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['zxtzsrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="移交罪犯机关类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['yjzfjglx']">
                <a-select-option v-for="(item,index) in yjzfjglxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="移交罪犯机关名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['yjzfjgmc']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="暂予监外执行决定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['zyjwzxjdrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="暂予监外执行决定书文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['zyjwzxjdswh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="暂予监外执行起日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['zyjwzxqr']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="暂予监外执行止日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['zyjwzxzr']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="决定书文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jdswh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销假释决定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['cxjsjdrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销假释原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['cxjsyy']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销假释决定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['cxjsjdjg']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销缓刑决定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['cxhxjdrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销缓刑原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['cxhxyy']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="撤销缓刑决定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['cxhxjdjg']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="收监决定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['sjjdrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="收监原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['sjyy']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="收监决定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['sjjdjg']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="矫正类别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['jzlb']">
                <a-select-option v-for="(item,index) in jzlbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="决定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jdjg']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="司法所"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['sfs']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="社区矫正开始日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['sqjzksrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="社区矫正结束日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['sqjzjsrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="矫正期限"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['jzqx']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="24" :sm="24" >
            <a-form-item
              label="社区矫正执行地"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              class="moveleft">
              <VDistpicker
                :disabled="disabled"
                :province="zxdareaCode.province"
                :city="zxdareaCode.city"
                :area="zxdareaCode.area"
                @selected="changeChoseCityzxd"
                style="display: inline-block"></VDistpicker>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item
              label="解除社矫/终止社矫类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['jcsjzzsjlx']">
                <a-select-option v-for="(item,index) in jcsjzzsjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="解除社矫/终止社矫原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select :disabled="disabled" v-decorator="['jcsjzzsjyy']">
                <a-select-option v-for="(item,index) in jcsjzzsjyyDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="通知书文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input :disabled="disabled" v-decorator="['tzswh']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="通知书日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['tzsrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="入矫日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['rjrq']" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="解除社矫/终止社矫日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker placeholder="" :disabled="disabled" style="width: 100%" v-decorator="['jcsjzzsjrq']" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<script>
  import moment from 'moment'
  import VDistpicker from 'v-distpicker/src/Distpicker';
  import { chargeInfoDetail } from '@/api/modular/main/chargeInfoManage';
  export default {
    components: {
      VDistpicker
    },
    data () {
      return {
        disabled: true,
        labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
        jzdareaCode: { province: '', city: '', area: '' },
        hjareaCode: { province: '', city: '', area: '' },
        zxdareaCode: { province: '', city: '', area: '' },
        yesOrNoData: [
          { code: '1', name: '是' },
          { code: '0', name: '否' }
        ],
        xbDropDown: [],
        zjlxDropDown: [],
        fzlxDropDown: [],
        gzqxDropDown: [],
        pjxzDropDown: [],
        fjxDropDown: [],
        sfwdDropDown: [],
        sfwsDropDown: [],
        sfyssDropDown: [],
        yjzfjglxDropDown: [],
        jzlbDropDown: [],
        jcsjzzsjlxDropDown: [],
        jcsjzzsjyyDropDown: [],
        visible: false,
        confirmLoading: false,
        drawetWidth: 1000,
        form: this.$form.createForm(this)
      }
    },
    created () {
      this.drawetWidth = window.innerWidth - 200
      const options = this.$options
      // this.yesOrNoData = options.filters['dictData']('yes_or_no')
      this.xbDropDown = options.filters['dictData']('xb')
      this.zjlxDropDown = options.filters['dictData']('zjlx')
      this.fzlxDropDown = options.filters['dictData']('fzlx')
      this.gzqxDropDown = options.filters['dictData']('gzqx')
      this.pjxzDropDown = options.filters['dictData']('zxzl')
      this.fjxDropDown = options.filters['dictData']('fjx')
      this.sfwdDropDown = options.filters['dictData']('wd')
      this.sfwsDropDown = options.filters['dictData']('ws')
      this.sfyssDropDown = options.filters['dictData']('ss')
      this.yjzfjglxDropDown = options.filters['dictData']('yjzfjglx')
      this.jzlbDropDown = options.filters['dictData']('jzlb')
      this.jcsjzzsjlxDropDown = options.filters['dictData']('jcsjzzsjlx')
      this.jcsjzzsjyyDropDown = options.filters['dictData']('jcsjzzsjyy')
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              tyfh: record.tyfh,
              sqjzajbh: record.sqjzajbh,
              zfbh: record.zfbh,
              xm: record.xm,
              xb: record.xb,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              hjdxz: record.hjdxz,
              xzdxz: record.xzdxz,
              sxpjszh: record.sxpjszh,
              fzlx: record.fzlx,
              gzqx: record.gzqx,
              hxkyqx: record.hxkyqx,
              sfszbf: record.sfszbf,
              pjxz: record.pjxz,
              yqtxqx: record.yqtxqx,
              fjx: record.fjx,
              spjgmc: record.spjgmc,
              bdzzqlnx: record.bdzzqlnx,
              jtfkje: record.jtfkje,
              qzcj: record.qzcj,
              mscc: record.mscc,
              sfwd: record.sfwd,
              sfws: record.sfws,
              sfyss: record.sfyss,
              jskyq: record.jskyq,
              jscdswh: record.jscdswh,
              jscdjg: record.jscdjg,
              jzjgName: record.jzjgName,
              yjzfjglx: record.yjzfjglx,
              yjzfjgmc: record.yjzfjgmc,
              zyjwzxjdswh: record.zyjwzxjdswh,
              jdswh: record.jdswh,
              cxjsyy: record.cxjsyy,
              cxjsjdjg: record.cxjsjdjg,
              cxhxjdrq: record.cxhxjdrq,
              cxhxyy: record.cxhxyy,
              cxhxjdjg: record.cxhxjdjg,
              sjyy: record.sjyy,
              sjjdjg: record.sjjdjg,
              jzlb: record.jzlb,
              jdjg: record.jdjg,
              sfs: record.sfs,
              jzqx: record.jzqx,
              sqjzzxd: record.sqjzzxd,
              jcsjzzsjlx: record.jcsjzzsjlx,
              jcsjzzsjyy: record.jcsjzzsjyy,
              tzswh: record.tzswh,
              sfsc: record.sfsc,
              jcydm: record.jcydm,
              fydm: record.fydm
            }
          )

          this.dictCode(this.jzdareaCode, record.xzd)
          this.dictCode(this.hjareaCode, record.hjd)
          this.dictCode(this.zxdareaCode, record.sqjzzxd)
          chargeInfoDetail({ chargeCode: record.jtzm }).then(res => {
              if (res.data) {
                this.form.setFieldsValue({ jtzm: res.data.charge })
              }
          })
        }, 100)
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        if (record.pjwssxrq != null) {
            this.form.getFieldDecorator('pjwssxrq', { initialValue: moment(record.pjwssxrq, 'YYYY-MM-DD') })
        }
        if (record.pjxqksrq != null) {
            this.form.getFieldDecorator('pjxqksrq', { initialValue: moment(record.pjxqksrq, 'YYYY-MM-DD') })
        }
        if (record.pjxqjsrq != null) {
            this.form.getFieldDecorator('pjxqjsrq', { initialValue: moment(record.pjxqjsrq, 'YYYY-MM-DD') })
        }
        if (record.jscdrq != null) {
            this.form.getFieldDecorator('jscdrq', { initialValue: moment(record.jscdrq, 'YYYY-MM-DD') })
        }
        if (record.jskyqqr != null) {
            this.form.getFieldDecorator('jskyqqr', { initialValue: moment(record.jskyqqr, 'YYYY-MM-DD') })
        }
        if (record.jskyqzr != null) {
            this.form.getFieldDecorator('jskyqzr', { initialValue: moment(record.jskyqzr, 'YYYY-MM-DD') })
        }
        if (record.jfzxrq != null) {
            this.form.getFieldDecorator('jfzxrq', { initialValue: moment(record.jfzxrq, 'YYYY-MM-DD') })
        }
        if (record.zxtzsrq != null) {
            this.form.getFieldDecorator('zxtzsrq', { initialValue: moment(record.zxtzsrq, 'YYYY-MM-DD') })
        }
        if (record.zyjwzxjdrq != null) {
            this.form.getFieldDecorator('zyjwzxjdrq', { initialValue: moment(record.zyjwzxjdrq, 'YYYY-MM-DD') })
        }
        if (record.zyjwzxqr != null) {
            this.form.getFieldDecorator('zyjwzxqr', { initialValue: moment(record.zyjwzxqr, 'YYYY-MM-DD') })
        }
        if (record.zyjwzxzr != null) {
            this.form.getFieldDecorator('zyjwzxzr', { initialValue: moment(record.zyjwzxzr, 'YYYY-MM-DD') })
        }
        if (record.cxjsjdrq != null) {
            this.form.getFieldDecorator('cxjsjdrq', { initialValue: moment(record.cxjsjdrq, 'YYYY-MM-DD') })
        }
        if (record.sjjdrq != null) {
            this.form.getFieldDecorator('sjjdrq', { initialValue: moment(record.sjjdrq, 'YYYY-MM-DD') })
        }
        if (record.sqjzksrq != null) {
            this.form.getFieldDecorator('sqjzksrq', { initialValue: moment(record.sqjzksrq, 'YYYY-MM-DD') })
        }
        if (record.sqjzjsrq != null) {
            this.form.getFieldDecorator('sqjzjsrq', { initialValue: moment(record.sqjzjsrq, 'YYYY-MM-DD') })
        }
        if (record.tzsrq != null) {
            this.form.getFieldDecorator('tzsrq', { initialValue: moment(record.tzsrq, 'YYYY-MM-DD') })
        }
        if (record.rjrq != null) {
            this.form.getFieldDecorator('rjrq', { initialValue: moment(record.rjrq, 'YYYY-MM-DD') })
        }
        if (record.jcsjzzsjrq != null) {
            this.form.getFieldDecorator('jcsjzzsjrq', { initialValue: moment(record.jcsjzzsjrq, 'YYYY-MM-DD') })
        }
      },
      changeChoseCityjzd: function (e) { // 后执行
        this.jzdareaCode.province = e.province.value
        this.jzdareaCode.city = e.city.value
        this.jzdareaCode.area = e.area.value
      },
      changeChoseCityhj: function (e) { // 后执行
        this.hjareaCode.province = e.province.value
        this.hjareaCode.city = e.city.value
        this.hjareaCode.area = e.area.value
      },
      changeChoseCityzxd: function (e) { // 后执行
        this.zxdareaCode.province = e.province.value
        this.zxdareaCode.city = e.city.value
        this.zxdareaCode.area = e.area.value
      },
      dictCode(select, code) {
        if (code.length !== 6) {
          return
        }
        select.province = code.substring(0, 2) + '0000'
        select.city = code.substring(0, 4) + '00'
        select.area = code
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.moveleft{
  /* position:relative;
  left:-25px */
}
</style>

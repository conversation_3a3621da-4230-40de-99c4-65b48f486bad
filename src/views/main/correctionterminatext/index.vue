<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="解矫类型">
                <a-select v-model="queryParam.jcsjzzsjlx" allow-clear placeholder="请选择解矫类型" default-value="0">
                  <a-select-option v-for="(item,index) in jcsjzzsjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正机构">
                <a-tree-select
                  style="width:244px;"
                  tree-data-simple-mode
                  v-model="queryParam.jzjg"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="orgTree"
                  placeholder="矫正机构"
                  :allowClear="true"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="发送时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" >
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">查看详情</a>
        </span>
        <span slot="jcsjzzsjlx" slot-scope="text">
          {{ 'jcsjzzsjlx' | dictType(text) }}
        </span>
        <span slot="timeFormat" slot-scope="text">
          {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { correctionTerminateXtPage } from '@/api/modular/main/correctionterminatext/correctionTerminateXtManage'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
         { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
         { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'sfs' },
         { ellipsis: true, title: '解矫类型', align: 'center', dataIndex: 'jcsjzzsjlx', scopedSlots: { customRender: 'jcsjzzsjlx' } },
         { ellipsis: true, title: '矫正开始时间', align: 'center', dataIndex: 'sqjzksrq', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '矫正结束时间', align: 'center', dataIndex: 'sqjzjsrq', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '解矫日期', align: 'center', dataIndex: 'jcsjzzsjrq', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '发送时间', align: 'center', dataIndex: 'sendTime', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return correctionTerminateXtPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        jcsjzzsjlxDropDown: [],
        orgTree: []
      }
    },
    created () {
      if (this.$route.query.date) {
        this.queryParam.dates = [moment(this.$route.query.date), moment(this.$route.query.date)]
      }
      this.getOrgTree()
      this.sysDictTypeDropDown()
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown () {
        this.jcsjzzsjlxDropDown = this.$options.filters['dictData']('jcsjzzsjlx')
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

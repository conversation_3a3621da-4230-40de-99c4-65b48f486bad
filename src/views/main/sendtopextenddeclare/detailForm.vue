<template>
  <sh-drawer
    title="查看"

    placement="right"
    :visible="visible"
    :footer="null"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form" v-if="visible">
        <div class="root">
          <div class="left">
            <a-menu mode="horizontal" v-model="selectedKeys" style="margin:20px 0">
              <a-menu-item key="1">续报信息</a-menu-item>
              <a-menu-item key="2">检察院意见</a-menu-item>
              <a-menu-item key="3">续报发送</a-menu-item>
              <a-menu-item key="4">反馈签收回执</a-menu-item>
            </a-menu>
            <div v-show="current === '1'">
              <h3 class="cus-title-d">
                案件信息
              </h3>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['tyfh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="社区矫正案件编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['sqjzajbh']" />
                  </a-form-item>
                </a-col>
              </a-row>
              <h3 class="cus-title-d">

                个人信息
              </h3>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="公安嫌疑人编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['gaxyrbh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="检察院嫌疑人编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jcyxyrbh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['xm']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="曾用名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['cym']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select disabled v-decorator="['xb']">
                      <a-select-option v-for="(item, index) in xbDropDown" :key="index" :value="item.code">{{ item.name
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select disabled v-decorator="['mz']">
                      <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{ item.name
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select disabled v-decorator="['zjlx']">
                      <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{ item.name
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zjhm']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['csrq']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="文化程度" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select disabled v-decorator="['whcd']">
                      <a-select-option v-for="(item, index) in whcdDropDown" :key="index" :value="item.code">{{ item.name
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" >
                  <a-form-item
                    label="现住地"
                    :labelCol="labelCol2"
                    :wrapperCol="wrapperCol2"
                    class="moveleft">
                    <VDistpicker
                      disabled
                      :province="xzdareaCode.province"
                      :city="xzdareaCode.city"
                      :area="xzdareaCode.area"
                      @selected="changeChoseCityxzd"
                      style="display: inline-block"></VDistpicker>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" >
                  <a-form-item
                    label="现住地详址"
                    :labelCol="labelCol2"
                    :wrapperCol="wrapperCol2"
                    class="moveleft">
                    <a-input disabled v-decorator="['xzdxz']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" >
                  <a-form-item
                    label="户籍地"
                    :labelCol="labelCol2"
                    :wrapperCol="wrapperCol2"
                    class="moveleft">
                    <VDistpicker
                      disabled
                      :province="hjdareaCode.province"
                      :city="hjdareaCode.city"
                      :area="hjdareaCode.area"
                      @selected="changeChoseCityhjd"
                      style="display: inline-block"></VDistpicker>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" >
                  <a-form-item
                    label="户籍地详址"
                    :labelCol="labelCol2"
                    :wrapperCol="wrapperCol2"
                    class="moveleft">
                    <a-input disabled v-decorator="['hjdxz']" />
                  </a-form-item>
                </a-col>
              </a-row>
              <h3 class="cus-title-d">

                暂予监外信息
              </h3>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="决定书文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jdswh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="决定暂予监外执行日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jdzyjwzxrq']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="暂予监外执行决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zyjwzxjdjg']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="暂予监外执行原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zyjwzxyy']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="暂予监外执行起日" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zyjwzxqr']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="暂予监外执行止日" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zyjwzxzr']" />
                  </a-form-item>
                </a-col>
              </a-row>
              <h3 class="cus-title-d">

                医学司法鉴定意见
              </h3>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="鉴定医院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jdyy']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="鉴定日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jdrq']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="诊断结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zdjg']" />
                  </a-form-item>
                </a-col>
              </a-row>
              <h3 class="cus-title-d">

                续报申请信息
              </h3>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="申请人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['sqrxm']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="与罪犯关系" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['yzfgx']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="续报申请事由" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['xbsqsy']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="申请日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['sqrq']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <!-- <a-upload disabled :multiple="false" :showUploadList="true" :file-list="fileList">
                    </a-upload> -->
                    <div
                      style="color: #008CFF;cursor: pointer;"
                      href=""
                      v-for="(v, i) in fileList"
                      :key="i"
                      @click="goPdf(v)">{{ v.name }}</div>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="选择检察院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['jcy']" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div v-show="current === '2'">
              <a-col :span="12">
                <a-form-item label="检察院意见" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['zyjwzxjcyj']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="意见备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jcyjbz']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="审查文书文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['scwswh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="意见反馈时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['yjfksj']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="意见书" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-upload disabled :multiple="false" :showUploadList="true" :file-list="fileList2">
                  </a-upload>
                </a-form-item>
              </a-col>
            </div>
            <div v-if="current === '3'">
              <SendFormDetail :dataObj="recordsObj" ref="SendFormDetail"/>
              <!-- <a-col :span="12">
                <a-form-item label="选择法院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['fy']" />
                </a-form-item>
              </a-col> -->
            </div>
            <div v-show="current === '4'">
              <a-col :span="12">
                <a-form-item label="签收日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['qsrq']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="送达文书内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['sdwsnr']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="签收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['qsdw']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="签收人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['qsr']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="回执文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['hzwsh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="回执附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <!-- <a-upload disabled :multiple="false" :showUploadList="true" :file-list="fileList3">
                  </a-upload> -->
                  <div
                    style="color: #008CFF;cursor: pointer;"
                    href=""
                    v-for="(v, i) in fileList3"
                    :key="i"
                    @click="goPdf(v)">{{ v.name }}</div>
                </a-form-item>
              </a-col>
            </div>
          </div>
          <div class="right">
            <h3>
              <span class="icon" />
              续报流程
            </h3>
            <div>
              <br>
              <div style="position:relative;left:3%" v-for="(p, index) in stepdata" :key="index">
                <div style="position:relative;">
                  <div style="height: 100px" v-if="p.step === '1'">
                    <div
                      style="border-radius: 4px;background:#008CFF;width:56px;height:56px;text-align:center;display:inline-block;margin: 15px;">
                      <img src="@/assets/icons/fysb.png" width="25px" style="padding:15px 0;border-radius: 3px;" alt="">
                    </div>
                    <div style="height:100px;width:300px;display:inline-block;position:absolute;top:8px;">
                      <h3 style="font-weight:800;margin:5px 0">续报申报</h3>
                      <div style="color:rgba(150, 151, 153, 1);margin:5px 0">
                        <div style="margin:8px 0 2px 0">
                          申请人: {{ p.context }}
                        </div>
                        <div>
                          提交时间: {{ formatTime(p.createTime) }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="height: 100px" v-if="p.step === '2'">
                    <div
                      style="border-radius: 4px;background:#008CFF;width:56px;height:56px;text-align:center;display:inline-block;margin: 15px;">
                      <img src="@/assets/icons/bl.png" width="25px" style="padding:15px 0;border-radius: 3px;" alt="">
                    </div>
                    <div style="height:100px;width:300px;display:inline-block;position:absolute;top:8px;">
                      <h3 style="font-weight:800;margin:5px 0">检察院反馈</h3>
                      <div style="color:rgba(150, 151, 153, 1);margin:5px 0">
                        <div style="margin:8px 0 2px 0">
                          反馈意见: {{ p.context }}
                        </div>
                        <div>
                          反馈时间: {{ formatTime(p.createTime) }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="height: 100px" v-if="p.step === '3'">
                    <div
                      style="border-radius: 4px;background:#008CFF;width:56px;height:56px;text-align:center;display:inline-block;margin: 15px;">
                      <img src="@/assets/icons/bl.png" width="25px" style="padding:15px 0;border-radius: 3px;" alt="">
                    </div>
                    <div style="height:100px;width:300px;display:inline-block;position:absolute;top:8px;">
                      <h3 style="font-weight:800;margin:5px 0">发送续报</h3>
                      <div style="color:rgba(150, 151, 153, 1);margin:5px 0">
                        <div style="margin:8px 0 2px 0">
                          发送人: {{ p.context }}
                        </div>
                        <div>
                          发送时间: {{ formatTime(p.createTime) }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="height: 100px" v-if="p.step === '4'">
                    <div
                      style="border-radius: 4px;background:#008CFF;width:56px;height:56px;text-align:center;display:inline-block;margin: 15px;">
                      <img src="@/assets/icons/bj.png" width="25px" style="padding:15px 0;border-radius: 3px;" alt="">
                    </div>
                    <div style="height:100px;width:300px;display:inline-block;position:absolute;top:8px;">
                      <h3 style="font-weight:800;margin:5px 0">反馈签收回执</h3>
                      <div style="color:rgba(150, 151, 153, 1);margin:5px 0">
                        <div style="margin:8px 0 2px 0">
                          法院: {{ p.context }}
                        </div>
                        <div>
                          回执时间: {{ formatTime(p.createTime) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-form>
    </a-spin>
    <!-- <a-button @click="handleCancel">取消</a-button> -->
  </sh-drawer>
</template>

<script>
import VDistpicker from 'v-distpicker/src/Distpicker'
import moment from 'moment';
import { stepLogList } from '@/api/modular/main/steplog/stepLogManage';
import { sendTopExtendDeclareDetail } from '@/api/modular/main/sendtopextenddeclare/sendTopExtendDeclareManage';
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';
import SendFormDetail from './sendFormDetail'
export default {
  name: 'DetailForm',
  // components: { SendFormDetail },
  data() {
    return {
      recordsObj: {},
      xzdareaCode: { province: '', city: '', area: '' },
      hjdareaCode: { province: '', city: '', area: '' },
      width: window.innerWidth - 200,
      current: '1',
      selectedKeys: ['1'],
      fileList: [],
      fileList2: [],
      fileList3: [],
      fileId: [],
      labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
      ztDropDown: [],
      zjlxDropDown: [],
      whcdDropDown: [],
      xbDropDown: [],
      mzDropDown: [],
      data: [],
      stepdata: [],
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  watch: {
    selectedKeys: {
      deep: true,
      immediate: true,
      handler(newValue) {
        const [a] = newValue
        this.current = a
      }
    }
  },
  created() {
    this.sysDictTypeDropDown()
  },
  components: {
    VDistpicker,
    SendFormDetail
  },
  methods: {
    changeChoseCityxzd: function (e) {
      this.xzdareaCode.province = e.province.value
      this.xzdareaCode.city = e.city.value
      this.xzdareaCode.area = e.area.value
    },
    changeChoseCityhjd: function (e) {
      this.hjdareaCode.province = e.province.value
      this.hjdareaCode.city = e.city.value
      this.hjdareaCode.area = e.area.value
    },
    /**
     * 获取字典数据
     */
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
    },
    // 初始化方法
    open(record) {
      this.visible = true
      this.form.resetFields()
console.log(record)
      this.recordsObj = record
      this.dictCode(this.xzdareaCode, record.xzd)
      this.dictCode(this.hjdareaCode, record.hjd)
      sendTopExtendDeclareDetail({ id: record.id }).then((res) => {
        if (res.success) {
          const data = res.data
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                jcy: data.jcyCode ? data.jcyCode.split('|')[1] : '',
                fy: data.fyCode ? data.fyCode.split('|')[1] : '',
                docList: data.docList,
                file4053: data.file4053,
                file4055: data.file4055,
                taskId4053: data.taskId4053,
                taskId4054: data.taskId4054,
                taskId4055: data.taskId4055,
                taskId4056: data.taskId4056,
                jcyfksj: data.jcyfksj,
                fyfksj: data.fyfksj,
                id: data.id,
                tyfh: data.tyfh,
                sqjzajbh: data.sqjzajbh,
                gaxyrbh: data.gaxyrbh,
                jcyxyrbh: data.jcyxyrbh,
                xm: data.xm,
                jzjg: data.jzjg,
                cym: data.cym,
                xb: data.xb,
                zjlx: data.zjlx,
                zjhm: data.zjhm,
                csrq: this.formatDate(data.csrq),
                zasnl: data.zasnl,
                gj: data.gj,
                mz: data.mz,
                csd: data.csd,
                hjd: data.hjd,
                hjdxz: data.hjdxz,
                xzd: data.xzd,
                xzdxz: data.xzdxz,
                whcd: data.whcd,
                sxpjjg: data.sxpjjg,
                pjwswh: data.pjwswh,
                // pjrq: data.pjrq,
                pjzm: data.pjzm,
                pjqtzm: data.pjqtzm,
                zx: data.zx,
                ypxq: data.ypxq,
                yxqqr: data.yxqqr,
                yxqzr: data.yxqzr,
                fjx: data.fjx,
                fjxjtqk: data.fjxjtqk,
                hxkyq: data.hxkyq,
                ccxpx: data.ccxpx,
                fjjewy: data.fjjewy,
                msccjewy: data.msccjewy,
                qtccxpxjewy: data.qtccxpxjewy,
                bdzzqlqx: data.bdzzqlqx,
                jdswh: data.jdswh,
                jdzyjwzxrq: this.formatDate(data.jdzyjwzxrq),
                zyjwzxjdjg: data.zyjwzxjdjg ? data.zyjwzxjdjg.split('|')[1] : '',
                zyjwzxyy: data.zyjwzxyy,
                jfzxrq: this.formatDate(data.jfzxrq),
                yjzfjglx: data.yjzfjglx,
                yjzfjgmc: data.yjzfjgmc,
                jzqx: data.jzqx,
                zyjwzxqr: this.formatDate(data.zyjwzxqr),
                zyjwzxzr: this.formatDate(data.zyjwzxzr),
                rjrq: this.formatDate(data.rjrq),
                jcqk: data.jcqk,
                zjqjgzbx: data.zjqjgzbx,
                jdyy: data.jdyy,
                jdrq: this.formatDate(data.jdrq),
                zdjg: data.zdjg,
                sqrxm: data.sqrxm,
                yzfgx: data.yzfgx,
                xbsqsy: data.xbsqsy,
                sqrq: this.formatDate(data.sqrq),
                jcybmsah: data.jcybmsah,
                scwswh: data.scwswh,
                zyjwzxjcyj: data.zyjwzxjcyj,
                yjfksj: data.yjfksj,
                jcyjbz: data.jcyjbz,
                wswh: data.wswh,
                qsrq: this.formatDate(data.qsrq),
                sdwsnr: data.sdwsnr,
                qsr: data.qsr,
                hzwsh: data.hzwsh
              }
            )
          }, 100)
          extOrgInfoList({ type: 20, orgName: data.qsdw }).then(res => {
            if (res.data.length > 0) {
              const d = res.data[0];
              this.form.setFieldsValue({ qsdw: d.orgName })
            }
          })
          this.fileList = res.data.fileList
          this.fileList2 = res.data.fileList2
          this.fileList3 = res.data.fileList3
        }
      })
      stepLogList({ bizId: record.id }).then(res => {
        this.stepdata = res.data
      })
    },
    // 查看附件
    goPdf(v) {
      this.$PdfDrawer.viewPdf({ title: v.name, url: v.url })
      console.log(v, 'vvv')
    },
    /**
     * 提交表单
     */

    handleSubmit() { },
    handleCancel() {
      this.current = '1'
      this.form.resetFields()
      this.visible = false
    },
    dictCode(select, code) {
      if (code.length !== 6) {
        return
      }
      select.province = code.substring(0, 2) + '0000'
      select.city = code.substring(0, 4) + '00'
      select.area = code
    },
    formatDate(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD')
    },
    formatTime(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
<style lang="less" scoped>
// ::v-deep .ant-form-item-label {
//     display: inline-block;
//     overflow: hidden;
//     line-height: 39.9999px;
//     white-space: nowrap;
//     text-align: left;
//     vertical-align: middle;
//   }

  .root {
    display: block;
    // min-height: 1000px;
    width: 100%;
    position: relative;

    .left {
      top: 0;
      display: inline-block;
      height: 100%;
      width: 70%;
      vertical-align: middle;
      padding: 10px;

      .left_title {
        background: #F3F3F3;
        border: 1px solid #E6E6E6;
        display: inline-block;
        width: 20%;
        vertical-align: middle;
        min-height: 40px;
        line-height: 40px;
        text-align: center;
      }

      .right_content1 {
        .right_content();
        width: 30%;
      }

      .right_content {
        vertical-align: middle;
        padding-left: 15px;
        display: inline-block;
        width: 80%;
        min-height: 40px;
        background: #fff;
        line-height: 40px;
        border: 1px solid #E6E6E6;
      }

      .inline {
        border: 1px solid #E6E6E6;
      }

      .icon {
        .icon()
      }

      .table {
        background: #F3F3F3;
        border: 1px solid #E6E6E6;
        height: 100%;
        width: 100%;
      }
    }

    .right {
      display: inline-block;
      vertical-align: top;
      width: 30%;
      padding: 10px;

      // background: red;
      .icon {
        .icon()
      }
    }
  }

  .icon {
    width: 4px;
    height: 14px;
    background: #008CFF;
    display: inline-block;
  }

  .shyj {
    padding: 2px 9px;
    background: rgba(0, 140, 255, 0.07);
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #008CFF;
    line-height: 0;
    border-radius: 4px;
    opacity: 1;
    border: 1px dashed rgba(0, 140, 255, 0.4);
  }

  // .moveleft {
  //   position: relative;
  //   left: 86px
  // }
</style>

<template>
  <sh-drawer
    title="新增"

    placement="right"
    :visible="visible"
    :after-visible-change="afterVisibleChange"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">案件信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="统一赋号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['tyfh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="社区矫正案件编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input placeholder="请输入社区矫正案件编号" v-decorator="['sqjzajbh', {rules: [{required: true, message: '请输入社区矫正案件编号！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="cus-title-d">个人信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="公安嫌疑人编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['gaxyrbh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="检察院嫌疑人编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jcyxyrbh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback

            >
              <a-input disabled placeholder="请选择矫正对象" v-decorator="['xm', {rules: [{required: true, message: '请选择矫正对象！'}]}]" />
              <!-- <a-button @click="chooseJzdx" style="position:absolute;right:-6px;top:-6px" type="primary">选择矫正对象</a-button> -->
            </a-form-item>

          </a-col>
          <a-col :span="12">

            <a-form-item
              label="曾用名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['cym']" />
            </a-form-item>

          </a-col>
          <a-col :span="12">
            <a-form-item
              label="性别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled placeholder="请选择性别" v-decorator="['xb']">
                <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="民族"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled placeholder="请选择民族" v-decorator="['mz']">
                <a-select-option v-for="(item,index) in mzDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="证件类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled placeholder="请选择证件类型" v-decorator="['zjlx']">
                <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="证件号码"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zjhm']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="出生日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['csrq']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="文化程度"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled placeholder="请选择文化程度" v-decorator="['whcd']">
                <a-select-option v-for="(item,index) in whcdDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="现住地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <sh-cascader-distpicker
                disabled
                v-decorator="['xzd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" >
            <a-form-item
              label="现住地详址"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              class=""
            >
              <a-input disabled v-decorator="['xzdxz']" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="户籍地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <sh-cascader-distpicker
                disabled
                v-decorator="['hjd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item
              label="户籍地详址"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              class=""
            >
              <a-input disabled v-decorator="['hjdxz']" />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="cus-title-d">暂予监外信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="决定书文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入决定书文号" v-decorator="['jdswh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="决定暂予监外执行日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker style="width: 100%" placeholder="请选择决定暂予监外执行日期" v-decorator="['jdzyjwzxrq']" @change="onChangejdzyjwzxrq"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="暂予监外执行决定机关"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="['zyjwzxjdjg']"
                show-search
                style="width: 100%"
                :options="extOrgFy" >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="暂予监外执行原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入暂予监外执行原因" v-decorator="['zyjwzxyy']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="暂予监外执行起日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker style="width: 100%" placeholder="请选择暂予监外执行起日" v-decorator="['zyjwzxqr']" @change="onChangezyjwzxqr"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="暂予监外执行止日"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker style="width: 100%" placeholder="请选择暂予监外执行止日" v-decorator="['zyjwzxzr']" @change="onChangezyjwzxzr"/>
            </a-form-item>
          </a-col>
          <!--<a-col :span="12">
            <a-form-item
              label="矫正机构"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-tree-select
                v-decorator="['jzjg', {rules: [{required: true, message: '请选择矫正机构！'}]}]"
                style="width: 100%"
                :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                :treeData="orgTree"
                placeholder="请选择矫正机构"
              >
              </a-tree-select>
            </a-form-item>
          </a-col>-->
        </a-row>
        <div class="cus-title-d">医学司法鉴定意见</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="鉴定医院"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input placeholder="请输入鉴定医院" v-decorator="['jdyy', {rules: [{required: true, message: '请输入鉴定医院！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="鉴定日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 100%" placeholder="请选择鉴定日期" v-decorator="['jdrq',{rules: [{ required: true, message: '请选择鉴定日期！' }]}]" @change="onChangejdrq"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="诊断结果"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input placeholder="请输入诊断结果" v-decorator="['zdjg', {rules: [{required: true, message: '请输入诊断结果！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="cus-title-d">续报申请信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="申请人姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input placeholder="请输入申请人姓名" v-decorator="['sqrxm', {rules: [{required: true, message: '请输入申请人姓名！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="与罪犯关系"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input placeholder="请输入与罪犯关系" v-decorator="['yzfgx', {rules: [{required: true, message: '请输入与罪犯关系！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="续报申请事由"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input placeholder="请输入续报申请事由" v-decorator="['xbsqsy', {rules: [{required: true, message: '请输入续报申请事由！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="申请日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 100%" placeholder="请选择申请日期" v-decorator="['sqrq',{rules: [{ required: true, message: '请选择申请日期！' }]}]" @change="onChangesqrq"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="《罪犯暂予监外执行续保及改造表现鉴定表》"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 13}
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 11 }
              }">
              <a-upload
                :customRequest="customRequest"
                :multiple="false"
                :showUploadList="true"
                :file-list="fileList1"
                :remove="handleRemove"
                :before-upload="beforeUpload">
                <a-button> <a-icon type="upload" />上传文件</a-button>
              </a-upload>
            </a-form-item>

          </a-col>
          <!-- <a-col :span="12">
            <a-form-item
              label="文书"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-upload
                :customRequest="customRequest"
                :multiple="false"
                :showUploadList="true"
                :file-list="fileList2"
                :remove="handleRemove2"
                :before-upload="beforeUpload2">
                <a-button> <a-icon type="upload" />上传文件</a-button>
              </a-upload>
            </a-form-item>
          </a-col> -->
          <a-col :span="24">
            <!-- <a-form-item
              label="选择检察院"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              has-feedback
            >
              <a-select
                v-decorator="['jcyCode', {rules: [{required: true, message: '请选择检察院'}]}]"
                show-search
                style="width: 100%"
                :options="extOrgJcy" >
              </a-select>
            </a-form-item> -->
            <a-form-item
              label="选择法院"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2">
              <a-select
                v-decorator="['fyCode', {rules: [{required: true, message: '请选择法院'}]}]"
                show-search
                style="width: 100%"
                :options="extOrgFy" >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <!-- <a-button @click="handleCancel">取消</a-button>
    <a-button type="primary" @click="handleSubmit('1')">提交</a-button> -->
    <chooseJzdx ref="chooseJzdxRef" @selectedRows="getData"/>
  </sh-drawer>
</template>

<script>
import { fileList, sysFileInfoUpload } from '@/api/modular/system/fileManage'

  import VDistpicker from 'v-distpicker/src/Distpicker'
  import chooseJzdx from './chooseJzdx.vue'

  import { sendTopExtendDeclareAdd, sendTopExtendDeclareEdit } from '@/api/modular/main/sendtopextenddeclare/sendTopExtendDeclareManage'
  import moment from 'moment';
  import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';

  export default {
    data() {
      return {
        xzdareaCode: { province: '', city: '', area: '' },
        hjdareaCode: { province: '', city: '', area: '' },
        width: window.innerWidth - 200,
        orgTree: [],
        fileList1: [],
        fileId1: [],
        fileList2: [],
        fileId2: [],
        labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
        extOrgJcy: [],
        extOrgFy: [],
        ztDropDown: [],
        zjlxDropDown: [],
        whcdDropDown: [],
        xbDropDown: [],
        mzDropDown: [],
        data: [],
        jdzyjwzxrqDateString: null,
        zyjwzxqrDateString: null,
        zyjwzxzrDateString: null,
        jdrqDateString: null,
        sqrqDateString: null,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created () {
      this.getExtOrgData()
      this.sysDictTypeDropDown()
    },
    components: {
      chooseJzdx,
      VDistpicker
    },
    methods: {
      getExtOrgData() {
        extOrgInfoList({ type: 30, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.extOrgJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          })
        })
        extOrgInfoList({ type: 20, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.extOrgFy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          })
        })
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown() {
        this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
        this.whcdDropDown = this.$options.filters['dictData']('whcd')
        this.xbDropDown = this.$options.filters['dictData']('xb')
        this.mzDropDown = this.$options.filters['dictData']('mz')
      },
      afterVisibleChange(val) {
        console.log('visible', val);
      },
      customRequest (data) {},
      beforeUpload(file) {
        this.fileList1 = [...this.fileList1, file]
      },
      handleRemove(file) {
        const index = this.fileList1.indexOf(file)
        const newFileList = this.fileList1.slice()
        newFileList.splice(index, 1)
        this.fileList1 = newFileList
      },
      beforeUpload2(file) {
        this.fileList2 = [...this.fileList2, file]
      },
      handleRemove2(file) {
        const index = this.fileList2.indexOf(file)
        const newFileList = this.fileList2.slice()
        newFileList.splice(index, 1)
        this.fileList2 = newFileList
      },
      sureUpload() {
        this.fileId1 = []
        const list = []
        if (this.fileList1 != null) {
          this.fileList1.forEach(file => {
            if (file.size != null) {
              const formData = new FormData()
              formData.append('file', file)
              formData.append('ext', 1)
              var p1 = new Promise((resolve, reject) => {
                sysFileInfoUpload(Object.assign(formData)).then(res => {
                  if (res.success) {
                    this.fileId1 = [...this.fileId1, res.data.id]
                    resolve(res.result)
                  } else {
                    this.$message.error(res.message)
                    this.uploadStatus = false
                    reject(res)
                  }
                  this.fileList1 = []
                })
              })
            }
            list.push(p1)
          })
        }
        this.fileId2 = []
        if (this.fileList2 != null) {
          this.fileList2.forEach(file => {
            if (file.size != null) {
              const formData = new FormData()
              formData.append('file', file)
              formData.append('ext', 1)
              var p1 = new Promise((resolve, reject) => {
                sysFileInfoUpload(Object.assign(formData)).then(res => {
                  if (res.success) {
                    this.fileId2 = [...this.fileId2, res.data.id]
                    resolve(res.result)
                  } else {
                    this.$message.error(res.message)
                    this.uploadStatus = false
                    reject(res)
                  }
                  this.fileList2 = []
                })
              })
            }
            list.push(p1)
          })
        }
        return Promise.all(list).then(res => {
          this.uploadStatus = true
        })
      },
    async getFileList() {
      console.log(this.record)
      const { data } = await fileList({ ids: this.data.file1 })
      this.fileList1 = data.map(item => {
        return { ...item, id: item.uid }
      })
      this.fileId1 = this.data.file1.split(',')
    },
    // 初始化方法
    async open(data, orgtree) {
      this.data = data
      this.orgTree = orgtree
      this.visible = true
      this.id = data.id
      await this.getFileList()
      setTimeout(() => {
        this.form.setFieldsValue(
          {
            jcyCode: data.jcyCode,
            fyCode: data.fyCode,
            docList: data.docList,
            file4053: data.file4053,
            file4055: data.file4055,
            taskId4053: data.taskId4053,
            taskId4054: data.taskId4054,
            taskId4055: data.taskId4055,
            taskId4056: data.taskId4056,
            jcyfksj: data.jcyfksj,
            fyfksj: data.fyfksj,
            id: data.id,
            tyfh: data.tyfh,
            sqjzajbh: data.sqjzajbh,
            gaxyrbh: data.gaxyrbh,
            jcyxyrbh: data.jcyxyrbh,
            xm: data.xm,
            jzjg: data.jzjg,
            cym: data.cym,
            xb: data.xb,
            zjlx: data.zjlx,
            zjhm: data.zjhm,
            csrq: this.formatDate(data.csrq),
            zasnl: data.zasnl,
            gj: data.gj,
            mz: data.mz,
            csd: data.csd,
            hjd: data.hjd,
            hjdxz: data.hjdxz,
            xzd: data.xzd,
            xzdxz: data.xzdxz,
            whcd: data.whcd,
            sxpjjg: data.sxpjjg,
            pjwswh: data.pjwswh,
            pjrq: this.formatDate(data.pjrq),
            pjzm: data.pjzm,
            pjqtzm: data.pjqtzm,
            zx: data.zx,
            ypxq: data.ypxq,
            yxqqr: data.yxqqr,
            yxqzr: data.yxqzr,
            fjx: data.fjx,
            fjxjtqk: data.fjxjtqk,
            hxkyq: data.hxkyq,
            ccxpx: data.ccxpx,
            fjjewy: data.fjjewy,
            msccjewy: data.msccjewy,
            qtccxpxjewy: data.qtccxpxjewy,
            bdzzqlqx: data.bdzzqlqx,
            jdswh: data.jdswh,
            jdzyjwzxrq: this.formatDate(data.jdzyjwzxrq),
            zyjwzxjdjg: data.zyjwzxjdjg,
            zyjwzxyy: data.zyjwzxyy,
            // jfzxrq: this.formatDate(data.jfzxrq),
            yjzfjglx: data.yjzfjglx,
            yjzfjgmc: data.yjzfjgmc,
            jzqx: data.jzqx,
            zyjwzxqr: data.zyjwzxqr,
            zyjwzxzr: data.zyjwzxzr,
            // rjrq: this.formatDate(data.rjrq),
            jcqk: data.jcqk,
            zjqjgzbx: data.zjqjgzbx,
            jdyy: data.jdyy,
            jdrq: this.formatDate(data.jdrq),
            zdjg: data.zdjg,
            sqrxm: data.sqrxm,
            yzfgx: data.yzfgx,
            xbsqsy: data.xbsqsy,
            sqrq: this.formatDate(data.sqrq),
            jcybmsah: data.jcybmsah,
            scwswh: data.scwswh,
            zyjwzxjcyj: data.zyjwzxjcyj,
            yjfksj: data.yjfksj,
            jcyjbz: data.jcyjbz,
            wswh: data.wswh,
            qsrq: this.formatDate(data.qsrq),
            sdwsnr: data.sdwsnr,
            qsdw: data.qsdw,
            qsr: data.qsr,
            hzwsh: data.hzwsh
          }
        )
      }, 100)
      /* stepLogList({ bizId: data.id }).then(res => {
        this.stepdata = res.data
        console.log(res.data);
      }) */
    },
    formatDate(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD')
    },
    formatTime(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD HH:mm:ss')
    },

      // 初始化方法
      add(orgTree) {
        this.orgTree = orgTree
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit() {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields(async (errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            await this.sureUpload()
            values.file1 = this.fileId1.join(',')
            values.file2 = this.fileId2.join(',')
            values.zt = '1'
            values.jdzyjwzxrq = this.jdzyjwzxrqDateString
            values.zyjwzxqr = this.zyjwzxqrDateString
            values.zyjwzxzr = this.zyjwzxzrDateString
            values.jdrq = this.jdrqDateString
            values.sqrq = this.sqrqDateString

            values.jzjg = this.jzjg
            values.jzjgName = this.jzjgName

            values.xzd = this.xzdareaCode.area
            values.hjd = this.hjdareaCode.area
            values.id = this.id
            values.zt = '3'
            sendTopExtendDeclareEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('操作成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('操作成功')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangejdzyjwzxrq(date, dateString) {
        if (dateString === '') {
          this.jdzyjwzxrqDateString = null
        } else {
          this.jdzyjwzxrqDateString = dateString
        }
      },
      onChangezyjwzxqr(date, dateString) {
        if (dateString === '') {
          this.zyjwzxqrDateString = null
        } else {
          this.zyjwzxqrDateString = dateString
        }
      },
      onChangezyjwzxzr(date, dateString) {
        if (dateString === '') {
          this.zyjwzxzrDateString = null
        } else {
          this.zyjwzxzrDateString = dateString
        }
      },
      onChangejdrq(date, dateString) {
        if (dateString === '') {
          this.jdrqDateString = null
        } else {
          this.jdrqDateString = dateString
        }
      },
      onChangesqrq(date, dateString) {
        if (dateString === '') {
          this.sqrqDateString = null
        } else {
          this.sqrqDateString = dateString
        }
      },
      handleCancel() {
        this.form.resetFields()
        this.jzjg = ''
        this.jzjgName = ''
        this.visible = false
      },
      chooseJzdx() {
        this.$refs.chooseJzdxRef.open()
      },
      dateFormat(d) {
        const m = moment(d)
        if (!m.isValid()) {
          return null
        } else {
          return m.format('YYYY-MM-DD')
        }
      },
      getData(value) {
        const data = value[0]
        if (!data) {
          return
        }
        this.jzjg = data.sqjz
        this.jzjgName = data.jzjgName

        this.form.setFieldsValue(
          {
            tyfh: data.tyfh,
            gaxyrbh: data.gaxyrbh,
            jcyxyrbh: data.jcyxyrbh,
            xm: data.xm,
            csrq: this.dateFormat(data.csrq),
            cym: data.cym,
            xb: data.xb,
            mz: data.mz,
            zjlx: '111',
            zjhm: data.sfzh,
            xzd: data.gdjzdszs + ',' + data.gdjzdszds + ',' + data.gdjzdszxq + ',' + data.gdjzd,
            xzdxz: data.gdjzdmx,
            hjd: data.hjszs + ',' + data.hjszds + ',' + data.hjszxq + ',' + data.hjszd,
            hjdxz: data.hjszdmx
          }
        )
        if (data.jdzyjwzxrq) {
          this.form.getFieldDecorator('jdzyjwzxrq', { initialValue: moment(data.jdzyjwzxrq, 'YYYY-MM-DD') })
          this.jdzyjwzxrqDateString = moment(data.zyjwzxqr).format('YYYY-MM-DD')
        }
        if (data.zyjwzxqr) {
          this.form.getFieldDecorator('zyjwzxqr', { initialValue: moment(data.zyjwzxqr, 'YYYY-MM-DD') })
          this.zyjwzxqrDateString = moment(data.zyjwzxqr).format('YYYY-MM-DD')
        }
        if (data.zyjwzxzr) {
          this.form.getFieldDecorator('zyjwzxzr', { initialValue: moment(data.zyjwzxzr, 'YYYY-MM-DD') })
          this.zyjwzxzrDateString = moment(data.zyjwzxzr).format('YYYY-MM-DD')
        }
      }
    }
  }
</script>
<style lang="less" scoped>
//  h3{
//   font-size: 16px;
//   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
//   font-weight: 400;
//   color: #1890FF;
//  }
//  .{
//    position:relative;
//    left:-25px
//  }
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="续报申请日期">
                <a-date-picker style="width: 100%" placeholder="续报申请日期" v-model="queryParam.sqrqDate" @change="onChangesqrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="续报申请状态">
                <a-select v-model="queryParam.zt" placeholder="请选择状态" >
                  <a-select-option v-for="(item,index) in ztDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="16" :sm="24" >
              <span class="table-page-search-submitButtons" style="float:right">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" >
          <a-button style="color:#fff;background:rgba(0, 181, 120, 1)" @click="$refs.addForm.add(orgTree)">新增续报征求检察意见</a-button>
          <a-button type="primary" @click="send">发送续报申请</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.detailForm.open(record)">查看</a>
        </span>
        <span slot="zt" slot-scope="text">
          {{ 'zwxbzt' | dictType(text) }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <send-form ref="sendForm" @ok="handleOk" />
      <detail-form ref="detailForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { getOrgTree } from '@/api/modular/system/orgManage'
  import { sendTopExtendDeclarePage } from '@/api/modular/main/sendtopextenddeclare/sendTopExtendDeclareManage'
  import addForm from './addForm.vue'
  import sendForm from './sendForm.vue'
  import detailForm from './detailForm.vue'
  export default {
    components: {
      STable,
      addForm,
      sendForm,
      detailForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        ztDropDown: [],
        // 表头
        columns: [
          {
            ellipsis: true,
            title: '续报申请状态',
            align: 'center',
            dataIndex: 'zt',
            scopedSlots: { customRender: 'zt' }
          },
          {
            ellipsis: true,
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            ellipsis: true,
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },

          {
            ellipsis: true,
            title: '续报申请日期',
            align: 'center',
            dataIndex: 'sqrq',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD')
            }
          },
          {
            ellipsis: true,
            title: '法院反馈日期',
            align: 'center',
            dataIndex: 'fyfksj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD')
            }
          },
          {
            ellipsis: true,
            title: '征求检察院名称',
            align: 'center',
            dataIndex: 'jcyCode',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return val.split('|')[1]
            }
          },
          {
            ellipsis: true,
            title: '征求法院名称',
            align: 'center',
            dataIndex: 'fyCode',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return val.split('|')[1]
            }
          },
          {
            ellipsis: true,
            title: '操作',
            width: '150px',
            dataIndex: 'action',
            scopedSlots: { customRender: 'action' }
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return sendTopExtendDeclarePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      this.sysDictTypeDropDown()
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamsqrq = this.queryParam.sqrqDate
        if (queryParamsqrq != null) {
            this.queryParam.sqrq = moment(queryParamsqrq).format('YYYY-MM-DD')
            if (queryParamsqrq.length < 1) {
                delete this.queryParam.sqrq
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown () {
        this.ztDropDown = this.$options.filters['dictData']('zwxbzt')
      },
      onChangesqrq(date, dateString) {
        this.sqrqDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      send() {
        // this.$refs.sendForm.open(this.selectedRows[0])

        if (this.selectedRows.length === 1 && this.selectedRows[0].zt === '2') {
          this.$refs.sendForm.open(this.selectedRows[0], this.orgTree)
        } else {
          this.$message.error('请选择一条待发送续报申请记录')
        }
        this.$refs.table.clearSelected()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

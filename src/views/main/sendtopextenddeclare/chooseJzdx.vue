<template>
  <a-modal
    title="选择矫正对象"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <div>
        <a-form v-bind="formItemLayout">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item label="矫正对象">
                <a-input placeholder="请输入姓名" v-model="queryParam.xm"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-decorator="['jzjg', { require:true,rules: [{ message: '请选择矫正单位!' }] }]"
                  placeholder=""
                  style="width:200px;height:36px"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="orgTree"
                  :treeDefaultExpandAll="false"
                  allow-clear
                  tree-default-expand-all
                  v-model="queryParam.jzjg">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :push="1">
              <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
              <a-button style="margin-left: 8px" @click="reset()">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div v-if="visible">
        <s-table
          ref="table"
          :pagination="pagination"
          :columns="columns"
          :data="loadData"
          :alert="false"
          :rowKey="(record) => record.id"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
        </s-table>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { STable } from '@/components'
import { getOrgTree } from '@/api/modular/system/orgManage'
import moment from 'moment'
import {
  addTopPsnPage
} from '@/api/modular/main/accepttemporarilyoutsideprison/acceptTemporarilyOutsidePrisonManage';

export default {
  data() {
    return {
      formItemLayout: { labelCol: { span: 6 }, wrapperCol: { span: 12 } },
      pagination: {
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: total => `Total ${total} items`,
        pageSizeOptions: ['5', '10', '20', '40', '60', '80', '100'],
        pageSize: 40
      },
      // 查询参数
      queryParam: {},
      name: '',
      orgTree: [],
      labelCol: { xs: { span: 24 }, sm: { span: 12 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 12 } },
      visible: false,
      confirmLoading: false,
      packageNameShow: true,
      form: this.$form.createForm(this),
      columns: [
        { title: '姓名', dataIndex: 'xm', align: 'center', key: 'xm' },
        { title: '矫正单位', dataIndex: 'jzjgName', align: 'center', key: 'jzjgName' }
      ],
      storeData: [],
      selectedRowKeys: [],
      selectedRows: [],
      loadData: parameter => {
        parameter.jzlb = '04'
        parameter.zhuangtai = '200'
        return addTopPsnPage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      }
    }
  },
  components: {
    STable
  },
  methods: {
    moment,
    reset() {
      this.jzjg = localStorage.getItem('orgId')
      this.queryParam = { jzjg: localStorage.getItem('orgId') }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = JSON.parse(JSON.stringify(selectedRows))
      this.storeData = [...this.selectedRows, ...this.storeData]
      const noRepeat = [...new Set(this.storeData.map(item => JSON.stringify(item)))]
      this.storeData = noRepeat.map(item => JSON.parse(item))
      if (this.selectedRowKeys.length < this.storeData.length) {
        const temp = []
        this.storeData.forEach(p1 => {
          this.selectedRowKeys.forEach(p2 => {
            if (p1.id === p2) {
              temp.push(p1)
            }
          })
        })
        this.storeData = temp
      }
    },
    // 初始化方法
    open() {
      this.visible = true
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      // todo 限制只选择一人
      this.$emit('selectedRows', this.storeData)
      this.data = []
      this.form.resetFields()
      this.selectedRows = []
      this.selectedRowKeys = []
      this.name = ''
      this.visible = false
    },
    handleCancel() {
      this.data = []
      this.name = ''
      this.form.resetFields()
      this.visible = false
    },
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  },
  created() {
    this.getOrgTree()
  },
  watch: {
    name: {
      handler() {
        this.$refs.table.refresh(true)
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep.ant-input{
  width: 200px;
}

</style>

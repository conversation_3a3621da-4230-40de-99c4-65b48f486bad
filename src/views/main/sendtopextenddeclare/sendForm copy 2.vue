<template>
  <sh-drawer
    title="发送续报申请"
    placement="right"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="root">
          <div class="left">
            <h3 class="cus-title-d">检察院意见</h3>
            <a-col :span="12">
              <a-form-item
                label="检察院意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input disabled v-decorator="['zyjwzxjcyj']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="意见备注"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input disabled v-decorator="['jcyjbz']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="意见书"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input disabled v-decorator="['yjbz']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="审查文书文号"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input disabled v-decorator="['wswh']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="意见反馈时间"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input disabled v-decorator="['yjfksj']" />
              </a-form-item>
            </a-col>
          </div>
          <div class="right">
            <a-col :span="24">
              <h3 class="cus-title-d">案件信息</h3>
            </a-col>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="统一赋号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['tyfh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="社区矫正案件编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled placeholder="请输入社区矫正案件编号" v-decorator="['sqjzajbh']" />
                </a-form-item>
              </a-col>
            </a-row>

            <h3 class="cus-title-d">个人信息</h3>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="公安嫌疑人编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['gaxyrbh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="检察院嫌疑人编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['jcyxyrbh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled placeholder="请选择矫正对象" v-decorator="['xm']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">

                <a-form-item
                  label="曾用名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['cym']" />
                </a-form-item>

              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select disabled placeholder="请选择性别" v-decorator="['xb']">
                    <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="民族"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select disabled placeholder="请选择民族" v-decorator="['mz']">
                    <a-select-option v-for="(item,index) in mzDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="证件类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select disabled placeholder="请选择证件类型" v-decorator="['zjlx']">
                    <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="证件号码"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['zjhm']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input disabled v-decorator="['csrq']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="文化程度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select disabled placeholder="请选择文化程度" v-decorator="['whcd']">
                    <a-select-option v-for="(item,index) in whcdDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24" >
                <a-form-item
                  label="现住地"
                  :labelCol="labelCol2"
                  :wrapperCol="wrapperCol2"

                >
                  <VDistpicker
                    disabled
                    :province="xzdareaCode.province"
                    :city="xzdareaCode.city"
                    :area="xzdareaCode.area"
                    @selected="changeChoseCityxzd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24" >
                <a-form-item
                  label="现住地详址"
                  :labelCol="labelCol2"
                  :wrapperCol="wrapperCol2"

                >
                  <a-input disabled v-decorator="['xzdxz']" />
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24" >
                <a-form-item
                  label="户籍地"
                  :labelCol="labelCol2"
                  :wrapperCol="wrapperCol2"

                >
                  <VDistpicker
                    disabled
                    :province="hjdareaCode.province"
                    :city="hjdareaCode.city"
                    :area="hjdareaCode.area"
                    @selected="changeChoseCityhjd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24" >
                <a-form-item
                  label="户籍地详址"
                  :labelCol="labelCol2"
                  :wrapperCol="wrapperCol2"

                >
                  <a-input disabled v-decorator="['hjdxz']" />
                </a-form-item>
              </a-col>
            </a-row>

            <h3 class="cus-title-d">暂予监外信息</h3>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="决定书文号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入决定书文号" v-decorator="['jdswh']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="决定暂予监外执行日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入决定书文号" v-decorator="['jdzyjwzxrq']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="暂予监外执行决定机关"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入暂予监外执行决定机关" v-decorator="['zyjwzxjdjg']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="暂予监外执行原因"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入暂予监外执行原因" v-decorator="['zyjwzxyy']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="暂予监外执行起日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入暂予监外执行原因" v-decorator="['zyjwzxqr']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="暂予监外执行止日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入暂予监外执行原因" v-decorator="['zyjwzxzr']" />
                </a-form-item>
              </a-col>
            </a-row>
            <h3 class="cus-title-d">医学司法鉴定意见</h3>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="鉴定医院"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入鉴定医院" v-decorator="['jdyy']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="鉴定日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入鉴定医院" v-decorator="['jdrq']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="诊断结果"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入诊断结果" v-decorator="['zdjg']" />
                </a-form-item>
              </a-col>
            </a-row>
            <h3 class="cus-title-d">续报申请信息</h3>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="申请人姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入申请人姓名" v-decorator="['sqrxm']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="与罪犯关系"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入与罪犯关系" v-decorator="['yzfgx']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="续报申请事由"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入续报申请事由" v-decorator="['xbsqsy']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="申请日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input disabled placeholder="请输入续报申请事由" v-decorator="['sqrq']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="《罪犯暂予监外执行续保及改造表现鉴定表》"
                  :labelCol="{
                    xs: { span: 24 },
                    sm: { span: 13}
                  }"
                  :wrapperCol="{
                    xs: { span: 24 },
                    sm: { span: 11 }
                  }"
                >
                  <a-upload
                    disabled
                    :customRequest="customRequest"
                    :multiple="true"
                    :showUploadList="false"
                    name="file">
                  </a-upload>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="文书"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-upload
                    disabled
                    :customRequest="customRequest"
                    :multiple="true"
                    :showUploadList="false"
                    name="file">
                  </a-upload>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="选择法院"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    v-model="fyCode"
                    show-search
                    style="width: 100%"
                    :options="extOrgFy" >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form>
    </a-spin>
    <!-- <a-button @click="handleCancel">取消</a-button>
    <a-button type="primary" @click="handleSubmit(1)">提交</a-button> -->
  </sh-drawer>
</template>

<script>
import VDistpicker from 'v-distpicker/src/Distpicker'
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import moment from 'moment';
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';
// import { stepLogList } from '@/api/modular/main/steplog/stepLogManage';
import {
  sendTopExtendDeclareEdit
} from '@/api/modular/main/sendtopextenddeclare/sendTopExtendDeclareManage';
export default {
  name: 'SendForm',
  data() {
    return {
      xzdareaCode: { province: '', city: '', area: '' },
      hjdareaCode: { province: '', city: '', area: '' },
      width: window.innerWidth - 200,
      fileList: [],
      fileId: [],
      labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
      id: '',
      fyCode: '',
      extOrgFy: [],
      ztDropDown: [],
      zjlxDropDown: [],
      whcdDropDown: [],
      xbDropDown: [],
      mzDropDown: [],
      data: [],
      jdzyjwzxrqDateString: '',
      zyjwzxqrDateString: '',
      zyjwzxzrDateString: '',
      jdrqDateString: '',
      sqrqDateString: '',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created () {
    this.getExtOrgData()
    this.sysDictTypeDropDown()
  },
  components: {
    VDistpicker
  },
  methods: {
    getExtOrgData() {
      extOrgInfoList({ type: 20, orgName: '' }).then(res => {
        res.data.forEach(p => {
          this.extOrgFy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
        })
      })
    },
    changeChoseCityxzd: function (e) { // 后执行
      this.xzdareaCode.province = e.province.value
      this.xzdareaCode.city = e.city.value
      this.xzdareaCode.area = e.area.value
    },
    changeChoseCityhjd: function (e) { // 后执行
      this.hjdareaCode.province = e.province.value
      this.hjdareaCode.city = e.city.value
      this.hjdareaCode.area = e.area.value
    },
    /**
     * 获取字典数据
     */
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
    },
    afterVisibleChange(val) {
      console.log('visible', val);
    },
    customRequest(data) {
      const formData = new FormData()
      formData.append('file', data.file)
      sysFileInfoUpload(formData).then((res) => {
        if (res.success) {
          this.$message.success('上传成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('上传失败：' + res.message)
        }
      })
    },
    // 初始化方法
    open(data) {
      this.id = data.id
      setTimeout(() => {
        this.form.setFieldsValue(
          {
            jcyCode: data.jcyCode,
            fyCode: data.fyCode,
            docList: data.docList,
            file4053: data.file4053,
            file4055: data.file4055,
            taskId4053: data.taskId4053,
            taskId4054: data.taskId4054,
            taskId4055: data.taskId4055,
            taskId4056: data.taskId4056,
            jcyfksj: data.jcyfksj,
            fyfksj: data.fyfksj,
            id: data.id,
            tyfh: data.tyfh,
            sqjzajbh: data.sqjzajbh,
            gaxyrbh: data.gaxyrbh,
            jcyxyrbh: data.jcyxyrbh,
            xm: data.xm,
            jzjg: data.jzjg,
            cym: data.cym,
            xb: data.xb,
            zjlx: data.zjlx,
            zjhm: data.zjhm,
            csrq: this.formatDate(data.csrq),
            zasnl: data.zasnl,
            gj: data.gj,
            mz: data.mz,
            csd: data.csd,
            hjd: data.hjd,
            hjdxz: data.hjdxz,
            xzd: data.xzd,
            xzdxz: data.xzdxz,
            whcd: data.whcd,
            sxpjjg: data.sxpjjg,
            pjwswh: data.pjwswh,
            pjrq: this.formatDate(data.pjrq),
            pjzm: data.pjzm,
            pjqtzm: data.pjqtzm,
            zx: data.zx,
            ypxq: data.ypxq,
            yxqqr: data.yxqqr,
            yxqzr: data.yxqzr,
            fjx: data.fjx,
            fjxjtqk: data.fjxjtqk,
            hxkyq: data.hxkyq,
            ccxpx: data.ccxpx,
            fjjewy: data.fjjewy,
            msccjewy: data.msccjewy,
            qtccxpxjewy: data.qtccxpxjewy,
            bdzzqlqx: data.bdzzqlqx,
            jdswh: data.jdswh,
            jdzyjwzxrq: this.formatDate(data.jdzyjwzxrq),
            zyjwzxjdjg: data.zyjwzxjdjg,
            zyjwzxyy: data.zyjwzxyy,
            // jfzxrq: this.formatDate(data.jfzxrq),
            yjzfjglx: data.yjzfjglx,
            yjzfjgmc: data.yjzfjgmc,
            jzqx: data.jzqx,
            zyjwzxqr: data.zyjwzxqr,
            zyjwzxzr: data.zyjwzxzr,
            // rjrq: this.formatDate(data.rjrq),
            jcqk: data.jcqk,
            zjqjgzbx: data.zjqjgzbx,
            jdyy: data.jdyy,
            jdrq: this.formatDate(data.jdrq),
            zdjg: data.zdjg,
            sqrxm: data.sqrxm,
            yzfgx: data.yzfgx,
            xbsqsy: data.xbsqsy,
            sqrq: this.formatDate(data.sqrq),
            jcybmsah: data.jcybmsah,
            scwswh: data.scwswh,
            zyjwzxjcyj: data.zyjwzxjcyj,
            yjfksj: data.yjfksj,
            jcyjbz: data.jcyjbz,
            wswh: data.wswh,
            qsrq: this.formatDate(data.qsrq),
            sdwsnr: data.sdwsnr,
            qsdw: data.qsdw,
            qsr: data.qsr,
            hzwsh: data.hzwsh
          }
        )
      }, 100)
      /* stepLogList({ bizId: data.id }).then(res => {
        this.stepdata = res.data
        console.log(res.data);
      }) */
      this.visible = true
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      this.confirmLoading = true
      if (!this.fyCode) {
        this.$message.error('请选择法院')
        return;
      }
      sendTopExtendDeclareEdit({
        id: this.id,
        zt: '3',
        fyCode: this.fyCode
      }).then((res) => {
        if (res.success) {
          this.$message.success('提交成功')
          this.confirmLoading = false
          this.$emit('ok', 1)
          this.handleCancel()
        } else {
          this.$message.error('提交失败')// + res.message
        }
      }).finally((res) => {
        this.confirmLoading = false
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    },
    dictCode(select, code) {
      if (code.length !== 6) {
        return
      }
      select.province = code.substring(0, 2) + '0000'
      select.city = code.substring(0, 4) + '00'
      select.area = code
    },
    formatDate(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD')
    },
    formatTime(time) {
      const m = moment(time)
      if (!m.isValid()) {
        return ''
      }
      return m.format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
<style lang="less" scoped>
.root {
  display: block;
  // min-height: 1000px;
  width: 100%;
  position: relative;

  // .left {
  //   top: 0;
  //   display: inline-block;
  //   height: 100%;
  //   width: 30%;
  //   vertical-align: middle;
  //   padding: 10px;

  //   .left_title {
  //     background: #F3F3F3;
  //     border: 1px solid #E6E6E6;
  //     display: inline-block;
  //     width: 20%;
  //     vertical-align: middle;
  //     min-height: 40px;
  //     line-height: 40px;
  //     text-align: center;
  //   }

  //   .right_content1 {
  //     .right_content();
  //     width: 30%;
  //   }

  //   .right_content {
  //     vertical-align: middle;
  //     padding-left: 15px;
  //     display: inline-block;
  //     width: 80%;
  //     min-height: 40px;
  //     background: #fff;
  //     line-height: 40px;
  //     border: 1px solid #E6E6E6;
  //   }

  //   .inline {
  //     border: 1px solid #E6E6E6;
  //   }

  //   .icon {
  //     .icon()
  //   }

  //   .table {
  //     background: #F3F3F3;
  //     border: 1px solid #E6E6E6;
  //     height: 100%;
  //     width: 100%;
  //   }
  // }

  // .right {
  //   display: inline-block;
  //   vertical-align: top;
  //   width: 60%;
  //   padding: 10px;

  //   // background: red;
  //   .icon {
  //     .icon()
  //   }
  // }
}

.icon {
  width: 4px;
  height: 14px;
  background: #008CFF;
  display: inline-block;
}

.shyj {
  padding: 2px 9px;
  background: rgba(0, 140, 255, 0.07);
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #008CFF;
  line-height: 0px;
  border-radius: 4px;
  opacity: 1;
  border: 1px dashed rgba(0, 140, 255, 0.4);
}

</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('irsVocationalSkillsLevelCertificate:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
              <a-form-item label="出生日期">
                <a-input v-model="queryParam.birthdate" allow-clear placeholder="请输入出生日期"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="证书类型">
                <a-input v-model="queryParam.certificatetype" allow-clear placeholder="请输入证书类型"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.gendername" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证单位">
                  <a-input v-model="queryParam.issuedorgan" allow-clear placeholder="请输入发证单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="等级">
                  <a-input v-model="queryParam.jdrank" allow-clear placeholder="请输入等级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="职业方向">
                  <a-input v-model="queryParam.jobfx" allow-clear placeholder="请输入职业方向"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="理论成绩">
                  <a-input v-model="queryParam.llscore" allow-clear placeholder="请输入理论成绩"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评定成绩">
                  <a-input v-model="queryParam.pdscore" allow-clear placeholder="请输入评定成绩"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="技能成绩">
                  <a-input v-model="queryParam.scscore" allow-clear placeholder="请输入技能成绩"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="单位统一社会信用代码">
                  <a-input v-model="queryParam.uscc" allow-clear placeholder="请输入单位统一社会信用代码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="综合成绩">
                  <a-input v-model="queryParam.zhscore" allow-clear placeholder="请输入综合成绩"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书颁发日期">
                  <a-input v-model="queryParam.zsbftime" allow-clear placeholder="请输入证书颁发日期"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="职业工种">
                  <a-input v-model="queryParam.zygzname" allow-clear placeholder="请输入职业工种"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.zsnum"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('irsVocationalSkillsLevelCertificate:add')" >
          <a-button type="primary" v-if="hasPerm('irsVocationalSkillsLevelCertificate:add')" icon="plus" @click="$refs.addForm.add()">新增职业技能等级证书</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('irsVocationalSkillsLevelCertificate:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('irsVocationalSkillsLevelCertificate:edit') & hasPerm('irsVocationalSkillsLevelCertificate:delete')"/>
          <a-popconfirm v-if="hasPerm('irsVocationalSkillsLevelCertificate:delete')" placement="topRight" title="确认删除？" @confirm="() => irsVocationalSkillsLevelCertificateDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { irsVocationalSkillsLevelCertificatePage, irsVocationalSkillsLevelCertificateDelete } from '@/api/modular/main/irsvocationalskillslevelcertificate/irsVocationalSkillsLevelCertificateManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '出生日期',
            align: 'center',
            dataIndex: 'birthdate'
          },
          {
          ellipsis: true,
          title: '证书类型',
            align: 'center',
            dataIndex: 'certificatetype'
          },
          {
          ellipsis: true,
          title: '性别',
            align: 'center',
            dataIndex: 'gendername'
          },
          {
          ellipsis: true,
          title: '发证单位',
            align: 'center',
            dataIndex: 'issuedorgan'
          },
          {
          ellipsis: true,
          title: '等级',
            align: 'center',
            dataIndex: 'jdrank'
          },
          {
          ellipsis: true,
          title: '职业方向',
            align: 'center',
            dataIndex: 'jobfx'
          },
          {
          ellipsis: true,
          title: '理论成绩',
            align: 'center',
            dataIndex: 'llscore'
          },
          {
          ellipsis: true,
          title: '评定成绩',
            align: 'center',
            dataIndex: 'pdscore'
          },
          {
          ellipsis: true,
          title: '技能成绩',
            align: 'center',
            dataIndex: 'scscore'
          },
          {
          ellipsis: true,
          title: '单位统一社会信用代码',
            align: 'center',
            dataIndex: 'uscc'
          },
          {
          ellipsis: true,
          title: '综合成绩',
            align: 'center',
            dataIndex: 'zhscore'
          },
          {
          ellipsis: true,
          title: '证书颁发日期',
            align: 'center',
            dataIndex: 'zsbftime'
          },
          {
          ellipsis: true,
          title: '职业工种',
            align: 'center',
            dataIndex: 'zygzname'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return irsVocationalSkillsLevelCertificatePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      if (this.hasPerm('irsVocationalSkillsLevelCertificate:edit') || this.hasPerm('irsVocationalSkillsLevelCertificate:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      irsVocationalSkillsLevelCertificateDelete (record) {
        irsVocationalSkillsLevelCertificateDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

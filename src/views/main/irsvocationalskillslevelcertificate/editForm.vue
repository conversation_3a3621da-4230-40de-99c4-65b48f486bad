<template>
  <a-modal
    title="编辑职业技能等级证书"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生日期" v-decorator="['birthdate', {rules: [{required: true, message: '请输入出生日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书类型" v-decorator="['certificatetype', {rules: [{required: true, message: '请输入证书类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['gendername', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证单位" v-decorator="['issuedorgan', {rules: [{required: true, message: '请输入发证单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入等级" v-decorator="['jdrank', {rules: [{required: true, message: '请输入等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职业方向"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职业方向" v-decorator="['jobfx', {rules: [{required: true, message: '请输入职业方向！'}]}]" />
        </a-form-item>
        <a-form-item
          label="理论成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入理论成绩" v-decorator="['llscore', {rules: [{required: true, message: '请输入理论成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评定成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评定成绩" v-decorator="['pdscore', {rules: [{required: true, message: '请输入评定成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="技能成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入技能成绩" v-decorator="['scscore', {rules: [{required: true, message: '请输入技能成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位统一社会信用代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位统一社会信用代码" v-decorator="['uscc', {rules: [{required: true, message: '请输入单位统一社会信用代码！'}]}]" />
        </a-form-item>
        <a-form-item v-show="false"><a-input v-decorator="['zsnum']" /></a-form-item>
        <a-form-item
          label="综合成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入综合成绩" v-decorator="['zhscore', {rules: [{required: true, message: '请输入综合成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书颁发日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书颁发日期" v-decorator="['zsbftime', {rules: [{required: true, message: '请输入证书颁发日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职业工种"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职业工种" v-decorator="['zygzname', {rules: [{required: true, message: '请输入职业工种！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsVocationalSkillsLevelCertificateEdit } from '@/api/modular/main/irsvocationalskillslevelcertificate/irsVocationalSkillsLevelCertificateManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              birthdate: record.birthdate,
              certificatetype: record.certificatetype,
              gendername: record.gendername,
              issuedorgan: record.issuedorgan,
              jdrank: record.jdrank,
              jobfx: record.jobfx,
              llscore: record.llscore,
              pdscore: record.pdscore,
              scscore: record.scscore,
              uscc: record.uscc,
              zsnum: record.zsnum,
              zhscore: record.zhscore,
              zsbftime: record.zsbftime,
              zygzname: record.zygzname
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsVocationalSkillsLevelCertificateEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

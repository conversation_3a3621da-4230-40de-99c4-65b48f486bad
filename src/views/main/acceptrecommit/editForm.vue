<template>
  <a-drawer
    title="详情"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer custom-wrapper"
    @close="handleCancel">
    <!-- <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  > -->
    <a-spin :spinning="confirmLoading">
      <div v-if="visible" class="flex-wrapper">
        <div class="flex-item">
          <h3 style="display:inline"><span style="background:#0EADEE">&nbsp;</span> &nbsp;社区矫正对象协同信息：</h3>
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="社区矫正对象编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['sqjzajbh']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jzdwmc']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="人员姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['xm']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['xb']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled value="身份证" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['zjhm']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jzlb']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="执行地县级人民检察院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jsdw']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['sqjzkssj']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['sqjzjssj']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="24" >
                <a-form-item label="居住地" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                  <a-input disabled v-decorator="['xzdxz']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="罪名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['pjzm']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="刑期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['ypxq']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jzjglxr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['jzjglxrdh']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">
              <a-col :md="24" :pull="2">
                <a-form-item label="文书" :labelCol="labelCol2" :wrapperCol="wrapperCol2">

                </a-form-item>
              </a-col>
            </a-row> -->
          </a-form>
          <h3 style="display:inline"><span style="background:#0EADEE">&nbsp;</span> &nbsp;公安处罚文书区域</h3>
          <div style="padding: 20px 10px 20px 14%;">
            <DocList @onSelect="handleSelect" :list="docList"/>
          </div>

          <h3 style="display:inline"><span style="background:#0EADEE">&nbsp;</span> &nbsp;公安再犯罪协同信息：</h3>
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="案件编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['gaajbh']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="嫌疑人编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['xyrbh']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="案件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['ajlxmc']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="措施类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['cslxmc']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="24" >
                <a-form-item label="措施内容" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                  <a-textarea :rows="4" disabled v-decorator="['csnr']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="办案单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['badwmc']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="主办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['zbr']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input disabled v-decorator="['zbrlxdh']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">
              <a-col :md="24" :pull="2">
                <a-form-item label="文书" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                  <a v-show="'袁湘' === record.xm" target="_blank" href="https://ruyan.zjsft.gov.cn/oss/zjgz/upload/yxys.pdf">社区矫正对象被限制人身自由通知书.doc</a>
                  <a v-for="(item,index) in docList" :key="index" href="javascript:" @click="$PdfDrawer.viewPdf({title:item.ws,url:item.ossUrl})">{{ item.ws }}</a>
                </a-form-item>
              </a-col>
            </a-row> -->

          </a-form>
        </div>
        <div class="flex-item">
          <PdfViewBox v-if="docList.length" ref="PdfViewBox" :pdfList="docList"/>
        </div>
      </div>

    </a-spin>
  </a-drawer>
</template>

<script>
  import moment from 'moment'
  import { acceptRecommitEdit } from '@/api/modular/main/acceptrecommit/acceptRecommitManage'
  import { acceptCorrectionDocList } from '@/api/modular/main/acceptDocManage';
  import PdfViewBox from './components/pdfViewBox.vue'
  import DocList from './components/DocList.vue';
  export default {
    components: { PdfViewBox, DocList },
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
        sqjzkssjDateString: '',
        sqjzjssjDateString: '',
        visible: false,
        editable: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        docList: [],
        xbDropDown: [],
        jzlbDropDown: [],
        record: {},
        drawetWidth: 1000
      }
    },
    created() {
    this.drawetWidth = window.innerWidth - 200
  },
    methods: {
      handleSelect(record) {
        console.log(this.record)
        this.$refs.PdfViewBox.changeSelect(record)
      },
      moment,
      // 初始化方法
      open (record, editable) {
        this.visible = true
        this.editable = editable
        this.record = record
        record.sqjzkssj = moment(record.sqjzkssj).format('YYYY-MM-DD')
        record.sqjzjssj = moment(record.sqjzjssj).format('YYYY-MM-DD')
        setTimeout(() => {
          this.form.setFieldsValue(
            record
          )
        }, 100)
        this.sqjzkssjDateString = moment(record.sqjzkssj).format('YYYY-MM-DD')
        this.sqjzjssjDateString = moment(record.sqjzjssj).format('YYYY-MM-DD')
        acceptCorrectionDocList({ contactId: record.id }).then(res => {
          if (res.success) {
            this.docList = res.data
          }
          console.log(this.docList, 'docList')
        })
      },
      handleSubmit () {
        if (this.editable) {
          const { form: { validateFields } } = this
          this.confirmLoading = true
          validateFields((errors, values) => {
            if (!errors) {
              for (const key in values) {
                if (typeof (values[key]) === 'object') {
                  values[key] = JSON.stringify(values[key])
                }
              }
              acceptRecommitEdit(values).then((res) => {
                if (res.success) {
                  this.$message.success('编辑成功')
                  this.confirmLoading = false
                  this.$emit('ok', values)
                  this.handleCancel()
                } else {
                  this.$message.error('编辑失败')//  + res.message
                }
              }).finally(() => {
                this.confirmLoading = false
              })
            } else {
              this.confirmLoading = false
            }
          })
        } else {
          this.form.resetFields()
          this.visible = false
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>

  .flex-wrapper{
    display: flex;
    .flex-item{
      flex:1;
      &:last-child{
        margin-left: 10px;
      }
    }
  }
</style>

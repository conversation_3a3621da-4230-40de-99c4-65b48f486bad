<template>
  <!-- <a-row :gutter="24"> -->
  <a-form :form="form">

    <a-row :gutter="24">
      <a-col :span="colSpan">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['xm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="colSpan">
        <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree
            :disabled="true"
            v-decorator="['jzdwmc', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="colSpan">
        <a-form-item label="文书名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['wsmc', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="colSpan">
        <a-form-item label="文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wsh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="colSpan">
        <a-form-item label="协同单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <!-- {{ parentData.extOrgInfoData }} -->
          <sh-select
            :options="[
              ...parentData.extOrgInfoData.jcy,
              ...parentData.extOrgInfoData.ga,
              ...parentData.extOrgInfoData.sqjz
            ]"
            labelKey="orgName"
            :disabled="true"
            valueKey="orgCode"
            style="width:100%"
            placeholder="请选择"
            v-decorator="['tsdwmc', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="colSpan">
        <a-form-item label="接收日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['sdsj', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="colSpan">
        <a-form-item label="反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['fkrq', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="colSpan">
        <a-form-item label="操作时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['czsj', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="colSpan">
        <a-form-item label="操作人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['czr', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <!-- v-if="typeModel" -->
      <a-col :span="colSpan" >
        <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea
            :rows="4"

            placeholder="请输入"
            v-decorator="['bzsm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <!-- <template v-else>
        <a-col :span="24">
          <a-form-item label="通知回执文书" :labelCol="labelCol" :wrapperCol="wrapperCol">

            <div style="position:relative">
              <sh-file-upload
                v-decorator="['hzws', {initialValue:[], rules: [{ required: true, message: '请输入' }] }]"
                :disabled="readOnly"
                :btnText="'附件上传'"
                accept=".pdf"
                uploadUrl="/api/sysFileInfo/uploadOss?ext=1" >
              </sh-file-upload>
              <div style="color:rgba(255, 54, 54, 1)">
                说明：请上传盖章的回执通知扫描件或电子件（文书号请使用当前页面自动生成的文书号，防止文号重复）
              </div>
              <a-button v-if="!readOnly" style="position:absolute;left:124px;top:4px;" type="dashed" @click="handleClickDownLoad('1769556076487897090')" > <a-icon type="download" /> 样表下载 </a-button>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="{span:2}" :wrapperCol="{span:22}">
            <a-textarea
              :rows="4"
              placeholder="请输入"
              v-decorator="['bzsm', { rules: [{ required: false, message: '请输入！' }] }]"
            />
          </a-form-item>
        </a-col>
      </template> -->

    </a-row>

  </a-form>
  <!-- </a-row> -->
</template>

  <script>
  import { sysFileInfoDownload } from '@/api/modular/system/fileManage';

  export default {
    inject: ['parentData'],
    props: {
        typeModel: {
            default: true,
            type: Boolean
        },
      readOnly: {
        default: false,
        type: Boolean
      },
      baseObj: {
        default: () => {},
        type: Object
      }
    },
    watch: {
      readOnly: {
            handler() {
                if (this.readOnly) {
                    this.colSpan = 12
                    this.labelCol = {
                        span: 4
                    }
                    this.wrapperCol = {
                        span: 20
                    }
                }
            },
             immediate: true
        },
        typeModel: {
            handler() {
                // if (this.typeModel) {
                    this.colSpan = 24
                    this.labelCol = {
                        span: 4
                    }
                    this.wrapperCol = {
                        span: 20
                    }
                // } else {
                //     this.colSpan = 12
                //         this.labelCol = {
                //             span: 4
                //         }
                //         this.wrapperCol = {
                //             span: 20
                //         }
                // }
            },
             immediate: true
        },
      baseObj: {
        handler() {
          this.$nextTick(() => {
            this.setVals()
          })
        },
        deep: true
      }
    },

    data() {
      return {
        isShow: false,
        colSpan: 12,
        labelCol: {
            span: 24
        },
        wrapperCol: {
            span: 24
        },
        form: this.$form.createForm(this)
      }
    },
    mounted() {
        // this.colSpan = 24
        // this.labelCol = {
        //     span: 4
        // }
        // this.wrapperCol = {
        //     span: 20
        // }
    },

    methods: {
      handleClickDownLoad(id) {
        sysFileInfoDownload({ id: id }).then((res) => {
          this.cardLoading = false
          this.downloadfile(res)
          // eslint-disable-next-line handle-callback-err
        }).catch((err) => {
          this.cardLoading = false
          this.$message.error('下载错误：获取文件流错误.' + err)
        })
      },
      downloadfile (res) {
        var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
        var contentDisposition = res.headers['content-disposition']
        var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
        var result = patt.exec(contentDisposition)
        var filename = result[1]
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob) // 创建下载的链接
        var reg = /^["](.*)["]$/g
        downloadElement.style.display = 'none'
        downloadElement.href = href
        downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href)
      },
      handleChange(e) {
        console.log(e)
      },
      setVals() {
        // 家庭成员及社会关系
        // if (this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y') {
        //   this.isShow = true
        // } else {
        //   this.isShow = false
        // }

        const allData = this.form.getFieldsValue()

        const values = {}
        Object.keys(allData).map(key => {
          if (this.baseObj[key]) {
            values[key] = this.baseObj[key]
          } else {
            values[key] = null
          }
        })
        // values.zsd = addres
        // values.hjszd = addres2
        values.fjx = values.fjx ? values.fjx.split(',') : []

        this.form.setFieldsValue({
          ...values
        })
      },

      handleSubmit() {
        return new Promise((resolve, reject) => {
          this.form.validateFieldsAndScroll((err, values) => {
            if (!err) {
              resolve(values)
            } else {
              this.$message.error('信息未填写完成,请检查')
              reject(err)
            }
          })
        })
      }
    }
  }
  </script>

  <style lang="less" scoped>
  /deep/.ant-card {
    // background: #f5f6fa;
  }

  /deep/.ant-card-body {
    padding-left: 0;
    padding-right: 0;
  }
  </style>

<template>
  <div style="width: 100%;height: 100%;">
    <a-alert v-if="selectedPdf.ws" :message="`${selectedPdf.ws}`" type="info" show-icon />
    <a-layout v-if="selectedPdf.ws" style="min-height: 100%;overflow: hidden;">
      <a-layout-header style="padding: 10px 0;">
        <div class="flex-space">
          <a-button type="link" @click="previousItem" :disabled="currentIndex === 0">
            <a-icon type="left" />上一份
          </a-button>
          <!-- <button @click="previousItem" :disabled="currentIndex === 0">上一个</button> -->
          <span>{{ selectedPdf.ws }}</span>
          <a-button type="link" @click="nextItem" :disabled="currentIndex === pdfList.length - 1">
            下一份<a-icon type="right" />
          </a-button>
          <!-- <button @click="nextItem" :disabled="currentIndex === pdfList.length - 1">下一个</button> -->
        </div>
      </a-layout-header>

      <a-layout-content style="padding: 24px;height: 600px;">
        <embed
          v-if="selectedPdf.ossUrl"
          :src="selectedPdf.ossUrl"
          type="application/pdf"
          style="width:100%;height:100%" />
        <!-- <iframe v-if="selectedPdf.uri" :src="selectedPdf.uri" width="100%" height="100%"></iframe> -->
        <p v-else>Please select a PDF from the menu</p>
      </a-layout-content>
    </a-layout>

  </div>
</template>

<script>
export default {
  props: {
    pdfList: {
      default: () => [],
      type: Array
    }
  },
  data() {
    return {
      currentIndex: 0,
      // pdfList: [
      //   { name: 'pdf1', url: 'pdfUrl1' },
      //   { name: 'pdf2', url: 'pdfUrl2' }
      //   // Add more PDFs as needed
      // ],
      selectKeys: []
      // selectedPdf: {} // Default selected PDF
    };
  },
  computed: {
    selectedPdf() {
      return this.pdfList[this.currentIndex];
    }
  },
  mounted() {
    // this.selectedPdf = this.pdfList[0]
    // this.selectKeys = this.pdfList[0].id
  },
  watch: {
    pdfList: {
      deep: true,
      handler(newVal, oldVal) {
        this.selectedPdf = this.pdfList[0]
        this.selectKeys = this.pdfList[0].id
      }
    }
  },
  methods: {
    previousItem() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },
    nextItem() {
      if (this.currentIndex < this.pdfList.length - 1) {
        this.currentIndex++;
      }
    },
    handleMenuClick({ key }) {
      this.selectedPdf = this.pdfList.find(pdf => pdf.id === key) || {};
    },
    changeSelect(record) {
      const index = this.pdfList.findIndex(item => item.id === record.id);

      console.log(index)
      this.currentIndex = index
    }

  }
};
</script>

<style lang="less" scoped>
.flex-space{
  display: flex;
  align-items: center;
  justify-content: space-between
}
/deep/.ant-layout-header {
  background: #fff;
  padding: 0;
  height: inherit;
  max-height: 146px;
  overflow: auto;
}

/deep/.ant-menu-item {
  margin: 0 !important;

  &:first-child {}
}
</style>

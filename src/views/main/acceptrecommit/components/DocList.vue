<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    :customRow="(record) => {
      return {
        on:{
          click:(event)=>{

            this.$emit('onSelect',record)
          }
        }

      }
    }"
    bordered>

    <template slot="ws" slot-scope="text">
      <a-button type="link">{{ text }}</a-button>
    </template>
    <template slot="ossUrl" slot-scope="text">

      <a-button @click="$PdfDrawer.viewPdf({title:'详情',url:text})" type="link">{{ text.match(/\/([^\/]+)\/?$/)[1] }}</a-button>
    </template>

  </a-table>
</template>
  <script>
  const columns = [
  {
          title: '序号',
          dataIndex: '',
          width: 60,
          key: 'rowIndex',
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
    {
      title: '法律文书名称',
      dataIndex: 'ws',
      scopedSlots: { customRender: 'ws' }
    }
    // {
    //   title: '附件',
    //   className: 'column-money',
    //   dataIndex: 'ossUrl',
    //   scopedSlots: { customRender: 'ossUrl' }
    // }

  ];

  const data = [
    {
      key: '1',
      name: 'John Brown',
      money: '￥300,000.00',
      address: 'New York No. 1 Lake Park'
    },
    {
      key: '2',
      name: 'Jim Green',
      money: '￥1,256,000.00',
      address: 'London No. 1 Lake Park'
    },
    {
      key: '3',
      name: 'Joe Black',
      money: '￥120,000.00',
      address: 'Sidney No. 1 Lake Park'
    }
  ];

  export default {
    props: {
      list: {
        default: () => [],
        type: Array
      }
    },
    data() {
      return {
        data,
        columns
      };
    }
  };
  </script>
  <style lang="less" scoped>
  th.column-money,
  td.column-money {
    text-align: right !important;
  }
 /deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin:  0 ;
  }

  /deep/.ant-table-body .ant-table-thead > tr > th{
    background: #fafafa !important;
  }

  </style>
  <style lang="less"></style>

<template>
  <a-modal
    title="已列管发送至公安的社区矫正对象清单"
    :width="950"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
    :footer="null"
    :destroyOnClose="true"
  >
    <div>
      <a-card :bordered="false" :bodyStyle="{ 'padding-bottom': '0px', 'margin-bottom': '10px' }">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正对象">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入矫正对象"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item label="矫正单位">
                  <sh-correction-org-tree placeholder="请选择矫正单位" v-model="queryParam.jzdw" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                  <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
                  <x-down ref="batchExport" @batchExport="batchExport" />
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false">
        <s-table
          ref="table"
          :columns="columns"
          :data="loadData"
          :alert="true"
          :rowKey="(record) => record.id"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        />
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { STable, XDown } from '@/components';
import { mapGetters } from 'vuex';
import { logExport, logPage } from '@/api/modular/main/acceptrecommit/acceptRecommitManage';

export default {
  components: { STable, XDown },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created () {
    this.queryParam.jzjg = this.userInfo.loginEmpInfo.orgId
  },
  data() {
    return {
      queryParam: {},
      visible: false,
      confirmLoading: false,
      loading: false,
      selectedRowKeys: [],
      selectedRows: [],
      loadData: parameter => {
        parameter.deleted = 0
        return logPage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      // 表头
      columns: [
        { ellipsis: true, title: '序号', align: 'center', width: 80, customRender: (text, r, i) => i + 1 },
        { ellipsis: true, title: '姓名', align: 'center', width: 65, dataIndex: 'xm' },
        { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jzjg_name' },
        { ellipsis: true, title: '接收单位', align: 'center', dataIndex: 'jsdw' },
        { ellipsis: true, title: '发送时间', align: 'center', dataIndex: 'fasongshijian' }
      ]
    }
  },
  methods: {
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    open() {
      this.visible = true
    },
    handleCancel () {
      this.selectedRowKeys = []
      this.$refs.table.refresh()
      this.visible = false
    },
    batchExport() {
      logExport(this.queryParam).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    }
}
}
</script>

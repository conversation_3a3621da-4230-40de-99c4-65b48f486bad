<template>
  <a-modal
    title="选择需发送至公安的社区矫正对象"
    :width="950"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <div>
      <a-card :bordered="false" :bodyStyle="tstyle">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-tree-select
                    v-model="queryParam.jzjg"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择矫正单位"
                  >
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24" >
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="$refs.table2.refresh(true)" >查询</a-button>
                  <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
                </span>
              </a-col>
            </a-row>
            <div style="color: rgba(255, 0, 0, 1);margin: 10px 0">
              系统默认自动发送新入矫对象至公安侧，请勿重复发送。 （该功能用于数据测试及部分数据丢失场景使用）
            </div>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false">
        <s-table
          ref="table2"
          :columns="columns"
          :data="loadData"
          :alert="true"
          :rowKey="(record) => record.id"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        />
      </a-card>
    </div>
  </a-modal>
</template>
<script>
import { STable } from '@/components'
import {
  correctionObjectInformationPage,
  sendCorrectionObjectInformation
} from '@/api/modular/main/correctionobjectinformation/correctionObjectInformationManage'
import { getOrgTree } from '@/api/modular/system/orgManage'

export default {
  components: { STable },
  data () {
    return {
      // 查询参数
      queryParam: {},
      confirmLoading: false,
      loading: false,
      orgTree: [],
      // 表头
      columns: [
        { ellipsis: true, title: '姓名', align: 'center', width: 65, dataIndex: 'xm' },
        { ellipsis: true, title: '矫正单位', align: 'center', width: 125, dataIndex: 'jzjgName' }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        parameter.zhuangtai = '200'
        return correctionObjectInformationPage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      visible: false
    }
  },
  created () {
    getOrgTree().then((res) => {
      if (res.success) {
        this.orgTree = res.data
      } else {
        this.$message.warning(res.message)
      }
    })
  },
  methods: {
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    open() {
      this.visible = true
    },
    handleCancel () {
      this.selectedRowKeys = []
      this.$refs.table2.refresh()
      this.visible = false
    },
    handleSubmit () {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warn('请选择一个矫正对象')
        return
      }
      sendCorrectionObjectInformation(this.selectedRowKeys).then((res) => {
        if (res.success) {
          this.$message.success(res.data)
        } else {
          this.$message.error(res.message)
        }
      })
      this.visible = false
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

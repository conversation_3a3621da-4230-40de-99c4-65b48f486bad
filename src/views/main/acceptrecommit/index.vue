<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="案件类型">
                <sh-select v-model="queryParam.ajlx" placeholder="请选择案件类型" dictType="ajlx"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="措施类型">
                <sh-select v-model="queryParam.cslx" placeholder="请选择措施类型" dictType="cslx"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.feedbackStatus" placeholder="请选择状态" @change="$forceUpdate()">
                  <a-select-option value="0">
                    待反馈
                  </a-select-option>
                  <a-select-option value="1">
                    已反馈
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="reset">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button
            type="primary"
            :disabled="!hasFeedback"
            icon="rollback"
            @click="$refs.feedbackForm.add(selectedRows[0], false)">反馈</a-button>
          <x-down ref="batchExport" @batchExport="batchExport" />
          <a-popconfirm
            v-if="hasPerm('sysPos:edit')"
            :disabled="!(selectedRowKeys.length === 1&&(selectedRows.length&&selectedRows[0].feedbackStatus === 0))"
            placement="topRight"
            title="确认删除？"
            @confirm="() => acceptRecommitDelete(selectedRows[0])">
            <a-button :disabled="!(selectedRowKeys.length === 1&&(selectedRows.length&&selectedRows[0].feedbackStatus === 0))" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
          <a-button v-if="hasPerm('acceptRecommit:send')" type="primary" @click="$refs.choosePsnAndSend.open()">手动发送列管对象</a-button>
          <a-button v-if="hasPerm('acceptRecommit:send')" type="primary" @click="$refs.psnSendLog.open()">已发送列管对象</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.open(record, false)">详情</a>
          <a-divider type="vertical" v-if="record.feedbackStatus" />
          <a v-if="record.feedbackStatus" @click="$refs.feedbackForm.add(record, true)">反馈详情</a>
          <a-divider v-if="record.feedbackStatus" type="vertical" />
          <a v-if="record.feedbackStatus" @click="$refs.linkFlow.view({ ...record},['XTBH44001','XTBH4005'])">消息链路</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
      <feedback-form ref="feedbackForm" @ok="handleOk" />
      <LinkFlow ref="linkFlow" />
      <choose-psn-and-send ref="choosePsnAndSend" />
      <psn-send-log ref="psnSendLog" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
import moment from 'moment'
import {
  acceptRecommitDelete,
  acceptRecommitExport,
  acceptRecommitPage
} from '@/api/modular/main/acceptrecommit/acceptRecommitManage'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage'
import feedbackForm from './FeedbackForm.vue'
import LinkFlow from '@/views/main/sendplacechange/components/LinkFlow.vue'
import ChoosePsnAndSend from '@/views/main/acceptrecommit/components/ChoosePsnAndSend'
import PsnSendLog from '@/views/main/acceptrecommit/components/PsnSendLog'
export default {
  components: {
    STable,
    XDown,
    editForm,
    feedbackForm,
    LinkFlow,
    ChoosePsnAndSend,
    PsnSendLog
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        feedbackStatus: ''
      },
      // 表头
      columns: [
        { ellipsis: true, title: '状态', align: 'center', dataIndex: 'feedbackStatus', customRender: val => val === 0 ? '待反馈' : '已反馈' },
        { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jzdwmc' },
        { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
        { ellipsis: true, title: '案件类型', align: 'center', dataIndex: 'ajlxmc' },
        { ellipsis: true, title: '办案单位名称', align: 'center', dataIndex: 'badwmc' },
        { ellipsis: true, title: '主办人', align: 'center', dataIndex: 'zbr' },
        { ellipsis: true, title: '送达时间', align: 'center', dataIndex: 'sdsj', customRender: val => val == null ? '' : moment(val).format('YYYY-MM-DD HH:mm') },
        { ellipsis: false, title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return acceptRecommitPage(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      hasFeedback: false,
      orgTree: [],
      ajlxDropDown: [],
      cslxDropDown: [],
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  watch: {
    $route(to, from) {
      // 对路由变化作出响应...
      this.queryParam.feedbackStatus = to.query.zt
      this.$refs.table.refresh()
    }
  },
  created() {
    if (this.$route.query.zt) {
      this.queryParam.feedbackStatus = this.$route.query.zt
    }
    this.getOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD HH:mm:ss')
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD HH:mm:ss')
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      delete obj.dates
      return obj
    },
    acceptRecommitDelete(record) {
      acceptRecommitDelete(record).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.$refs.table.refresh()
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /**
     * 批量导出
     */
    batchExport() {
      acceptRecommitExport().then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows

      if (this.selectedRows.length === 1) {
        if (this.selectedRows[0].feedbackStatus === 0) {
          // 待反馈
          this.hasFeedback = true
        }
      }
    },
    sysDictTypeDropDown() {
      this.ajlxDropDown = this.$options.filters['dictData']('ajlx')
      this.cslxDropDown = this.$options.filters['dictData']('cslx')
    },
    reset() {
      this.queryParam = {}
      this.$refs.table.refresh()
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

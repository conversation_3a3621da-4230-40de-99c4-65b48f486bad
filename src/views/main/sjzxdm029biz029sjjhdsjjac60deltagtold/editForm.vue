<template>
  <a-modal
    title="编辑离退休人员类别信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="基准待遇享受ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入基准待遇享受ID" v-decorator="['baz178', {rules: [{required: true, message: '请输入基准待遇享受ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社会保障号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社会保障号码" v-decorator="['aac002', {rules: [{required: true, message: '请输入社会保障号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['aac003', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="离退休类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入离退休类别" v-decorator="['aic161', {rules: [{required: true, message: '请输入离退休类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="业务产生时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择业务产生时间" v-decorator="['bizTime',{rules: [{ required: true, message: '请选择业务产生时间！' }]}]" @change="onChangebizTime"/>
        </a-form-item>
        <a-form-item
          label="数据同步时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据同步时间" v-decorator="['loadTime',{rules: [{ required: true, message: '请选择数据同步时间！' }]}]" @change="onChangeloadTime"/>
        </a-form-item>
        <a-form-item
          label="数据采集时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据采集时间" v-decorator="['cdcColTime',{rules: [{ required: true, message: '请选择数据采集时间！' }]}]" @change="onChangecdcColTime"/>
        </a-form-item>
        <a-form-item
          label="数据同步 DM 结果表时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据同步 DM 结果表时间" v-decorator="['cdcInsTime',{rules: [{ required: true, message: '请选择数据同步 DM 结果表时间！' }]}]" @change="onChangecdcInsTime"/>
        </a-form-item>
        <a-form-item
          label="险种指标"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入险种指标" v-decorator="['aae140', {rules: [{required: true, message: '请输入险种指标！'}]}]" />
        </a-form-item>
        <a-form-item
          label="离退休状态指标"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入离退休状态指标" v-decorator="['aac084', {rules: [{required: true, message: '请输入离退休状态指标！'}]}]" />
        </a-form-item>
        <a-form-item
          label="离退休日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入离退休日期" v-decorator="['aic162', {rules: [{required: true, message: '请输入离退休日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="待遇享受开始年月"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入待遇享受开始年月" v-decorator="['aic160', {rules: [{required: true, message: '请输入待遇享受开始年月！'}]}]" />
        </a-form-item>
        <a-form-item
          label="基础养老金终止发放年月"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入基础养老金终止发放年月" v-decorator="['aae042', {rules: [{required: true, message: '请输入基础养老金终止发放年月！'}]}]" />
        </a-form-item>
        <a-form-item
          label="待遇发放状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入待遇发放状态" v-decorator="['aae116', {rules: [{required: true, message: '请输入待遇发放状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否享受基础养老金"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否享受基础养老金" v-decorator="['bae116', {rules: [{required: true, message: '请输入是否享受基础养老金！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldEdit } from '@/api/modular/main/sjzxdm029biz029sjjhdsjjac60deltagtold/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        bizTimeDateString: '',
        loadTimeDateString: '',
        cdcColTimeDateString: '',
        cdcInsTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              baz178: record.baz178,
              aac002: record.aac002,
              aac003: record.aac003,
              aic161: record.aic161,
              aae140: record.aae140,
              aac084: record.aac084,
              aic162: record.aic162,
              aic160: record.aic160,
              aae042: record.aae042,
              aae116: record.aae116,
              bae116: record.bae116,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName
            }
          )
        }, 100)
        // 时间单独处理
        if (record.bizTime != null) {
            this.form.getFieldDecorator('bizTime', { initialValue: moment(record.bizTime, 'YYYY-MM-DD') })
        }
        this.bizTimeDateString = moment(record.bizTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.loadTime != null) {
            this.form.getFieldDecorator('loadTime', { initialValue: moment(record.loadTime, 'YYYY-MM-DD') })
        }
        this.loadTimeDateString = moment(record.loadTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.cdcColTime != null) {
            this.form.getFieldDecorator('cdcColTime', { initialValue: moment(record.cdcColTime, 'YYYY-MM-DD') })
        }
        this.cdcColTimeDateString = moment(record.cdcColTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.cdcInsTime != null) {
            this.form.getFieldDecorator('cdcInsTime', { initialValue: moment(record.cdcInsTime, 'YYYY-MM-DD') })
        }
        this.cdcInsTimeDateString = moment(record.cdcInsTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.bizTime = this.bizTimeDateString
            values.loadTime = this.loadTimeDateString
            values.cdcColTime = this.cdcColTimeDateString
            values.cdcInsTime = this.cdcInsTimeDateString
            sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangebizTime(date, dateString) {
        this.bizTimeDateString = dateString
      },
      onChangeloadTime(date, dateString) {
        this.loadTimeDateString = dateString
      },
      onChangecdcColTime(date, dateString) {
        this.cdcColTimeDateString = dateString
      },
      onChangecdcInsTime(date, dateString) {
        this.cdcInsTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

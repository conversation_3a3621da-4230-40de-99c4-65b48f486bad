<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.aac003" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="离退休状态">
                <a-select v-model="queryParam.aac084" allow-clear placeholder="请选择离退休状态">
                  <a-select-option value="0">
                    否
                  </a-select-option>
                  <a-select-option value="1">
                    是
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="离退休类别">
                <a-select v-model="queryParam.aic161" allow-clear placeholder="请选择离退休类别">
                  <a-select-option value="1">
                    离休
                  </a-select-option>
                  <a-select-option value="2">
                    正常退休
                  </a-select-option>
                  <a-select-option value="3">
                    退职
                  </a-select-option>
                  <a-select-option value="4">
                    因病退休（职）
                  </a-select-option>
                  <a-select-option value="5">
                    特殊工种退休
                  </a-select-option>
                  <a-select-option value="6">
                    原工伤退休
                  </a-select-option>
                  <a-select-option value="7">
                    政策性提前退休
                  </a-select-option>
                  <a-select-option value="8">
                    建国前老工人退休
                  </a-select-option>
                  <a-select-option value="9">
                    其他
                  </a-select-option>
                  <a-select-option value="21">
                    正常退休
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="ltxzt" slot-scope="text, record">
          <span v-if="record.aac084==='0'">否</span>
          <span v-if="record.aac084==='1'">是</span>
        </span>
        <span slot="dyffzt" slot-scope="text, record">
          <span v-if="record.aae116==='1'">正常发放</span>
          <span v-if="record.aae116==='2'">暂停发放</span>
          <span v-if="record.aae116==='3'">待遇终止</span>
          <span v-if="record.aae116==='4'">待审核</span>
        </span>
        <span slot="ltxlb" slot-scope="text, record">
          <span v-if="record.aic161==='1'">离休</span>
          <span v-if="record.aic161==='2'">正常退休</span>
          <span v-if="record.aic161==='3'">退职</span>
          <span v-if="record.aic161==='4'">因病退休（职）</span>
          <span v-if="record.aic161==='5'">特殊工种退休</span>
          <span v-if="record.aic161==='6'">原工伤退休</span>
          <span v-if="record.aic161==='7'">政策性提前退休</span>
          <span v-if="record.aic161==='8'">建国前老工人退休</span>
          <span v-if="record.aic161==='9'">其他</span>
          <span v-if="record.aic161==='21'">正常退休</span>
        </span>
        <span slot="xzzb" slot-scope="text, record">
          <span v-if="record.aae140==='110'">城镇企业职工基本养老保险</span>
          <span v-if="record.aae140==='120'">机关事业单位养老保险</span>
          <span v-if="record.aae140==='170'">城乡居民基本养老保险</span>
          <span v-if="record.aae140==='180'">职业年金</span>
          <span v-if="record.aae140==='210'">失业保险</span>
          <span v-if="record.aae140==='410'">工伤保险</span>
          <span v-if="record.aae140==='420'">职业伤害保障</span>
          <span v-if="record.aae140==='430'">补充工伤保险</span>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldPage,
  sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldDelete
} from '@/api/modular/main/sjzxdm029biz029sjjhdsjjac60deltagtold/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'aac003'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        {
          ellipsis: true,
          title: '社会保障号码',
          align: 'center',
          dataIndex: 'aac002'
        },
        {
          ellipsis: true,
          title: '离退休类别',
          align: 'center',
          dataIndex: 'aic161',
          scopedSlots: { customRender: 'ltxlb' }
        },
        {
          ellipsis: true,
          title: '离退休状态',
          align: 'center',
          dataIndex: 'aac084',
          scopedSlots: { customRender: 'ltxzt' }
        },
        {
          ellipsis: true,
          title: '离退休日期',
          align: 'center',
          dataIndex: 'aic162'
        },
        {
          ellipsis: true,
          title: '待遇享受开始年月',
          align: 'center',
          dataIndex: 'aic160'
        },
        {
          ellipsis: true,
          title: '待遇发放状态',
          align: 'center',
          dataIndex: 'aae116',
          scopedSlots: { customRender: 'dyffzt' }

        },
        {
          ellipsis: true,
          title: '是否享受基础养老金',
          align: 'center',
          dataIndex: 'bae116'
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const queryParambizTime = this.queryParam.bizTimeDate
      if (queryParambizTime != null) {
        this.queryParam.bizTime = moment(queryParambizTime).format('YYYY-MM-DD')
        if (queryParambizTime.length < 1) {
          delete this.queryParam.bizTime
        }
      }
      const queryParamloadTime = this.queryParam.loadTimeDate
      if (queryParamloadTime != null) {
        this.queryParam.loadTime = moment(queryParamloadTime).format('YYYY-MM-DD')
        if (queryParamloadTime.length < 1) {
          delete this.queryParam.loadTime
        }
      }
      const queryParamcdcColTime = this.queryParam.cdcColTimeDate
      if (queryParamcdcColTime != null) {
        this.queryParam.cdcColTime = moment(queryParamcdcColTime).format('YYYY-MM-DD')
        if (queryParamcdcColTime.length < 1) {
          delete this.queryParam.cdcColTime
        }
      }
      const queryParamcdcInsTime = this.queryParam.cdcInsTimeDate
      if (queryParamcdcInsTime != null) {
        this.queryParam.cdcInsTime = moment(queryParamcdcInsTime).format('YYYY-MM-DD')
        if (queryParamcdcInsTime.length < 1) {
          delete this.queryParam.cdcInsTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldDelete(record) {
      sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onChangebizTime(date, dateString) {
      this.bizTimeDateString = dateString
    },
    onChangeloadTime(date, dateString) {
      this.loadTimeDateString = dateString
    },
    onChangecdcColTime(date, dateString) {
      this.cdcColTimeDateString = dateString
    },
    onChangecdcInsTime(date, dateString) {
      this.cdcInsTimeDateString = dateString
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

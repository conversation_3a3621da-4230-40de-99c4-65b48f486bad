<template>
  <a-modal
    title="编辑矫正对象社会保险参保信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['baz159']" /></a-form-item>
        <a-form-item
          label="个人编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人编号" v-decorator="['aac001', {rules: [{required: true, message: '请输入个人编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社会保障号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社会保障号码" v-decorator="['aac002', {rules: [{required: true, message: '请输入社会保障号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['aac003', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类型" v-decorator="['aac058', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['aac147', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位编号" v-decorator="['aab001', {rules: [{required: true, message: '请输入单位编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="统一社会信用代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入统一社会信用代码" v-decorator="['bab010', {rules: [{required: true, message: '请输入统一社会信用代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位名称" v-decorator="['aab004', {rules: [{required: true, message: '请输入单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="险种类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入险种类型" v-decorator="['aae140', {rules: [{required: true, message: '请输入险种类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员参保状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员参保状态" v-decorator="['aac008', {rules: [{required: true, message: '请输入人员参保状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人缴费状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人缴费状态" v-decorator="['aac031', {rules: [{required: true, message: '请输入个人缴费状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开始日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开始日期" v-decorator="['aae030', {rules: [{required: true, message: '请输入开始日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="终止日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入终止日期" v-decorator="['aae031', {rules: [{required: true, message: '请输入终止日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="行政区划代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行政区划代码" v-decorator="['aab301', {rules: [{required: true, message: '请输入行政区划代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="业务产生时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择业务产生时间" v-decorator="['bizTime',{rules: [{ required: true, message: '请选择业务产生时间！' }]}]" @change="onChangebizTime"/>
        </a-form-item>
        <a-form-item
          label="数据同步时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据同步时间" v-decorator="['loadTime',{rules: [{ required: true, message: '请选择数据同步时间！' }]}]" @change="onChangeloadTime"/>
        </a-form-item>
        <a-form-item
          label="数据采集时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据采集时间" v-decorator="['cdcColTime',{rules: [{ required: true, message: '请选择数据采集时间！' }]}]" @change="onChangecdcColTime"/>
        </a-form-item>
        <a-form-item
          label="数据同步 DM 结果表时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据同步 DM 结果表时间" v-decorator="['cdcInsTime',{rules: [{ required: true, message: '请选择数据同步 DM 结果表时间！' }]}]" @change="onChangecdcInsTime"/>
        </a-form-item>
        <a-form-item
          label="有效标识"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效标识" v-decorator="['aae100', {rules: [{required: true, message: '请输入有效标识！'}]}]" />
        </a-form-item>
        <a-form-item
          label="同步时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入同步时间" v-decorator="['dt', {rules: [{required: true, message: '请输入同步时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldEdit } from '@/api/modular/main/sjzxdm029biz029sjjhdsjjac02deltagtold/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        bizTimeDateString: '',
        loadTimeDateString: '',
        cdcColTimeDateString: '',
        cdcInsTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              baz159: record.baz159,
              aac001: record.aac001,
              aac002: record.aac002,
              aac003: record.aac003,
              aac058: record.aac058,
              aac147: record.aac147,
              aab001: record.aab001,
              bab010: record.bab010,
              aab004: record.aab004,
              aae140: record.aae140,
              aac008: record.aac008,
              aac031: record.aac031,
              aae030: record.aae030,
              aae031: record.aae031,
              aab301: record.aab301,
              aae100: record.aae100,
              dt: record.dt,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName
            }
          )
        }, 100)
        // 时间单独处理
        if (record.bizTime != null) {
            this.form.getFieldDecorator('bizTime', { initialValue: moment(record.bizTime, 'YYYY-MM-DD') })
        }
        this.bizTimeDateString = moment(record.bizTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.loadTime != null) {
            this.form.getFieldDecorator('loadTime', { initialValue: moment(record.loadTime, 'YYYY-MM-DD') })
        }
        this.loadTimeDateString = moment(record.loadTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.cdcColTime != null) {
            this.form.getFieldDecorator('cdcColTime', { initialValue: moment(record.cdcColTime, 'YYYY-MM-DD') })
        }
        this.cdcColTimeDateString = moment(record.cdcColTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.cdcInsTime != null) {
            this.form.getFieldDecorator('cdcInsTime', { initialValue: moment(record.cdcInsTime, 'YYYY-MM-DD') })
        }
        this.cdcInsTimeDateString = moment(record.cdcInsTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.bizTime = this.bizTimeDateString
            values.loadTime = this.loadTimeDateString
            values.cdcColTime = this.cdcColTimeDateString
            values.cdcInsTime = this.cdcInsTimeDateString
            sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangebizTime(date, dateString) {
        this.bizTimeDateString = dateString
      },
      onChangeloadTime(date, dateString) {
        this.loadTimeDateString = dateString
      },
      onChangecdcColTime(date, dateString) {
        this.cdcColTimeDateString = dateString
      },
      onChangecdcInsTime(date, dateString) {
        this.cdcInsTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

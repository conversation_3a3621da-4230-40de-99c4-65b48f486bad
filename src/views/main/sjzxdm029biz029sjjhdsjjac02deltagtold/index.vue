<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.aac003" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="社会保障号">
                <a-input v-model="queryParam.aac002" allow-clear placeholder="请输入社会保障号码"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="险种类型">
                <a-select v-model="queryParam.aae140" allow-clear placeholder="请选择险种类型">
                  <a-select-option value="110">
                    城镇企业职工基本养老保险
                  </a-select-option>
                  <a-select-option value="120">
                    机关事业单位养老保险
                  </a-select-option>
                  <a-select-option value="170">
                    城乡居民基本养老保险
                  </a-select-option>
                  <a-select-option value="180">
                    职业年金
                  </a-select-option>
                  <a-select-option value="210">
                    失业保险
                  </a-select-option>
                  <a-select-option value="410">
                    工伤保险
                  </a-select-option>
                  <a-select-option value="420">
                    职业伤害保障
                  </a-select-option>
                  <a-select-option value="430">
                    补充工伤保险
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.baz159"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="zjlx" slot-scope="text, record">
          <span v-if="record.aac058==='01'">中华人民共和国居民身份证</span>
          <span v-if="record.aac058==='02'">中国人民解放军军官证</span>
          <span v-if="record.aac058==='03'">中国人民武装警察警官证</span>
          <span v-if="record.aac058==='04'">香港特区护照/港澳居民来往内地通行证</span>
          <span v-if="record.aac058==='05'">澳门特区护照/港澳居民来往内地通行证</span>
          <span v-if="record.aac058==='06'">台湾居民来往大陆通行证</span>
          <span v-if="record.aac058==='07'">外国人永久居留证</span>
          <span v-if="record.aac058==='08'">外国人护照</span>
        </span>
        <span slot="xzlx" slot-scope="text, record">
          <span v-if="record.aae140==='110'">城镇企业职工基本养老保险</span>
          <span v-if="record.aae140==='120'">机关事业单位养老保险</span>
          <span v-if="record.aae140==='170'">城乡居民基本养老保险</span>
          <span v-if="record.aae140==='180'">职业年金</span>
          <span v-if="record.aae140==='210'">失业保险</span>
          <span v-if="record.aae140==='410'">工伤保险</span>
          <span v-if="record.aae140==='420'">职业伤害保障</span>
          <span v-if="record.aae140==='430'">补充工伤保险</span>
        </span>
        <span slot="cbzt" slot-scope="text, record">
          <span v-if="record.aac008==='1'">正常参保</span>
          <span v-if="record.aac008==='2'">终止参保</span>
          <span v-if="record.aac008==='3'"></span>
          <span v-if="record.aac008==='4'">终止参保</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldPage,
  sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldDelete
} from '@/api/modular/main/sjzxdm029biz029sjjhdsjjac02deltagtold/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'aac003'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        {
          ellipsis: true,
          title: '社会保障号码',
          align: 'center',
          dataIndex: 'aac002'
        },
        {
          ellipsis: true,
          title: '单位名称',
          align: 'center',
          dataIndex: 'aab004'
        },
        {
          ellipsis: true,
          title: '险种类型',
          align: 'center',
          dataIndex: 'aae140',
          scopedSlots: { customRender: 'xzlx' }
        },
        {
          ellipsis: true,
          title: '参保状态',
          align: 'center',
          dataIndex: 'aac008',
          scopedSlots: { customRender: 'cbzt' }
        },
        {
          ellipsis: true,
          title: '开始日期',
          align: 'center',
          dataIndex: 'aae030'
        },
        {
          ellipsis: true,
          title: '结束日期',
          align: 'center',
          dataIndex: 'aae031'
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const queryParambizTime = this.queryParam.bizTimeDate
      if (queryParambizTime != null) {
        this.queryParam.bizTime = moment(queryParambizTime).format('YYYY-MM-DD')
        if (queryParambizTime.length < 1) {
          delete this.queryParam.bizTime
        }
      }
      const queryParamloadTime = this.queryParam.loadTimeDate
      if (queryParamloadTime != null) {
        this.queryParam.loadTime = moment(queryParamloadTime).format('YYYY-MM-DD')
        if (queryParamloadTime.length < 1) {
          delete this.queryParam.loadTime
        }
      }
      const queryParamcdcColTime = this.queryParam.cdcColTimeDate
      if (queryParamcdcColTime != null) {
        this.queryParam.cdcColTime = moment(queryParamcdcColTime).format('YYYY-MM-DD')
        if (queryParamcdcColTime.length < 1) {
          delete this.queryParam.cdcColTime
        }
      }
      const queryParamcdcInsTime = this.queryParam.cdcInsTimeDate
      if (queryParamcdcInsTime != null) {
        this.queryParam.cdcInsTime = moment(queryParamcdcInsTime).format('YYYY-MM-DD')
        if (queryParamcdcInsTime.length < 1) {
          delete this.queryParam.cdcInsTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldDelete(record) {
      sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onChangebizTime(date, dateString) {
      this.bizTimeDateString = dateString
    },
    onChangeloadTime(date, dateString) {
      this.loadTimeDateString = dateString
    },
    onChangecdcColTime(date, dateString) {
      this.cdcColTimeDateString = dateString
    },
    onChangecdcInsTime(date, dateString) {
      this.cdcInsTimeDateString = dateString
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

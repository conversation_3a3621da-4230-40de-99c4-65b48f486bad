<template>
  <a-modal
    title="罪名选择"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="罪名代码">
                <a-input v-model="queryParam.chargeCode" allow-clear placeholder="请输入罪名代码"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="罪名">
                <a-input v-model="queryParam.charge" allow-clear placeholder="请输入罪名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="getList" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-table ref="table" :columns="columns" :data-source="data" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
    </a-table>
  </a-modal>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { chargeInfoList } from '@/api/modular/main/chargeInfoManage'
  export default {
    components: { STable },
    data () {
      return {
        queryParam: {},
        visible: false,
        confirmLoading: false,
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        selectedRowKeys: [],
        selectedRows: [],
        columns: [
          {
            ellipsis: true,
            title: '罪名代码',
            align: 'center',
            dataIndex: 'chargeCode'
          },
          {
            ellipsis: true,
            title: '罪名',
            align: 'center',
            dataIndex: 'charge'
          }
        ],
        data: []
      }
    },
    methods: {
      moment,
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      getList () {
        chargeInfoList(this.queryParam).then(res => {
          this.data = res.data
        })
      },
      // 初始化方法
      open () {
        this.visible = true
        this.getList()
      },
      handleSubmit () {
       this.$emit('ok', this.selectedRows)
       this.handleCancel()
      },
      handleCancel () {
        this.selectedRows = []
        this.selectedRowKeys = []
        this.visible = false
      }
    }
  }
</script>

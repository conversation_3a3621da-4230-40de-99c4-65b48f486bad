<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzdwId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select
                  v-show="current === 0"
                  style="width: 100%"
                  v-model="queryParam.dczt"
                  placeholder="请选择调查状态"
                >
                  <a-select-option v-for="(item, index) in dcpgztData" :key="index" :value="item.code">{{
                    item.name
                  }}</a-select-option>
                </a-select>
                <a-select
                  v-show="current === 1"
                  style="width: 100%"
                  v-model="queryParam.bdzt"
                  placeholder="请选择报到状态"
                >
                  <a-select-option v-for="(item, index) in bdztData" :key="index" :value="item.code">{{
                    item.name
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="handleSearch">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <div style="margin:10px">
      <a-button v-if="current" @click="current = 0">外省迁入</a-button>
      <a-button type="primary" v-if="!current">外省迁入</a-button>
      <a-button type="primary" v-if="current">跨省迁出</a-button>
      <a-button v-if="!current" @click="current = 1">跨省迁出</a-button>
    </div>
    <a-card :bordered="false" v-show="current === 0">
      <!--外省迁入-->
      <s-table
        ref="tableImmigration"
        :columns="columnsImmigration"
        :data="loadImmigrationData"
        :alert="false"
        :rowKey="record => record.id"
        :rowSelection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" :disabled="disabledAccept" icon="plus" @click="$refs.acceptForm.add(selectedRows[0])"
            >受理</a-button
          >
          <a-button
            :disabled="disabledInvestigate"
            type="primary"
            icon="plus"
            @click="$refs.addForm.add(selectedRows[0])"
            >迁入调查评估</a-button
          >
          <a-button
            :disabled="disabledFeedBack"
            type="primary"
            icon="plus"
            @click="$refs.fkxcResult.edit(selectedRows[0])"
            >调查评估意见反馈</a-button
          >
          <a-button
            :disabled="disabledReport"
            type="primary"
            icon="plus"
            @click="$refs.Firstreport.edit(selectedRows[0])"
            >首次报到登记/反馈</a-button
          >
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.detailForm.add(record)">调查评估详情</a>
          <a-divider type="vertical" />
          <a @click="$refs.fkxcResult.edit(record, true)">评估意见详情</a>
          <a-divider type="vertical" />
          <a @click="$refs.Firstreport.edit(record, true)">报道登记详情</a>
        </span>
        <span slot="jszt" slot-scope="text">
          {{ 'jszt' | dictType(text) }}
        </span>
        <span slot="dczt" slot-scope="text">
          {{ 'dcpgzt' | dictType(text) }}
        </span>
      </s-table>
      <detailForm ref="detailForm" @ok="handleOk" />
      <add-form ref="addForm" @ok="handleOk" />
      <fkxcResult ref="fkxcResult" @ok="handleOk" />
      <Immigration ref="Immigration" @ok="handleOk" />
      <Firstreport ref="Firstreport" @ok="handleOk" />
      <accept-form ref="acceptForm" @ok="handleOk" />
    </a-card>
    <a-card :bordered="false" v-show="current === 1">
      <!--跨省迁出-->
      <s-table
        ref="tableEmigration"
        :columns="columnsEmigration"
        :data="loadEmigrationData"
        :alert="false"
        :rowKey="record => record.id"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.transprovincialout.edit()">跨省迁出</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.detailsFormRef.edit(record, true)">详情</a>
        </span>
        <span slot="bdzt" slot-scope="text">
          {{ 'bdzt' | dictType(text) }}
        </span>
      </s-table>
      <transprovincialout ref="transprovincialout" @ok="handleOk" />
      <detailsForm ref="detailsFormRef" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import addForm from './addForm.vue'
import detailForm from './detailForm.vue'
import { STable } from '@/components'
import moment from 'moment'
import { correctionPlacechangeImmigrationPage } from '@/api/modular/main/correctionplacechangeimmigration/correctionPlacechangeImmigrationManage'
import { correctionPlacechangeEmigrationPage } from '@/api/modular/main/correctionplacechangeemigration/correctionPlacechangeEmigrationManage'
import fkxcResult from './fkxcResult.vue'
import Immigration from './Immigration.vue'
import transprovincialout from './transprovincialout.vue'
import detailsForm from './detailsForm.vue'
import Firstreport from './Firstreport.vue'
import acceptForm from './acceptForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage'

export default {
  components: {
    detailForm,
    addForm,
    detailsForm,
    Firstreport,
    Immigration,
    fkxcResult,
    STable,
    transprovincialout,
    acceptForm
  },
  data() {
    return {
      current: 0,
      // 查询参数
      queryParam: {},
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 表头 外省迁入
      columnsImmigration: [
        {
          ellipsis: true,
          title: '调查状态',
          align: 'center',
          dataIndex: 'dczt',
          scopedSlots: { customRender: 'dczt' }
        },
        {
          ellipsis: true,
          title: '接收状态',
          align: 'center',
          dataIndex: 'jszt',
          scopedSlots: { customRender: 'jszt' }
        },
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'xm'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'zpdcdwmc'
        },
        {
          ellipsis: true,
          title: '迁入原因',
          align: 'center',
          dataIndex: 'qryy'
        },
        {
          ellipsis: true,
          title: '申请时间',
          align: 'center',
          dataIndex: 'sqsj',
          customRender: (val) => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          ellipsis: true,
          title: '迁出省(市)',
          align: 'center',
          dataIndex: 'qcss'
        },
        {
          ellipsis: true,
          title: '操作',
          align: 'center',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 外省迁入 加载数据方法 必须为 Promise 对象
      loadImmigrationData: parameter => {
        return correctionPlacechangeImmigrationPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 表头 跨省迁出
      columnsEmigration: [
        {
          ellipsis: true,
          title: '报到状态',
          align: 'center',
          dataIndex: 'bdzt',
          scopedSlots: { customRender: 'bdzt' }
        },
        {
          ellipsis: true,
          title: '调查评估意见',
          align: 'center',
          dataIndex: 'dcpgyj'
        },
        {
          title: '姓名',
          ellipsis: true,
          align: 'center',
          dataIndex: 'xm'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzdwmc'
        },
        {
          ellipsis: true,
          title: '迁出原因',
          align: 'center',
          dataIndex: 'bgly'
        },
        {
          ellipsis: true,
          title: '迁入省(市)',
          align: 'center',
          dataIndex: 'qrd'
        },
        {
          ellipsis: true,
          title: '迁入单位',
          align: 'center',
          dataIndex: 'qrjzdw'
        },
        {
          ellipsis: true,
          title: '报道时间',
          align: 'center',
          dataIndex: 'bdsj',
          customRender: (val) => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          ellipsis: true,
          title: '操作',
          // width: '150px',
          align: 'center',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 跨省迁出 加载数据方法 必须为 Promise 对象
      loadEmigrationData: parameter => {
        return correctionPlacechangeEmigrationPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedEmigrationRowKeys: [],
      selectedEmigrationRows: [],
      orgTree: [],
      dcpgztData: [],
      bdztData: []
    }
  },
  watch: {
    current: {
      handler() {
        this.handleOk()
      }
    }
  },
  computed: {
    disabledAccept() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].dczt !== '0'
    },
    disabledInvestigate() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].dczt === '0'
    },
    disabledFeedBack() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].dczt !== '7'
    },
    disabledReport() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].sfasbd
    }
  },
  created() {
    this.getOrgTree()
    this.dataTypeItem()
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD ') + '00:00:00'
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD ') + '23:59:59'
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      delete obj.dates
      return obj
    },
    handleSearch() {
      if (this.current === 0) {
        this.$refs.tableImmigration.refresh(true)
      }
      if (this.current === 1) {
        this.$refs.tableEmigration.refresh(true)
      }
    },
    handleOk() {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.selectedEmigrationRowKeys = []
      this.selectedEmigrationRows = []
      this.$refs.tableImmigration.clearSelected()
      this.$refs.tableEmigration.clearSelected()
      this.$refs.tableImmigration.refresh()
      this.$refs.tableEmigration.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /**
     * 获取字典数据
     */
    dataTypeItem() {
      this.dcpgztData = this.$options.filters['dictData']('dcpgzt')
      this.bdztData = this.$options.filters['dictData']('bdzt')
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

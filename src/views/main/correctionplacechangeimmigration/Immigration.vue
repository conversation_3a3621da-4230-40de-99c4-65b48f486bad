<template>
  <a-modal
    title="调查评估"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    footer=""
  >
    <!-- 步骤条 -->
    <div style="margin-bottom:20px">
      <a-steps v-model:current="current" type="navigation" :style="stepStyle" >
        <!-- <a-step status="finish" title="Step 1" />
      <a-step status="process" title="Step 2" />
      <a-step status="wait" title="Step 3" />
      <a-step status="wait" title="Step 4" /> -->
        <a-step title="指派" />
        <a-step title="接收" />
        <a-step title="调查" />
        <a-step title="初审/小组意见" />
        <a-step title="初审/集体评议" />
        <a-step title="审批" />
        <!-- <a-step title="文书打印" /> -->
      </a-steps>
    </div>
    <!-- 指派页面 -->
    <div v-show="current===0">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <div class="border">
            <div
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
                /* margin-bottom: 20px; */
              "

            >委托信息</div>
          </div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width: 60%" placeholder="请输入委托单位" v-decorator="['wtdwmc']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="收到委托时间"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width: 55%" placeholder="请选择收到委托时间" v-decorator="['sdwtsj']" @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width: 60%" placeholder="请输入委托编号" v-decorator="['wtbh']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托调查书"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary">点击上传</a-button>
                  <!-- <a-buton type="primary" @click="handleSubmit">点击上传</a-buton> -->
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托文书附件"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary">点击上传</a-button>
                  <!-- <a-buton type="primary" @click="handleSubmit">点击上传</a-buton> -->
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div
            style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          >
            被调查对象基本信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width: 60%" placeholder="请输入姓名" v-decorator="['xm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="拟适用社区矫正人员类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['nsysqjzrylx', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="拟适用社区矫正人员类型"
                    style="width: 200px"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['xb', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择性别"
                    style="width: 200px"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width: 55%" placeholder="请选择出生日期" v-decorator="['csrq']" @change="onChangezpsj"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="身份证号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width: 60%" placeholder="请输入身份证号" v-decorator="['sfzh']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" >
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <VDistpicker
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-radio-group name="radioGroup" v-model:value="sfjzdmx">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="sfjzdmx==='1'">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:400px" placeholder="请输入居住地明细地址" v-decorator="['jzddzmx', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 户籍是否与居住地相同 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-radio-group name="radioGroup" v-model:value="hjjzdsfxt">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="hjjzdsfxt==='2'">
              <a-col :md="12" :sm="24" :pull="0" >
                <a-form-item
                  label=" 户籍地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >

                  <VDistpicker
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有户籍地址地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-radio-group name="radioGroup" v-model:value="sfhjdzmx">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="hjjzdsfxt==='2'&&sfhjdzmx==='1'" >
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 户籍地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:400px" placeholder="请输入户籍明细地址" v-decorator="['hjdzmx', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 工作单位 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:400px" placeholder="如无工作单位,填写'不详'" v-decorator="['hzdw', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 具体罪名 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary"> 选择</a-button>
                  <a-button type="primary" style="margin-left:10px"> 删除</a-button>
                  <!-- <a-input style="width:400px" placeholder="如无工作单位,填写'不详'" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">

              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有原判刑期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-radio-group name="radioGroup" v-model:value="sfyypxxq">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="sfyypxxq==='1'">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 原判刑刑期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:400px" placeholder="用中文简体填写（例：两年六个月）" v-decorator="['ypxq', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原判刑期开始日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width: 55%" placeholder="请选择原判刑期开始日期" v-decorator="['ypxqksrq']" @change="onChangezpsj"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原判刑期结束日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width: 55%" placeholder="请选择原判刑期结束日期" v-decorator="['ypxqjsrq']" @change="onChangezpsj"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 判决机关 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:200px" placeholder="请输入判决机关" v-decorator="['pjjg', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="原判刑罚"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['ypxf', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择原判刑罚"
                    style="width: 200px"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="附加刑"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['fjx', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择附加刑"
                    style="width: 200px"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="判决日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width: 55%" placeholder="请选择判决日期" v-decorator="['pjrq']" @change="onChangezpsj"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div
            style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          >
            指派信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="指派单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zpwdmc', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择指派单位"
                    style="width: 200px"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 指派人 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['zpr', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 指派时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input disabled style="width:200px" placeholder="默认当前时间" v-decorator="['zpsj', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="备注"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    v-decorator="['zpbz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <!-- <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 原判刑刑期 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:400px" placeholder="用中文简体填写（例：两年六个月）" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col> -->
          </div>
        </a-form>
      </a-spin>
    </div>
    <!-- 接收页面 -->
    <div v-show="current===1">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
        >接收信息</div>
      </div>
      <div class="border" style="height:">

        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="接收状态"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-select
                v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                show-search
                placeholder="请选择接收状态"
                style="width: 200px"
                :options="options4"
                @focus="handleFocus"
                @blur="handleBlur"
                @change="handleChange"
              >
              </a-select>
              <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 接收人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 接收时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input disabled style="width:200px" placeholder="默认当前时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="备注"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 原判刑刑期 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:400px" placeholder="用中文简体填写（例：两年六个月）" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col> -->

      </div>
    </div>
    <!-- 调查页面 -->
    <div v-show="current===2">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">
          <span v-if="dcgc">调查过程</span>
          <span v-if="dcpgb">文书制作--浙江省社区矫正调查评估表</span>
          <span v-if="dcpgyjs">文书制作--调查评估意见书</span>
          <span v-else>调查信息</span>
        </div>
      </div>
      <div class="border" style="">
        <div class="dc" v-show="!qrdc&&!dcgc&&!dcpgb&&!dcpgyjs">
          <!-- 调查外部内容页 -->
          <div class="box" style="" @click="qrdc=!qrdc">
            <div class="title">
              确认调查</div>
            <div class="tj">
              <!-- <div v-if="1">已提交</div>
              <div v-else>未提交</div> -->
            </div>
          </div>
          <div class="box" style="" @click="dcgc=!dcgc">
            <div class="title">
              调查过程</div>
            <div class="tj">
              <!-- <div v-if="1">已提交</div>
              <div v-else>未提交</div> -->
            </div>
          </div>
          <div class="box" style="" @click="dcpgb=!dcpgb">
            <div class="title">
              浙江省矫正调查评估表</div>
            <div class="tj">
              <!-- <div v-if="1">已提交</div>
              <div v-else>未提交</div> -->
            </div>
          </div>
          <div class="box" style="" @click="dcpgyjs=!dcpgyjs">
            <div class="title">
              调查评估意见书</div>
            <div class="tj">
              <!-- <div v-if="1">已提交</div>
              <div v-else>未提交</div> -->
            </div>
          </div>
        </div>
        <div v-if="qrdc">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="是否开展调查"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-select
                  v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                  show-search
                  placeholder="请选择初审结果"
                  style="width: 200px"
                  :options="options4"
                  @focus="handleFocus"
                  @blur="handleBlur"
                  @change="handleChange"
                >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width: 200px" placeholder="默认当前时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="备注"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-textarea
                  v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div v-if="dcgc">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查单位（县区局）"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-select
                  v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                  show-search
                  placeholder="请选择初审结果"
                  disabled
                  style="width: 750px"
                  :options="options4"
                  @focus="handleFocus"
                  @blur="handleBlur"
                  @change="handleChange"
                >
                </a-select>
              <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查单位（司法所）"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-select
                  v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                  show-search
                  placeholder="请选择初审结果"
                  disabled
                  style="width: 750px"
                  :options="options4"
                  @focus="handleFocus"
                  @blur="handleBlur"
                  @change="handleChange"
                >
                </a-select>
              <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
              </a-form-item>
            </a-col>
          </a-row>
          <div class="wsinfo" style="width:100px">年 月 日调查</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="被调查人姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:100%" placeholder="直接获取，暂为空，审批通过之后自动显示" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="与被告人（罪犯）关系"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="YDS,只能选择直系亲属YDS01下子节点,且为打开状态"
                    style="width: 100%"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 调查时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width:200px" placeholder="直接获取“收到委托时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="调查地点"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:750px" placeholder="直接获取，可修改" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="调查事项"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="调查人"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary" >请选择</a-button>
                  <a-button type="primary" >删除</a-button>
                  <a-button type="primary" >新增</a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="上传笔录"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary">点击上传</a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="制作笔录"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-button type="primary">制作笔录</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="委托调查材料"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-button type="primary">点击上传</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div v-if="dcpgb">
          <div class="wsinfo" style="width:70px">调查对象</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="民族"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择民族"
                    style="width: 80%"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="别名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:80%" placeholder="请输入别名" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="曾用名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:80%" placeholder="请输入曾用名" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="籍贯"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:80%" placeholder="请输入籍贯" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="家庭住址"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:750px" placeholder="请输入家庭住址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="联系电话"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:250px" placeholder="请输入联系电话" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="职业"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择民族"
                    style="width: 80%"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

          </div>
          <div class="wsinfo" style="width:110px">家庭和社会关系</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="有无家庭成员"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-radio-group name="radioGroup" v-model:value="value">
                    <a-radio value="1">有</a-radio>
                    <a-radio value="2">无</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="社会交往情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="主要社会关系"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="未成年对象的其他情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:70px">个性特点</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="生理状况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="心理特征"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="性格类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    show-search
                    placeholder="请选择民族"
                    style="width: 80%"
                    :options="options4"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="爱好特长"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:70px">一贯表现</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="工作（学习）表现"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="遵纪守法情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="有无不良嗜好、行为恶习"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:140px">犯罪情况和悔罪表现</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="犯罪原因"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="主观恶性"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="是否有犯罪前科"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="认罪悔罪态度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:70px">社会反响</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="被害人或其亲属态度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="社会公众态度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:70px">监管条件</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="家庭成员态度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="经济生活状况和环境"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="基层组织意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="工作单位、就读学校和村（社区）"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />  <a-button type="primary" @click="handleSubmit">点击上传</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="wsinfo" style="width:70px">审批意见</div>
          <div class="border" style="width:90%;margin:5% 5% 0 5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="辖区公安派出所意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />  <a-button type="primary" @click="handleSubmit">点击上传</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="浙江省社区矫正调查评估表"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-button type="primary" @click="handleSubmit">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div v-if="dcpgyjs">
          <div class="wsinfo">文书信息</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="字"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:750px" disabled placeholder="直接获取，暂为空，审批通过之后自动显示" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:750px" disabled placeholder="直接获取“委托单位" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 调查开始时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width:200px" placeholder="直接获取“收到委托时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 调查结束时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker style="width:200px" placeholder="直接获取“调查结束时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:200px" disabled placeholder="直接获取，可修改" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="有关情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    placeholder="直接获取，可修改"
                    v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="评估意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:750px" disabled placeholder="暂不显示，不可操作，区县司法局局长填写" v-decorator="['hjdxz', {rules: [{required: false, message: '暂不显示，不可操作，区县司法局局长填写 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查评估意见书"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-button type="primary">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    <!-- 初审小组意见 -->
    <div v-show="current===3">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
        >司法所负责人初审信息</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="初审结果"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-select
                v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                show-search
                placeholder="请选择初审结果"
                style="width: 200px"
                :options="options4"
                @focus="handleFocus"
                @blur="handleBlur"
                @change="handleChange"
              >
              </a-select>
              <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 初审人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 初审时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 200px" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
              <!-- <a-input style="width:200px" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="初审意见"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
      >
        司法所调查评估小组意见信息</div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 合议事项 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 主持人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 合议地点 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width:200px" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 合议人员 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:800px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 记录人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="合议情况"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="合议意见"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 负责人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="备注"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估小组意见表："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
        文书制作--调查评估意见书</div>
      <div class="border" style="">
        <div class="wsinfo">文书信息</div>
        <div class="border" style="width:90%;margin:5%;padding:20px">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="字"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取，暂为空，审批通过之后自动显示" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="委托单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取“委托单位" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查开始时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“收到委托时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查结束时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“调查结束时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="姓名"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:200px" disabled placeholder="直接获取，可修改" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="有关情况"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-textarea
                  placeholder="直接获取，可修改"
                  v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="评估意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="暂不显示，不可操作，区县司法局局长填写" v-decorator="['hjdxz', {rules: [{required: false, message: '暂不显示，不可操作，区县司法局局长填写 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估意见书"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
    <!-- 集体评议 -->
    <div v-show="current===4">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
        >区县司法局初审信息</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="初审结果"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-select
                v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                show-search
                placeholder="请选择初审结果"
                style="width: 200px"
                :options="options4"
                @focus="handleFocus"
                @blur="handleBlur"
                @change="handleChange"
              >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 初审人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 初审时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 200px" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="初审意见"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
        区县司法局评议审核信息</div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 评议审核事项 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 主持人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 评议审核地点 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width:200px" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 评议审核人员 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:800px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 记录人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 负责人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="评议审核情况"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="评议审核意见"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="备注"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估小组意见表："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
        文书制作--调查评估意见书</div>
      <div class="border" >
        <div class="wsinfo">文书信息</div>
        <div class="border" style="width:90%;margin:5%;padding:20px">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="字"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取，暂为空，审批通过之后自动显示" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="委托单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取“委托单位" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查开始时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“收到委托时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查结束时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“调查结束时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="姓名"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:200px" disabled placeholder="直接获取，可修改" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="有关情况"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-textarea
                  placeholder="直接获取，可修改"
                  v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="评估意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="暂不显示，不可操作，区县司法局局长填写" v-decorator="['hjdxz', {rules: [{required: false, message: '暂不显示，不可操作，区县司法局局长填写 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估意见书"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
    <!-- 审批 -->
    <div v-show="current===5">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
        >区县司法局负责人审批信息</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="审批结果"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-select
                v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                show-search
                placeholder="请选择初审结果"
                style="width: 200px"
                :options="options4"
                @focus="handleFocus"
                @blur="handleBlur"
                @change="handleChange"
              >
              </a-select>
              <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 调查意见审批人 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 审批时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 200px" placeholder="默认当前时间" v-decorator="['zpsj']" @change="onChangezpsj"/>

              <!-- <a-input style="width:200px" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="评估意见 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <!-- <a-input disabled style="width:200px" placeholder="默认登录用户" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
              <a-select
                v-decorator="['zqyjtqyj', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                show-search
                placeholder="请选择初审结果"
                style="width: 200px"
                :options="options4"
                @focus="handleFocus"
                @blur="handleBlur"
                @change="handleChange"
              >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label=" 调查结束时间 "
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-date-picker style="width: 200px" placeholder="默认当前时间" v-decorator="['zpsj']" @change="onChangezpsj"/>

              <!-- <a-input style="width:200px" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="审批意见"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-textarea
                v-decorator="['hjdxz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                :auto-size="{ minRows: 5, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
        文书制作--调查评估意见书</div>
      <div class="border">
        <div class="wsinfo">文书信息</div>
        <div class="border" style="width:90%;margin:5%;padding:20px">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="字"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取，暂为空，审批通过之后自动显示" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="委托单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="直接获取“委托单位" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查开始时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“收到委托时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" >
              <a-form-item
                label=" 调查结束时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width:200px" placeholder="直接获取“调查结束时间”" v-decorator="['zpsj']" @change="onChangezpsj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="姓名"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:200px" disabled placeholder="直接获取，可修改" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="有关情况"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-textarea
                  placeholder="直接获取，可修改"
                  v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="评估意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-input style="width:750px" disabled placeholder="暂不显示，不可操作，区县司法局局长填写" v-decorator="['hjdxz', {rules: [{required: false, message: '暂不显示，不可操作，区县司法局局长填写 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    <!-- 文书打印 -->
    <div v-show="current===6">
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">指派委托材料</div>
      </div>
      <div class="border" style="height:200px">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <!-- 委托调查书：
            <a href="">1.上传成功的文件.doc</a> -->
            <a-form-item
              label="委托调查书："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <!-- <a-input style="width:750px" disabled placeholder="暂不显示，不可操作，区县司法局局长填写" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
              <a href="">1.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="    委托文书附件："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a href="">1.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
              <br>
              <a href="">2.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">调查材料/文书</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="张三调查笔录"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
              <a href="">1.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="李四调查笔录"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
              <a href="">1.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="委托调查材料"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
              <a href="">1.上传成功的文件.doc</a>
              &nbsp;<a href="">下载</a>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="浙江省社区矫正调查评估表"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">
          小组意见材料/文书</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估小组意见表："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">
          集体评议材料/文书</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="集体评议审核意见表："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div class="border">
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          class="border">
          调查评估意见书</div>
      </div>
      <div class="border" style="">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="调查评估意见书："
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              has-feedback
            >
              <a-button type="primary">文书打印</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
    <div
      style="display:flex;
      margin-left: 38%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <div v-show="current===0">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit">指派</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleEdit">保存</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===1">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit">提交</a-button>
        </span>
        <!-- <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleEdit">保存</a-button>
        </span> -->
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===2">

        <div v-show="!qrdc&&!dcgc&&!dcpgb&&!dcpgyjs">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="handleSubmit">完整信息提交</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">暂存并关闭</a-button>
          </span>
        </div>
        <div v-show="qrdc">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="qrdc=!qrdc">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" @click="qrdc=!qrdc">取消</a-button>
          </span>
        </div>
        <div v-show="dcgc">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="dcgc=!dcgc">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" @click="dcgc=!dcgc">取消</a-button>
          </span>
        </div>
        <div v-show="dcpgb">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="dcpgb=!dcpgb">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" @click="dcpgb=!dcpgb">取消</a-button>
          </span>
        </div>
        <div v-show="dcpgyjs">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="dcpgyjs=!dcpgyjs">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" @click="dcpgyjs=!dcpgyjs">取消</a-button>
          </span>
        </div>
      </div>
      <div v-show="current===3">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit">提交并盖章</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleEdit">保存</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===4">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit">提交</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleEdit">保存</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===5">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit">提交</a-button>
        </span>
        <!-- <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleEdit">保存</a-button>
        </span> -->
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===6">
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">关闭</a-button>
        </span>
      </div>
    </div>
    <!-- <dcpgqrdc ref="editForm" @ok="handleOk" /> -->
  </a-modal>
</template>

<script>
  // import dcpgqrdc from './dcpgqrdc.vue'
  import { coordinateInvestigateAdd, coordinateInvestigateEdit } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  import VDistpicker from 'v-distpicker/src/Distpicker'
export default {
    components: { VDistpicker },
    data () {
      return {
        stepStyle: {
          marginBottom: '10px',
          boxShadow: '0px -1px 0 0 #e8e8e8 inset'
        },
     jzdareaCode: { province: '', city: '', area: '' },
     hjareaCode: { province: '', city: '', area: '' },
     handleChange: (value) => {
      console.log(`selected ${value}`)
    },
    handleBlur: () => {
      console.log('blur')
    },
    handleFocus: () => {
      console.log('focus')
    },
          qrdc: false, // 确认调查展示状态
          dcgc: false,
          dcpgb: false,
          dcpgyjs: false,
          sfyypxxq: 1,
          sfhjdzmx: 1,
          hjjzdsfxt: 1,
          sfjzdmx: 1,
          options4: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        current: 0,
        sdwtsjDateString: '',
        csrqDateString: '',
        ypxqksrqDateString: '',
        ypxqjsrqDateString: '',
        pjrqDateString: '',
        zpsjDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
              changeChoseCityjzd: function(e) { // 后执行
                this.jzdareaCode.province = e.province.value
                this.jzdareaCode.city = e.city.value
                this.jzdareaCode.area = e.area.value
                console.log('jzdareaCode', this.jzdareaCode)
                // if (!this.respSuccess) { // 区渲染失败
                //     this.choseCityMap({ code: e.city.code, value: e.city.value })
                // }
            },
              changeChoseCityhj: function(e) { // 后执行
                this.hjareaCode.province = e.province.value
                this.hjareaCode.city = e.city.value
                this.hjareaCode.area = e.area.value
                console.log('hjareaCode', this.hjareaCode)
                // if (!this.respSuccess) { // 区渲染失败
                //     this.choseCityMap({ code: e.city.code, value: e.city.value })
                // }
            },
          getdata() {
      //            sysDictTypeDropDown({ code: 'tqyj' }).then(res => {
      //   console.log(res.data)
      //   res.data.forEach(p => {
      //         this.options4.push({ value: p.code, label: p.value })
      //   })
      // })
        // extOrgInfoList({ type: 2, orgName: '杭州' }).then(res => {
        //   console.log('法院', res.data)
        //   res.data.forEach(p => {
        //     this.optionsFy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
        //   })
        // })
        // extOrgInfoList({ type: 1, orgName: '杭州' }).then(res => {
        //   console.log('检察院', res.data)
        //   res.data.forEach(p => {
        //     this.optionsJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
        //   })
        // })
      },
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdwtsj = this.sdwtsjDateString
            values.csrq = this.csrqDateString
            values.ypxqksrq = this.ypxqksrqDateString
            values.ypxqjsrq = this.ypxqjsrqDateString
            values.pjrq = this.pjrqDateString
            values.zpsj = this.zpsjDateString
            coordinateInvestigateAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleEdit() {
   const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdwtsj = this.sdwtsjDateString
            values.csrq = this.csrqDateString
            values.ypxqksrq = this.ypxqksrqDateString
            values.ypxqjsrq = this.ypxqjsrqDateString
            values.pjrq = this.pjrqDateString
            values.zpsj = this.zpsjDateString
            coordinateInvestigateEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdwtsj(date, dateString) {
        this.sdwtsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangeypxqksrq(date, dateString) {
        this.ypxqksrqDateString = dateString
      },
      onChangeypxqjsrq(date, dateString) {
        this.ypxqjsrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangezpsj(date, dateString) {
        console.log(dateString)
        this.zpsjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<style lang="less" scoped>
.title{
  position: relative;top:97px;left:60px
}
.tj{
  position: relative;top:99px;left:65px
}
.dc{
   display:flex;
   justify-content: space-around;
  }
.border{
    border-style: solid;border-width: 1px;
        border-style: solid;
        border-color: rgba(204, 204, 204, 1);
}
.box{
  flex:1;
  width:150px;
  height:100px;
  margin:50px;
  // background-color: red;
}
.wsinfo{
  z-index:auto;
  position:relative;
  left:90px;
  top:70px;
  background: white;
  width:70px;
  padding-left:5px
}
</style>

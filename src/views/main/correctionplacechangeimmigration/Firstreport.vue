<template>
  <sh-drawer
    title="矫正衔接反馈"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">委托协查信息
        </div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="委托编号">
                <a-input :disabled="disabled" allow-clear placeholder="请输入委托编号" v-decorator="['wtbh']"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input
                  v-if="!linkDisabled"
                  allow-clear
                  placeholder="自动关联"
                  :disabled="true"
                  v-decorator="['xm']"
                  style="width: 160px;display:inline-block" />
                <a-input v-else :disabled="true" style="display:inline-block" v-decorator="['xm']"/>
                <a-button :disabled="disabled" style="display:inline-block" @click="chooseSqjzry" v-if="!linkDisabled">选择</a-button>
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-form-item :labelCol="labelCol2" :wrapperCol="wrapperCol2" label="身份证">
                <a-input allow-clear placeholder="" :disabled="true" v-decorator="['sfzh']"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="cus-title-d">报道接收情况</div>
        <div style="background: white;height: 300px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否按时报道">
                <a-radio-group :disabled="disabled" v-decorator="['sfasbd',{rules: [{ required: true, message: '请选择是否按时报道！' }]}]" >
                  <a-radio v-for="(item,index) in optionssp" :key="index" :value="item.value">{{ item.label }}</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报到时间">
                <a-date-picker :disabled="disabled" style="width: 100%" placeholder="请选择报到时间" v-decorator="['bdsj']" @change="onChangebdsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="反馈时间">
                <a-date-picker :disabled="disabled" style="width: 100%" placeholder="请选择反馈时间" v-decorator="['fksj']" @change="onChangefksj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="24" :sm="24">
              <a-form-item :labelCol="labelCol2" :wrapperCol="wrapperCol2" label="备注">
                <a-textarea :disabled="disabled" allow-clear placeholder="请输入备注" v-decorator="['fkbz']" />
              </a-form-item>
            </a-col>

          </a-row>

        </div>

      </a-form>
    </a-spin>
    <div
      v-show="!disabled"
      style="display:flex;
      margin-left: 34%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <span style="flex: 1;margin: 10px;">
        <a-button type="primary" @click="handleSubmit">完整信息提交</a-button>
      </span>
      <span style="flex: 1;">
        <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">暂存并关闭</a-button>
      </span>
    </div>
    <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
  </sh-drawer>
</template>

<script>
 import chooseSqjzryRadio from '../correctionobjectinformation/chooseSqjzryRadio.vue'
  import moment from 'moment'
  import { correctionPlacechangeImmigrationAdd } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  export default {
    components: { chooseSqjzryRadio },
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
        disabled: false,
        optionssp: [{ label: '是', value: '1' }, { label: '否', value: '0' }],

        linkDisabled: false,
        bdsjDateString: null,
        fksjDateString: null,
        visible: false,
        confirmLoading: false,
        id: '',
        form: this.$form.createForm(this)
      }
    },

    methods: {
      moment,
      onChangebdsj(date, dateString) {
        if (dateString === '') {
          this.bdsjDateString = null
        } else {
          this.bdsjDateString = dateString
        }
      },
      onChangefksj(date, dateString) {
        if (dateString === '') {
          this.fksjDateString = null
        } else {
          this.fksjDateString = dateString
        }
      },
      // 初始化方法
      chooseSqjzry () {
        // 关联社区矫正对象
        this.$refs.chooseSqjzryRadio.choose('stay')
      },
      edit(record, disabled = false) {
        this.disabled = disabled
        this.visible = true
        this.id = record.id
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              sfasbd: record.sfasbd,
              fkbz: record.fkbz,
              id: record.id,
              wtdwmc: record.wtdwmc,
              wtdwId: record.wtdwId,
              wtbh: record.wtbh,
              wtdcs: record.wtdcs,
              wtwsfj: record.wtwsfj,
              xm: record.xm,
              nsysqjzrylx: record.nsysqjzrylx,
              xb: record.xb,
              sfzh: record.sfzh,
              jzddzP: record.jzddzP,
              jzddzC: record.jzddzC,
              jzddz: record.jzddz,
              sfyjzddzmx: record.sfyjzddzmx,
              jzddzmx: record.jzddzmx,
              hjsfyjzdxt: record.hjsfyjzdxt,
              hjdzP: record.hjdzP,
              hjdzC: record.hjdzC,
              hjdz: record.hjdz,
              sfyhjdzmx: record.sfyhjdzmx,
              hjdzmx: record.hjdzmx,
              hzdw: record.hzdw,
              nsyjzlb: record.nsyjzlb,
              sfyypxq: record.sfyypxq,
              ypxq: record.ypxq,
              ypxf: record.ypxf,
              fjx: record.fjx,
              pjjg: record.pjjg,
              zpwdmc: record.zpwdmc,
              zpdwId: record.zpdwId,
              zpr: record.zpr,
              zpbz: record.zpbz
            }
          )
        }, 100)
        // 时间单独处理
        if (record.bdsj != null) {
          this.form.getFieldDecorator('bdsj', { initialValue: moment(record.bdsj, 'YYYY-MM-DD') })
          this.bdsjDateString = moment(record.bdsj).format('YYYY-MM-DD')
        }
        // 时间单独处理
        if (record.fksj != null) {
          this.form.getFieldDecorator('fksj', { initialValue: moment(record.fksj, 'YYYY-MM-DD') })
          this.fksjDateString = moment(record.fksj).format('YYYY-MM-DD')
        }
      },
      jzdx(jzdx) {
        this.form.setFieldsValue(
          {
            xm: jzdx.xm,
            sqjzryId: jzdx.id,
            sfzh: jzdx.sfzh
          })
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.id = this.id
            values.bdsj = this.bdsjDateString
            values.fksj = this.fksjDateString
            correctionPlacechangeImmigrationAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style scoped>
.title{
    border-width: 0;
    width: 100%;
    /* height: 35px; */
    background: inherit;
    background-color: rgba(242, 242, 242, 1);
    border: none;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    font-weight: 700;
    font-style: normal;
    text-align: left;
    line-height:35px;
}
</style>

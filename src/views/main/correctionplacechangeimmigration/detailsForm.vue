<template>
  <sh-drawer
    title="跨省迁出"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">迁出人员信息</div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item v-show="false" :labelCol="labelCol" :wrapperCol="wrapperCol"
                ><a-input v-decorator="['id']"
              /></a-form-item>
              <a-form-item v-show="false" :labelCol="labelCol" :wrapperCol="wrapperCol"
                ><a-input v-decorator="['sqjzryId']"
              /></a-form-item>
              <a-form-item label="委托编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入委托编号" v-decorator="['wtbh']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-if="!linkDisabled"
                  allow-clear
                  placeholder="自动关联"
                  :disabled="true"
                  v-decorator="['xm']"
                  style="width: 160px;display:inline-block"
                />
                <a-input v-else :disabled="true" style="display:inline-block" v-decorator="['xm']" />
                <a-button style="display:inline-block" @click="chooseSqjzry" v-if="!linkDisabled">选择</a-button>
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-form-item label="身份证" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <a-input allow-clear placeholder="" :disabled="true" v-decorator="['sfzh']" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="cus-title-d">执行地变更信息</div>
        <div style="background: white;height: 300px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="申请时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%"
                  :disabled="linkDisabled"
                  v-decorator="['sqsj']"
                  @change="onChangesqsj"
                />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="迁入地" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input allow-clear placeholder="请输入迁入地" :disabled="linkDisabled" v-decorator="['qrd']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="迁入地明细" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input allow-clear placeholder="请输入迁入地明细" :disabled="linkDisabled" v-decorator="['qrdmx']" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="拟迁入矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  allow-clear
                  placeholder="请输入拟迁入矫正单位"
                  :disabled="linkDisabled"
                  v-decorator="['qrjzdw']"
                />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="变更理由" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input allow-clear placeholder="请输入变更理由" :disabled="linkDisabled" v-decorator="['bgly']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="附件上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-upload
                  :disabled="linkDisabled"
                  :multiple="false"
                  :showUploadList="true"
                  :file-list="fileList"
                  :remove="handleRemove"
                  :before-upload="beforeUpload"
                >
                  <a-button v-if="!linkDisabled"> <a-icon type="upload" />上传文件</a-button>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
    <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
  </sh-drawer>
</template>

<script>
import moment from 'moment'
import chooseSqjzryRadio from '../correctionobjectinformation/chooseSqjzryRadio.vue'
import {
  correctionPlacechangeEmigrationAdd,
  correctionPlacechangeEmigrationDetail
} from '@/api/modular/main/correctionplacechangeemigration/correctionPlacechangeEmigrationManage'
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
export default {
  components: { chooseSqjzryRadio },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      linkDisabled: true,
      sqsjDateString: '',
      visible: false,
      confirmLoading: false,
      fileIsChanged: false,
      fileId: [],
      fileList: [],
      form: this.$form.createForm(this)
    }
  },
  methods: {
    moment,
    // 初始化方法
    edit(record, linkDisabled) {
      this.linkDisabled = linkDisabled
      this.visible = true
      if (record) {
        setTimeout(() => {
          this.form.setFieldsValue({
            id: record.id,
            wtbh: record.wtbh,
            xm: record.xm,
            sfzh: record.sfzh,
            qrd: record.qrd,
            qrdmx: record.qrdmx,
            qrjzdw: record.qrjzdw,
            bgly: record.bgly
          })
        }, 100)
        if (record.sqsj != null) {
          const m = moment(record.sqsj, 'YYYY-MM-DD')
          if (m.isValid()) {
            this.form.getFieldDecorator('sqsj', { initialValue: m })
            this.sqsjDateString = moment(record.sqsj).format('YYYY-MM-DD')
          } else {
            this.form.getFieldDecorator('sqsj', { initialValue: null })
          }
        }

        // 加载附件
        correctionPlacechangeEmigrationDetail(record.id).then(res => {
          if (res.success) {
            this.fileList = res.data.fileList
            this.fileId = res.data.file1.split(',')
          } else {
            this.$message.error('附件加载失败') // + res.message
          }
        })
      }
    },
    jzdx(jzdx) {
      this.form.setFieldsValue({
        xm: jzdx.xm,
        sqjzryId: jzdx.id,
        sfzh: jzdx.sfzh
      })
    },
    chooseSqjzry() {
      // 关联社区矫正对象
      this.$refs.chooseSqjzryRadio.choose('stay')
    },
    handleSubmit() {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof values[key] === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          await this.sureUpload()
          values.sqsj = this.sqsjDateString
          values.file1 = this.fileId.join(',')
          correctionPlacechangeEmigrationAdd(values)
            .then(res => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败') // + res.message
              }
            })
            .finally(res => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    onChangesqsj(date, dateString) {
      this.sqsjDateString = dateString
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload(file) {
      if (this.fileList.length > 2) {
        this.$message.warn('附件超过2个，请先删除后再上传')
        return
      }
      this.fileList = [...this.fileList, file]
      return false
    },
    sureUpload() {
      this.fileId = []
      const list = []
      console.log(this.fileList, '当前所有文件集合')
      this.fileList.forEach(file => {
        if (file.size != null) {
          const formData = new FormData()
          formData.append('file', file)
          var p1 = new Promise((resolve, reject) => {
            sysFileInfoUpload(Object.assign(formData)).then(res => {
              if (res.success) {
                this.fileId = [...this.fileId, res.data.id]
                resolve(res.result)
              } else {
                this.$message.error(res.message)
                this.uploadStatus = false
                reject(res)
              }
              this.fileList = []
            })
          })
        }
        list.push(p1)
      })
      return Promise.all(list).then(res => {
        this.uploadStatus = true
      })
    }
  }
}
</script>

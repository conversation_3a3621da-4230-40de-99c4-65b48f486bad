<template>
  <a-modal
    title="编辑执行地变更_外省迁入"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="委托编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="拟适用社区矫正人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入拟适用社区矫正人员类型" v-decorator="['nsysqjzrylx', {rules: [{required: true, message: '请输入拟适用社区矫正人员类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="居住地地址(省)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址(省)" v-decorator="['jzddzP', {rules: [{required: true, message: '请输入居住地地址(省)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地地址(市)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址(市)" v-decorator="['jzddzC', {rules: [{required: true, message: '请输入居住地地址(市)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址" v-decorator="['jzddz', {rules: [{required: true, message: '请输入居住地地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有居住地地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有居住地地址明细" v-decorator="['sfyjzddzmx', {rules: [{required: true, message: '请输入是否有居住地地址明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址明细" v-decorator="['jzddzmx', {rules: [{required: true, message: '请输入居住地地址明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍是否与居住地相同"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍是否与居住地相同" v-decorator="['hjsfyjzdxt', {rules: [{required: true, message: '请输入户籍是否与居住地相同！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地址(省)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址(省)" v-decorator="['hjdzP', {rules: [{required: true, message: '请输入户籍地址(省)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地址(市)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址(市)" v-decorator="['hjdzC', {rules: [{required: true, message: '请输入户籍地址(市)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址" v-decorator="['hjdz', {rules: [{required: true, message: '请输入户籍地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有户籍地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有户籍地址明细" v-decorator="['sfyhjdzmx', {rules: [{required: true, message: '请输入是否有户籍地址明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址明细" v-decorator="['hjdzmx', {rules: [{required: true, message: '请输入户籍地址明细！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工作单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工作单位" v-decorator="['hzdw', {rules: [{required: true, message: '请输入工作单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="拟适用矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入拟适用矫正类别" v-decorator="['nsyjzlb', {rules: [{required: true, message: '请输入拟适用矫正类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有原判刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有原判刑期" v-decorator="['sfyypxq', {rules: [{required: true, message: '请输入是否有原判刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="原判刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入原判刑期" v-decorator="['ypxq', {rules: [{required: true, message: '请输入原判刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="原判刑期开始日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原判刑期开始日期" v-decorator="['ypxqksrq',{rules: [{ required: true, message: '请选择原判刑期开始日期！' }]}]" @change="onChangeypxqksrq"/>
        </a-form-item>
        <a-form-item
          label="原判刑期结束日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原判刑期结束日期" v-decorator="['ypxqjsrq',{rules: [{ required: true, message: '请选择原判刑期结束日期！' }]}]" @change="onChangeypxqjsrq"/>
        </a-form-item>
        <a-form-item
          label="原判刑罚"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入原判刑罚" v-decorator="['ypxf', {rules: [{required: true, message: '请输入原判刑罚！'}]}]" />
        </a-form-item>
        <a-form-item
          label="附加刑"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附加刑" v-decorator="['fjx', {rules: [{required: true, message: '请输入附加刑！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入判决机关" v-decorator="['pjjg', {rules: [{required: true, message: '请输入判决机关！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择判决日期" v-decorator="['pjrq',{rules: [{ required: true, message: '请选择判决日期！' }]}]" @change="onChangepjrq"/>
        </a-form-item>
        <a-form-item
          label="指派调查单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派调查单位" v-decorator="['zpdwdcmc', {rules: [{required: true, message: '请输入指派调查单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指派调查单位ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派调查单位ID" v-decorator="['zpdwdcId', {rules: [{required: true, message: '请输入指派调查单位ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指派人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派人" v-decorator="['zpr', {rules: [{required: true, message: '请输入指派人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指派时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择指派时间" v-decorator="['zpsj',{rules: [{ required: true, message: '请选择指派时间！' }]}]" @change="onChangezpsj"/>
        </a-form-item>
        <a-form-item
          label="指派备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派备注" v-decorator="['zpbz', {rules: [{required: true, message: '请输入指派备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭和社会关系"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭和社会关系" v-decorator="['jthshgx', {rules: [{required: true, message: '请输入家庭和社会关系！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社会危险性、对所居住社区的影响等情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社会危险性、对所居住社区的影响等情况" v-decorator="['shwxxyx', {rules: [{required: true, message: '请输入社会危险性、对所居住社区的影响等情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="拟禁止的事项"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入拟禁止的事项" v-decorator="['njzdsx', {rules: [{required: true, message: '请输入拟禁止的事项！'}]}]" />
        </a-form-item>
        <a-form-item
          label="犯罪行为的后果和影响"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入犯罪行为的后果和影响" v-decorator="['fzxwdhghyx', {rules: [{required: true, message: '请输入犯罪行为的后果和影响！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地村（居）民委员会和被害人意见"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地村（居）民委员会和被害人意见" v-decorator="['wyhhbhryj', {rules: [{required: true, message: '请输入居住地村（居）民委员会和被害人意见！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查评估意见"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查评估意见" v-decorator="['dcpgyj', {rules: [{required: true, message: '请输入调查评估意见！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查人" v-decorator="['dcr', {rules: [{required: true, message: '请输入调查人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查单位" v-decorator="['dcdw', {rules: [{required: true, message: '请输入调查单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查单位ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查单位ID" v-decorator="['dcdwId', {rules: [{required: true, message: '请输入调查单位ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指派矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派矫正单位" v-decorator="['zpjzdw', {rules: [{required: true, message: '请输入指派矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指派矫正单位ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派矫正单位ID" v-decorator="['zpjzdwId', {rules: [{required: true, message: '请输入指派矫正单位ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查意见审核人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查意见审核人" v-decorator="['dcyjshr', {rules: [{required: true, message: '请输入调查意见审核人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="附件1"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附件1" v-decorator="['file1', {rules: [{required: true, message: '请输入附件1！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否按时报到"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否按时报到" v-decorator="['sfasbd', {rules: [{required: true, message: '请输入是否按时报到！'}]}]" />
        </a-form-item>
        <a-form-item
          label="报到时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择报到时间" v-decorator="['bdsj',{rules: [{ required: true, message: '请选择报到时间！' }]}]" @change="onChangebdsj"/>
        </a-form-item>
        <a-form-item
          label="反馈时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择反馈时间" v-decorator="['fksj',{rules: [{ required: true, message: '请选择反馈时间！' }]}]" @change="onChangefksj"/>
        </a-form-item>
        <a-form-item
          label="反馈备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入反馈备注" v-decorator="['fkbz', {rules: [{required: true, message: '请输入反馈备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（字典 0正常 1停用 2删除)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（字典 0正常 1停用 2删除)" v-decorator="['zt', {rules: [{required: true, message: '请输入状态（字典 0正常 1停用 2删除)！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { correctionPlacechangeImmigrationEdit } from '@/api/modular/main/correctionplacechangeimmigration/correctionPlacechangeImmigrationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        csrqDateString: '',
        ypxqksrqDateString: '',
        ypxqjsrqDateString: '',
        pjrqDateString: '',
        zpsjDateString: '',
        bdsjDateString: '',
        fksjDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              wtbh: record.wtbh,
              xm: record.xm,
              sfzh: record.sfzh,
              nsysqjzrylx: record.nsysqjzrylx,
              xb: record.xb,
              jzddzP: record.jzddzP,
              jzddzC: record.jzddzC,
              jzddz: record.jzddz,
              sfyjzddzmx: record.sfyjzddzmx,
              jzddzmx: record.jzddzmx,
              hjsfyjzdxt: record.hjsfyjzdxt,
              hjdzP: record.hjdzP,
              hjdzC: record.hjdzC,
              hjdz: record.hjdz,
              sfyhjdzmx: record.sfyhjdzmx,
              hjdzmx: record.hjdzmx,
              hzdw: record.hzdw,
              nsyjzlb: record.nsyjzlb,
              sfyypxq: record.sfyypxq,
              ypxq: record.ypxq,
              ypxf: record.ypxf,
              fjx: record.fjx,
              pjjg: record.pjjg,
              zpdwdcmc: record.zpdwdcmc,
              zpdwdcId: record.zpdwdcId,
              zpr: record.zpr,
              zpbz: record.zpbz,
              jthshgx: record.jthshgx,
              shwxxyx: record.shwxxyx,
              njzdsx: record.njzdsx,
              fzxwdhghyx: record.fzxwdhghyx,
              wyhhbhryj: record.wyhhbhryj,
              dcpgyj: record.dcpgyj,
              dcr: record.dcr,
              dcdw: record.dcdw,
              dcdwId: record.dcdwId,
              zpjzdw: record.zpjzdw,
              zpjzdwId: record.zpjzdwId,
              dcyjshr: record.dcyjshr,
              file1: record.file1,
              sfasbd: record.sfasbd,
              fkbz: record.fkbz,
              zt: record.zt
            }
          )
        }, 100)
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqksrq != null) {
            this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
        }
        this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqjsrq != null) {
            this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
        }
        this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zpsj != null) {
            this.form.getFieldDecorator('zpsj', { initialValue: moment(record.zpsj, 'YYYY-MM-DD') })
        }
        this.zpsjDateString = moment(record.zpsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.bdsj != null) {
            this.form.getFieldDecorator('bdsj', { initialValue: moment(record.bdsj, 'YYYY-MM-DD') })
        }
        this.bdsjDateString = moment(record.bdsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.fksj != null) {
            this.form.getFieldDecorator('fksj', { initialValue: moment(record.fksj, 'YYYY-MM-DD') })
        }
        this.fksjDateString = moment(record.fksj).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.csrq = this.csrqDateString
            values.ypxqksrq = this.ypxqksrqDateString
            values.ypxqjsrq = this.ypxqjsrqDateString
            values.pjrq = this.pjrqDateString
            values.zpsj = this.zpsjDateString
            values.bdsj = this.bdsjDateString
            values.fksj = this.fksjDateString
            correctionPlacechangeImmigrationEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangeypxqksrq(date, dateString) {
        this.ypxqksrqDateString = dateString
      },
      onChangeypxqjsrq(date, dateString) {
        this.ypxqjsrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangezpsj(date, dateString) {
        this.zpsjDateString = dateString
      },
      onChangebdsj(date, dateString) {
        this.bdsjDateString = dateString
      },
      onChangefksj(date, dateString) {
        this.fksjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

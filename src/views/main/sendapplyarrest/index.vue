<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <sh-correction-org-tree v-model="queryParam.jzjgId" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <sh-select
                  allowClear
                  :whetherToAddAll="true"
                  v-model="queryParam.zt"
                  :options="statusList"
                  @change="$forceUpdate()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="发送时间">
                <a-range-picker v-model="queryParam.date" />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <!--          <a-button type="primary" icon="plus" @click="$refs.addForm.add()">新增发送提请逮捕</a-button>-->
        </template>
        <span slot="action" slot-scope="text, record">
          <template v-if="record.zt === '0'">
            <a @click="$refs.addForm.edit(record)">发送</a>
            <a-divider type="vertical" />
          </template>
          <template v-if="record.zt && record.zt !== '0'">
            <a @click="$refs.detailVue.open(record)">详情</a>
            <a-divider type="vertical" />
          </template>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => sendApplyArrestDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
        <span slot="timeFormat" slot-scope="text">
          {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
      <DetailVue ref="detailVue" />
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import { sendApplyArrestDelete, sendApplyArrestPage } from '@/api/modular/main/sendapplyarrest/sendApplyArrestManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import DetailVue from './detail.vue'

export default {
  components: {
    STable,
    addForm,
    editForm,
    DetailVue
  },
  data() {
    return {
      statusList: [
        { name: '待发送', code: '0' },
        { name: '待反馈回执', code: '1' },
        { name: '待作出决定', code: '2' },
        { name: '已完结', code: '3' }
      ],
      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '状态',
          width: 100,
          align: 'center',
          dataIndex: 'zt',
          customRender: val => {
            const result = this.statusList.find(item => item.code === val)
            return result ? result.name : '未知状态'
          }
        },
        { ellipsis: true, title: '姓名', align: 'center', width: 80, dataIndex: 'xm' },
        { ellipsis: true, title: '矫正单位', align: 'center', width: 180, dataIndex: 'sfs' },
        {
          ellipsis: true,
          title: '提请日期',
          align: 'center',
          with: 150,
          dataIndex: 'tqrq',
          scopedSlots: { customRender: 'timeFormat' }
        },
        { ellipsis: true, title: '提请法院', align: 'center', dataIndex: 'xtfyName' },
        { ellipsis: true, title: '抄送单位', align: 'center', dataIndex: 'xtjcyName' },
        {
          ellipsis: true,
          title: '发送时间',
          align: 'center',
          with: 150,
          dataIndex: 'fssj',
          scopedSlots: { customRender: 'timeFormat' }
        },
        {
          ellipsis: true,
          title: '反馈时间',
          align: 'center',
          with: 120,
          dataIndex: 'fkrq',
          scopedSlots: { customRender: 'timeFormat' }
        },
        { ellipsis: true, title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return sendApplyArrestPage(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    if (this.$route.query.zt) {
      this.queryParam.zt = this.$route.query.zt
    }
  },
  watch: {
    $route(to, from) {
      // 对路由变化作出响应...
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.queryParam.zt = to.query.zt
      this.$refs.table.refresh()
    }
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const date = this.queryParam.date
      if (date) {
        this.queryParam.searchBeginTime = this.queryParam.date[0].format('YYYY-MM-DD')
        this.queryParam.searchEndTime = this.queryParam.date[1].format('YYYY-MM-DD')
        delete this.queryParam.date
      }
      return JSON.parse(JSON.stringify(this.queryParam))
    },
    sendApplyArrestDelete(record) {
      sendApplyArrestDelete(record).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

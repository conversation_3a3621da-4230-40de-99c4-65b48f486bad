<template>
  <a-drawer
    title="编辑"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <a-row :gutter="20">
          <a-form :form="form">
            <div class="cus-title">案件信息</div>
            <a-col :span="12">
              <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="请输入" v-decorator="['tyfh', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正案件编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="请输入" v-decorator="['sqjzajbh', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <div class="cus-title">罪犯信息</div>
            <a-col :span="12">
              <a-form-item label="社区矫正对象姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-choose-sqjzry-radio v-decorator="['xm', { rules: [{ required: true, message: '请选择' }] }]" />

              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select
                  dictType="sex"
                  style="width: 100%;"
                  placeholder="请选择"
                  v-decorator="['xb', { rules: [{ required: true, message: '请输入' }] }]" />

              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%;"
                  v-decorator="['csrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select
                  dictType="SQJZ_JZLB_NEW"
                  style="width: 100%;"
                  placeholder="请选择"
                  v-decorator="['jzlb', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="户籍地" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">

                <sh-distpicker
                  v-decorator="['hjd', { initialValue: {}, rules: [{ required: true, message: '请输入' }, { validator: distpickerValidator }] }]" />
                <!-- <a-input placeholder="请输入" v-decorator="['zt', {rules: [{required: true, message: '请输入'}]}]" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="户籍地详细地址" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-input placeholder="请输入" v-decorator="['hjdxz', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="现住地" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">

                <sh-distpicker
                  v-decorator="['xzd', { initialValue: {}, rules: [{ required: true, message: '请输入' }, { validator: distpickerValidator }] }]" />
                <!-- <a-input placeholder="请输入" v-decorator="['zt', {rules: [{required: true, message: '请输入'}]}]" /> -->
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="现住地详址" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-input placeholder="请输入" v-decorator="['xzdxz', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select
                  v-decorator="['jdjg', {rules: [{required: true, message: '请选择决定机关！'}]}]"
                  style="width: 100%;"
                  :show-search="true"
                  :options="extOrgInfoData.fy"
                  valueKey="orgCode"
                  labelKey="orgName"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-correction-org-tree v-decorator="['sfs', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%;"
                  v-decorator="['sqjzksrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%;"
                  v-decorator="['sqjzjsrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择" />
              </a-form-item>
            </a-col>
            <div class="cus-title">提请逮捕信息</div>
            <a-col :span="12">
              <a-form-item label="建议文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="请输入" v-decorator="['jyswh', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="提请日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%;"
                  v-decorator="['tqrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="事由及依据" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-textarea :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="请输入" v-decorator="['syjyj', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="文书材料" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <sh-file-upload
                  v-decorator="['ws', {initialValue:[], rules: [{ required: true, message: '请输入' }] }]"
                  uploadUrl="/api/sysFileInfo/uploadOss" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <div class="cus-title">协同信息</div>
            </a-col>

            <a-col :span="12">
              <a-form-item label="受理法院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select
                  v-decorator="['xtfy', {rules: [{required: true, message: '请选择！'}]}]"
                  style="width: 100%;"
                  :show-search="true"
                  :options="extOrgInfoData.fy"
                  valueKey="orgCode"
                  labelKey="orgName"
                  @change="handleChangeFy"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="执行地检察院" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select
                  v-decorator="['xtjcy', {rules: [{required: true, message: '请选择！'}]}]"
                  style="width: 100%;"
                  :show-search="true"
                  :options="extOrgInfoData.jcy"
                  valueKey="orgCode"
                  labelKey="orgName"
                  @change="handleChangeJcy"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发送单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-correction-org-tree v-decorator="['fsdw', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发送时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  style="width: 100%;"
                  v-decorator="['fssj', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择" />
              </a-form-item>
            </a-col>
          </a-form>
        </a-row>
      </a-spin>
    </div>
    <div
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">
      <a-button style="margin-Right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>

import { sendApplyArrestAdd, sendApplyArrestEdit } from '@/api/modular/main/sendapplyarrest/sendApplyArrestManage'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
import { fileList } from '@/api/modular/system/fileManage'

export default {
  data() {
    return {
      drawerWidth: 1000,
      extOrgInfoData: {}, // 矫正决定机关
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      visible: false,
      xtfyName: '',
      xtjcyName: '',
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    this.drawerWidth = window.innerWidth * 0.8
    this.getExtOrgInfoList()
  },
  methods: {

   // 外部单位信息列表
    getExtOrgInfoList() {
      extOrgInfoList().then((res) => {
        // 社区矫正决定机关
        const fy = []
        //
        const ga = []
        // 检察院
        const jcy = []
        res.data.forEach((v) => {
          if (v.type === 20) {
            fy.push(v)
          } else if (v.type === 40) {
            ga.push(v)
          } else if (v.type === 30) {
            jcy.push(v)
          }
        })
        this.extOrgInfoData = { fy, ga, jcy }
      })
    },
    distpickerValidator(ule, value, callback) {
      if (value.selectedProvince && value.selectedCity && value.selectedDistrict) {
        callback()
      } else {
        callback(new Error('请选择'))
      }
    },
    parseAdministrativeCode(code) {
      if (code.length !== 6) {
        return null; // 行政区划代码必须是6位
      }

      const provinceCode = code.slice(0, 2) + '0000'; // 获取省级代码
      const cityCode = code.slice(0, 4) + '00'; // 获取市级代码

      return {
        selectedProvince: provinceCode,
        selectedCity: cityCode,
        selectedDistrict: code
      };
    },
   async getFileList() {
     const { data } = await fileList({ ids: this.record.ws })
     this.form.setFieldsValue({
      ws: data.map(item => { return { ...item, id: item.uid } })
     })
    },
    // 初始化方法
    add(record) {
      this.visible = true
    },

    edit(record) {
      if (record) {
        this.visible = true
        this.record = { ...record }
        this.getFileList()
        this.$nextTick(() => {
      // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
          const addres = this.parseAdministrativeCode(this.record.hjd) // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
          const addres2 = this.parseAdministrativeCode(this.record.xzd)
          this.form.setFieldsValue(
            {
              ...this.record,
              hjd: addres,
              xzd: addres2
            })
        })
    }
},
    /**
     * 提交表单
     */
    handleSubmit() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          // for (const key in values) {
          //   if (typeof (values[key]) === 'object') {
          //     values[key] = JSON.stringify(values[key])
          //   }
          // }
          const postData = {
            ...values
          }
          postData.xm = postData.xm.xm
          postData.ws = postData.ws.map(item => item.id).toString()
          postData.hjd = postData.hjd.selectedDistrict
          postData.xzd = postData.xzd.selectedDistrict
          postData.xtfyName = this.xtfyName
          postData.xtjcyName = this.xtjcyName

          console.log(postData)
          if (this.record) {
            postData.id = this.record.id
            sendApplyArrestEdit(postData).then((res) => {
            if (res.success) {
              this.$message.success('操作成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('操作失败' + res.message)// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
            return false
          }
          sendApplyArrestAdd(postData).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败' + res.message)// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleChangeFy(fyCode) {
      this.xtfyName = this.extOrgInfoData.fy.filter(item => item.orgCode === fyCode)[0].orgName
    },
    handleChangeJcy(fyCode) {
      this.xtjcyName = this.extOrgInfoData.jcy.filter(item => item.orgCode === fyCode)[0].orgName
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .ant-drawer-body {
  padding-bottom: 78px;
}

.cus-title {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after{
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

<template>
  <a-modal
    title="编辑发送提请逮捕"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入状态" v-decorator="['zt', {rules: [{required: true, message: '请输入状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="协同受理法院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入协同受理法院" v-decorator="['xtfy', {rules: [{required: true, message: '请输入协同受理法院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="协同检察院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入协同检察院" v-decorator="['xtjcy', {rules: [{required: true, message: '请输入协同检察院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发送单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入发送单位" v-decorator="['fsdw', {rules: [{required: true, message: '请输入发送单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发送时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择发送时间" v-decorator="['fssj',{rules: [{ required: true, message: '请选择发送时间！' }]}]" @change="onChangefssj"/>
        </a-form-item>
        <a-form-item
          label="统一赋号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入统一赋号" v-decorator="['tyfh', {rules: [{required: true, message: '请输入统一赋号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正案件编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入社区矫正案件编号" v-decorator="['sqjzajbh', {rules: [{required: true, message: '请输入社区矫正案件编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="罪犯编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入罪犯编号" v-decorator="['zfbh', {rules: [{required: true, message: '请输入罪犯编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入证件类型" v-decorator="['zjlx', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="户籍地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入户籍地" v-decorator="['hjd', {rules: [{required: true, message: '请输入户籍地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地详址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入户籍地详址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍地详址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现住地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入现住地" v-decorator="['xzd', {rules: [{required: true, message: '请输入现住地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现住地详址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入现住地详址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入现住地详址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="生效判决机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入生效判决机关" v-decorator="['sxpjjg', {rules: [{required: true, message: '请输入生效判决机关！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决文书文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入判决文书文号" v-decorator="['pjwswh', {rules: [{required: true, message: '请输入判决文书文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择判决日期" v-decorator="['pjrq',{rules: [{ required: true, message: '请选择判决日期！' }]}]" @change="onChangepjrq"/>
        </a-form-item>
        <a-form-item
          label="判决罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入判决罪名" v-decorator="['pjzm', {rules: [{required: true, message: '请输入判决罪名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决其他罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入判决其他罪名" v-decorator="['pjqtzm', {rules: [{required: true, message: '请输入判决其他罪名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="原判刑罚"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入原判刑罚" v-decorator="['ypxf', {rules: [{required: true, message: '请输入原判刑罚！'}]}]" />
        </a-form-item>
        <a-form-item
          label="附加刑"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入附加刑" v-decorator="['fjx', {rules: [{required: true, message: '请输入附加刑！'}]}]" />
        </a-form-item>
        <a-form-item
          label="禁止令内容"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入禁止令内容" v-decorator="['jzlnr', {rules: [{required: true, message: '请输入禁止令内容！'}]}]" />
        </a-form-item>
        <a-form-item
          label="禁止期限起日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择禁止期限起日" v-decorator="['jzqxqr',{rules: [{ required: true, message: '请选择禁止期限起日！' }]}]" @change="onChangejzqxqr"/>
        </a-form-item>
        <a-form-item
          label="禁止期限止日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择禁止期限止日" v-decorator="['jzqxzr',{rules: [{ required: true, message: '请选择禁止期限止日！' }]}]" @change="onChangejzqxzr"/>
        </a-form-item>
        <a-form-item
          label="裁定假释法院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入裁定假释法院" v-decorator="['cdjsfy', {rules: [{required: true, message: '请输入裁定假释法院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="裁定假释法院案号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入裁定假释法院案号" v-decorator="['cdjsfyah', {rules: [{required: true, message: '请输入裁定假释法院案号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="裁定假释日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择裁定假释日期" v-decorator="['cdjsrq',{rules: [{ required: true, message: '请选择裁定假释日期！' }]}]" @change="onChangecdjsrq"/>
        </a-form-item>
        <a-form-item
          label="裁定假释文书号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入裁定假释文书号" v-decorator="['cdjswsh', {rules: [{required: true, message: '请输入裁定假释文书号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正类别" v-decorator="['jzlb', {rules: [{required: true, message: '请输入矫正类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="决定机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入决定机关" v-decorator="['jdjg', {rules: [{required: true, message: '请输入决定机关！'}]}]" />
        </a-form-item>
        <a-form-item
          label="司法所"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入司法所" v-decorator="['sfs', {rules: [{required: true, message: '请输入司法所！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正开始日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择社区矫正开始日期" v-decorator="['sqjzksrq',{rules: [{ required: true, message: '请选择社区矫正开始日期！' }]}]" @change="onChangesqjzksrq"/>
        </a-form-item>
        <a-form-item
          label="社区矫正结束日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择社区矫正结束日期" v-decorator="['sqjzjsrq',{rules: [{ required: true, message: '请选择社区矫正结束日期！' }]}]" @change="onChangesqjzjsrq"/>
        </a-form-item>
        <a-form-item
          label="矫正期限"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入矫正期限" v-decorator="['jzqx', {rules: [{required: true, message: '请输入矫正期限！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正执行地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入社区矫正执行地" v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入社区矫正执行地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="建议书文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入建议书文号" v-decorator="['jyswh', {rules: [{required: true, message: '请输入建议书文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提请日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择提请日期" v-decorator="['tqrq',{rules: [{ required: true, message: '请选择提请日期！' }]}]" @change="onChangetqrq"/>
        </a-form-item>
        <a-form-item
          label="事由及依据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入事由及依据" v-decorator="['syjyj', {rules: [{required: true, message: '请输入事由及依据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="回执结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入回执结果" v-decorator="['hzjg', {rules: [{required: true, message: '请输入回执结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="回执时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择回执时间" v-decorator="['hzsj',{rules: [{ required: true, message: '请选择回执时间！' }]}]" @change="onChangehzsj"/>
        </a-form-item>
        <a-form-item
          label="回执说明"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入回执说明" v-decorator="['hzsm', {rules: [{required: true, message: '请输入回执说明！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提请逮捕决定"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入提请逮捕决定" v-decorator="['tqdbjd', {rules: [{required: true, message: '请输入提请逮捕决定！'}]}]" />
        </a-form-item>
        <a-form-item
          label="反馈日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-date-picker style="width: 100%" placeholder="请选择反馈日期" v-decorator="['fkrq',{rules: [{ required: true, message: '请选择反馈日期！' }]}]" @change="onChangefkrq"/>
        </a-form-item>
        <a-form-item
          label="决定说明"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入决定说明" v-decorator="['jdsm', {rules: [{required: true, message: '请输入决定说明！'}]}]" />
        </a-form-item>
        <a-form-item
          label="决定文书号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入决定文书号" v-decorator="['jdwsh', {rules: [{required: true, message: '请输入决定文书号！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { sendApplyArrestEdit } from '@/api/modular/main/sendapplyarrest/sendApplyArrestManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        fssjDateString: '',
        csrqDateString: '',
        pjrqDateString: '',
        jzqxqrDateString: '',
        jzqxzrDateString: '',
        cdjsrqDateString: '',
        sqjzksrqDateString: '',
        sqjzjsrqDateString: '',
        tqrqDateString: '',
        hzsjDateString: '',
        fkrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              zt: record.zt,
              xtfy: record.xtfy,
              xtjcy: record.xtjcy,
              fsdw: record.fsdw,
              tyfh: record.tyfh,
              sqjzajbh: record.sqjzajbh,
              zfbh: record.zfbh,
              xm: record.xm,
              xb: record.xb,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              hjd: record.hjd,
              hjdxz: record.hjdxz,
              xzd: record.xzd,
              xzdxz: record.xzdxz,
              sxpjjg: record.sxpjjg,
              pjwswh: record.pjwswh,
              pjzm: record.pjzm,
              pjqtzm: record.pjqtzm,
              ypxf: record.ypxf,
              fjx: record.fjx,
              jzlnr: record.jzlnr,
              cdjsfy: record.cdjsfy,
              cdjsfyah: record.cdjsfyah,
              cdjswsh: record.cdjswsh,
              jzlb: record.jzlb,
              jdjg: record.jdjg,
              sfs: record.sfs,
              jzqx: record.jzqx,
              sqjzzxd: record.sqjzzxd,
              jyswh: record.jyswh,
              syjyj: record.syjyj,
              hzjg: record.hzjg,
              hzsm: record.hzsm,
              tqdbjd: record.tqdbjd,
              jdsm: record.jdsm,
              jdwsh: record.jdwsh
            }
          )
        }, 100)
        // 时间单独处理
        if (record.fssj != null) {
            this.form.getFieldDecorator('fssj', { initialValue: moment(record.fssj, 'YYYY-MM-DD') })
        }
        this.fssjDateString = moment(record.fssj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jzqxqr != null) {
            this.form.getFieldDecorator('jzqxqr', { initialValue: moment(record.jzqxqr, 'YYYY-MM-DD') })
        }
        this.jzqxqrDateString = moment(record.jzqxqr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jzqxzr != null) {
            this.form.getFieldDecorator('jzqxzr', { initialValue: moment(record.jzqxzr, 'YYYY-MM-DD') })
        }
        this.jzqxzrDateString = moment(record.jzqxzr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.cdjsrq != null) {
            this.form.getFieldDecorator('cdjsrq', { initialValue: moment(record.cdjsrq, 'YYYY-MM-DD') })
        }
        this.cdjsrqDateString = moment(record.cdjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.sqjzksrq != null) {
            this.form.getFieldDecorator('sqjzksrq', { initialValue: moment(record.sqjzksrq, 'YYYY-MM-DD') })
        }
        this.sqjzksrqDateString = moment(record.sqjzksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.sqjzjsrq != null) {
            this.form.getFieldDecorator('sqjzjsrq', { initialValue: moment(record.sqjzjsrq, 'YYYY-MM-DD') })
        }
        this.sqjzjsrqDateString = moment(record.sqjzjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.tqrq != null) {
            this.form.getFieldDecorator('tqrq', { initialValue: moment(record.tqrq, 'YYYY-MM-DD') })
        }
        this.tqrqDateString = moment(record.tqrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.hzsj != null) {
            this.form.getFieldDecorator('hzsj', { initialValue: moment(record.hzsj, 'YYYY-MM-DD') })
        }
        this.hzsjDateString = moment(record.hzsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.fkrq != null) {
            this.form.getFieldDecorator('fkrq', { initialValue: moment(record.fkrq, 'YYYY-MM-DD') })
        }
        this.fkrqDateString = moment(record.fkrq).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.fssj = this.fssjDateString
            values.csrq = this.csrqDateString
            values.pjrq = this.pjrqDateString
            values.jzqxqr = this.jzqxqrDateString
            values.jzqxzr = this.jzqxzrDateString
            values.cdjsrq = this.cdjsrqDateString
            values.sqjzksrq = this.sqjzksrqDateString
            values.sqjzjsrq = this.sqjzjsrqDateString
            values.tqrq = this.tqrqDateString
            values.hzsj = this.hzsjDateString
            values.fkrq = this.fkrqDateString
            sendApplyArrestEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangefssj(date, dateString) {
        this.fssjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangejzqxqr(date, dateString) {
        this.jzqxqrDateString = dateString
      },
      onChangejzqxzr(date, dateString) {
        this.jzqxzrDateString = dateString
      },
      onChangecdjsrq(date, dateString) {
        this.cdjsrqDateString = dateString
      },
      onChangesqjzksrq(date, dateString) {
        this.sqjzksrqDateString = dateString
      },
      onChangesqjzjsrq(date, dateString) {
        this.sqjzjsrqDateString = dateString
      },
      onChangetqrq(date, dateString) {
        this.tqrqDateString = dateString
      },
      onChangehzsj(date, dateString) {
        this.hzsjDateString = dateString
      },
      onChangefkrq(date, dateString) {
        this.fkrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

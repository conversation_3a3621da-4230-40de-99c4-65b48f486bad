<template>
  <a-drawer
    title="详情"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-tabs v-model="activeKey">
        <a-tab-pane key="1" tab="提请信息">
          <a-descriptions title="案件信息" bordered :column="2">
            <a-descriptions-item label="统一赋号">
              {{ sourceData.tyfh }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正案件编号">
              {{ sourceData.sqjzajbh }}
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="罪犯信息" bordered :column="2">
            <a-descriptions-item label="社区矫正对象姓名">
              {{ sourceData.xm }}
            </a-descriptions-item>
            <a-descriptions-item label="性别">
              <!-- {{ sourceData.xb }} -->
              {{ 'sex' | dictType(sourceData.xb) }}
            </a-descriptions-item>
            <a-descriptions-item label="出生日期">
              {{ sourceData.csrq }}
            </a-descriptions-item>
            <a-descriptions-item label="矫正类别">
              <!-- {{ sourceData.jzlb }} -->
              {{ 'SQJZ_JZLB_NEW' | dictType(sourceData.jzlb) }}
            </a-descriptions-item>
            <a-descriptions-item label="户籍地" :span="2">
              <!-- {{ sourceData.hjd }} -->
              <sh-distpicker :readonly="true" v-model="sourceData.hjd" />
            </a-descriptions-item>
            <a-descriptions-item label="户籍地详情" :span="2">
              <a-badge status="processing" />
              {{ sourceData.hjdxz }}
            </a-descriptions-item>
            <a-descriptions-item label="居住地" :span="2">
              <!-- {{ sourceData.xzd }} -->
              <sh-distpicker :readonly="true" v-model="sourceData.xzd" />
            </a-descriptions-item>
            <a-descriptions-item label="居住地详情" :span="2">
              <a-badge status="processing" />
              {{ sourceData.xzdxz }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正决定机关">
              {{ sourceData.xtfyName }}
            </a-descriptions-item>
            <a-descriptions-item label="矫正单位">
              {{ sourceData.sfs }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正开始日期">
              {{ sourceData.sqjzksrq }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正结束日期">
              {{ sourceData.sqjzjsrq }}
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="提请逮捕信息" bordered :column="2">
            <a-descriptions-item label="建议文书号">
              {{ sourceData.jyswh }}
            </a-descriptions-item>
            <a-descriptions-item label="提请日期">
              {{ sourceData.tqrq }}
            </a-descriptions-item>
            <a-descriptions-item label="事由及依据" :span="2">
              {{ sourceData.syjyj }}
            </a-descriptions-item>
            <a-descriptions-item label="文书材料" :span="2">
              <!-- {{ sourceData.ws }} -->
              <sh-file-upload
                :disabled="true"
                v-model="sourceData.ws"
              />
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="协同信息" bordered :column="2">
            <a-descriptions-item label="受理法院">
              {{ sourceData.xtfyName }}
            </a-descriptions-item>
            <a-descriptions-item label="执行地检察院">
              {{ sourceData.xtjcyName }}
            </a-descriptions-item>
            <a-descriptions-item label="发送单位">
              {{ sourceData.fsdw }}
            </a-descriptions-item>
            <a-descriptions-item label="发送时间">
              {{ sourceData.fssj }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="2" tab="回执信息" force-render>
          <a-descriptions title="案件信息" bordered :column="2">
            <a-descriptions-item label="统一赋号">
              {{ sourceData.tyfh }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正案件编号">
              {{ sourceData.sqjzajbh }}
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="反馈回执信息" bordered :column="2">
            <a-descriptions-item label="回执结果">
              {{ sourceData.hzjg }}
            </a-descriptions-item>
            <a-descriptions-item label="回执日期">
              {{ sourceData.hzsj }}
            </a-descriptions-item>
            <a-descriptions-item label="回执说明" :span="2">
              {{ sourceData.hzsm }}
            </a-descriptions-item>
            <a-descriptions-item label="回执单位" :span="2">
              {{ sourceData.xtfyName }}
            </a-descriptions-item>
            <a-descriptions-item label="回执文书" :span="2">
              <sh-file-upload
                :disabled="true"
                v-model="hzFileList"
              />
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="3" tab="决定信息" force-render>
          <a-descriptions title="案件信息" bordered :column="2">
            <a-descriptions-item label="统一赋号">
              {{ sourceData.tyfh }}
            </a-descriptions-item>
            <a-descriptions-item label="社区矫正案件编号">
              {{ sourceData.sqjzajbh }}
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions title="决定信息" bordered :column="2">
            <a-descriptions-item label="提请逮捕决定">
              {{ sourceData.tqdbjd }}
            </a-descriptions-item>
            <a-descriptions-item label="反馈日期">
              {{ sourceData.fkrq }}
            </a-descriptions-item>
            <a-descriptions-item label="决定说明" :span="2">
              {{ sourceData.jdsm }}
            </a-descriptions-item>
            <a-descriptions-item label="决定文书号">
              {{ sourceData.jdwsh }}
            </a-descriptions-item>
            <a-descriptions-item label="决定法院">
              {{ sourceData.xtfyName }}
            </a-descriptions-item>
            <a-descriptions-item label="回执文书" :span="2">
              <sh-file-upload
                :disabled="true"
                v-model="hzFileList"
              />
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>

    </div>

  </a-drawer>
</template>

<script>
import { fileList } from '@/api/modular/system/fileManage'
import { acceptCorrectionDocList } from '@/api/modular/main/acceptDocManage';

export default {
    data() {
        return {
            fileList: [], // 文书材料
            hzFileList: [], // 回执文书材料
            jdFileList: [], // 决定回执文书材料
            activeKey: '1',
            confirmLoading: false,
            drawetWidth: 1000,
            visible: false,
            sourceData: {}
        }
    },
    created() {
        this.drawetWidth = window.innerWidth * 0.8
        // this.getExtOrgInfoList()
    },
    methods: {
        async getFileListAll(id) {
          acceptCorrectionDocList({ contactId: id }).then(res => {
            if (res.success) {
              this.docList = res.data
              this.hzFileList = res.data.map(item => {
                return {
                  name: item.ws,
                  url: item.ossUrl,
                  uid: item.id,
                  ...item
                }
              })
            }
          })
        },
        async getFileList(ids) {
            console.log(this.record)
            const { data } = await fileList({ ids })
            this.fileList = data
            this.sourceData.ws = data
        },
        open(record) {
            const ndata = { ...record }
            this.getFileList(ndata.ws)
            this.getFileListAll(ndata.id)
            ndata.hjd = this.parseAdministrativeCode(ndata.hjd) // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
            ndata.xzd = this.parseAdministrativeCode(ndata.xzd) // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
            ndata.ws = []
            this.sourceData = { ...ndata }

            this.visible = true;
        },
        parseAdministrativeCode(code) {
            if (code.length !== 6) {
                return null; // 行政区划代码必须是6位
            }

            const provinceCode = code.slice(0, 2) + '0000'; // 获取省级代码
            const cityCode = code.slice(0, 4) + '00'; // 获取市级代码

            return {
                selectedProvince: provinceCode,
                selectedCity: cityCode,
                selectedDistrict: code
            };
        },
        handleCancel() {
            this.sourceData = null
            this.visible = false
        }
    }
}
</script>

<style lang="less" scoped>
/deep/.ant-descriptions-bordered .ant-descriptions-item-label {
    width: 200px;
    text-align: right;
}
/deep/.ant-descriptions{
    margin-bottom: 20px;
}
</style>

<template>
  <a-modal
    title="编辑签章维护表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-tree-select
            v-decorator="['jzjg', {rules: [{required: true, message: '请选择矫正单位！'}]}]"
            style="width: 100%"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="orgTree"
            @change="changeOrg"
            placeholder="请选择矫正单位"
          >
          </a-tree-select>
        </a-form-item>
        <a-form-item
          label="签章名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入签章名称" v-decorator="['sealName', {rules: [{required: true, message: '请输入签章名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签章编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入签章编号" v-decorator="['sealNo', {rules: [{required: true, message: '请输入签章编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签章类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-select v-decorator="['sealType', {rules: [{required: true, message: '请选择签章类型！'}]}]" allow-clear placeholder="请选择签章类型">
            <a-select-option value="1">
              矫正机构章
            </a-select-option>
            <a-select-option value="2">
              司法局章
            </a-select-option>
            <a-select-option value="3">
              个人签名章
            </a-select-option>
            <a-select-option value="4">
              司法所章
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="是否启用"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-select v-decorator="['enabled', {rules: [{required: true, message: '请选择是否启用！'}]}]" allow-clear placeholder="请选择是否启用">
            <a-select-option value="0">
              启用
            </a-select-option>
            <a-select-option value="1">
              禁用
            </a-select-option>

          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { signatureMaintenanceEdit } from '@/api/modular/main/signaturemaintenance/signatureMaintenanceManage'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        orgTree: [],
        jzjgName: undefined,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.getOrgTree()
        this.jzjgName = record.jzjgName
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              sealName: record.sealName,
              sealNo: record.sealNo,
              sealType: record.sealType.toString(),
              enabled: record.enabled.toString()
            }
          )
        }, 100)
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      changeOrg(a, b) {
        this.jzjgName = b[0]
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            if (values.sealNo.length !== 20 && values.sealNo.length !== 14) {
              this.$message.error('签章编号必须是20位或者14位的有效编号！')
              this.confirmLoading = true
              this.confirmLoading = false
              return
            }
            if (this.jzjgName !== '') {
              values.jzjgName = this.jzjgName
            }
            signatureMaintenanceEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.jzjgName = ''
        this.visible = false
      }
    }
  }
</script>

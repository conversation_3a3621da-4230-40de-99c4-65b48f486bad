<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="签章名称">
                <a-input v-model="queryParam.sealName" allow-clear placeholder="请输入签章名称"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="签章编号">
                <a-input v-model="queryParam.sealNo" allow-clear placeholder="请输入签章编号"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="签章类型">
                <a-select v-model="queryParam.sealType" allow-clear placeholder="请选择签章类型">
                  <a-select-option value="1">
                    矫正机构章
                  </a-select-option>
                  <a-select-option value="2">
                    司法局章
                  </a-select-option>
                  <a-select-option value="3">
                    个人签名章
                  </a-select-option>
                  <a-select-option value="4">
                    司法所章
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col :md="20" :sm="24">
            </a-col>
            <a-col :md="4" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="reset">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.addForm.add()">新增签章</a-button>
          <x-down
            ref="batchExport"
            @batchExport="batchExport"/>
        </template>
        <span slot="qzlx" slot-scope="text, record">
          <span v-if="record.sealType===1">矫正机构章</span>
          <span v-if="record.sealType===2">司法局章</span>
          <span v-if="record.sealType===3">个人签名章</span>
          <span v-if="record.sealType===4">司法所章</span>
        </span>
        <span slot="positive" slot-scope="text, record">
          <span v-if="record.positive===0">待检测</span>
          <span v-if="record.positive===1">已授权</span>
          <span v-if="record.positive===2">未授权</span>
        </span>
        <span slot="sfqy" slot-scope="text,record">
          <a-popconfirm
            placement="top"
            :title="text===0? '确定停用该签章？':'确定启用该签章？'"
            @confirm="() => editSignStatus(text,record)">
            <a v-if="record.enabled===0">启用</a>
            <a v-if="record.enabled===1">禁用</a>
          </a-popconfirm>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical"/>
          <a @click="$refs.authCheckForm.edit(record)">检测</a>
          <a-divider type="vertical"/>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => signatureMaintenanceDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
      <auth-check-form ref="authCheckForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
import {
  signatureMaintenancePage,
  signatureMaintenanceDelete,
  signatureMaintenanceExport, signatureMaintenanceChangeStatus
} from '@/api/modular/main/signaturemaintenance/signatureMaintenanceManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';
import authCheckForm from '@/views/main/signaturemaintenance/authCheckForm.vue';

export default {
  components: {
    XDown,
    STable,
    addForm,
    editForm,
    authCheckForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],

      // 表头
      columns: [
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '签章名称',
          align: 'center',
          dataIndex: 'sealName'
        },
        {
          ellipsis: true,
          title: '签章编号',
          align: 'center',
          dataIndex: 'sealNo'
        },
        {
          ellipsis: true,
          title: '签章类型',
          align: 'center',
          dataIndex: 'sealType',
          scopedSlots: { customRender: 'qzlx' }
        },
        {
          ellipsis: true,
          title: '是否启用',
          align: 'center',
          dataIndex: 'enabled',
          scopedSlots: { customRender: 'sfqy' }
        },
        {
          ellipsis: true,
          title: '是否授权',
          align: 'center',
          dataIndex: 'positive',
          scopedSlots: { customRender: 'positive' }
        },
        {
          ellipsis: true,
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          ellipsis: true,
          title: '创建人',
          align: 'center',
          dataIndex: 'createUser'
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return signatureMaintenancePage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
    this.columns.push({
      title: '操作',
      width: '150px',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' }
    })
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    editSignStatus(code, record) {
      if (code === 0) {
        record.enabled = 1
      }
      if (code === 1) {
        record.enabled = 0
      }
      signatureMaintenanceChangeStatus(record).then((res) => {
        if (res.success) {
          this.$message.success('修改状态成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error(res.message) // + res.message
        }
      })
    },
    batchExport() {
      signatureMaintenanceExport().then((res) => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    signatureMaintenanceDelete(record) {
      signatureMaintenanceDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    reset() {
      this.queryParam = {}
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

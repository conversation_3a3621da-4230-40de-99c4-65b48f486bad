<template>
  <a-modal
    title="签章授权检测结果"
    :width="700"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :bodyStyle="{ padding: '24px' }"
  >
    <div class="auth-check-container">
      <!-- 检测结果状态卡片 -->
      <a-row :gutter="16" class="status-cards">
        <a-col :span="12">
          <a-card size="small" class="status-card">
            <div class="status-item">
              <div class="status-icon">
                <a-icon
                  :type="getStatusIcon(signStatus)"
                  :style="{ color: getStatusColor(signStatus), fontSize: '20px' }"
                />
              </div>
              <div class="status-content">
                <div class="status-label">印章权限</div>
                <div class="status-value" :style="{ color: getStatusColor(signStatus) }">
                  {{ signStatus }}
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card size="small" class="status-card">
            <div class="status-item">
              <div class="status-icon">
                <a-icon
                  :type="getStatusIcon(signInfo)"
                  :style="{ color: getStatusColor(signInfo), fontSize: '20px' }"
                />
              </div>
              <div class="status-content">
                <div class="status-label">印章生效检测</div>
                <div class="status-value" :style="{ color: getStatusColor(signInfo) }">
                  {{ signInfo }}
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 印章图片展示 -->
      <a-card v-if="successStatus" title="印章图片" class="seal-image-card" size="small">
        <div class="seal-image-container">
          <div class="seal-image-wrapper">
            <img
              v-if="imageSrc"
              :src="imageSrc"
              alt="印章图片"
              class="seal-image"
            />
            <a-spin v-else size="large" />
          </div>
        </div>
      </a-card>

      <!-- 无印章图片时的提示 -->
      <a-card v-else title="印章图片" class="seal-image-card no-image" size="small">
        <a-empty description="暂无印章图片">
          <a-icon slot="image" type="file-image" style="font-size: 48px; color: #d9d9d9;" />
        </a-empty>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import {
  signatureMaintenanceCheck
} from '@/api/modular/main/signaturemaintenance/signatureMaintenanceManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        orgTree: [],
        imageSrc: '',
        successStatus: false,
        signInfo: '',
        signStatus: '',
        backInfo: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        signatureMaintenanceCheck({ id: record.id }).then((res) => {
          if (res.success) {
            this.successStatus = true
            this.convertBase64ToImage('data:image/jpeg;base64,' + res.data.sealDetail.picB64)
            this.signStatus = res.data.sealMsg
            this.signInfo = res.data.sealStatusMsg
          } else {
            this.successStatus = false
            this.signStatus = res.data.sealMsg
            this.signInfo = res.data.sealStatusMsg
          }
        })
        this.visible = true
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      convertBase64ToImage(base64ImageData) {
        // 创建Image对象
        const img = new Image();

        // 设置src属性为base64数据
        img.src = base64ImageData;

        // 在图片加载完成后，将图片地址赋值给imageSrc变量
        img.onload = () => {
          this.imageSrc = img.src;
        };

        // 在图片加载失败时，打印错误信息
        img.onerror = (error) => {
          console.error('加载图片出错：', error);
        };
      },
      // 获取状态图标
      getStatusIcon(status) {
        if (!status) return 'question-circle'
        const statusStr = status.toString().toLowerCase()
        if (statusStr.includes('成功') || statusStr.includes('有效') || statusStr.includes('正常')) {
          return 'check-circle'
        } else if (statusStr.includes('失败') || statusStr.includes('无效') || statusStr.includes('异常')) {
          return 'close-circle'
        } else {
          return 'exclamation-circle'
        }
      },
      // 获取状态颜色
      getStatusColor(status) {
        if (!status) return '#999'
        const statusStr = status.toString().toLowerCase()
        if (statusStr.includes('成功') || statusStr.includes('有效') || statusStr.includes('正常')) {
          return '#52c41a'
        } else if (statusStr.includes('失败') || statusStr.includes('无效') || statusStr.includes('异常')) {
          return '#ff4d4f'
        } else {
          return '#faad14'
        }
      },
      changeOrg(_, b) {
        this.jzjgName = b[0]
      },
      handleSubmit () {
        this.handleCancel()
      },
      handleCancel () {
        this.form.resetFields()
        this.$emit('ok')
        this.jzjgName = ''
        this.successStatus = false
        this.visible = false
      }
    }
  }
</script>

<style lang="less" scoped>
.auth-check-container {
  .status-cards {
    margin-bottom: 24px;
  }

  .status-card {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .status-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      .status-icon {
        margin-right: 12px;
        display: flex;
        align-items: center;
      }

      .status-content {
        flex: 1;

        .status-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .status-value {
          font-size: 16px;
          font-weight: 600;
          line-height: 1.2;
        }
      }
    }
  }

  .seal-image-card {
    border-radius: 8px;

    &.no-image {
      .ant-card-body {
        padding: 40px 24px;
      }
    }

    .seal-image-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;

      .seal-image-wrapper {
        position: relative;
        display: inline-block;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        background: #fafafa;
        padding: 16px;

        .seal-image {
          max-width: 280px;
          max-height: 280px;
          width: auto;
          height: auto;
          display: block;
          border-radius: 4px;
        }
      }
    }
  }
}

// 全局样式调整
:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;

  .ant-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;

  .ant-card-head-title {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
  }
}
</style>

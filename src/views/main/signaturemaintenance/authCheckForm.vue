<template>
  <a-modal
    title="签章授权检测结果"
    :width="400"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div>
      <h2>印章权限：{{ signStatus }}</h2>
      <h2>印章生效检测：{{ signInfo }}</h2>
    </div>
    <div>
      <h2>印章图片：</h2>
      <a-avatar v-if="successStatus" :src="imageSrc" shape="square" style="width: 300px; height: 300px;"></a-avatar>
    </div>
  </a-modal>
</template>

<script>
import {
  signatureMaintenanceCheck
} from '@/api/modular/main/signaturemaintenance/signatureMaintenanceManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        orgTree: [],
        imageSrc: '',
        successStatus: false,
        signInfo: '',
        signStatus: '',
        backInfo: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        signatureMaintenanceCheck({ id: record.id }).then((res) => {
          if (res.success) {
            this.successStatus = true
            this.convertBase64ToImage('data:image/jpeg;base64,' + res.data.sealDetail.picB64)
            this.signStatus = res.data.sealMsg
            this.signInfo = res.data.sealStatusMsg
          } else {
            this.successStatus = false
            this.signStatus = res.data.sealMsg
            this.signInfo = res.data.sealStatusMsg
          }
        })
        this.visible = true
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      convertBase64ToImage(base64ImageData) {
        // 创建Image对象
        const img = new Image();

        // 设置src属性为base64数据
        img.src = base64ImageData;

        // 在图片加载完成后，将图片地址赋值给imageSrc变量
        img.onload = () => {
          this.imageSrc = img.src;
        };

        // 在图片加载失败时，打印错误信息
        img.onerror = (error) => {
          console.error('加载图片出错：', error);
        };
      },
      changeOrg(a, b) {
        this.jzjgName = b[0]
      },
      handleSubmit () {
        this.handleCancel()
      },
      handleCancel () {
        this.form.resetFields()
        this.$emit('ok')
        this.jzjgName = ''
        this.successStatus = false
        this.visible = false
      }
    }
  }
</script>

<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>
      <a-form :form="form">
        <a-form-item label="问题" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入问题"
            v-decorator="['topicName', { rules: [{ required: true, message: '请输入问题！' }] }]"
          />
        </a-form-item>
        <a-form-item label="指标" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入指标"
            v-decorator="['indexName', { rules: [{ required: true, message: '请输入指标！' }] }]"
          />
        </a-form-item>
        <a-form-item
          :label="'指标选项' + (index + 1)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          v-for="(item, index) in indicatorOptions"
          :key="index"
          class="indicator-options"
        >
          <a-input
            placeholder="请输入指标选项"
            v-decorator="[`content${index}`, { rules: [{ required: false, message: '请输入指标选项！' }] }]"
          />
          <a-input
            class="score-input"
            placeholder="请输入分数"
            v-decorator="[`itemScore${index}`]"
            @change="validateScore"
          />
          <a-icon type="plus-circle" v-if="index === indicatorOptions.length - 1" class="add-icon" @click="addItems" />
          <a-icon
            type="minus-circle"
            :style="{ right: index === indicatorOptions.length - 1 ? '-60px' : '-30px' }"
            @click="indicatorOptions.splice(index, 1)"
            class="delete-icon"
          />
        </a-form-item>
        <!-- <a-form-item label="总分" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入总分"
            v-decorator="['topicScore', { rules: [{ required: true, message: '请输入总分！' }] }]"
          />
        </a-form-item> -->
        <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入备注"
            v-decorator="['remark', { rules: [{ required: false, message: '请输入备注！' }] }]"
          />
        </a-form-item>

        <!-- <a-form-item label="删除状态， 0：未删除  1：已删除" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入删除状态， 0：未删除  1：已删除"
            v-decorator="[
              'delFlag',
              { rules: [{ required: true, message: '请输入删除状态， 0：未删除  1：已删除！' }] }
            ]"
          />
        </a-form-item> -->
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  paperQuestionBankAdd,
  paperQuestionBankEdit,
  paperQuestionBankDetail
} from '@/api/modular/main/paperquestionbank/paperQuestionBankManage'
export default {
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      indicatorOptions: [
        {
          content: '',
          itemScore: ''
        }
      ],
      title: '新增题库',
      id: ''
    }
  },
  methods: {
    validateScore(e) {
      console.log(e.target.value)
      const reg = /^(0|[1-9]\d*)$/
      if (!reg.test(e.target.value)) {
        this.$message.warning('请输入正确的正整数')
        e.target.value = ''
      }
    },
    addItems() {
      this.indicatorOptions.push({
        content: '',
        itemScore: ''
      })
    },
    // 初始化方法
    add(record) {
      this.visible = true
      this.title = '新增题库'
      this.id = ''
    },
    edit(record) {
      this.visible = true
      this.title = '编辑题库'
      this.id = record.id
      paperQuestionBankDetail({ id: record.id }).then(res => {
        if (res.success) {
          this.$nextTick(() => {
            this.indicatorOptions =
              res.data.itemList && res.data.itemList.length > 0 ? res.data.itemList : [{ content: '', itemScore: '' }]
            res.data.itemList.forEach((item, index) => {
              this.$nextTick(() => {
                this.form.setFieldsValue({
                  [`content${index}`]: item.content,
                  [`itemScore${index}`]: item.itemScore
                })
              })
            })
            this.form.setFieldsValue(record)
            console.log(this.indicatorOptions, this.form.getFieldsValue())
          })
        }
      })
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            console.log(values[key], 'values[key]')
            if (typeof values[key] === 'object' && values[key]) {
              values[key] = JSON.stringify(values[key])
            }
          }
          console.log(values, 'values')
          values.itemList = []
          this.indicatorOptions.forEach((item, index) => {
            const obj = {
              content: values[`content${index}`],
              itemScore: values[`itemScore${index}`],
              serialNumber: index + 1
            }
            values.itemList.push(obj)
          })
          if (this.id === '') {
            paperQuestionBankAdd(values)
              .then(res => {
                if (res.success) {
                  this.$message.success('新增成功')
                  this.confirmLoading = false
                  this.handleCancel()
                } else {
                  this.$message.error('新增失败') // + res.message
                }
              })
              .finally(res => {
                this.confirmLoading = false
              })
          } else {
            values.id = this.id
            paperQuestionBankEdit(values)
              .then(res => {
                if (res.success) {
                  this.$message.success('编辑成功')
                  this.confirmLoading = false

                  this.handleCancel()
                } else {
                  this.$message.error('编辑失败') // + res.message
                }
              })
              .finally(res => {})
          }
          this.confirmLoading = false
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.$emit('ok')
      this.id = ''
      this.indicatorOptions = [
        {
          content: '',
          itemScore: ''
        }
      ]
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.indicator-options {
  display: flex;
  position: relative;
  align-items: center;
  /deep/.ant-form-item-children {
    display: flex;
    align-items: center;
    .score-input {
      width: 20%;
      margin-left: 10px;
    }
    .add-icon {
      position: absolute;
      right: -40px;
      color: #1690ff;
      font-size: 20px;
      cursor: pointer;
      margin-right: 10px;
    }
    .delete-icon {
      position: absolute;
      right: -40px;
      color: #b1b1b1;
      font-size: 20px;
      cursor: pointer;
    }
  }
}
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="问题描述">
                <a-input v-model="queryParam.topicName" allow-clear placeholder="请输入问题" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="指标">
                <a-input v-model="queryParam.indexName" allow-clear placeholder="请输入指标" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button
                  style="margin-left: 8px"
                  @click="
                    queryParam = {}
                    $refs.table.refresh(true)
                  "
                  >重置</a-button
                >
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.addForm.add()">新增题库</a-button>
          <a-popconfirm
            :disabled="!selectedRowKeys.length"
            placement="topRight"
            title="确认删除？"
            @confirm="() => paperQuestionBankBatchDelete(selectedRows[0])"
          >
            <a-button :disabled="!selectedRowKeys.length" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
          <x-down ref="batchExport" @batchExport="batchExport" />
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.addForm.edit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => paperQuestionBankDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
        <span slot="items" slot-scope="text, record">
          <ellipsis :length="25" tooltip>{{ record.items }}</ellipsis>
        </span>
        <span slot="topicName" slot-scope="text, record">
          <ellipsis :length="25" tooltip>{{ record.topicName }}</ellipsis>
        </span>
        <span slot="indexName" slot-scope="text, record">
          <ellipsis :length="25" tooltip>{{ record.indexName }}</ellipsis>
        </span>
        <span slot="remark" slot-scope="text, record">
          <ellipsis :length="25" tooltip>{{ record.remark }}</ellipsis>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown, Ellipsis } from '@/components'
import {
  paperQuestionBankPage,
  paperQuestionBankDelete,
  paperQuestionBankExport
} from '@/api/modular/main/paperquestionbank/paperQuestionBankManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
export default {
  components: {
    STable,
    addForm,
    editForm,
    Ellipsis,
    XDown
  },
  data() {
    return {
      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        {
          title: '序号',
          align: 'center',
          dataIndex: '',
          width: '70px',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '问题',
          align: 'center',
          dataIndex: 'topicName',
          width: '18%',
          scopedSlots: { customRender: 'topicName' }
        },
        {
          title: '指标',
          align: 'center',
          dataIndex: 'indexName',
          width: '18%',
          scopedSlots: { customRender: 'indexName' }
        },
        {
          title: '指标选项',
          align: 'center',
          dataIndex: 'items',
          width: '18%',
          scopedSlots: { customRender: 'items' }
        },
        {
          title: '总分',
          align: 'center',
          width: '7%',
          dataIndex: 'topicScore'
        },
        {
          title: '备注',
          align: 'center',
          dataIndex: 'remark',
          width: '18%',
          scopedSlots: { customRender: 'remark' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return paperQuestionBankPage(Object.assign(parameter, this.queryParam)).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.columns.push({
      title: '操作',
      width: '120px',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' }
    })
  },
  methods: {
    paperQuestionBankBatchDelete() {
      paperQuestionBankDelete({ idList: this.selectedRowKeys }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    paperQuestionBankDelete(record) {
      paperQuestionBankDelete({ idList: [record.id] }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    batchExport() {
      paperQuestionBankExport(this.queryParam).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

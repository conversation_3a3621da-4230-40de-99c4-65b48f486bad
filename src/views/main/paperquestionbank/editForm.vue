<template>
  <a-modal
    title="编辑题库"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="问题名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入问题名称" v-decorator="['topicName', {rules: [{required: true, message: '请输入问题名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指标"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入指标" v-decorator="['indexName', {rules: [{required: true, message: '请输入指标！'}]}]" />
        </a-form-item>
        <a-form-item
          label="总分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入总分" v-decorator="['topicScore', {rules: [{required: true, message: '请输入总分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入备注" v-decorator="['remark', {rules: [{required: true, message: '请输入备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="指标选项"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入指标选项" v-decorator="['items', {rules: [{required: true, message: '请输入指标选项！'}]}]" />
        </a-form-item>
        <a-form-item
          label="删除状态， 0：未删除  1：已删除"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入删除状态， 0：未删除  1：已删除" v-decorator="['delFlag', {rules: [{required: true, message: '请输入删除状态， 0：未删除  1：已删除！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { paperQuestionBankEdit } from '@/api/modular/main/paperquestionbank/paperQuestionBankManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              topicName: record.topicName,
              indexName: record.indexName,
              topicScore: record.topicScore,
              remark: record.remark,
              items: record.items,
              delFlag: record.delFlag
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            paperQuestionBankEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <a-drawer
    title="交付衔接协同"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">案件信息</div>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数据交换类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['sjlx']"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数据去向" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['sjly']"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="交换日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['jhrq']"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数据数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['tbsl']"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="交换方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['jhfs']"/>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="数据交换说明" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
                <a-textarea disabled v-decorator="['jhsm']" :auto-size="{ minRows: 3, maxRows: 6 }"/>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <div class="cus-title-d">数据信息</div>
            </a-col>
            <a-col :span="12">
              <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="xm"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-tree-select
                  v-model="jzdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <s-table
                ref="table"
                :columns="columns"
                :data="loadData"
                :alert="false"
                :rowKey="(record) => record.id">
                <span slot="timeFormat" slot-scope="text">
                  {{ text === null ? '' : moment(text).format('YYYY-MM-DD HH:mm') }}
                </span>
              </s-table>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
    </div>
  </a-drawer>
</template>

<script>
  import { STable } from '@/components';
  import { logPage } from '@/api/modular/main/acceptrecommit/acceptRecommitManage';
  import { getOrgTree } from '@/api/modular/system/orgManage';
  import moment from 'moment';
  import { mapGetters } from 'vuex';
  export default {
    components: { STable },
    data () {
      return {
        drawerWidth: 1000,
        xm: '',
        jzdw: '',
        labelCol: { xs: { span: 24 }, sm: { span: 5 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 15 } },
        columns: [
         { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
         { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jzjg_name' },
         { ellipsis: true, title: '接收单位', align: 'center', dataIndex: 'jsdw' },
         { ellipsis: true, title: '交换时间', align: 'center', dataIndex: 'fasongshijian', scopedSlots: { customRender: 'timeFormat' } }
        ],
        loadData: parameter => {
          parameter.xm = this.xm
          parameter.jzdw = this.jzdw
          parameter.fsrq = this.jhrq
          return logPage(Object.assign(parameter)).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    created() {
      getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        try {
          this.$refs.table.refresh()
        } catch (e) {
          // 首次进入界面，因表格加载顺序，会抛异常，我们不予理会
        }
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              sjlx: record.sjlx,
              sjly: record.sjly,
              jhrq: record.jhrq,
              tbsl: record.tbsl,
              jhfs: record.jhfs,
              jhsm: record.jhsm
            }
          )
        }, 100)
        this.jhrq = record.jhrq
        this.jzdw = this.userInfo.loginEmpInfo.orgId
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
&::after {
   display: block;
   content: '';
   width: 6px;
   height: 60%;
   border-radius: 5px;
   position: absolute;
   left: 0;
   top: 50%;
   transform: translateY(-50%);
   background: #1990ff;
 }
}
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="交换类型">
                <a-select v-model="queryParam.sjlx" placeholder="请选择交换类型">
                  <a-select-option v-for="(item, index) in sjlxDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="交换日期">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator"></template>
        <span slot="action" slot-scope="text, record">
          <a v-if="record.path === ''" @click="$refs.editForm.edit(record)">查看详情</a>
          <a v-else @click="$router.push({path:record.path+record.jhrq})">跳转详情</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { ywxtCountPage } from '@/api/modular/main/ywxt/ywxtManage'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      editForm
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
         { ellipsis: true, title: '数据交换类型', align: 'center', dataIndex: 'sjlx' },
         { ellipsis: true, title: '交换类型', align: 'center', dataIndex: 'jhlx' },
         { ellipsis: true, title: '数据去向/来源', align: 'center', dataIndex: 'sjly' },
         { ellipsis: true, title: '交换方式', align: 'center', dataIndex: 'jhfs' },
         { ellipsis: true, title: '数据交换说明', align: 'center', dataIndex: 'jhsm' },
         { ellipsis: true, title: '同步数量', align: 'center', dataIndex: 'tbsl' },
         { ellipsis: true, title: '交换日期', align: 'center', dataIndex: 'jhrq' },
         { ellipsis: true, title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return ywxtCountPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            res.data.rows.forEach((item) => {
              item.tbsl = item.tbsl + '条'
              const values = this.cc.filter(i => i.code === item.sjlx)
              if (values.length === undefined || values.length === 0) {
                item.sjlx = ''
                item.jhlx = ''
                item.sjly = ''
                item.jhfs = ''
                item.jhsm = ''
                item.path = ''
              } else {
                item.sjlx = values[0].context.sjlx
                item.jhlx = values[0].context.jhlx
                item.sjly = values[0].context.sjly
                item.jhfs = values[0].context.jhfs
                item.jhsm = values[0].context.jhsm
                item.path = values[0].context.path
              }
            })
            return res.data
          })
        },
        sjlxDropDown: [],
        cc: [
          { code: 'T01',
            context: {
              sjlx: '解除终止矫正信息',
              jhlx: '对外共享',
              sjly: '政法一体化中间平台',
              jhfs: '定时增量推送',
              jhsm: '每日定期将解矫的社区矫正对象增量推送至检察机关等单位',
              path: '/terminate?date='
            } },
          { code: 'T02',
            context: {
              sjlx: '社区矫正对象信息共享',
              jhlx: '对外共享',
              sjly: '省公安厅及公安分局',
              jhfs: '定时增量推送',
              jhsm: '每日定期将在矫的社区矫正对象增量推送至对应公安分局单位',
              path: ''
            } },
          { code: 'T03',
            context: {
              sjlx: '公安处罚信息',
              jhlx: '数据获取',
              sjly: '省公安厅及公安分局',
              jhfs: '实时调用',
              jhsm: '公安发现社矫存在处罚信息收实时推送至社区矫正系统',
              path: '/acceptRecommit?date='
            } },
          { code: 'T04',
            context: {
              sjlx: '矫正衔接信息',
              jhlx: '数据获取',
              sjly: '政法一体化中间平台',
              jhfs: '协同办案',
              jhsm: '获取社区矫正决定机关实时推送的社区矫正对象文书及数据信息',
              path: '/jzdxxj?date='
            } },
          { code: 'T05',
            context: {
              sjlx: '矫正衔接反馈',
              jhlx: '数据获取',
              sjly: '政法一体化中间平台',
              jhfs: '协同办案',
              jhsm: '反馈社区矫正对象衔接回执至社区矫正决定机关等单位',
              path: '/jzdxxj?date='
            } },
          { code: 'T06',
            context: {
              sjlx: '调查评估信息',
              jhlx: '数据获取',
              sjly: '政法一体化中间平台',
              jhfs: '协同办案',
              jhsm: '获取委托单位实时推送的委托调查评估信息',
              path: '/acceptInvestInfo?date='
            } },
          { code: 'T07',
            context: {
              sjlx: '调查评估反馈',
              jhlx: '数据获取',
              sjly: '政法一体化中间平台',
              jhfs: '协同办案',
              jhsm: '反馈调查评估结果意见至委托及检察单位',
              path: '/acceptInvestInfo?date='
            } },
          { code: 'T08',
            context: {
              sjlx: '信访信息',
              jhlx: '数据获取',
              sjly: '省信访局',
              jhfs: '实时调用',
              jhsm: '信访局发现社区矫正对象存在信访情形时实时推送',
              path: '/xfxt?date='
            } }
        ],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.sjlxDropDown = this.$options.filters['dictData']('sjlx')
    },
    methods: {
      switchingDate() {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = dates[0].format('YYYY-MM-DD')
          this.queryParam.searchEndTime = dates[1].format('YYYY-MM-DD')
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

<template>
  <a-modal
    title="终止流程"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item style="display: none;">
          <a-input v-decorator="['id']" />
        </a-form-item>
        <a-form-item label="流程终止原因" :labelCol="{ span: 5 }" :wrapperCol="{ span: 15 }">
          <a-textarea :disabled="disabled" :rows="4" v-decorator="['terminateReason', {rules: [{required: true, message: '请输入流程终止原因！'}]}]"/>
        </a-form-item>
      </a-form></a-spin>
  </a-modal>
</template>

<script>
import { acceptInvestInfoTerminate } from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage';

export default {
  name: 'Terminate',
  data () {
    return {
      form: this.$form.createForm(this),
      visible: false,
      disabled: false,
      confirmLoading: false
    }
  },
  methods: {
    open(record, disabled) {
      this.visible = true
      this.disabled = disabled
      setTimeout(() => {
        this.form.setFieldsValue(
          {
            id: record.id,
            terminateReason: record.terminateReason
          }
        )
      }, 100)
    },
    handleSubmit () {
      if (this.disabled) {
        this.visible = false
        return
      }
      const { form: { validateFields } } = this
      this.confirmLoading = true
      const terminateThis = this
      this.$confirm({
        title: '流程终止确认',
        content: h => <div style="color:red;"> 该功能用于调查评估中途终止进行信息备注。如您需终止流程，建议先线下通知对应委托单位，流程终止不会发送对应信息至对应委托单位。</div>,
        onOk() {
          console.log('OK');
          validateFields((errors, values) => {
            if (!errors) {
              acceptInvestInfoTerminate(values).then((res) => {
                if (res.success) {
                  terminateThis.$message.success('成功')
                  terminateThis.visible = false
                  terminateThis.confirmLoading = false
                  terminateThis.$emit('ok', values)
                  terminateThis.form.resetFields()
                } else {
                  terminateThis.$message.error('失败：' + res.message)
                }
              }).finally(() => {
                terminateThis.confirmLoading = false
              })
            } else {
              terminateThis.confirmLoading = false
            }
          })
        },
        onCancel() {
          console.log('Cancel');
          terminateThis.confirmLoading = false
          terminateThis.visible = false
        }
      });
    },
    handleCancel () {
      this.visible = false
    }
  }
}
</script>

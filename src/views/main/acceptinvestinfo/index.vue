<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.bgrxm" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据状态">
                <a-select v-model="queryParam.zt" placeholder="请选择状态" @change="$forceUpdate()">
                  <a-select-option v-for="(item, index) in ztDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="送达时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <a-tree-select
                  v-model="queryParam.jsdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据来源">
                <a-select v-model="queryParam.sjlylx" placeholder="请选择数据来源">
                  <a-select-option value="20">法院</a-select-option>
                  <a-select-option value="30">检察院</a-select-option>
                  <a-select-option value="4">公安</a-select-option>
                  <a-select-option value="61">监狱</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="协同区域">
                <a-select v-model="queryParam.dataFrom" placeholder="请选择数据来源">
                  <a-select-option value="1">本省</a-select-option>
                  <a-select-option value="2">外省</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="r => r.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" :disabled="!hasAppoint" icon="arrow-down" @click="accept()">接收</a-button>
          <a-button type="primary" :disabled="!hasFeedback" icon="rollback" @click="feedback()">反馈</a-button>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => acceptInvestInfoDelete()">
            <a-button :disabled="!(selectedRowKeys.length === 1)" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
          <a-button
            v-if="hasPerm('sysPos:add')"
            type="primary"
            :disabled="!(selectedRowKeys.length === 1)"
            @click="setStatus">修改状态</a-button>
          <a-button
            type="primary"
            :disabled="!(selectedRowKeys.length === 1)"
            @click="$refs.terminate.open(selectedRows[0], false)">流程终止</a-button>
        </template>
        <span slot="action" slot-scope="t, r">
          <a type="primary" icon="plus" @click="$refs.AcceptForm.add(r, 'view')">接收详情</a>
          <a-divider type="vertical" v-if="r.zt === '4'" />
          <a v-if="r.zt === '4'" @click="$refs.FeedbackForm.add(r, 'view')">反馈详情</a>
          <!--          <a v-if="r.zt === '4'" @click="$refs.feedbackForm31001.init(r,orgTree,true)">反馈详情</a>-->
          <a-divider v-if="r.zt === '4'||r.zt === '2'" type="vertical" />
          <a v-if="r.zt === '4'||r.zt === '2'" @click="$refs.linkFlow.view({ ...r})">消息链路</a>
          <a-divider type="vertical" v-if="r.zt === '98'" />
          <a v-if="r.zt === '98'" @click="$refs.terminate.open(r, true)">终止详情</a>
        </span>
        <span slot="zt" slot-scope="text, r1">
          {{ 'accept_data_status' | dictType(text) }}
          <a-tag style="margin-left: 10px;" color="blue" v-if="r1.returned === '1'">数据质检</a-tag>
        </span>
        <span slot="sjlylx" slot-scope="text">
          {{ 'xtjgdm' | dictType(text) }}
        </span>
        <span slot="timeFormat" slot-scope="text">
          {{ text === null ? '' : moment(text).format('YYYY-MM-DD HH:mm') }}
        </span>
      </s-table>

      <AcceptForm ref="AcceptForm" @ok="handleOk" />
      <FeedbackForm ref="FeedbackForm" @ok="handleOk" />
      <feedback-form31001 ref="feedbackForm31001" @ok="handleOk" />
      <Terminate ref="terminate" @ok="handleOk" />
      <LinkFlow ref="linkFlow" />
    </a-card>
    <a-modal
      title="修改状态"
      :width="900"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-spin :spinning="confirmLoading">
        <a-form>
          <a-form-item label="数据状态" :labelCol="{ span: 5 }" :wrapperCol="{ span: 15 }">
            <a-select v-model="updateSelect.zt" placeholder="请选择状态">
              <a-select-option v-for="(item, index) in ztDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="调查机构" :labelCol="{ span: 5 }" :wrapperCol="{ span: 15 }">
            <sh-correction-org-tree :params="{ level: 4 }" v-model="updateSelect.dcdw"/>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  acceptInvestInfoPage,
  acceptInvestInfoDelete,
  acceptInvestInfoSetStatus
} from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
import AcceptForm from './AcceptForm.vue'
import FeedbackForm from '@/views/main/acceptinvestinfo/FeedbackForm'
import feedbackForm31001 from '@/views/main/acceptinvestinfo/feedbackForm31001'
import Terminate from '@/views/main/acceptinvestinfo/Terminate'
import LinkFlow from '@/views/main/sendplacechange/components/LinkFlow.vue'
export default {
  components: {
    STable,
    AcceptForm,
    FeedbackForm,
    feedbackForm31001,
    Terminate,
    LinkFlow
  },
  data() {
    return {
      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        { ellipsis: true, title: '数据状态', align: 'left', dataIndex: 'zt', scopedSlots: { customRender: 'zt' } },
        {
          ellipsis: true,
          title: '数据来源',
          align: 'center',
          dataIndex: 'sjlylx',
          scopedSlots: { customRender: 'sjlylx' }
        },
        { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'bgrxm' },
        { ellipsis: true, title: '接收单位', align: 'center', dataIndex: 'jsdwmc' },
        { ellipsis: true, title: '推送单位', align: 'center', dataIndex: 'tsdwmc' },
        {
          ellipsis: true,
          title: '送达时间',
          align: 'center',
          dataIndex: 'sdsj',
          scopedSlots: { customRender: 'timeFormat' }
        },
        {
          ellipsis: true,
          title: '反馈时间',
          align: 'center',
          dataIndex: 'fksj',
          scopedSlots: { customRender: 'timeFormat' }
        },
        { ellipsis: false, title: '查看', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return acceptInvestInfoPage(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      record: {},
      orgTree: [],
      ztDropDown: [],
      zt: '',
      updateSelect: {},
      visible: false,
      confirmLoading: false,
      hasFeedback: false,
      hasAppoint: false
    }
  },
  watch: {
    $route(to, from) {
      // 对路由变化作出响应...
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.queryParam.zt = to.query.zt
      this.$refs.table.refresh()
    }
  },
  created() {
    this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
    if (this.$route.query.zt) {
      this.queryParam.zt = this.$route.query.zt
    }
    getOrgTree().then(res => {
      if (res.success) {
        this.orgTree = res.data
      } else {
        this.$message.warning(res.message)
      }
    })
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      delete obj.dates
      return obj
    },
    acceptInvestInfoDelete() {
      acceptInvestInfoDelete(this.selectedRows[0]).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    accept() {
      if (this.selectedRows.length === 1) {
        this.$refs.AcceptForm.add(this.selectedRows[0])
      } else {
        this.$message.error('请选择一条记录')
      }
      this.$refs.table.clearSelected()
    },
    feedback() {
      if (this.selectedRows.length === 1) {
        // this.$refs.FeedbackForm.add(this.selectedRows[0])
        this.$refs.feedbackForm31001.init(this.selectedRows[0], this.orgTree, false)
      } else {
        this.$message.error('请选择一条记录')
      }
      this.$refs.table.clearSelected()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.hasFeedback = false
      this.hasAppoint = false
      if (this.selectedRows.length === 1) {
        if (this.selectedRows[0].zt === '0') {
          // 待接收
          this.hasAppoint = true
        }
        if (this.selectedRows[0].zt === '1') {
          // 待反馈
          this.hasFeedback = true
        }
      }
    },
    setStatus() {
      if (this.selectedRows.length === 1) {
        this.visible = true
        this.updateSelect = { zt: this.selectedRows[0].zt, id: this.selectedRows[0].id, dcdw: this.selectedRows[0].dcdw }
      } else {
        this.$message.error('请选择一条记录')
      }
    },
    handleSubmit() {
      acceptInvestInfoSetStatus(this.updateSelect).then(res => {
        if (res.success) {
          this.$message.success('操作成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('操作失败') // + res.message
        }
      })
      this.visible = false
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

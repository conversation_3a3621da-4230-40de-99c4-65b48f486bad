<template>
  <a-drawer
    title="调查评估协同"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel"
  >
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper">
          <div class="accept-left">
            <BaseInfo :readOnly="readOnly" ref="BaseInfo" :baseObj="detailObj" />
          </div>
          <div class="accept-right">
            <!-- <div class="titlesd">流程记录</div> -->
            <StepVue :steps="steps" />
          </div>
        </section>

        <div class="cus-title-d" style="margin-top: 20px;">区县司法局审核信息</div>

        <AduitForm :readOnly="readOnly" :baseObj="detailObj" ref="AduitForm" />
        <a-alert style="margin-left: 12.3%;" message="说明: 请认真核对一体化协同的调查评估委托。如选择“接收”后数据会自动指派至“执法办案”的调查评估模块的“司法所接收”阶段；选择“退回”则不需要填写上述必填的调查评估信息，退回案件建议线下联系一下委托单位。" type="info" show-icon />

      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px'
      }"
    >
      <a-button style="marginRight: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
// import { sendApplyArrestAdd, sendApplyArrestEdit } from '@/api/modular/main/sendapplyarrest/sendApplyArrestManage'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
import { sysDictDataTree } from '@/api/modular/system/dictDataManage'

import { fileList } from '@/api/modular/system/fileManage'
// 基本信息
import BaseInfo from '@/views/main/acceptinvestinfo/components/BaseInfo'

// 审核信息
import AduitForm from '@/views/main/acceptinvestinfo/components/AduitForm'
// 步骤
import StepVue from '@/views/main/acceptcorrectionobject/components/Step.vue'
import {
  acceptInvestInfoEdit,
  acceptInvestInfoDetail, acceptInvestInfoStep
} from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage'

export default {
  components: { BaseInfo, AduitForm, StepVue },
  data() {
    return {
      drawerWidth: 1000,
      steps: [],
      readOnly: false,
      extOrgInfoData: {}, // 矫正决定机关
      zmTreeDropDown: [],
      record: {},
      detailObj: {},
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  provide() {
    return {
      parentData: this.$data
    }
  },
  created() {
    this.drawerWidth = window.innerWidth - 200
    this.getExtOrgInfoList()
  },
  methods: {
    // 外部单位信息列表
    getExtOrgInfoList() {
      extOrgInfoList().then((res) => {
        // 法院
        const fy = []
        // 公安
        const ga = []
        // 检察院
        const jcy = []
        // 监狱
        const jy = []
        res.data.forEach((v, i) => {
          if (v.type === 20) {
            fy.push(v)
          } else if (v.type === 40) {
            ga.push(v)
          } else if (v.type === 30) {
            jcy.push(v)
          } else if (v.type === 61) {
            jy.push(v)
          }
        })
        this.extOrgInfoData = { fy, ga, jcy, jy }
      })
      sysDictDataTree({ dictTypeId: '1681127536492331009' }).then(res => {
        if (res.success) {
          this.zmTreeDropDown = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    async getFileList() {
      console.log(this.record)
      const { data } = await fileList({ ids: this.record.hzcl })
      console.log(data)
      this.detailObj.hzcl = data.map(item => {
        return { ...item, id: item.uid }
      })
      this.form.setFieldsValue({
        hzcl: data.map(item => {
          return { ...item, id: item.uid }
        })
      })
    },
    // 初始化方法
    add(record, type) {
      this.record = record
      this.readOnly = !!type;
      this.visible = true
      this.getDetail()
    },
    async getDetail() {
      this.detailObj = {}
      const { data } = await acceptInvestInfoDetail({
        id: this.record.id
      })

      this.detailObj = { ...data }
      this.$nextTick(() => {
        this.$refs.BaseInfo.setValues()
        this.$refs.AduitForm.setValues()
      })
      // 流程
      acceptInvestInfoStep({ id: this.record.id }).then(res => {
        if (res.success) {
          this.steps = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },

    edit(record, type) {
      if (record) {
        if (type) {
          this.readOnly = true
        }
        this.visible = true
        this.record = { ...record }

        this.getFileList()
      }
    },
    /**
     * 提交表单
     */
    async handleSubmit() {
         // 审核表单
            const data = await this.$refs.AduitForm.handleSubmit()
            console.log(data.shjg)
      // 基本信息
      let data1 = null
      if (data.shjg === '1') {
         data1 = await this.$refs.BaseInfo.handleSubmit()
      } else {
        data1 = await this.$refs.BaseInfo.getValues()
      }

      data.shjgName = this.$options.filters['dictType']('shjg', data.shjg)
      data1.fjx = data1.fjx ? data1.fjx.toString() : ''

      this.confirmLoading = true
      const postData = {
        ...data1,
        ...data,
        zt: 1,
        hzcl: data.hzcl ? data.hzcl.map(item2 => item2.id).toString() : ''
      }
      postData.id = this.record.id
      postData.chargesList = postData.chargesList.map(charge => {
        delete charge.id
        return {
          contactId: this.record.id,
          // contactId: charge.contactId, // 主记录id
          zm: charge.zm || charge.value, // 罪名code 可选值为罪名树中pid不为0的项
          zmName: charge.zmName, // 罪名value
          fzlx: charge.fzlx, // 犯罪类型code 值为选中罪名上级
          fzlxName: charge.fzlxName, // 犯罪类型value
          jtzm: charge.jtzm, // 具体罪名 文本框value
          ...charge
        }
      })

      console.log(data, data1, postData)

      // return false

      acceptInvestInfoEdit(postData)
        .then(res => {
          if (res.success) {
            this.$message.success('操作成功')
            this.confirmLoading = false
            this.$emit('ok', postData)
            this.handleCancel()
          } else {
            this.$message.error('操作失败:' + res.message) // + res.message
          }
        })
        .finally(res => {
          this.confirmLoading = false
        })
      // const { form: { validateFields } } = this
      // this.confirmLoading = true
      // validateFields((errors, values) => {
      //   if (!errors) {
      //     // for (const key in values) {
      //     //   if (typeof (values[key]) === 'object') {
      //     //     values[key] = JSON.stringify(values[key])
      //     //   }
      //     // }
      //     const postData = {
      //       ...values
      //     }
      //     postData.xm = postData.xm.xm
      //     postData.ws = postData.ws.map(item => item.id).toString()
      //     postData.hjd = postData.hjd.selectedDistrict
      //     postData.xzd = postData.xzd.selectedDistrict

      //     console.log(postData)
      //     if (this.record) {
      //       postData.id = this.record.id
      //       sendApplyArrestEdit(postData).then((res) => {
      //       if (res.success) {
      //         this.$message.success('操作成功')
      //         this.confirmLoading = false
      //         this.$emit('ok', values)
      //         this.handleCancel()
      //       } else {
      //         this.$message.error('操作失败' + res.message)// + res.message
      //       }
      //     }).finally((res) => {
      //       this.confirmLoading = false
      //     })
      //       return false
      //     }
      //     sendApplyArrestAdd(postData).then((res) => {
      //       if (res.success) {
      //         this.$message.success('新增成功')
      //         this.confirmLoading = false
      //         this.$emit('ok', values)
      //         this.handleCancel()
      //       } else {
      //         this.$message.error('新增失败' + res.message)// + res.message
      //       }
      //     }).finally((res) => {
      //       this.confirmLoading = false
      //     })
      //   } else {
      //     this.confirmLoading = false
      //   }
      // })
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.accept-wrapper {
  display: flex;
  width: 100%;
  .accept-left {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
  }
  .accept-right {
    border-left: 1px solid #e8e8e8;
    // margin-top: 44px;
    position: relative;
    max-width: 300px;
    // padding-top: 20px;

    // .titlesd{
    //   position: absolute;
    //   top: -44px;
    //   left: -1px;
    //   line-height: 43px;
    //   border-bottom: 1px solid #e8e8e8;
    //   width: 100%;
    //   font-size: 16px;
    // font-family: Microsoft YaHei, Microsoft YaHei;
    // font-weight: bold;
    // color: #1890FF;
    // padding-left: 20px;
    // }
  }
}
/deep/ .ant-drawer-body {
  padding-top: 0;
  padding-bottom: 78px;
}

.cus-title {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;

  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

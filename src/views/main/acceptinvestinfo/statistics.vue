<template>
  <div>
    <a-card :bordered="false" :bodyStyle="{ 'padding-bottom': '0px', 'margin-bottom': '10px' }">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="送达时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <a-tree-select
                  v-model="queryParam.jsdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="handleOk" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <div style="margin:10px">
      <a-button @click="type=1;handleOk">社区矫正对象衔接</a-button>
      <a-button @click="type=2;handleOk">调查评估协同</a-button>
    </div>
    <a-card :bordered="false">
      <s-table
        v-show="type === 1"
        ref="tableBase"
        :columns="columns"
        :data="loadDataBase"
        :showPagination="false">
        <template class="table-operator" slot="operator">
          <x-down
            ref="batchExport"
            @batchExport="batchExportBase"/>
        </template>
      </s-table>
      <s-table
        v-show="type === 2"
        ref="tableInv"
        :columns="columns"
        :data="loadDataInv"
        :showPagination="false">
        <template class="table-operator" slot="operator">
          <x-down
            ref="batchExport"
            @batchExport="batchExportInv"/>
        </template>
      </s-table>
    </a-card>
  </div>
</template>
<script>
  import { STable, XDown } from '@/components'
  import moment from 'moment'
  import {
    acceptInvestInfoStatistics,
    acceptInvestInfoStatisticsExport
  } from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage'
  import {
    acceptBaseInfoStatistics,
    acceptBaseInfoStatisticsExport
  } from '@/api/modular/main/acceptcorrectionobject/acceptCorrectionObjectManage';
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      XDown
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        type: 1,
        // 表头
        columns: [
         { title: '矫正单位', align: 'center', dataIndex: 'orgName' },
         { title: '接收情况',
           children: [
           { title: '已接收', align: 'center', customRender: (val, row) => row.zt1 - row.zt2 - row.zt3 },
           { title: '拒接收', align: 'center', dataIndex: 'zt3' },
           { title: '待接收', align: 'center', dataIndex: 'zt2' },
           { title: '接收总计', align: 'center', dataIndex: 'zt1' }
           ] },
         { title: '反馈情况',
           children: [
             { title: '待反馈', align: 'center', dataIndex: 'zt4' },
             { title: '已反馈', align: 'center', dataIndex: 'zt5' },
             { title: '反馈总计', align: 'center', customRender: (val, row) => row.zt4 + row.zt5 }
           ] },
         { title: '协同总计', align: 'center', customRender: (val, row) => row.zt1 }
        ],
        loadDataBase: parameter => {
          return acceptBaseInfoStatistics(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        loadDataInv: parameter => {
          return acceptInvestInfoStatistics(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: []
      }
    },
    created () {
      getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      batchExportBase () {
        acceptBaseInfoStatisticsExport(this.switchingDate()).then((res) => {
          this.$refs.batchExport.downloadfile(res)
        })
      },
      batchExportInv () {
        acceptInvestInfoStatisticsExport(this.switchingDate()).then((res) => {
          this.$refs.batchExport.downloadfile(res)
        })
      },
      handleOk () {
        if (this.type === 1) {
          this.$refs.tableBase.refresh()
        } else if (this.type === 2) {
          this.$refs.tableInv.refresh()
        }
      }
    }
  }
</script>

<template>
  <a-drawer
    title="反馈信息"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <div v-if="visible" style="padding-bottom: 0;">

      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper class1" >
          <div class="accept-left">
            <a-form :form="form">
              <a-row :gutter="24">
                <div class="cus-title" style="margin-top: 10px;">案件信息</div>
                <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                <a-col :span="12">
                  <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['tyfh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['ajbh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['ajmc']" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <div class="cus-title" style="margin-top: 10px;">反馈信息</div>
                <a-col :span="12">
                  <a-form-item label="调查单位（区县）" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <sh-correction-org-tree disabled v-decorator="['dcdw']"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['dcdwlxr', { rules: [{ required: true, message: '请输入！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查单位联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['dcdwlxdh', { rules: [{ required: true, message: '请输入！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查评估文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['dcpgwsh', { rules: [{ required: true, message: '请输入！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <sh-select
                      dictType="dcpgyj"
                      style="width: 100%;"
                      placeholder="请选择"
                      :disabled="readOnly"
                      v-decorator="['dcpgjl', { rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查评估日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="readOnly"
                      valueFormat="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                      placeholder="请选择"
                      v-decorator="['dcpgrq', { initialValue: undefined, rules: [{ required: true, message: '请选择！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="住所地" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <sh-cascader-distpicker
                      :disabled="readOnly"
                      v-decorator="['sjjzdqh', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="住所地详细地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['sjjzdmx', { rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="家庭监护人或保证人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['jhrxm', { rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="监护人与罪犯的关系" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['jhryzfgx', { rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="反馈委托机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="true"
                      placeholder="请输入"
                      v-decorator="['tsdwmc', { rules: [{ required: true, message: '请输入' }] }]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" v-if="record.sjlylx !== '30'">
                  <a-form-item label="抄送检察机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <sh-select
                      :options="extOrgJcy"
                      labelKey="orgName"
                      :disabled="readOnly"
                      valueKey="orgCode"
                      style="width:100%"
                      placeholder="请选择"
                      v-decorator="['csdwdm', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <div class="cus-title" style="margin-top: 10px;">调查过程信息（多个信息可以用、隔开）</div>
                <a-col :span="12">
                  <a-form-item label="被调查人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input :disabled="readOnly" placeholder="请输入被调查人姓名" v-decorator="['bdrxm', { rules: [{ required: true, message: '请输入被调查人姓名！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="被调查人与被告人（罪犯）关系" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input :disabled="readOnly" placeholder="请输入关系" v-decorator="['bdcrybgrgx', { rules: [{ required: true, message: '请输入关系！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="readOnly"
                      valueFormat="YYYY-MM-DD"
                      style="width: 100%"
                      placeholder="请选择调查时间"
                      v-decorator="['dcsj', { initialValue: undefined, rules: [{ required: true, message: '请选择调查时间！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="调查地点" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入调查地点"
                      v-decorator="['dcdd', { rules: [{ required: true, message: '请输入调查地点' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="调查事项" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入调查事项"
                      v-decorator="['dcsx', { rules: [{ required: true, message: '请输入调查事项' }] }]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <div class="cus-title" style="margin-top: 10px;">文书制作信息</div>
                <a-col :span="12">
                  <a-form-item label="调查开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="readOnly"
                      valueFormat="YYYY-MM-DD"
                      style="width: 100%"
                      placeholder="请选择"
                      v-decorator="['wsDckssj', { initialValue: undefined, rules: [{ required: true, message: '请选择！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="调查结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="readOnly"
                      valueFormat="YYYY-MM-DD"
                      style="width: 100%"
                      placeholder="请选择"
                      v-decorator="['wsDcjssj', { initialValue: undefined, rules: [{ required: true, message: '请选择！' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="委托单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="readOnly"
                      placeholder="请输入"
                      v-decorator="['wsWtdw', { rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="人员类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <sh-select
                      dictType="nsysqjzrylx"
                      style="width: 100%;"
                      placeholder="请选择"
                      :disabled="readOnly"
                      v-decorator="['nsysqjzrylx', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="调查有关情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea :disabled="readOnly" :rows="8" placeholder="请输入" v-decorator="['wsDcygqk', { rules: [{ required: true, message: '请输入' }] }]"></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
            <a-alert style="margin-left: 21%;margin-bottom: 20px" message="说明：请录入调查评估相关信息，点击生成文书，即可按修改后的信息自动生成相关文书并进行盖章。后续会持续升级自动获取数据等功能。" type="info" show-icon />
          </div>
          <div class="accept-right" >
            <div class="cus-title" >调查评估意见书</div>
            <div style="margin-top: 10px;">
              <span style="margin-right: 4px;color: #f5222d;font-size: 14px;">*</span> 盖章上传:
              <sh-file-upload
                v-model="hzcl"
                :disabled="readOnly"
                :btnText="'附件上传'"
                uploadUrl="/api/sysFileInfo/uploadOss" >
              </sh-file-upload>
            </div>
            <div v-show="!readOnly" style="color: rgba(255, 0, 0, 1);margin: 10px 0">
              说明：系统提供默认的回执样表供在线签章和样表下载使用，如需修改请修改后点击重新生成文书。已授权电子签章用户点击智能签章即可完成附件上传。无法使用电子签章用户可使用样表下载后，线下盖章手动上传。如协同单位有特殊回执文件要求，以对方要求为主。
              由于一体化系统限制，请上传单个文件
            </div>
            <div class="right-con">
              <a-space style="margin-bottom: 10px;" v-show="!readOnly">
                <a-button :loading="loading" @click="getNoSignPdf()" type="">
                  生成文书
                </a-button>
                <a-button :loading="loading" @click="getSignedPdf()" type="primary" >
                  智能盖章
                </a-button>
                <a-button :loading="loading" @click="getNoSignPdf()" type="">
                  撤销
                </a-button>
                <a-button :loading="loading" @click="handleClickDownLoad2('1769556076487897090')" type="">
                  样表下载
                </a-button>
                <sh-select
                  v-model="sealType"
                  :options="[{name:'矫正机构章',code:'1'},{name:'司法局章',code:'2'}]"
                  style="width: 100%;"
                  :disabled="readOnly"/>
              </a-space>
              <div>
                <embed v-if="pdfUrl" :src="pdfUrl" type="application/pdf" style="width:100%;height:892px" />
              </div>
            </div>
          </div>
        </section>
      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
import pdf from 'vue-pdf';
import moment from 'moment';
import { mapGetters } from 'vuex';
import {
  acceptInvestInfoDetail,
  acceptInvestInfoEdit, acceptInvestInfoGetWdResult, getHzcl,
  getNoSignPdf31001,
  getSignedPdf31001
} from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage';
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';

export default {
  components: { pdf },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 10 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 14 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 5 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 19 } },
      orgTree: [],
      extOrgJcy: [],
      hzcl: [],
      pdfUrl: '',
      sealType: '2',
      loading: false,
      isOk: false,
      drawerWidth: window.innerWidth - 100,
      readOnly: false,
      record: {},
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    extOrgInfoList({ type: 30, orgName: '' }).then(res => {
      res.data.forEach(p => {
        this.extOrgJcy.push(p)
      })
    })
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    this.drawerWidth = window.innerWidth - 100
  },
  methods: {
    async init(record, orgTree, type) {
      this.visible = true
      this.orgTree = orgTree
      this.hzcl = []
      this.pdfUrl = ''
      this.sealType = window.localStorage.getItem('sealType31001')
      this.record = record
      this.readOnly = !!type;
      this.loadData(record.id)
    },
    async handleClickDownLoad2() {
      this.loading = true
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          const allData = this.form.getFieldsValue()
          allData.dcpgjl = this.$options.filters['dictType']('dcpgyj', allData.dcpgjl)
          allData.wsRylx = this.$options.filters['dictType']('nsysqjzrylx', allData.nsysqjzrylx)
          allData.dcpgwsh = allData.dcpgwsh || ''

          getNoSignPdf31001(allData).then((res) => {
            this.loading = false
            this.isOk = false
            // 将 ArrayBuffer 转换为 Blob
            var blob = new Blob([res.data], { type: 'application/pdf' });
            var filename = '样表'
            var downloadElement = document.createElement('a')
            var href = window.URL.createObjectURL(blob) // 创建下载的链接
            var reg = /^["](.*)["]$/g
            downloadElement.style.display = 'none'
            downloadElement.href = href
            downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
            document.body.appendChild(downloadElement)
            downloadElement.click() // 点击下载
            document.body.removeChild(downloadElement) // 下载完成移除元素
            window.URL.revokeObjectURL(href)
          })
        } else {
          this.loading = false
        }
      })
    },
    async getNoSignPdf() {
      this.loading = true
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          const allData = this.form.getFieldsValue()
          allData.dcpgjl = this.$options.filters['dictType']('dcpgyj', allData.dcpgjl)
          allData.wsRylx = this.$options.filters['dictType']('nsysqjzrylx', allData.nsysqjzrylx)
          allData.dcpgwsh = allData.dcpgwsh || ''

          getNoSignPdf31001(allData).then((res) => {
            this.loading = false
            this.isOk = false
            const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
            this.pdfUrl = URL.createObjectURL(pdfBlob)
          })
        } else {
          this.loading = false
        }
      })
    },
    async getSignedPdf() {
      this.loading = true

      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          const allData = this.form.getFieldsValue()
          allData.dcpgjl = this.$options.filters['dictType']('dcpgyj', allData.dcpgjl)
          allData.wsRylx = this.$options.filters['dictType']('nsysqjzrylx', allData.nsysqjzrylx)
          allData.dcpgwsh = allData.dcpgwsh || ''
          allData.sealType = this.sealType

          window.localStorage.setItem('sealType31001', this.sealType)

          getSignedPdf31001(allData).then((res) => {
            console.log(res)
            this.loading = false
            this.isOk = false
            const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
            this.pdfUrl = URL.createObjectURL(pdfBlob)
            getHzcl({ id: allData.id }).then((res) => {
              this.hzcl = res.data
            })
          }).finally(() => {
            this.loading = false
          })
        } else {
          this.loading = false
        }
      })
    },
    loadData(id) {
      acceptInvestInfoDetail({ id: id }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            const allData = this.form.getFieldsValue()
            const values = {}
            Object.keys(allData).map(key => {
              if (res.data[key]) {
                values[key] = res.data[key]
              } else {
                values[key] = null
              }
            })

            values.wsWtdw = res.data.wsWtdw || res.data.wtdwdd
            values.wsDcygqk = res.data.wsDcygqk || ''

            if (res.data.wsDckssj != null) {
              values.wsDckssj = res.data.wsDckssj
            } else if (res.data.wtsj != null) {
              values.wsDckssj = res.data.wtsj
            } else {
              values.wsDckssj = moment().format('YYYY-MM-DD HH:mm:ss')
            }
            if (res.data.wsDcjssj != null) {
              values.wsDcjssj = res.data.wsDcjssj
            } else if (res.data.dcpgrq != null) {
              values.wsDcjssj = res.data.dcpgrq
            } else {
              values.wsDcjssj = moment().format('YYYY-MM-DD HH:mm:ss')
            }

            this.form.setFieldsValue({
              ...values
            })
            acceptInvestInfoGetWdResult({ id: id }).then((res) => {
              if (res.success) {
                setTimeout(() => {
                  const values = {}
                  values.wsDcygqk = res.data.wsDcygqk || ''
                  values.dcpgjl = res.data.dcpgjl || ''

                  if (res.data.wsDckssj != null) {
                    values.wsDckssj = res.data.wsDckssj
                  } else if (res.data.wtsj != null) {
                    values.wsDckssj = res.data.wtsj
                  } else {
                    values.wsDckssj = moment().format('YYYY-MM-DD HH:mm:ss')
                  }
                  if (res.data.wsDcjssj != null) {
                    values.wsDcjssj = res.data.wsDcjssj
                  } else if (res.data.dcpgrq != null) {
                    values.wsDcjssj = res.data.dcpgrq
                  } else {
                    values.wsDcjssj = moment().format('YYYY-MM-DD HH:mm:ss')
                  }

                  this.form.setFieldsValue({
                    ...values
                  })
                  this.getNoSignPdf()
                }, 100)
              }
            })
          }, 100)
        }
      })
    },
    handleSubmit() {
      if (!this.hzcl?.length) {
        this.$message.error('请上传盖章文件')
        return false
      }
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.wsRylx = this.$options.filters['dictType']('nsysqjzrylx', values.nsysqjzrylx)
          values.fkr = this.userInfo.name
          values.fksj = moment().format('YYYY-MM-DD HH:mm:ss')
          values.hzcl = this.hzcl.map(item => item.id || item.uid).toString()
          values.zt = '4'
          acceptInvestInfoEdit(values).then((res) => {
            if (res.success) {
              this.$message.success('提交成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('提交失败')// + res.message
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.accept-wrapper {
  display: flex;
  width: 100%;
  flex-flow: column;

  &.class1 {
    flex-flow: row;

    .accept-left {
      flex: 1;
      overflow: hidden;
    }

    .accept-right {
      flex: 1;
      overflow: hidden;
      border-left: 1px solid #e8e8e8;

      .right-con {
        min-height: 500px;
        background: #EEEDED;
        padding: 10px;
      }
    }
  }

  .accept-left {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
    padding: 20px;
  }

  .accept-right {
    padding: 20px;
    position: relative;
  }
}

.cus-title-d {
  font-size: 20px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  color: #333333;
}

.cus-title {

  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  // margin-bottom: 20px;
  padding-left: 10px;
  font-size: 20px;
  font-family: Microsoft YaHei, Microsoft YaHei;

  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

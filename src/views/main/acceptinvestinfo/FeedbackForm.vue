<template>
  <a-drawer
    title="调查评估协同反馈"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper">
          <div class="accept-left">
            <BaseInfo :readOnly="readOnly" ref="BaseInfo" :baseObj="detailObj" />
          </div>
          <div class="accept-right">
            <StepVue :steps="steps"/>
          </div>
        </section>
        <div class="cus-title-d" style="margin-top: 20px;">区县司法局审核信息</div>
        <AduitForm ref="AduitForm" :readOnly="readOnly" :baseObj="detailObj" />
      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-button style="marginRight: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
  </a-drawer>
</template>

  <script>

  import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
  import {
    acceptInvestInfoEdit,
    acceptInvestInfoDetail,
    acceptInvestInfoStep
  } from '@/api/modular/main/acceptinvestinfo/acceptInvestInfoManage'
  import { fileList } from '@/api/modular/system/fileManage'
  // 基本信息
  import BaseInfo from '@/views/main/acceptinvestinfo/components/BaseInfoFeed'
  // 审核信息
  import AduitForm from '@/views/main/acceptinvestinfo/components/AduitFormFeed'
  // 步骤
  import StepVue from '@/views/main/acceptcorrectionobject/components/Step.vue';

  export default {
    components: { BaseInfo, AduitForm, StepVue },
    data() {
      return {
        drawetWidth: 1000,
        steps: [],
        readOnly: false,
        extOrgJcy: [], // 检察院下拉框
        record: {},
        detailObj: {},
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    provide() {
      return {
        parentData: this.$data
      }
    },
    created() {
      this.drawetWidth = window.innerWidth - 200
      this.getExtOrgInfoList()
    },
    methods: {
      // 外部单位信息列表
      getExtOrgInfoList() {
        extOrgInfoList({ type: 30, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.extOrgJcy.push(p)
          })
        })
      },
      async getFileList() {
        console.log(this.record)
        const { data } = await fileList({ ids: this.record.hzcl })
        console.log(data)
        this.detailObj.hzcl = data.map(item => { return { ...item, id: item.uid } })
        // this.form.setFieldsValue({
        //   hzcl: data.map(item => { return { ...item, id: item.uid } })
        // })
        // console.log(this.detailObj)
        return Promise.resolve()
        // this.$refs.BaseInfo.setVals()
      },
      // 初始化方法
      add(record, type) {
        this.record = record
        this.readOnly = !!type;
        this.visible = true
        this.getDetail()
      },
      async getDetail() {
        this.detailObj = {}
        const { data } = await acceptInvestInfoDetail({
          id: this.record.id
        })
        this.detailObj = { ...data, hzcl: [] }
        await this.getFileList()
        this.$nextTick(() => {
          this.$refs.BaseInfo.setVals()
        })
        // 流程
        acceptInvestInfoStep({ id: this.record.id }).then(res => {
          if (res.success) {
            this.steps = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      },

      edit(record, type) {
        if (record) {
          if (type) {
            this.readOnly = true
          }
          this.visible = true
          this.record = { ...record }
          this.getFileList()
        }
      },
      /**
       * 提交表单
       */
      async handleSubmit() {
        // 基本信息
        const data1 = await this.$refs.BaseInfo.handleSubmit()

        // 审核表单
        const data = await this.$refs.AduitForm.handleSubmit()
        this.confirmLoading = true
        const postData = {
        ...data1,
        ...data,
         zt: 4,
         hzcl: data1.hzcl ? data1.hzcl.map(item2 => item2.id).toString() : ''
        }
        postData.id = this.record.id

        console.log(data, data1, postData)

        // return false

        acceptInvestInfoEdit(postData).then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.confirmLoading = false
            this.$emit('ok', postData)
            this.handleCancel()
          } else {
            this.$message.error('操作失败' + res.message)// + res.message
          }
        }).finally(() => {
          this.confirmLoading = false
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.record = null
        this.visible = false
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .accept-wrapper{
    display: flex;
    width: 100%;
    .accept-left{
      flex:1;
      overflow: auto;
      overflow-x: hidden;
    }
    .accept-right{
      border-left: 1px solid #e8e8e8;
    // margin-top: 44px;
    position: relative;
    max-width: 300px;
    // padding-top: 20px;

    // .titlesd{
    //   position: absolute;
    //   top: -44px;
    //   left: -1px;
    //   line-height: 43px;
    //   border-bottom: 1px solid #e8e8e8;
    //   width: 100%;
    //   font-size: 16px;
    // font-family: Microsoft YaHei, Microsoft YaHei;
    // font-weight: bold;
    // color: #1890FF;
    // padding-left: 20px;
    // }
    }
  }
  /deep/ .ant-drawer-body {
    padding-top: 0;
    padding-bottom: 78px;
  }

  .cus-title {
    font-size: 18px;
    overflow: hidden;
    /* 字体大小 */
    font-weight: bold;
    /* 字体粗细 */
    color: #333;
    /* 字体颜色 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    /* 文本阴影 */
    position: relative;
    margin-bottom: 20px;
    padding-left: 10px;

    &::after {
      display: block;
      content: '';
      width: 6px;
      height: 60%;
      border-radius: 5px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      background: #1990ff;
    }
  }
  </style>

<template>
  <!-- <a-row :gutter="24"> -->
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="cus-title-d">案件信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['tyfh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['ajbh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['ajmc', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">委托调查信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="委托编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wtbh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="委托单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wtdwdd', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="委托单位地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wtdwdd', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="委托时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            disabled
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['wtsj', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="委托方联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wtflxr', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="委托方联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['wtflxdh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree
            :disabled="true"
            v-decorator="['jsdwmc', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">文书信息</div>
        <a-button
          v-show="docListArr && docListArr.length > 0"
          style="float: right;margin-right: 55px;margin-top: -50px;"
          type="primary"
          icon="download"
          @click="downloadDocList"
          :loading="exportBatchLoading">
          打包下载
        </a-button>
      </a-col>
      <a-col :span="23">
        <div style="margin-bottom: 20px;">
          <DocList :list="docListArr" />
        </div>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">调查对象信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否成年" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group
            :disabled="readOnly"
            v-decorator="['sfwcn', { rules: [{ required: true, message: '请输入' }] }]"
          >
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['bgrxm', { rules: [{ required: true, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="sex"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['bgrxb', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            :disabled="readOnly"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['bgrcsrq', { initialValue: undefined, rules: [{ required: true, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['lxdh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zjlx"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['zjlx', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['zjhm', { rules: [{ required: true, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="监护人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['jhrxm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="监护人联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['jhrlxdh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24">
        <a-form-item label="住所地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-cascader-distpicker
            :disabled="readOnly"
            v-decorator="['zsd', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"/>
          <p v-show="baseObj.zsd" class="tips">说明:目前接收到跨部门协同推送的三级行政区划，依据司法部技术标准，请在提交前补充第四级行政区划（街道级）。</p>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="住所地详细地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['zsdxxdz', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍所在地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-cascader-distpicker
            :disabled="readOnly"
            v-decorator="['hjszd', { rules: [{ required: true, message: '请输入' }] }]"/>
          <p v-show="baseObj.hjszd" class="tips">说明:目前接收到跨部门协同推送的三级行政区划，依据司法部技术标准，请在提交前补充第四级行政区划（街道级）。</p>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍地址明细" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['hjdzmx', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="工作单位" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['gzdw', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24">
        <a-form-item label="主要犯罪事实" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['zyfzss', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="拟适用社区矫正人员类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="nsysqjzrylx"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['nsysqjzrylx', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="拟适用社区矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['nsyjzlb', { initialValue: '', rules: [{ required: true, message: '请选择' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否速裁" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            :options="[{name:'否',code:'0'},{name:'是',code:'1'}]"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['sfsc', { rules: [{ required: true, message: '请选择' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否有原判刑期" :labelCol="labelCol" :wrapperCol="wrapperCol">

          <sh-select
            style="width: 100%;"
            @change="onChange"
            :options="[{name:'无',code:'0'},{name:'有',code:'1'}]"
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['sfyypxq', { initialValue: '1', rules: [{ required: true, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>

      <template v-if="showF">

        <a-col :span="12">
          <a-form-item label="原判刑期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              :disabled="readOnly"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              placeholder="请输入"
              v-decorator="['ypxq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="原判刑期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              :disabled="readOnly"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              placeholder="请选择"
              v-decorator="['ypxqksrq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="原判刑期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              :disabled="readOnly"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              placeholder="请选择"
              v-decorator="['ypxqjsrq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="原判刑罚" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <sh-select
              dictType="SQJZ_YPXF"
              style="width: 100%;"
              placeholder="请选择"
              :disabled="readOnly"
              v-decorator="['ypxf', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
            />
          <!-- <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['ypxf', { rules: [{ required: false, message: '请输入' }] }]" /> -->
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="附加刑" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <sh-select
              v-decorator="['fjx']"
              apiURL="/sysDictData/list?typeId=1668140386272436225"
              labelKey="value"
              mode="multiple"
              :disabled="readOnly"
              @change="onChange2"
              style="width: 100%"
              placeholder="请选择附加刑"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="showF2.includes('102')">
          <a-form-item label="罚金" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <a-input
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['fjxFj', { rules: [{ required: false, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="showF2.includes('101')">
          <a-form-item label="剥夺政治权利" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <a-input
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['fjxBdzzql', { rules: [{ required: false, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="showF2.includes('103')">
          <a-form-item label="没收财产" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <a-input
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['fjxMscc', { rules: [{ required: false, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="判决机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['pjjg', { rules: [{ required: false, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="判决日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              valueFormat="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              placeholder="请选择"
              :disabled="readOnly"
              v-decorator="['pjrq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
            />
          </a-form-item>
        </a-col>
      </template>
      <a-col :span="24">
        <a-form-item label="罪名" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-button v-if="!readOnly" @click="handleAddZm">添加</a-button>
          <p v-show="caseReason" style="color: rgba(255, 0, 0, 1);">已收到跨部门办案平台推送的案由罪名为： {{ caseReason }} ，由于标准不一，无法关联，需重新选择</p>
          <add-list
            ref="AddList"
            :disabled="readOnly"
            v-decorator="['chargesList', { initialValue: [], rules: [{ required: true, message: '请输入' }] }]"
          />
          <!-- <a-tree-select
            v-decorator="['jtzms']"
            :disabled="readOnly"
            show-search
            tree-node-filter-prop="label"
            style="width: 100%"
            tree-data-simple-mode
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="parentData.zmTreeDropDown"
            placeholder="请选择具体罪名">
          </a-tree-select> -->
        </a-form-item>
      </a-col>

      <a-col :span="23">
        <!-- <MembersList :list="baseObj.familyList" /> -->
      </a-col>
    </a-row>
    <zmSelect ref="zmSelectRef" @ok="hancleAddOk" />
    <SelectZm ref="SelectZm" @onOk="hancleAddOk" />
  </a-form>
  <!-- </a-row> -->
</template>

<script>
// 成员列表
import MembersList from '@/views/main/acceptcorrectionobject/components/MembersList'
// 文书信息
import DocList from '@/views/main/acceptcorrectionobject/components/DocList'
import AddList from './AddList.vue'
import zmSelect from '../../chargeInfo/zmSelect.vue'
import SelectZm from './SelectZm.vue'
import { acceptCorrectionDocPage } from '@/api/modular/main/subList/subListManage'
import { docBatchDownLoad } from '@/api/modular/main/acceptDocManage';

export default {
  inject: ['parentData'],
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {},
      type: Object
    }
  },
  // watch: {
  //   baseObj: {
  //     handler() {
  //       this.$nextTick(() => {
  //         // alert(22)
  //         this.setValues()
  //       })
  //     },
  //     deep: true
  //   }
  // },
  components: { MembersList, DocList, AddList, zmSelect, SelectZm },
  data() {
    return {
      exportBatchLoading: false,
      docListArr: [],
      isShow: false,
      showF: true,
      caseReason: null,
      showF2: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      form: this.$form.createForm(this)
    }
  },
  mounted() {},

  methods: {
    onChange2(e) {
      this.showF2 = e
    },
    onChange(e) {
      console.log(e)
      if (e === '0') {
        this.showF = false
      } else {
        this.showF = true
      }
    },
    hancleAddOk(e) {
      console.log(e)
      this.$refs.AddList.handleAdd(e)
    },
    handleAddZm() {
      // this.$refs.zmSelectRef.open()
      this.$refs.SelectZm.showModal()
    },
    handleChange(e) {
      console.log(e)
    },
    setValues() {
      this.caseReason = null
      this.showF = this.baseObj.sfyypxq !== '0';
      this.showF2 = this.baseObj.fjx
      this.$nextTick(() => {
        // 家庭成员及社会关系
        this.isShow = !!(this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y');
        const allData = this.form.getFieldsValue()
        const values = {}
        Object.keys(allData).map(key => {
          if (this.baseObj[key]) {
            values[key] = this.baseObj[key]
          } else {
            values[key] = null
          }
        })
        values.fjx = values.fjx ? values.fjx.split(',') : []
        values.sfsc = '0'

        this.caseReason = this.baseObj.caseReason
        this.form.setFieldsValue({
          ...values
        })
        this.getDocList()
      })
    },
    async getDocList() {
      const { data } = await acceptCorrectionDocPage(Object.assign({ contactId: this.baseObj.id }, {}))
      console.log(data)
      this.docListArr = data.rows
    },
    downloadDocList() {
      this.exportBatchLoading = true
      docBatchDownLoad({ contactId: this.baseObj.id, xm: this.baseObj.bgrxm }).then((res) => {
        this.downloadFile(res)
      }).catch((err) => {
        this.exportBatchLoading = false
        this.$message.error('下载错误：获取文件流错误 ' + err)
      })
    },
    downloadFile(res) {
      this.exportBatchLoading = false
      var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
      var contentDisposition = res.headers['content-disposition']
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var result = patt.exec(contentDisposition)
      var filename = result[1]
      var downloadElement = document.createElement('a')
      var href = window.URL.createObjectURL(blob) // 创建下载的链接
      var reg = /^["](.*)["]$/g
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    },
    parseAdministrativeCode(code) {
      if (code.length !== 6) {
        return null // 行政区划代码必须是6位
      }

      const provinceCode = code.slice(0, 2) + '0000' // 获取省级代码
      const cityCode = code.slice(0, 4) + '00' // 获取市级代码

      return {
        selectedProvince: provinceCode,
        selectedCity: cityCode,
        selectedDistrict: code
      }
    },
    distpickerValidator(ule, value, callback) {
      if (value.selectedProvince && value.selectedCity && value.selectedDistrict) {
        callback()
      } else {
        callback(new Error('请选择'))
      }
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    },
    getValues() {
      return this.form.getFieldsValue()
    }
  }
}
</script>

<style lang="less" scoped>
.tips {
  // color: rgba(0, 0, 0, 0.45);
  color: #e4393c;
  margin: 0;
  line-height: 20px;
}
/deep/.ant-card {
  // background: #f5f6fa;
}

/deep/.ant-card-body {
  padding-left: 0;
  padding-right: 0;
}
</style>

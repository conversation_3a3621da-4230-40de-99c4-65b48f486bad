<template>
  <a-modal title="Title" :visible="visible" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
    <div v-if="visible" style="height: 60vh;" class="filter-tree-wrap">
      <a-input
        placeholder="输入关键字进行过滤"
        v-model="filterText">
      </a-input>
      <div class="filter-tree">
        <Tree
          :props="defaultProps"
          :data="parentData.zmTreeDropDown"
          show-checkbox
          :node-key="'value'"
          default-expand-all
          :filter-node-method="filterNode"
          ref="tree">
        </Tree>
      </div>

    </div>

    <!-- <Tree/> -->
  </a-modal>
</template>

<script>
import { Tree } from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css'
export default {
    inject: ['parentData'],

    components: { Tree },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
    data() {
        return {
            ModalText: 'Content of the modal',
            visible: false,
            confirmLoading: false,
            filterText: '',
        data: [{
          id: 1,
          label: '一级 1',
          children: [{
            id: 4,
            label: '二级 1-1',
            children: [{
              id: 9,
              label: '三级 1-1-1'
            }, {
              id: 10,
              label: '三级 1-1-2'
            }]
          }]
        }, {
          id: 2,
          label: '一级 2',
          children: [{
            id: 5,
            label: '二级 2-1'
          }, {
            id: 6,
            label: '二级 2-2'
          }]
        }, {
          id: 3,
          label: '一级 3',
          children: [{
            id: 7,
            label: '二级 3-1'
          }, {
            id: 8,
            label: '二级 3-2'
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'title'
        }
        };
    },
    methods: {
        filterNode(value, data) {
        if (!value) return true;
        return data.title.indexOf(value) !== -1;
      },
        showModal() {
            this.visible = true;
        },
        handleOk(e) {
            // this.ModalText = 'The modal will be closed after two seconds';
            this.confirmLoading = true;
            setTimeout(() => {
            console.log(this.$refs.tree.getCheckedNodes())
              this.$emit('onOk', this.$refs.tree.getCheckedNodes())
                this.visible = false;
                this.confirmLoading = false;
            }, 300);
        },
        handleCancel(e) {
            console.log('Clicked cancel button');
            this.visible = false;
        }
    }
}
</script>

<style lang="less" scoped>
.filter-tree-wrap{
  height: 60h;
  overflow: hidden;
  flex-flow: column;
  display: flex;
  .filter-tree{
    flex:1;
    overflow: auto;
  }
}
</style>

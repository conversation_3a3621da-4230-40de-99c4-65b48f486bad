<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="反馈人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['fkr', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="反馈时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            valueFormat="YYYY-MM-DD HH:mm:ss"
            disabled
            v-decorator="['fksj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  computed: {
    ...mapGetters(['userInfo'])
  },
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {},
      type: Object
    }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      form: this.$form.createForm(this)
    }
  },
  methods: {
    setValues() {
      if (this.readOnly) {
        this.form.setFieldsValue({
          fkr: this.baseObj.fkr,
          fksj: moment(this.baseObj.fksj).format('YYYY-MM-DD HH:mm:ss')
        })
      } else {
        this.form.setFieldsValue({
          fkr: this.userInfo.name,
          fksj: moment().format('YYYY-MM-DD HH:mm:ss')
        })
      }
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped></style>

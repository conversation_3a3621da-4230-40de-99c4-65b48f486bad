<template>
  <div ref="viewBox" style="margin:10px 0;">
    <a-table :title="null" :data-source="dataSource" :columns="columns" size="middle" :pagination="false">
      <template slot="sort" slot-scope="text, record,index">
        <span class="num van-handle"> <a-icon type="ordered-list"/> {{ index + 1 }} </span>
      </template>
      <template slot="zmName" slot-scope="text">
        {{ text }}
      </template>
      <template slot="jtzm" slot-scope="text, record">
        <editable-cell v-if="!disabled" :text="text" :type="1" @change="onCellChange(record.key, 'jtzm', $event)"/>
        <div v-else>{{ text }}</div>
      </template>
      <template slot="operation" slot-scope="text, record">
        <a-popconfirm v-if="dataSource.length>0" title="确认删除吗?" @confirm="() => onDelete(record.key)">
          <a href="javascript:;">删除</a>
        </a-popconfirm>
      </template>
    </a-table>
  </div>
</template>
<script>
import EditableCell from './EditableCell'
import Sortable from 'sortablejs'

export default {
  inject: ['parentData'],

  components: {
    EditableCell
  },
  props: {
    value: '',
    disabled: {
      default: false,
      type: Boolean

    }
  },
  data() {
    return {
      sortable: null,
      oldList: [],
      newList: [],
      dataSource: [],
      count: 2,
      columns: [
        { ellipsis: true, title: '序号', width: 80, dataIndex: 'key', align: 'center', scopedSlots: { customRender: 'sort' } },
        { ellipsis: true, title: '罪名', dataIndex: 'zmName', width: 180, align: 'center', scopedSlots: { customRender: 'zmName' }, sortDirections: ['descend', 'ascend', 'descend'], defaultSortOrder: 'descend' },
        { ellipsis: true, title: '犯罪类型', dataIndex: 'fzlxName', width: 180, align: 'center', scopedSlots: { customRender: 'fzlxName' }, sortDirections: ['descend', 'ascend', 'descend'], defaultSortOrder: 'descend' },
        { ellipsis: true, title: '具体罪名', dataIndex: 'jtzm', scopedSlots: { customRender: 'jtzm' }, sortDirections: ['descend', 'ascend', 'descend'], defaultSortOrder: 'descend' },
        { ellipsis: true, title: '操作', width: 120, dataIndex: 'operation', scopedSlots: { customRender: 'operation' } }
      ]
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.dataSource = newVal.map((item, index) => {
          return {
            title: item.title,
            headerOrgName: item.headerOrgName,
            key: index,
            ...item
          }
        })
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setSort()
      if (this.disabled) {
        this.columns.splice(-1)
      }
    })
  },
  methods: {
    setSort() {
      const el = this.$refs.viewBox.querySelectorAll(
        '.ant-table-body > table > tbody'
      )[0]
      console.log(el)
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        handle: '.van-handle',
        animation: 350,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: (evt) => {
          const targetRow = this.dataSource.splice(evt.oldIndex, 1)[0]
          this.dataSource.splice(evt.newIndex, 0, targetRow)

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0]
          this.newList.splice(evt.newIndex, 0, tempIndex)
        }
      })
    },
    onCellChange(key, dataIndex, value) {
      const dataSource = [...this.dataSource]
      const target = dataSource.find(item => item.key === key)
      if (target) {
        target[dataIndex] = value
        this.dataSource = dataSource
      }
      this.$emit('input', this.dataSource)
      this.$emit('change', this.dataSource)
    },
    onDelete(key) {
      const dataSource = [...this.dataSource]
      this.dataSource = dataSource.filter(item => item.key !== key)
      this.$emit('input', this.dataSource)
      this.$emit('change', this.dataSource)
    },
    handleAdd(list) {
      if (list && list.length) {
        list.map(item => {
          let cds
          if (item.pid) {
            cds = this.parentData.zmTreeDropDown.find(d => {
              return d.id === item.pid
            })
            console.log(cds)
          }

          const { count, dataSource } = this
          delete item.children
          const newData = {
            key: dataSource.length + 1,
            zmName: item.title,
            jtzm: item.title,
            fzlx: cds.value,
            fzlxName: cds.title,

            ...item
            // age: 32,
            // address: `London, Park Lane no. ${count}`
          }
          this.dataSource = [...dataSource, newData]
          this.count = count + 1
        })

        this.$emit('input', this.dataSource)
        this.$emit('change', this.dataSource)
        return
      }
      const { count, dataSource } = this
      const newData = {
        key: dataSource.length + 1,
        title: `子主题`,
        headerOrgName: '承办部门'
        // age: 32,
        // address: `London, Park Lane no. ${count}`
      }
      this.dataSource = [...dataSource, newData]
      this.count = count + 1
      this.$emit('input', this.dataSource)
      this.$emit('change', this.dataSource)
    }
  }
}
</script>
<style lang="less" scoped>
.van-handle {
  cursor: pointer;
}

.editable-cell {
  position: relative;
}

.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
  padding-right: 24px;
}

.editable-cell-text-wrapper {
  padding: 5px 24px 5px 5px;
}

.editable-cell-icon,
.editable-cell-icon-check {
  position: absolute;
  right: 0;
  width: 20px;
  cursor: pointer;
}

.editable-cell-icon {
  line-height: 18px;
  display: none;
}

.editable-cell-icon-check {
  line-height: 28px;
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

.editable-cell-icon:hover,
.editable-cell-icon-check:hover {
  color: #108ee9;
}

.editable-add-btn {
  margin-bottom: 8px;
}

/deep/ .ant-table-title {
  display: none;
}
</style>

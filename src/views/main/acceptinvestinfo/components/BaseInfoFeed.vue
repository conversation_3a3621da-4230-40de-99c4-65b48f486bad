<template>
  <!-- <a-row :gutter="24"> -->
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="cus-title-d">案件信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['tyfh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['jyajbh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['ajmc', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24">
        <div class="cus-title-d">反馈信息</div>
      </a-col>

      <a-col :span="12">
        <a-form-item label="调查单位（区县）" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree
            :disabled="true"
            v-decorator="['dcdw', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="调查单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['dcdwlxr', { rules: [{ required: true, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="调查单位联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['dcdwlxdh', { rules: [{ required: true, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="调查评估文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['dcpgwsh', { rules: [{ required: true, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="dcpgyj"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['dcpgjl', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="调查评估日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            :disabled="readOnly"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['dcpgrq', { initialValue: undefined, rules: [{ required: true, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24">
        <a-form-item label="住所地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-cascader-distpicker
            :disabled="readOnly"
            v-decorator="['sjjzdqh', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="住所地详细地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['sjjzdmx', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="家庭监护人或保证人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['jhrxm', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="监护人与罪犯的关系" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['jhryzfgx', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="反馈委托机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="true"
            placeholder="请输入"
            v-decorator="['tsdwmc', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="抄送检察机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            :options="[
              ...parentData.extOrgJcy
            ]"
            labelKey="orgName"
            :disabled="readOnly"
            valueKey="orgCode"
            style="width:100%"
            placeholder="请选择"
            v-decorator="['csdwdm', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="调查结论附件" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-file-upload
            v-decorator="['hzcl', { initialValue: [], rules: [{ required: true, message: '请输入' }] }]"
            :disabled="readOnly"
            uploadUrl="/api/sysFileInfo/uploadOss?ext=1"
          />
        </a-form-item>
      </a-col>
      <a-col :span="23">
        <!-- <MembersList :list="baseObj.familyList" /> -->
      </a-col>
    </a-row>
  </a-form>
  <!-- </a-row> -->
</template>

<script>
// 成员列表
import MembersList from '@/views/main/acceptcorrectionobject/components/MembersList'
// 文书信息
import DocList from '@/views/main/acceptcorrectionobject/components/DocList'
import { acceptCorrectionDocPage } from '@/api/modular/main/subList/subListManage'

export default {
  inject: ['parentData'],
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {},
      type: Object
    }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  components: { MembersList, DocList },
  data() {
    return {
      docListArr: [],
      isShow: false,
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      form: this.$form.createForm(this)
    }
  },
  mounted() {},

  methods: {
    handleChange(e) {
      console.log(e)
    },
    setValues() {
      if (!this.baseObj.sjjzdqh) {
        this.baseObj.sjjzdqh = this.baseObj.zsd
      }
      if (!this.baseObj.sjjzdmx) {
        this.baseObj.sjjzdmx = this.baseObj.zsdxxdz
      }
      // 家庭成员及社会关系
      if (this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y') {
        this.isShow = true
      } else {
        this.isShow = false
      }
      const allData = this.form.getFieldsValue()

      const values = {}
      Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })

      this.form.setFieldsValue({
        ...values
      })
      this.getDocList()
    },
    async getDocList() {
      const { data } = await acceptCorrectionDocPage(Object.assign({ contactId: this.baseObj.id }, {}))
      console.log(data)
      this.docListArr = data.rows
    },
    parseAdministrativeCode(code) {
      if (code.length !== 6) {
        return null // 行政区划代码必须是6位
      }

      const provinceCode = code.slice(0, 2) + '0000' // 获取省级代码
      const cityCode = code.slice(0, 4) + '00' // 获取市级代码

      return {
        selectedProvince: provinceCode,
        selectedCity: cityCode,
        selectedDistrict: code
      }
    },
    distpickerValidator(ule, value, callback) {
      debugger
      // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
      if (value.selectedProvince && value.selectedCity && value.selectedDistrict) {
        callback()
      } else {
        callback(new Error('请选择'))
      }
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-card {
  // background: #f5f6fa;
}

/deep/.ant-card-body {
  padding-left: 0;
  padding-right: 0;
}
</style>

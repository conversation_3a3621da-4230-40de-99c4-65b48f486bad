<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.sfzh" allow-clear placeholder="请输入身份证号"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="业务类型">
                <a-select v-model="queryParam.ywlx" allow-clear placeholder="请选择业务类型">
                  <!--        <span slot="ywlx" slot-scope="text, record">-->
                  <!--          <span v-if="record.ywlx==='IA'">结婚登记</span>-->
                  <!--          <span v-if="record.ywlx==='IB'">离婚登记</span>-->
                  <!--          <span v-if="record.ywlx==='ICA'">补办结婚登记</span>-->
                  <!--          <span v-if="record.ywlx==='ICB'">补办离婚登记</span>-->
                  <!--        </span>-->
                  <a-select-option value="结婚登记">
                    结婚登记
                  </a-select-option>
                  <a-select-option value="离婚登记">
                    离婚登记
                  </a-select-option>
                  <a-select-option value="补发结婚证">
                    补发结婚证
                  </a-select-option>
                  <a-select-option value="补发离婚证">
                    补发离婚证
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>

        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { irsMarriageRegistrationInformationPage, irsMarriageRegistrationInformationDelete } from '@/api/modular/main/irsmarriageregistrationinformation/irsMarriageRegistrationInformationManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        // 表头
        columns: [
          {
            ellipsis: true,
            title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
            ellipsis: true,
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            ellipsis: true,
            title: '状态',
            align: 'center',
            dataIndex: 'zhuangtai',
            scopedSlots: { customRender: 'zhuangtai' }
          },
          {
            ellipsis: true,
            title: '身份证号',
            align: 'center',
            dataIndex: 'sfzh'
          },
          {
            ellipsis: true,
            title: '业务类型',
            align: 'center',
            dataIndex: 'ywlx',
            scopedSlots: { customRender: 'ywlx' }
          },
          {
            ellipsis: true,
            title: '登记日期',
            align: 'center',
            dataIndex: 'djrq',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD')
            }
          },
          {
            ellipsis: true,
            title: '登记机关',
            align: 'center',
            dataIndex: 'djjg'
          },
          {
            ellipsis: true,
            title: '更新时间',
            align: 'center',
            dataIndex: 'updateTime'
          },
          {
            ellipsis: true,
            title: '操作',
            width: '80px',
            dataIndex: 'action',
            scopedSlots: { customRender: 'action' }
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return irsMarriageRegistrationInformationPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
    },
    methods: {
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamblrq = this.queryParam.blrqDate
        if (queryParamblrq != null) {
            this.queryParam.blrq = moment(queryParamblrq).format('YYYY-MM-DD')
            if (queryParamblrq.length < 1) {
                delete this.queryParam.blrq
            }
        }
        const queryParamdjrq = this.queryParam.djrqDate
        if (queryParamdjrq != null) {
            this.queryParam.djrq = moment(queryParamdjrq).format('YYYY-MM-DD')
            if (queryParamdjrq.length < 1) {
                delete this.queryParam.djrq
            }
        }
        const queryParamnfcsrq = this.queryParam.nfcsrqDate
        if (queryParamnfcsrq != null) {
            this.queryParam.nfcsrq = moment(queryParamnfcsrq).format('YYYY-MM-DD')
            if (queryParamnfcsrq.length < 1) {
                delete this.queryParam.nfcsrq
            }
        }
        const queryParamnvfcsrq = this.queryParam.nvfcsrqDate
        if (queryParamnvfcsrq != null) {
            this.queryParam.nvfcsrq = moment(queryParamnvfcsrq).format('YYYY-MM-DD')
            if (queryParamnvfcsrq.length < 1) {
                delete this.queryParam.nvfcsrq
            }
        }
        const queryParamtongTime = this.queryParam.tongTimeDate
        if (queryParamtongTime != null) {
            this.queryParam.tongTime = moment(queryParamtongTime).format('YYYY-MM-DD')
            if (queryParamtongTime.length < 1) {
                delete this.queryParam.tongTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      irsMarriageRegistrationInformationDelete (record) {
        irsMarriageRegistrationInformationDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangeblrq(date, dateString) {
        this.blrqDateString = dateString
      },
      onChangedjrq(date, dateString) {
        this.djrqDateString = dateString
      },
      onChangenfcsrq(date, dateString) {
        this.nfcsrqDateString = dateString
      },
      onChangenvfcsrq(date, dateString) {
        this.nvfcsrqDateString = dateString
      },
      onChangetongTime(date, dateString) {
        this.tongTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

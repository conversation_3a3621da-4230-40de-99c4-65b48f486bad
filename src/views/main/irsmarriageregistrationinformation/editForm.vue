<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-card>
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">基本信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['xm']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="矫正单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jzjgName']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="公民身份证号码"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['sfzh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="业务类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ywlx']" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="jh">
            <a-form-item
              label="结婚证字号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jhzzh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="!jh">
            <a-form-item
              label="离婚证字号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['lhzzh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="录入时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['blrq']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="登记时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['djrq']" />
            </a-form-item>
          </a-col>
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">证件信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="男方姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nfxm']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="男方证件类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nfsfzjlx']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="男方身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nfsfzjh']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="女方姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nvfxm']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="女方证件类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nvfsfzjlx']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="女方身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['nvfsfzjh']" />
            </a-form-item>
          </a-col>
        </a-form>
      </a-card>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { irsMarriageRegistrationInformationEdit } from '@/api/modular/main/irsmarriageregistrationinformation/irsMarriageRegistrationInformationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        blrqDateString: '',
        djrqDateString: '',
        nfcsrqDateString: '',
        nvfcsrqDateString: '',
        tongTimeDateString: '',
        jh: false,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        if (record.ywlx === '结婚登记' || record.ywlx === '补发结婚证') {
          this.jh = true
        }
        if (record.ywlx === '离婚登记' || record.ywlx === '补发离婚证') {
          this.jh = false
        }
        if (record.zhuangtai === '200') {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '在矫'
              }
            )
          }, 100)
        } else {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '解矫'
              }
            )
          }, 100)
        }
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              djjg: record.djjg,
              fzjgdm: record.fzjgdm,
              hydjy: record.hydjy,
              jhzzh: record.jhzzh,
              lhzzh: record.lhzzh,
              nfgj: record.nfgj,
              nfsfzjh: record.nfsfzjh,
              nfsfzjlx: record.nfsfzjlx,
              nfxm: record.nfxm,
              nvfgj: record.nvfgj,
              nvfsfzjh: record.nvfsfzjh,
              nvfsfzjlx: record.nvfsfzjlx,
              nvfxm: record.nvfxm,
              qtbz: record.qtbz,
              xtbz: record.xtbz,
              xzqh: record.xzqh,
              ywlx: record.ywlx,
              blrq: record.blrq,
              djrq: record.djrq,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              xm: record.xm,
              sfzh: record.sfzh,
              jzjgName: record.jzjgName
            }
          )
        }, 100)
        // // 时间单独处理
        // if (record.blrq != null) {
        //     this.form.getFieldDecorator('blrq', { initialValue: moment(record.blrq, 'YYYY-MM-DD') })
        // }
        // this.blrqDateString = moment(record.blrq).format('YYYY-MM-DD')
        // // 时间单独处理
        // if (record.djrq != null) {
        //     this.form.getFieldDecorator('djrq', { initialValue: moment(record.djrq, 'YYYY-MM-DD') })
        // }
        // this.djrqDateString = moment(record.djrq).format('YYYY-MM-DD')
        // // 时间单独处理
        // if (record.nfcsrq != null) {
        //     this.form.getFieldDecorator('nfcsrq', { initialValue: moment(record.nfcsrq, 'YYYY-MM-DD') })
        // }
        // this.nfcsrqDateString = moment(record.nfcsrq).format('YYYY-MM-DD')
        // // 时间单独处理
        // if (record.nvfcsrq != null) {
        //     this.form.getFieldDecorator('nvfcsrq', { initialValue: moment(record.nvfcsrq, 'YYYY-MM-DD') })
        // }
        // this.nvfcsrqDateString = moment(record.nvfcsrq).format('YYYY-MM-DD')
        // // 时间单独处理
        // if (record.tongTime != null) {
        //     this.form.getFieldDecorator('tongTime', { initialValue: moment(record.tongTime, 'YYYY-MM-DD') })
        // }
        // this.tongTimeDateString = moment(record.tongTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.blrq = this.blrqDateString
            values.djrq = this.djrqDateString
            values.nfcsrq = this.nfcsrqDateString
            values.nvfcsrq = this.nvfcsrqDateString
            values.tongTime = this.tongTimeDateString
            irsMarriageRegistrationInformationEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeblrq(date, dateString) {
        this.blrqDateString = dateString
      },
      onChangedjrq(date, dateString) {
        this.djrqDateString = dateString
      },
      onChangenfcsrq(date, dateString) {
        this.nfcsrqDateString = dateString
      },
      onChangenvfcsrq(date, dateString) {
        this.nvfcsrqDateString = dateString
      },
      onChangetongTime(date, dateString) {
        this.tongTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

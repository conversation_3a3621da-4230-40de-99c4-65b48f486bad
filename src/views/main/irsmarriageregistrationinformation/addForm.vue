<template>
  <a-modal
    title="新增婚姻登记信息查询（新国标）"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="录入时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择录入时间" v-decorator="['blrq',{rules: [{ required: true, message: '请选择录入时间！' }]}]" @change="onChangeblrq"/>
        </a-form-item>
        <a-form-item
          label="登记机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入登记机关" v-decorator="['djjg', {rules: [{required: true, message: '请输入登记机关！'}]}]" />
        </a-form-item>
        <a-form-item
          label="登记日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择登记日期" v-decorator="['djrq',{rules: [{ required: true, message: '请选择登记日期！' }]}]" @change="onChangedjrq"/>
        </a-form-item>
        <a-form-item
          label="证照颁发机构代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证照颁发机构代码" v-decorator="['fzjgdm', {rules: [{required: true, message: '请输入证照颁发机构代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻登记员"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻登记员" v-decorator="['hydjy', {rules: [{required: true, message: '请输入婚姻登记员！'}]}]" />
        </a-form-item>
        <a-form-item
          label="结婚证字号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入结婚证字号" v-decorator="['jhzzh', {rules: [{required: true, message: '请输入结婚证字号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="离婚证字号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入离婚证字号" v-decorator="['lhzzh', {rules: [{required: true, message: '请输入离婚证字号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="男方出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择男方出生日期" v-decorator="['nfcsrq',{rules: [{ required: true, message: '请选择男方出生日期！' }]}]" @change="onChangenfcsrq"/>
        </a-form-item>
        <a-form-item
          label="男方国籍"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入男方国籍" v-decorator="['nfgj', {rules: [{required: true, message: '请输入男方国籍！'}]}]" />
        </a-form-item>
        <a-form-item
          label="男方身份证件号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入男方身份证件号" v-decorator="['nfsfzjh', {rules: [{required: true, message: '请输入男方身份证件号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="男方身份证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入男方身份证件类型" v-decorator="['nfsfzjlx', {rules: [{required: true, message: '请输入男方身份证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="男方姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入男方姓名" v-decorator="['nfxm', {rules: [{required: true, message: '请输入男方姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="女方出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择女方出生日期" v-decorator="['nvfcsrq',{rules: [{ required: true, message: '请选择女方出生日期！' }]}]" @change="onChangenvfcsrq"/>
        </a-form-item>
        <a-form-item
          label="女方国籍"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入女方国籍" v-decorator="['nvfgj', {rules: [{required: true, message: '请输入女方国籍！'}]}]" />
        </a-form-item>
        <a-form-item
          label="女方身份证件号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入女方身份证件号" v-decorator="['nvfsfzjh', {rules: [{required: true, message: '请输入女方身份证件号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="女方身份证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入女方身份证件类型" v-decorator="['nvfsfzjlx', {rules: [{required: true, message: '请输入女方身份证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="女方姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入女方姓名" v-decorator="['nvfxm', {rules: [{required: true, message: '请输入女方姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="其他备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入其他备注" v-decorator="['qtbz', {rules: [{required: true, message: '请输入其他备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="同步时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择同步时间" v-decorator="['tongTime',{rules: [{ required: true, message: '请选择同步时间！' }]}]" @change="onChangetongTime"/>
        </a-form-item>
        <a-form-item
          label="系统备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入系统备注" v-decorator="['xtbz', {rules: [{required: true, message: '请输入系统备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="行政区划代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行政区划代码" v-decorator="['xzqh', {rules: [{required: true, message: '请输入行政区划代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="业务类型（IA：结婚登记，IB：离婚登记，ICA：补办结婚登记，ICB：补办离婚登记）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入业务类型（IA：结婚登记，IB：离婚登记，ICA：补办结婚登记，ICB：补办离婚登记）" v-decorator="['ywlx', {rules: [{required: true, message: '请输入业务类型（IA：结婚登记，IB：离婚登记，ICA：补办结婚登记，ICB：补办离婚登记）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsMarriageRegistrationInformationAdd } from '@/api/modular/main/irsmarriageregistrationinformation/irsMarriageRegistrationInformationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        blrqDateString: '',
        djrqDateString: '',
        nfcsrqDateString: '',
        nvfcsrqDateString: '',
        tongTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.blrq = this.blrqDateString
            values.djrq = this.djrqDateString
            values.nfcsrq = this.nfcsrqDateString
            values.nvfcsrq = this.nvfcsrqDateString
            values.tongTime = this.tongTimeDateString
            irsMarriageRegistrationInformationAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeblrq(date, dateString) {
        this.blrqDateString = dateString
      },
      onChangedjrq(date, dateString) {
        this.djrqDateString = dateString
      },
      onChangenfcsrq(date, dateString) {
        this.nfcsrqDateString = dateString
      },
      onChangenvfcsrq(date, dateString) {
        this.nvfcsrqDateString = dateString
      },
      onChangetongTime(date, dateString) {
        this.tongTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-card>
          <a-col :span="12">
            <a-form-item
              label="姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahap0016', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="矫正状态"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zhuangtai', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="矫正单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jzjgName', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="身份证"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahap0015', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="户籍性质"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['azcp0013', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="低保类别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhax0005', {rules: [{ message: ''}]}]">
                <a-select-option value="1">
                  收入型低保
                </a-select-option>
                <a-select-option value="2">
                  支出型贫困
                </a-select-option>
                <a-select-option value="3">
                  残疾人单列户施保
                </a-select-option>
                <a-select-option value="4">
                  重病型单列户施保
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="申请救助原因"
              :labelCol="bigLabelCol"
              :wrapperCol="bigWrapperCol"
            >
              <a-input disabled v-decorator="['ahax0004', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="户籍地址"
              :labelCol="bigLabelCol"
              :wrapperCol="bigWrapperCol"
            >
              <a-input disabled v-decorator="['ahaf0005', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="现居地址"
              :labelCol="bigLabelCol"
              :wrapperCol="bigWrapperCol"
            >
              <a-input disabled v-decorator="['ahaf0006', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="更新时间"
              :labelCol="bigLabelCol"
              :wrapperCol="bigWrapperCol"
            >
              <a-input disabled v-decorator="['updateTime', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
        </a-card>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsSubsistenceAssistanceInformationEdit } from '@/api/modular/main/irssubsistenceassistanceinformation/irsSubsistenceAssistanceInformationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        bigLabelCol: {
          xs: { span: 24 },
          sm: { span: 4 }
        },
        bigWrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        if (record.zhuangtai === '200') {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '在矫'
              }
            )
          }, 100)
        } else {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '解矫'
              }
            )
          }, 100)
        }
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              ahaf0005: record.ahaf0005,
              ahaf0006: record.ahaf0006,
              ahaf0008: record.ahaf0008,
              ahap0015: record.ahap0015,
              ahap0016: record.ahap0016,
              ahax0003: record.ahax0003,
              ahax0004: record.ahax0004,
              ahax0010: record.ahax0010,
              ahax0011: record.ahax0011,
              ahax0027: record.ahax0027,
              ahax0036: record.ahax0036,
              azaa0002: record.azaa0002,
              azcp0013: record.azcp0013,
              azdf0005: record.azdf0005,
              azdf0011: record.azdf0011,
              azdf0013: record.azdf0013,
              bhax0002: record.bhax0002,
              bhax0005: record.bhax0005,
              bhax0008: record.bhax0008,
              bhax0015: record.bhax0015,
              bhax0016: record.bhax0016,
              bhax0018: record.bhax0018,
              bhax0020: record.bhax0020,
              bhax0023: record.bhax0023,
              bhax0024: record.bhax0024,
              bhax0025: record.bhax0025,
              bhax0026: record.bhax0026,
              bhax0027: record.bhax0027,
              bhax0028: record.bhax0028,
              bhax0029: record.bhax0029,
              bhax0030: record.bhax0030,
              bhax0031: record.bhax0031,
              bhax0032: record.bhax0032,
              bhax0033: record.bhax0033,
              bhax0035: record.bhax0035,
              bhax0036: record.bhax0036,
              bhax0037: record.bhax0037,
              bhax0038: record.bhax0038,
              bhax0039: record.bhax0039,
              bhax0040: record.bhax0040,
              bhax0112: record.bhax0112,
              bhax0113: record.bhax0113,
              bhax0114: record.bhax0114,
              bhax0115: record.bhax0115,
              jzjgName: record.jzjgName,
              updateTime: record.updateTime,
              fileurl: record.fileurl
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsSubsistenceAssistanceInformationEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

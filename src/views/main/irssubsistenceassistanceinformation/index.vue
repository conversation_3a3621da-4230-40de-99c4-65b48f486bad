<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.ahap0016" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.ahap0015" allow-clear placeholder="请输入身份证号"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="低保类别">
                <a-select v-model="queryParam.bhax0005" allow-clear placeholder="请选择低保类别">
                  <a-select-option value="1">
                    收入型低保
                  </a-select-option>
                  <a-select-option value="2">
                    支出型贫困
                  </a-select-option>
                  <a-select-option value="3">
                    残疾人单列户施保
                  </a-select-option>
                  <a-select-option value="4">
                    重病型单列户施保
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.bhax0039"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="dblb" slot-scope="text, record">
          <span v-if="record.bhax0005==='1'">收入型低保</span>
          <span v-if="record.bhax0005==='2'">支出型贫困</span>
          <span v-if="record.bhax0005==='3'">残疾人单列户施保</span>
          <span v-if="record.bhax0005==='4'">重病型单列户施保</span>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import {
  irsSubsistenceAssistanceInformationPage,
  irsSubsistenceAssistanceInformationDelete
} from '@/api/modular/main/irssubsistenceassistanceinformation/irsSubsistenceAssistanceInformationManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'ahap0016'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        // {
        //   ellipsis: true,
        //   title: '联系电话',
        //   align: 'center',
        //   dataIndex: 'ahaf0008'
        // },
        {
          ellipsis: true,
          title: '身份证号码',
          align: 'center',
          dataIndex: 'ahap0015'
        },
        {
          ellipsis: true,
          title: '低保类别',
          align: 'center',
          dataIndex: 'bhax0005',
          scopedSlots: { customRender: 'dblb' }
        },
        {
          ellipsis: true,
          title: '户籍性质',
          align: 'center',
          dataIndex: 'azcp0013'
        },
        {
          ellipsis: true,
          title: '户籍地址',
          align: 'center',
          dataIndex: 'ahaf0005'
        },
        {
          ellipsis: true,
          title: '居住地址',
          align: 'center',
          dataIndex: 'ahaf0006'
        },
        {
          ellipsis: true,
          title: '申请救助原因',
          align: 'center',
          dataIndex: 'ahax0004'
        },
        {
          ellipsis: true,
          title: '年份',
          align: 'center',
          dataIndex: 'bhax0114'
        },
        {
          ellipsis: true,
          title: '月份',
          align: 'center',
          dataIndex: 'bhax0115'
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          width: '50px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return irsSubsistenceAssistanceInformationPage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
    // if (this.hasPerm('irsSubsistenceAssistanceInformation:edit') || this.hasPerm('irsSubsistenceAssistanceInformation:delete')) {
    //   this.columns.push({
    //     title: '操作',
    //     width: '150px',
    //     dataIndex: 'action',
    //     scopedSlots: { customRender: 'action' }
    //   })
    // }
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    irsSubsistenceAssistanceInformationDelete(record) {
      irsSubsistenceAssistanceInformationDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

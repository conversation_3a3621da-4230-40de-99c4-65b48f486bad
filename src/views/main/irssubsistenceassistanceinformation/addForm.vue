<template>
  <a-modal
    title="新增低保救助信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="户籍地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址" v-decorator="['ahaf0005', {rules: [{required: true, message: '请输入户籍地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地址" v-decorator="['ahaf0006', {rules: [{required: true, message: '请输入居住地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="联系电话"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入联系电话" v-decorator="['ahaf0008', {rules: [{required: true, message: '请输入联系电话！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申请人身份证"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请人身份证" v-decorator="['ahap0015', {rules: [{required: true, message: '请输入申请人身份证！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申请人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请人" v-decorator="['ahap0016', {rules: [{required: true, message: '请输入申请人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="救助证编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入救助证编号" v-decorator="['ahax0003', {rules: [{required: true, message: '请输入救助证编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申请救助原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请救助原因" v-decorator="['ahax0004', {rules: [{required: true, message: '请输入申请救助原因！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户银行"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户银行" v-decorator="['ahax0010', {rules: [{required: true, message: '请输入开户银行！'}]}]" />
        </a-form-item>
        <a-form-item
          label="银行账号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入银行账号" v-decorator="['ahax0011', {rules: [{required: true, message: '请输入银行账号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="月人均收入"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入月人均收入" v-decorator="['ahax0027', {rules: [{required: true, message: '请输入月人均收入！'}]}]" />
        </a-form-item>
        <a-form-item
          label="保障标准"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入保障标准" v-decorator="['ahax0036', {rules: [{required: true, message: '请输入保障标准！'}]}]" />
        </a-form-item>
        <a-form-item
          label="行政区划"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行政区划" v-decorator="['azaa0002', {rules: [{required: true, message: '请输入行政区划！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍性质"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍性质" v-decorator="['azcp0013', {rules: [{required: true, message: '请输入户籍性质！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭总人口"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="手机号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入手机号码" v-decorator="['azdf0011', {rules: [{required: true, message: '请输入手机号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="累计收入"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="申请类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请类别" v-decorator="['bhax0002', {rules: [{required: true, message: '请输入申请类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="低保类别 (1收入型低保，3残疾人单列户施保，2支出型贫困，4重病型单列户施保)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入低保类别 (1收入型低保，3残疾人单列户施保，2支出型贫困，4重病型单列户施保)" v-decorator="['bhax0005', {rules: [{required: true, message: '请输入低保类别 (1收入型低保，3残疾人单列户施保，2支出型贫困，4重病型单列户施保)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="致贫原因 (1疾病，5失业，2灾害，6失地，3残疾，99其他，4缺乏劳动力)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入致贫原因 (1疾病，5失业，2灾害，6失地，3残疾，99其他，4缺乏劳动力)" v-decorator="['bhax0008', {rules: [{required: true, message: '请输入致贫原因 (1疾病，5失业，2灾害，6失地，3残疾，99其他，4缺乏劳动力)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户人" v-decorator="['bhax0015', {rules: [{required: true, message: '请输入开户人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户人身份证"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户人身份证" v-decorator="['bhax0016', {rules: [{required: true, message: '请输入开户人身份证！'}]}]" />
        </a-form-item>
        <a-form-item
          label="累计月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入累计月份" v-decorator="['bhax0018', {rules: [{required: true, message: '请输入累计月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="累计支出"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="人均保障金"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="一般残人数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入一般残人数" v-decorator="['bhax0024', {rules: [{required: true, message: '请输入一般残人数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="重残人数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="保障总人口"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="一般残补差"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入一般残补差" v-decorator="['bhax0027', {rules: [{required: true, message: '请输入一般残补差！'}]}]" />
        </a-form-item>
        <a-form-item
          label="重残补差"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入重残补差" v-decorator="['bhax0028', {rules: [{required: true, message: '请输入重残补差！'}]}]" />
        </a-form-item>
        <a-form-item
          label="残疾补差金"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="户保障金"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="其他补助金"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="总发放金额"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="救助日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入救助日期" v-decorator="['bhax0033', {rules: [{required: true, message: '请输入救助日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="低保类型 (1A类（年审），2B类（季审）)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入低保类型 (1A类（年审），2B类（季审）)" v-decorator="['bhax0035', {rules: [{required: true, message: '请输入低保类型 (1A类（年审），2B类（季审）)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查人" v-decorator="['bhax0036', {rules: [{required: true, message: '请输入调查人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查时间" v-decorator="['bhax0037', {rules: [{required: true, message: '请输入调查时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查结果" v-decorator="['bhax0038', {rules: [{required: true, message: '请输入调查结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单据类型 默认：db_jz"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单据类型 默认：db_jz" v-decorator="['bhax0040', {rules: [{required: true, message: '请输入单据类型 默认：db_jz！'}]}]" />
        </a-form-item>
        <a-form-item
          label="省厅区划编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入省厅区划编码" v-decorator="['bhax0112', {rules: [{required: true, message: '请输入省厅区划编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民政部区划编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民政部区划编码" v-decorator="['bhax0113', {rules: [{required: true, message: '请输入民政部区划编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年份" v-decorator="['bhax0114', {rules: [{required: true, message: '请输入年份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入月份" v-decorator="['bhax0115', {rules: [{required: true, message: '请输入月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="版式地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入版式地址" v-decorator="['fileurl', {rules: [{required: true, message: '请输入版式地址！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsSubsistenceAssistanceInformationAdd } from '@/api/modular/main/irssubsistenceassistanceinformation/irsSubsistenceAssistanceInformationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsSubsistenceAssistanceInformationAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

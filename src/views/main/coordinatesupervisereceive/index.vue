<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.zpdwId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zt" placeholder="请选择状态">
                  <a-select-option v-for="(item, index) in ztDropDown" :key="index" :value="item.code">{{
                    item.name
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="10" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  :show-time="{
                    hideDisabledOptions: true,
                    defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
                  }"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <div style="margin: 10px">
      <a-button v-if="current" @click="current = 0">本省外出至外省</a-button>
      <a-button type="primary" v-if="!current">本省外出至外省</a-button>
      <a-button type="primary" v-if="current">外省外出至本省</a-button>
      <a-button v-if="!current" @click="current = 1">外省外出至本省</a-button>
    </div>
    <div v-show="current === 0">
      <a-card :bordered="false">
        <s-table ref="table" :columns="columnsSubmit" :data="loadData" :alert="false" :rowKey="(record) => record.id">
          <template class="table-operator" slot="operator">
            <a-button type="primary" icon="plus" @click="$refs.prosupervision.edit()">提请跨省外出监管</a-button>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="$refs.editForm.edit(record)">监管详情</a>
            <a-divider type="vertical" />
            <a-popconfirm
              placement="topRight"
              title="确认删除？"
              @confirm="() => coordinateSuperviseReceiveDelete(record)"
            >
              <a>删除</a>
            </a-popconfirm>
          </span>
        </s-table>
        <add-form ref="addForm" @ok="handleOk" />
        <edit-form ref="editForm" @ok="handleOk" />
        <prosupervision ref="prosupervision" @ok="handleOk" />
      </a-card>
    </div>
    <div v-show="current === 1">
      <a-card :bordered="false">
        <s-table
          ref="table1"
          :columns="columnsReceive"
          :data="loadData1"
          :alert="false"
          :rowKey="(record) => record.id"
          :rowSelection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template class="table-operator" slot="operator">
            <a-button
              type="primary"
              :disabled="disabledAccept"
              icon="plus"
              @click="$refs.acceptForm.open(selectedRows[0], true)"
            >受理</a-button
            >
            <a-button
              type="primary"
              :disabled="disabledFeedBack"
              icon="plus"
              @click="$refs.Regulatory.open(selectedRows[0], true)"
            >监管反馈</a-button
            >
          </template>
          <span slot="action" slot-scope="text, record">
            <a v-if="record.zt !== '0'" @click="$refs.acceptForm.open(record, false)">受理详情</a>
            <a-divider type="vertical" v-if="record.zt === '2'" />
            <a v-if="record.zt === '2'" @click="$refs.Regulatory.open(record, false)">反馈详情</a>
          </span>
        </s-table>
        <add-form ref="addForm" @ok="handleOk" />
        <edit-form ref="editForm" @ok="handleOk" />
        <prosupervision ref="prosupervision" @ok="handleOk" />
        <Regulatory ref="Regulatory" @ok="handleOk" />
        <accept-form ref="acceptForm" @ok="handleOk" />
      </a-card>
    </div>
  </div>
</template>
<script>
import prosupervision from './prosupervision.vue'
import Regulatory from './Regulatory.vue'
import { STable } from '@/components'
import moment from 'moment'
import {
  coordinateSuperviseReceivePage,
  coordinateSuperviseReceiveDelete
} from '@/api/modular/main/coordinatesupervisereceive/coordinateSuperviseReceiveManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import acceptForm from './acceptForm.vue'
import { coordinateSuperviseSubmitPage } from '@/api/modular/main/coordinatesupervisesubmit/coordinateSuperviseSubmitManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
export default {
  components: {
    prosupervision,
    STable,
    addForm,
    editForm,
    Regulatory,
    acceptForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 表头
      columnsReceive: [
        {
          ellipsis: true,
          title: '外出天数',
          align: 'center',
          dataIndex: 'wcts'
        },
        {
          ellipsis: true,
          title: '是否提请监管',
          align: 'center',
          dataIndex: 'sftqjg',
          customRender: (val) => {
            if (val === '0') {
              return '否'
            } else if (val === '1') {
              return '是'
            } else {
              return ''
            }
          }
        },
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'xm'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzdw'
        },
        {
          ellipsis: true,
          title: '委托单位',
          align: 'center',
          dataIndex: 'wtdwmc'
        },
        {
          ellipsis: true,
          title: '外出地点',
          align: 'center',
          dataIndex: 'wcmdd'
        },
        {
          ellipsis: true,
          title: '外出开始时间',
          align: 'center',
          dataIndex: 'wckssj',
          customRender: (val) => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          ellipsis: true,
          title: '外出结束时间',
          align: 'center',
          dataIndex: 'wcjssj',
          customRender: (val) => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        }
      ],
      columnsSubmit: [
        {
          ellipsis: true,
          title: '通报状态',
          align: 'center',
          dataIndex: 'tbzt',
          customRender: (val, record) => {
            return '已自动通报'
          }
        },
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'xm'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzdw'
        },
        {
          ellipsis: true,
          title: '外出原因',
          align: 'center',
          dataIndex: 'wcyy'
        },
        {
          ellipsis: true,
          title: '申请时间',
          align: 'center',
          dataIndex: 'wcsqsj',
          customRender: (val, record) => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          ellipsis: true,
          title: '外出目的地',
          align: 'center',
          dataIndex: 'wcmdd'
        },
        {
          ellipsis: true,
          title: '是否提请监管',
          align: 'center',
          dataIndex: 'sftqjg',
          customRender: (val, record) => {
            return '是'
          }
        }
      ],
      current: 0,
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象 本省至外省
      loadData: (parameter) => {
        return coordinateSuperviseSubmitPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      // 加载数据方法 必须为 Promise 对象 外省至本省
      loadData1: (parameter) => {
        return coordinateSuperviseReceivePage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      orgTree: [],
      ztDropDown: [],
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.columnsSubmit.push({
      title: '操作',
      width: '150px',
      align: 'center',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' }
    })
    this.columnsReceive.push({
      title: '操作',
      width: '150px',
      align: 'center',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' }
    })
    this.getOrgTree()
    this.sysDictTypeDropDown()
  },
  computed: {
    disabledAccept() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].zt !== '0'
    },
    disabledFeedBack() {
      return !this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].zt !== '1'
    }
  },
  watch: {
    current: {
      handler() {
        this.selectedRows = []
        this.selectedRowKeys = []
        this.$refs.table.clearSelected()
        this.$refs.table1.clearSelected()
        this.$refs.table.refresh()
        this.$refs.table1.refresh()
      }
    }
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD HH:mm:ss')
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD HH:mm:ss')
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      delete obj.dates
      return obj
    },
    coordinateSuperviseReceiveDelete(record) {
      coordinateSuperviseReceiveDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    handleOk() {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.clearSelected()
      this.$refs.table1.clearSelected()
      this.$refs.table.refresh()
      this.$refs.table1.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    sysDictTypeDropDown() {
      this.ztDropDown = this.$options.filters['dictData']('zt')
    },
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

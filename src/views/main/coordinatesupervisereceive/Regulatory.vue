<template>
  <sh-drawer
    title="提请协助监管"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">社区矫正对象基本信息
        </div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="委托编号">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="身份证">
                <a-input
                  :disabled="linkDisabled"
                  style="width: 100%"
                  placeholder="请输入身份证号"
                  v-decorator="['sfzh',{rules: [{ required: true, message: '请输入身份证！'},{
                    validator:IDValidator ,
                  },]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="cus-title-d" style="margin-top:35px ;">外出申请信息</div>
        <div style="background: white;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出申请编号">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入外出申请编号" v-decorator="['wcsqbh', {rules: [{required: true, message: '请输入外出申请编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请时间">
                <a-date-picker style="width:100%" :disabled="linkDisabled" placeholder="请选择申请时间" v-decorator="['wcsqsj', {rules: [{required: true, message: '请选择申请时间 ！'}]}]" @change="onChangewcsqsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出目的地">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入外出目的地" v-decorator="['wcmdd', {rules: [{required: true, message: '请输入外出目的地 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出开始时间">
                <a-date-picker style="width:100%" :disabled="linkDisabled" placeholder="请选择外出开始时间" v-decorator="['wckssj', {rules: [{required: true, message: '请选择外出开始时间 ！'}]}]" @change="onChangewckssj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出结束时间">
                <a-date-picker style="width:100%" :disabled="linkDisabled" placeholder="请选择外出结束时间" v-decorator="['wcjssj', {rules: [{required: true, message: '请选择外出结束时间 ！'}]}]" @change="onChangewcjssj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出原因">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入外出原因" v-decorator="['wcyy', {rules: [{required: true, message: '请输入外出原因 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>

        </div>
        <div class="cus-title-d" style="margin-top:35px ;">协助监管信息</div>
        <div style="background: white;height: 300px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="个别教育">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入个别教育" v-decorator="['gbjyzt', {rules: [{required: true, message: '请输入个别教育 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="个别教育内容">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入个别教育内容" v-decorator="['gbjynr', {rules: [{required: true, message: '请输入个别教育内容 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="个别教育地点">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入个别教育地点" v-decorator="['gbjydd', {rules: [{required: true, message: '请输入个别教育地点 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="个别教育日期">
                <a-date-picker style="width:100%" :disabled="linkDisabled" placeholder="请选择申请时间" v-decorator="['gbjyrq', {rules: [{required: true, message: '请选择申请时间 ！'}]}]" @change="onChangegbjyrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="个别教育机构">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入个别教育机构" v-decorator="['gbjyjg', {rules: [{required: true, message: '请输入个别教育机构 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报到日期">
                <a-date-picker style="width:100%" :disabled="linkDisabled" placeholder="请选择报道日期" v-decorator="['bdrq', {rules: [{required: true, message: '请选择报道日期 ！'}]}]" @change="onChangebdrq"/>
              </a-form-item>
            </a-col>

          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报到地点">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入报到地点" v-decorator="['bddd', {rules: [{required: true, message: '请输入个别教育地点 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报到方式">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['bdfs', {rules: [{required: true, message: '请选择报到方式 ！'}]}]"
                  show-search
                  placeholder="请选择报到方式"
                  :options="bdfsDropDown">
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="报到机构">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['bdjg', {rules: [{required: true, message: '请选择报到机构 ！'}]}]"
                  show-search
                  placeholder="请选择报到机构"
                  :options="sfjDropDown">
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
    <div
      style="display:flex;
      margin-left: 34%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <span style="flex: 1;margin: 10px;" v-if="!linkDisabled">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
      </span>
      <span style="flex: 1;">
        <a-button style="flex: 1;margin: 10px;" @click="handleCancel">关闭</a-button>
      </span>
    </div>
  </sh-drawer>
</template>

<script>
import { sysDictTypeDropDown } from '@/api/modular/system/dictManage'
import moment from 'moment'
import { coordinateSuperviseReceiveEdit } from '@/api/modular/main/coordinatesupervisereceive/coordinateSuperviseReceiveManage';

export default {
  data() {
    return {
      id: undefined,
      linkDisabled: false,
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      bdfsDropDown: [],
      sfjDropDown: [],
      bdrqDateString: '',
      gbjyrqDateString: '',
      wcjssjDateString: '',
      wckssjDateString: '',
      wcsqsjDateString: '',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    this.getdata()
  },
  methods: {
    moment,
    // 初始化方法
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    getdata() {
      sysDictTypeDropDown({ code: 'bdfs' }).then(res => {
        res.data.forEach(p => {
          this.bdfsDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'sfj' }).then(res => {
        res.data.forEach(p => {
          this.sfjDropDown.push({ value: p.code, label: p.value })
        })
      })
    },
    open(record, editable) {
      this.id = record.id
      this.visible = true
      this.linkDisabled = !editable
      setTimeout(() => {
        this.form.setFieldsValue(
          {
            gbjyzt: record.gbjyzt,
            gbjynr: record.gbjynr,
            gbjydd: record.gbjydd,
            gbjyjg: record.gbjyjg,
            bddd: record.bddd,
            bdfs: record.bdfs,
            bdjg: record.bdjg,
            wcsqbh: record.wcsqbh,
            wcmdd: record.wcmdd,
            wcyy: record.wcyy,
            id: record.id,
            wtdwmc: record.wtdwmc,
            wtdwId: record.wtdwId,
            wtbh: record.wtbh,
            wtdcs: record.wtdcs,
            wtwsfj: record.wtwsfj,
            xm: record.xm,
            nsysqjzrylx: record.nsysqjzrylx,
            xb: record.xb,
            sfzh: record.sfzh,
            jzddzP: record.jzddzP,
            jzddzC: record.jzddzC,
            jzddz: record.jzddz,
            sfyjzddzmx: record.sfyjzddzmx,
            jzddzmx: record.jzddzmx,
            hjsfyjzdxt: record.hjsfyjzdxt,
            hjdzP: record.hjdzP,
            hjdzC: record.hjdzC,
            hjdz: record.hjdz,
            sfyhjdzmx: record.sfyhjdzmx,
            hjdzmx: record.hjdzmx,
            hzdw: record.hzdw,
            nsyjzlb: record.nsyjzlb,
            sfyypxq: record.sfyypxq,
            ypxq: record.ypxq,
            ypxf: record.ypxf,
            fjx: record.fjx,
            pjjg: record.pjjg,
            zpwdmc: record.zpwdmc,
            zpdwId: record.zpdwId,
            zpr: record.zpr,
            zpbz: record.zpbz
          }
        )
      }, 100)
      // 时间单独处理

      if (record.wcsqsj != null) {
        this.form.getFieldDecorator('wcsqsj', { initialValue: moment(record.wcsqsj, 'YYYY-MM-DD') })
        this.wcsqsjDateString = moment(record.wcsqsj).format('YYYY-MM-DD')
      }

      if (record.wckssj != null) {
        this.form.getFieldDecorator('wckssj', { initialValue: moment(record.wckssj, 'YYYY-MM-DD') })
        this.wckssjDateString = moment(record.wckssj).format('YYYY-MM-DD')
      }

      if (record.wcjssj != null) {
        this.form.getFieldDecorator('wcjssj', { initialValue: moment(record.wcjssj, 'YYYY-MM-DD') })
        this.wcjssjDateString = moment(record.wcjssj).format('YYYY-MM-DD')
      }

      if (record.gbjyrq != null) {
        this.form.getFieldDecorator('gbjyrq', { initialValue: moment(record.gbjyrq, 'YYYY-MM-DD') })
        this.gbjyrqDateString = moment(record.gbjyrq).format('YYYY-MM-DD')
      }

      if (record.bdrq != null) {
        this.form.getFieldDecorator('bdrq', { initialValue: moment(record.bdrq, 'YYYY-MM-DD') })
        this.bdrqDateString = moment(record.bdrq).format('YYYY-MM-DD')
      }
    },
    handleSubmit() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.id = this.id
          values.bdrq = this.bdrqDateString
          values.gbjyrq = this.gbjyrqDateString
          values.wcjssj = this.wcjssjDateString
          values.wckssj = this.wckssjDateString
          values.wcsqsj = this.wcsqsjDateString
          values.zt = '2'

          coordinateSuperviseReceiveEdit(values).then((res) => {
            if (res.success) {
              this.$message.success('提交成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('提交失败')//  + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },

    onChangewcsqsj(date, dateString) {
      this.wcsqsjDateString = dateString
    },
    onChangewckssj(date, dateString) {
      this.wckssjDateString = dateString
    },
    onChangewcjssj(date, dateString) {
      this.wcjssjDateString = dateString
    },
    onChangegbjyrq(date, dateString) {
      this.gbjyrqDateString = dateString
    },
    onChangebdrq(date, dateString) {
      this.bdrqDateString = dateString
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>
<style scoped>
.title{
  width: 100%;
    /* height: 35px; */
    background: inherit;
    background-color: rgba(242, 242, 242, 1);
    border: 0 none;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    font-weight: 700;
    font-style: normal;
    text-align: left;
    line-height:35px;
}
</style>

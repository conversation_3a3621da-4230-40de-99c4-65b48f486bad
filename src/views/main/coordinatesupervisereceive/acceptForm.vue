<template>
  <sh-drawer
    title="受理"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="受理结果"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                :disabled="linkDisabled"
                v-decorator="['zt', {rules: [{required: true, message: '请选择受理结果！'}]}]"
                placeholder="请选择受理结果" >
                <a-select-option v-for="(item,index) in dropDown" :key="index" :value="item.value" >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="受理时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker
                :disabled="linkDisabled"
                style="width: 100%"
                placeholder="请选择受理时间"
                v-decorator="['slsj',{rules: [{ required: true, message: '请选择受理时间！' }]}]"
                @change="onChangeSlsj"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="24" :sm="24" >
            <a-form-item
              label="说明"
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2">
              <a-textarea :disabled="linkDisabled" :rows="4" placeholder="请输入内容" v-decorator="['slsm']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" >
            <a-form-item
              label="文书信息"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-upload
                :disabled="linkDisabled"
                :multiple="false"
                :showUploadList="true"
                :file-list="fileList"
                :remove="handleRemove"
                :before-upload="beforeUpload">
                <a-button type="primary" v-if="!linkDisabled"> <a-icon type="upload" />点击上传</a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </sh-drawer>
</template>

<script>
import { fileList, sysFileInfoUpload } from '@/api/modular/system/fileManage';
  import { coordinateSuperviseReceiveAcept } from '@/api/modular/main/coordinatesupervisereceive/coordinateSuperviseReceiveManage';
  import moment from 'moment';
  export default {
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
        linkDisabled: false,
        slsjDateString: '',
        dropDown: [{ label: '接收', value: '1' }, { label: '退回', value: '2' }],
        fileId: '',
        fileList: [],
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      open (record, editable) {
        this.visible = true
        this.id = record.id
        this.linkDisabled = !editable

        setTimeout(() => {
          this.form.setFieldsValue(
            {
              zt: record.zt === '0' ? null : record.zt,
              slsm: record.slsm
            }
          )
        }, 100)
        // 时间单独处理
        if (record.slsj != null) {
          this.form.getFieldDecorator('slsj', { initialValue: moment(record.slsj, 'YYYY-MM-DD') })
        }
        // 附件信息
        if (record.slws != null) {
          fileList({ ids: record.slws }).then(res => {
            if (res.success) {
              this.fileList = res.data
            }
          })
        }
      },
      handleRemove(file) {
        const index = this.fileList.indexOf(file)
        const newFileList = this.fileList.slice()
        newFileList.splice(index, 1)
        this.fileList = newFileList
      },
      beforeUpload(file) {
        this.fileList = [...this.fileList, file]
        return false
      },
      sureUpload() {
        this.fileId = []
        const list = []
        if (this.fileList != null) {
          this.fileList.forEach(file => {
            if (file.size != null) {
              const formData = new FormData()
              formData.append('file', file)
              var p1 = new Promise((resolve, reject) => {
                sysFileInfoUpload(Object.assign(formData)).then(res => {
                  if (res.success) {
                    this.fileId = [...this.fileId, res.data.id]
                    resolve(res.result)
                  } else {
                    this.$message.error(res.message)
                    this.uploadStatus = false
                    reject(res)
                  }
                  this.fileList = []
                })
              })
            }
            list.push(p1)
          })
        }
        return Promise.all(list).then(res => {
          this.uploadStatus = true
        })
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields(async (errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            await this.sureUpload()
            values.id = this.id
            values.slws = this.fileId.join(',')
            values.slsj = this.slsjDateString
            coordinateSuperviseReceiveAcept(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeSlsj(date, dateString) {
        this.slsjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.move-left{
  position:relative;
  left:-25px
}
</style>

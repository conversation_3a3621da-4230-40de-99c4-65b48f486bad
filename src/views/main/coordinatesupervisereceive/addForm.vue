<template>
  <a-modal
    title="新增接收外出监管协同"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="委托单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托单位" v-decorator="['wtdwmc', {rules: [{required: true, message: '请输入委托单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="委托单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托单位id" v-decorator="['wtdwId', {rules: [{required: true, message: '请输入委托单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="收到委托时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择收到委托时间" v-decorator="['sdwtsj',{rules: [{ required: true, message: '请选择收到委托时间！' }]}]" @change="onChangesdwtsj"/>
        </a-form-item>
        <a-form-item
          label="委托编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正人员ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社区矫正人员ID" v-decorator="['sqjzryId', {rules: [{required: true, message: '请输入社区矫正人员ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['sfzh', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzdw', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位ID" v-decorator="['jzdwId', {rules: [{required: true, message: '请输入矫正单位ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="外出申请编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入外出申请编号" v-decorator="['wcsqbh', {rules: [{required: true, message: '请输入外出申请编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="外出申请时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择外出申请时间" v-decorator="['wcsqsj',{rules: [{ required: true, message: '请选择外出申请时间！' }]}]" @change="onChangewcsqsj"/>
        </a-form-item>
        <a-form-item
          label="外出目的地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入外出目的地" v-decorator="['wcmdd', {rules: [{required: true, message: '请输入外出目的地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="外出开始时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择外出开始时间" v-decorator="['wckssj',{rules: [{ required: true, message: '请选择外出开始时间！' }]}]" @change="onChangewckssj"/>
        </a-form-item>
        <a-form-item
          label="外出结束时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择外出结束时间" v-decorator="['wcjssj',{rules: [{ required: true, message: '请选择外出结束时间！' }]}]" @change="onChangewcjssj"/>
        </a-form-item>
        <a-form-item
          label="外出原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入外出原因" v-decorator="['wcyy', {rules: [{required: true, message: '请输入外出原因！'}]}]" />
        </a-form-item>
        <a-form-item
          label="外出天数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入外出天数" v-decorator="['wcts', {rules: [{required: true, message: '请输入外出天数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否提请监管"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否提请监管" v-decorator="['sftqjg', {rules: [{required: true, message: '请输入是否提请监管！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个别教育主题"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个别教育主题" v-decorator="['gbjyzt', {rules: [{required: true, message: '请输入个别教育主题！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个别教育内容"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个别教育内容" v-decorator="['gbjynr', {rules: [{required: true, message: '请输入个别教育内容！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个别教育地点"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个别教育地点" v-decorator="['gbjydd', {rules: [{required: true, message: '请输入个别教育地点！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个别教育日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择个别教育日期" v-decorator="['gbjyrq',{rules: [{ required: true, message: '请选择个别教育日期！' }]}]" @change="onChangegbjyrq"/>
        </a-form-item>
        <a-form-item
          label="个别教育机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个别教育机构" v-decorator="['gbjyjg', {rules: [{required: true, message: '请输入个别教育机构！'}]}]" />
        </a-form-item>
        <a-form-item
          label="报到日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择报到日期" v-decorator="['bdrq',{rules: [{ required: true, message: '请选择报到日期！' }]}]" @change="onChangebdrq"/>
        </a-form-item>
        <a-form-item
          label="报到地点"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入报到地点" v-decorator="['bddd', {rules: [{required: true, message: '请输入报到地点！'}]}]" />
        </a-form-item>
        <a-form-item
          label="报到方式"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入报到方式" v-decorator="['bdfs', {rules: [{required: true, message: '请输入报到方式！'}]}]" />
        </a-form-item>
        <a-form-item
          label="报到机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入报到机构" v-decorator="['bdjg', {rules: [{required: true, message: '请输入报到机构！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（字典 0正常 1停用 2删除)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（字典 0正常 1停用 2删除)" v-decorator="['zt', {rules: [{required: true, message: '请输入状态（字典 0正常 1停用 2删除)！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { coordinateSuperviseReceiveAdd } from '@/api/modular/main/coordinatesupervisereceive/coordinateSuperviseReceiveManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        sdwtsjDateString: '',
        wcsqsjDateString: '',
        wckssjDateString: '',
        wcjssjDateString: '',
        gbjyrqDateString: '',
        bdrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdwtsj = this.sdwtsjDateString
            values.wcsqsj = this.wcsqsjDateString
            values.wckssj = this.wckssjDateString
            values.wcjssj = this.wcjssjDateString
            values.gbjyrq = this.gbjyrqDateString
            values.bdrq = this.bdrqDateString
            coordinateSuperviseReceiveAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdwtsj(date, dateString) {
        this.sdwtsjDateString = dateString
      },
      onChangewcsqsj(date, dateString) {
        this.wcsqsjDateString = dateString
      },
      onChangewckssj(date, dateString) {
        this.wckssjDateString = dateString
      },
      onChangewcjssj(date, dateString) {
        this.wcjssjDateString = dateString
      },
      onChangegbjyrq(date, dateString) {
        this.gbjyrqDateString = dateString
      },
      onChangebdrq(date, dateString) {
        this.bdrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

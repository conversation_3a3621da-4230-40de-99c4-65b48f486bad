<template>
  <sh-drawer
    title="提请协助监管"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">社区矫正对象基本信息
        </div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="委托编号">
                <a-input :disabled="disabled" allow-clear placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名">
                <a-input :disabled="disabled" allow-clear placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-form-item :labelCol="labelCol2" :wrapperCol="wrapperCol2" label="身份证">
                <a-input
                  :disabled="disabled"
                  style="width: 100%"
                  placeholder="请输入身份证号"
                  v-decorator="['sfzh',{rules: [{ required: true, message: '请输入身份证！'},{
                    validator:IDValidator ,
                  },]}]" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="cus-title-d">外出申请信息</div>
        <div style="background: white;height: 300px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出申请编号">
                <a-input :disabled="disabled" allow-clear placeholder="请输入外出申请编号" v-decorator="['wcsqbh', {rules: [{required: true, message: '请输入外出申请编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请时间">
                <a-date-picker style="width: 100%" :disabled="disabled" placeholder="请选择申请时间" v-decorator="['wcsqsj']" @change="onChangewcsqsj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出目的地">
                <a-input :disabled="disabled" allow-clear placeholder="请输入外出目的地" v-decorator="['wcmdd', {rules: [{required: true, message: '请输入外出目的地 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出开始时间">
                <a-date-picker style="width: 100%" :disabled="disabled" placeholder="请选择外出开始时间" v-decorator="['wckssj']" @change="onChangewckssj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出结束时间">
                <a-date-picker style="width: 100%" :disabled="disabled" placeholder="请选择外出开始时间" v-decorator="['wcjssj']" @change="onChangewcjssj"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="外出原因">
                <a-input :disabled="disabled" allow-clear placeholder="请输入外出原因" v-decorator="['wcyy', {rules: [{required: true, message: '请输入外出原因 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>

        </div>

      </a-form>
    </a-spin>
    <div
      v-show="!disabled"
      style="display:flex;
      margin-left: 34%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <span style="flex: 1;margin: 10px;">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
      </span>
      <span style="flex: 1;">
        <a-button style="flex: 1;margin: 10px;" @click="handleCancel">关闭</a-button>
      </span>
    </div>
  </sh-drawer>
</template>

<script>
  import moment from 'moment'
  import { coordinateSuperviseReceiveEdit } from '@/api/modular/main/coordinatesupervisereceive/coordinateSuperviseReceiveManage';
  export default {
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
        disabled: false,
        wcjssjDateString: '',
        wckssjDateString: '',
        sdwtsjDateString: '',
        csrqDateString: '',
        ypxqksrqDateString: '',
        ypxqjsrqDateString: '',
        wcsqsjDateString: '',
        pjrqDateString: '',
        zpsjDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      onChangewckssj(date, dateString) {
        this.wckssjDateString = dateString
      },
      onChangewcjssj(date, dateString) {
        this.wcjssjDateString = dateString
      },
      onChangewcsqsj(date, dateString) {
        this.wcsqsjDateString = dateString
      },
      // 初始化方法
      edit (record) {
        this.visible = true

        if (record) {
          this.disabled = true
        } else {
          this.disabled = false
        }
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              wcmdd: record.wcmdd,
              wcyy: record.wcyy,
              wcsqbh: record.wcsqbh,
              id: record.id,
              wtdwmc: record.wtdwmc,
              wtdwId: record.wtdwId,
              wtbh: record.wtbh,
              wtdcs: record.wtdcs,
              wtwsfj: record.wtwsfj,
              xm: record.xm,
              nsysqjzrylx: record.nsysqjzrylx,
              xb: record.xb,
              sfzh: record.sfzh,
              jzddzP: record.jzddzP,
              jzddzC: record.jzddzC,
              jzddz: record.jzddz,
              sfyjzddzmx: record.sfyjzddzmx,
              jzddzmx: record.jzddzmx,
              hjsfyjzdxt: record.hjsfyjzdxt,
              hjdzP: record.hjdzP,
              hjdzC: record.hjdzC,
              hjdz: record.hjdz,
              sfyhjdzmx: record.sfyhjdzmx,
              hjdzmx: record.hjdzmx,
              hzdw: record.hzdw,
              nsyjzlb: record.nsyjzlb,
              sfyypxq: record.sfyypxq,
              ypxq: record.ypxq,
              ypxf: record.ypxf,
              fjx: record.fjx,
              pjjg: record.pjjg,
              zpwdmc: record.zpwdmc,
              zpdwId: record.zpdwId,
              zpr: record.zpr,
              zpbz: record.zpbz
            }
          )
        }, 100)
        // 时间单独处理

        if (record.wcsqsj != null) {
            this.form.getFieldDecorator('wcsqsj', { initialValue: moment(record.wcsqsj, 'YYYY-MM-DD') })
        }
        if (record.wckssj != null) {
            this.form.getFieldDecorator('wckssj', { initialValue: moment(record.wckssj, 'YYYY-MM-DD') })
        }
        if (record.wcjssj != null) {
            this.form.getFieldDecorator('wcjssj', { initialValue: moment(record.wcjssj, 'YYYY-MM-DD') })
        }

        if (record.sdwtsj != null) {
            this.form.getFieldDecorator('sdwtsj', { initialValue: moment(record.sdwtsj, 'YYYY-MM-DD') })
        }
        this.sdwtsjDateString = moment(record.sdwtsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqksrq != null) {
            this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
        }
        this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqjsrq != null) {
            this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
        }
        this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zpsj != null) {
            this.form.getFieldDecorator('zpsj', { initialValue: moment(record.zpsj, 'YYYY-MM-DD') })
        }
        this.zpsjDateString = moment(record.zpsj).format('YYYY-MM-DD')
      },
      IDValidator (rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
      // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }

            values.wckssj = this.wckssjDateString
            values.wcjssj = this.wcjssjDateString

            values.wcsqsj = this.wcsqsjDateString
            // values.sdwtsj = this.sdwtsjDateString
            // values.csrq = this.csrqDateString
            // values.ypxqksrq = this.ypxqksrqDateString
            // values.ypxqjsrq = this.ypxqjsrqDateString
            // values.pjrq = this.pjrqDateString
            // values.zpsj = this.zpsjDateString
            coordinateSuperviseReceiveEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdwtsj(date, dateString) {
        this.sdwtsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangeypxqksrq(date, dateString) {
        this.ypxqksrqDateString = dateString
      },
      onChangeypxqjsrq(date, dateString) {
        this.ypxqjsrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangezpsj(date, dateString) {
        this.zpsjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style scoped>
.title{
    border-width: 0;
    width: 100%;
    /* height: 35px; */
    background: inherit;
    background-color: rgba(242, 242, 242, 1);
    border: none;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    font-weight: 700;
    font-style: normal;
    text-align: left;
    line-height:35px;
}
</style>

<template>
  <a-drawer
    title="详情"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer custom-wrapper"
    @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-divider orientation="left" orientation-margin="0px">退回信息</a-divider>
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item
              label="退回原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <sh-select v-decorator="['thlx']" disabled dictType="thlx"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="案件类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <sh-select v-decorator="['type']" disabled dictType="xtbh"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="协同发起时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker v-decorator="['tssj']" disabled style="width:100%;"/>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item
              label="退回时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-date-picker v-decorator="['thsj']" disabled style="width:100%;"/>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item
              label="具体原因"
              :labelCol="{ xs: { span: 24 }, sm: { span: 3 } }"
              :wrapperCol="{ xs: { span: 24 }, sm: { span: 20 } }">
              <a-input v-decorator="['thyy']" disabled/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-drawer>

</template>

<script>
  import moment from 'moment'
  export default {
    data () {
      return {
        drawerWidth: 1000,
        labelCol: { xs: { span: 24 }, sm: { span: 6 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created() {
      this.drawerWidth = window.innerWidth - 200
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              thlx: record.thlx,
              type: record.type,
              thyy: record.thyy
            }
          )
        }, 100)
        // 时间单独处理
        if (record.thsj != null) {
            this.form.getFieldDecorator('thsj', { initialValue: moment(record.thsj, 'YYYY-MM-DD') })
        }
        // 时间单独处理
        if (record.tssj != null) {
            this.form.getFieldDecorator('tssj', { initialValue: moment(record.tssj, 'YYYY-MM-DD') })
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="案件类型">
                <a-select style="width: 100%" v-model="queryParam.xtbh" placeholder="请输入退回类型">
                  <a-select-option v-for="(item,index) in xtbhData" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="退回原因">
                <a-select style="width: 100%" v-model="queryParam.thlx" placeholder="请输入退回类型">
                  <a-select-option v-for="(item,index) in thlxData" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="退回时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
          <a-divider type="vertical" v-if="record.type !=='XTBH44002'"/>
          <a v-if="record.type !=='XTBH44002'" @click="detail(record)">接收详情</a>
        </span>
        <span slot="xtbh" slot-scope="text">
          {{ 'xtbh' | dictType(text) }}
        </span>
        <span slot="thlx" slot-scope="text">
          {{ 'thlx' | dictType(text) }}
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
      <corr-accept-form ref="CorrAcceptForm" @ok="handleOk"/>
      <inv-accept-form ref="InvAcceptForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { acceptReturnInfoPage } from '@/api/modular/main/acceptreturninfo/acceptReturnInfoManage'
  import editForm from './editForm.vue'
  import corrAcceptForm from '@/views/main/acceptcorrectionobject/AcceptForm.vue'
  import invAcceptForm from '@/views/main/acceptinvestinfo/AcceptForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage'
  export default {
    components: {
      STable,
      editForm,
      corrAcceptForm,
      invAcceptForm
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
          { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jsdwmc' },
          { ellipsis: true, title: '案件类型', align: 'center', dataIndex: 'type', scopedSlots: { customRender: 'xtbh' } },
          { ellipsis: true, title: '退回原因', align: 'center', dataIndex: 'thlx', scopedSlots: { customRender: 'thlx' } },
          {
            ellipsis: true,
            title: '退回时间',
            align: 'center',
            dataIndex: 'thsj',
            customRender: (val) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD HH:mm')
            }
          },
          {
            ellipsis: true,
            title: '协同发起时间',
            align: 'center',
            dataIndex: 'tssj',
            customRender: (val) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD HH:mm')
            }
          },
          { title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return acceptReturnInfoPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: [],
        orgTree: [],
        thlxData: [],
        xtbhData: []
      }
    },
    created () {
      getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
      this.thlxData = this.$options.filters['dictData']('thlx')
      this.xtbhData = this.$options.filters['dictData']('xtbh')
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      detail(record) {
        switch (record.type) {
          case 'XTBH4003':
          case 'XTBH4022_1':
          case 'XTBH4052_1':
          case 'XTBH4072_1':
            record.xtbh = record.type
            this.$refs.CorrAcceptForm.add(record, true);
            break;
          case 'XTBH31001':
          case 'XTBH31003':
            this.$refs.InvAcceptForm.add(record, true);
            break;
          default:
            break;
        }
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

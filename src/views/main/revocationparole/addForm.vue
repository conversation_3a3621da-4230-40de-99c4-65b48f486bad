<template>
  <a-modal
    title="提请撤销假释协同流程"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    footer=""
  >
    <a-steps v-if="zt===1" type="navigation" v-model:value="current" :style="stepStyle">
      <a-step status="process" title="征求检察建议" />
      <a-step status="wait" :disabled="true" title="接收检察建议" />
      <a-step status="wait" :disabled="true" title="提请撤销假释" />
    </a-steps>
    <a-steps v-else-if="zt===2" type="navigation" v-model:value="current" :style="stepStyle">
      <a-step status="finish" title="征求检察建议" />
      <a-step status="process" title="接收检察建议" />
      <a-step status="wait" :disabled="true" title="提请撤销假释" />
    </a-steps>
    <a-steps v-else-if="zt===3" type="navigation" v-model:value="current" :style="stepStyle">
      <a-step status="finish" title="征求检察建议" />
      <a-step status="finish" title="接收检察建议" />
      <a-step status="process" title="提请撤销假释" />
    </a-steps>
    <a-steps v-else-if="zt===4" type="navigation" v-model:value="current" :style="stepStyle">
      <a-step status="finish" title="征求检察建议" />
      <a-step status="finish" title="接收检察建议" />
      <a-step status="finish" title="提请撤销假释" />
    </a-steps>
    <template>
      <div v-show="current===0" class="bk">
        <div class="bk boxLabel">矫正对象基本信息（如有部分同步信息缺失,请手动补全）</div>
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 姓名 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input disabled class="width" placeholder="请输入姓名 " v-decorator="['xm', {rules: [{required: true, message: '选择矫正对象 ！'}]}]"/>
                  <a-button @click="chooseSqjzry" v-if="showbuttonflag">选择矫正对象</a-button>
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 证件类型 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zjlx', {rules: [{required: true, message: '请选择证件类型 ！'}]}]"
                    show-search
                    :disabled="zt !== 0 && zt !== 1"
                    placeholder="请选择证件类型"
                    style="width: 200px"
                  >
                    <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 性别 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['xb', {rules: [{required: true, message: '请选择选择性别 ！'}]}]"
                    show-search
                    :disabled="zt !== 0 && zt !== 1"
                    placeholder="请选择选择性别"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >

                  <a-date-picker
                    v-decorator="['csrq', {rules: [{required: true, message: '请选择出生日期 ！'}]}]"
                    v-if="zt===1"
                  />
                  <a-date-picker
                    v-else
                    disabled
                    v-decorator="['csrq', {rules: [{required: true, message: '请选择出生日期 ！'}]}]"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 证件号码 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input class="width" v-if="zt===1" placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
                  <a-input class="width" v-else disabled placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 国籍 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['gj', {rules: [{required: true, message: '请选择选择国籍'}]}]"
                    show-search
                    :disabled="zt !== 0 && zt !== 1"
                    placeholder="请选择选择国籍"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in guojiDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="current===0">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <VDistpicker
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    v-if="zt===1"
                    style="display: inline-block"></VDistpicker>
                  <VDistpicker
                    v-else
                    disabled
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <!-- <a-col :md="8" :sm="24">

              </a-col> -->
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===1" style="width:400px" placeholder="请输入居住地明细地址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]" />
                  <a-input v-else disabled style="width:400px" placeholder="请输入居住地明细地址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 户籍地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback

                >

                  <VDistpicker
                    v-if="zt===1"
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                  <VDistpicker
                    v-else
                    disabled
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 户籍地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input style="width:400px" v-if="zt===1" placeholder="请输入户籍明细地址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                  <a-input style="width:400px" v-else disabled placeholder="请输入户籍明细地址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 矫正类别 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['jzlb', {rules: [{required: true, message: '请选择选择矫正类别 ！'}]}]"
                    show-search
                    :disabled="zt !== 0 && zt !== 1"
                    placeholder="请选择选择矫正类别"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in jzlbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="社区矫正执行地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===1" style="width:200px" placeholder="请输入社区矫正执行地" v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入社区矫正执行地！'}]}]" />
                  <a-input v-else disabled style="width:200px" placeholder="请输入社区矫正执行地" v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入社区矫正执行地！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 司法所 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input class="width" v-if="zt===1" placeholder="请输入司法所司法所" v-decorator="['sfs', {rules: [{required: true, message: '请输入司法所司法所 ！'}]}]" />
                  <a-input class="width" v-else disabled placeholder="请输入司法所司法所" v-decorator="['sfs', {rules: [{required: true, message: '请输入司法所司法所 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正开始日期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===1" v-decorator="['sqjzksrq', {rules: [{required: true, message: '请选择矫正开始日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['sqjzksrq', {rules: [{required: true, message: '请选择矫正开始日期 ！'}]}]" />
                  <!-- <a-input placeholder="请输入矫正开始日期" v-decorator="['sqjzksrq', {rules: [{required: true, message: '请输入 社区矫正案件编号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item
                  label="矫正结束日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===1" v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入矫正结束日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入矫正结束日期 ！'}]}]" />

                  <!-- <a-input placeholder="请输入矫正结束日期 " v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入 统一赋号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正期限 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===1" class="width" placeholder="请输入矫正期限" v-decorator="['jzqx', {rules: [{required: true, message: '请输入矫正期限！'}]}]" />
                  <a-input v-else disabled class="width" placeholder="请输入矫正期限" v-decorator="['jzqx', {rules: [{required: true, message: '请输入矫正期限！'}]}]" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正期间行为表现 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===1" style="width:400px" placeholder="请输入矫正期间行为表现 " v-decorator="['jzqjbxqk', {rules: [{required: true, message: '请输入矫正期间行为表现！'}]}]" />
                  <a-input v-else disabled style="width:400px" placeholder="请输入矫正期间行为表现 " v-decorator="['jzqjbxqk', {rules: [{required: true, message: '请输入矫正期间行为表现！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <div
              style="font-family: '微软雅黑', sans-serif;
            font-weight: 400;font-style: normal;color: #1078C9;
            background-color: rgba(228, 243, 255, 1);
             margin-bottom: 20px;"
              class="bk">征求检察建议信息</div>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 社区矫正决定机关 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['fy', {rules: [{required: true, message: '请选择社区矫正决定机关'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsFy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                    v-if="zt===1"
                  >
                  </a-select>
                  <a-select
                    v-else
                    disabled
                    v-decorator="['fy', {rules: [{required: true, message: '请选择社区矫正决定机关'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsFy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-input placeholder="请输入 统一赋号 " v-decorator="['tyfh', {rules: [{required: true, message: '请输入 统一赋号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 征求检察院名称 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['jcy', {rules: [{required: true, message: '请选择征求检察院名称'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsJcy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                    v-if="zt===1"
                  >
                  </a-select>
                  <a-select
                    v-else
                    disabled
                    v-decorator="['jcy', {rules: [{required: true, message: '请选择征求检察院名称'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsJcy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 建议文书号 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===1" placeholder="请输入建议文书号 " class="width" v-decorator="['zqyjjyswh', {rules: [{required: true, message: '请输入建议文书号 ！'}]}]" />
                  <a-input v-else disabled placeholder="请输入建议文书号 " class="width" v-decorator="['zqyjjyswh', {rules: [{required: true, message: '请输入建议文书号 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 提请日期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===1" v-decorator="['zqyjrq', {rules: [{required: true, message: '请选择提请日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['zqyjrq', {rules: [{required: true, message: '请选择提请日期 ！'}]}]" />

                </a-form-item>
              </a-col>
              <a-col :md="18" :sm="18" :pull="2">
                <a-form-item
                  label=" 提请理由 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    v-if="zt===1"
                    v-decorator="['zqyjtqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    placeholder="请输入提请理由"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-textarea
                    v-else
                    disabled
                    v-decorator="['zqyjtqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    placeholder="请输入提请理由"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <!-- <a-input style="height:100px" placeholder="请输入提请理由" v-decorator="['tqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 提请依据 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['tqyj', {rules: [{required: true, message: '请选择提请依据 ！'}]}]"
                    show-search
                    :disabled="zt !== 0 && zt !== 1"
                    placeholder="请选择提请依据"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in tqyjDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
      </div>
      <div v-show="current===1" class="bk">
        <div class="bk boxLabel">检察建议</div>
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="18" :sm="18" :pull="2" >
                <a-form-item
                  label="撤销假释检察建议"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    v-if="zt===2"
                    :value="cxhxjcyj"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-textarea
                    v-else
                    disabled
                    :value="cxhxjcyj"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="18" :sm="18" :pull="2" >
                <a-form-item
                  label="备注"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    :value="jcyjbz"
                    v-if="zt===2"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-textarea
                    v-else
                    disabled
                    :value="jcyjbz"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="12" >
                <a-form-item
                  label="文书文号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input
                    v-if="zt===2"
                    :value="jcyjwswh"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-input
                    v-else
                    disabled
                    :value="jcyjwswh"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>

              <a-col :md="12" :pull="1">
                <a-form-item
                  label="反馈时间"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input
                    disabled
                    v-if="zt===2"
                    :value="yjfksj"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-input

                    v-else
                    disabled
                    :value="yjfksj"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>

          </a-form>
        </a-spin>
      </div>
      <div v-show="current===2&&(zt===3||zt===4)" class="bk">
        <div class="bk boxLabel">矫正对象基本信息</div>
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 姓名 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" class="width" placeholder="请输入姓名 " v-decorator="['xm', {rules: [{required: true, message: '请输入姓名 ！'}]}]" />
                  <a-input v-else disabled class="width" placeholder="请输入姓名 " v-decorator="['xm', {rules: [{required: true, message: '请输入姓名 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 证件类型 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['zjlx', {rules: [{required: true, message: '请选择证件类型 ！'}]}]"
                    show-search
                    :disabled="zt !== 3"
                    placeholder="请选择证件类型"
                    style="width: 200px"
                  >
                    <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 性别 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['xb', {rules: [{required: true, message: '请选择选择性别 ！'}]}]"
                    show-search
                    :disabled="zt !== 3"
                    placeholder="请选择选择性别"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >

                  <a-date-picker
                    v-decorator="['csrq', {rules: [{required: true, message: '请选择出生日期 ！'}]}]"
                    v-if="zt===3"
                  />
                  <a-date-picker
                    v-else
                    disabled
                    v-decorator="['csrq', {rules: [{required: true, message: '请选择出生日期 ！'}]}]"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 证件号码 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" class="width" placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
                  <a-input v-else disabled class="width" placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 国籍 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['gj', {rules: [{required: true, message: '请选择选择国籍'}]}]"
                    show-search
                    :disabled="zt !== 3"
                    placeholder="请选择选择国籍"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in guojiDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="current===2">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <VDistpicker
                    v-if="zt===3"
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    style="display: inline-block"></VDistpicker>
                  <VDistpicker
                    v-else
                    disabled
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">

              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" style="width:400px" placeholder="请输入居住地明细地址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]" />
                  <a-input v-else disabled style="width:400px" placeholder="请输入居住地明细地址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :pull="0" v-if="current===2">
                <a-form-item
                  label=" 户籍地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >

                  <VDistpicker
                    v-if="zt===3"
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                  <VDistpicker
                    v-else
                    disabled
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 户籍地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" style="width:400px" placeholder="请输入户籍明细地址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                  <a-input v-else disabled style="width:400px" placeholder="请输入户籍明细地址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 矫正类别 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['jzlb', {rules: [{required: true, message: '请选择选择矫正类别 ！'}]}]"
                    show-search
                    :disabled="zt !== 3"
                    placeholder="请选择选择矫正类别"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in jzlbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="社区矫正执行地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" style="width:200px" placeholder="请输入社区矫正执行地" v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入社区矫正执行地！'}]}]" />
                  <a-input v-else disabled style="width:200px" placeholder="请输入社区矫正执行地" v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入社区矫正执行地！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 司法所 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" class="width" placeholder="请输入司法所司法所" v-decorator="['sfs', {rules: [{required: true, message: '请输入司法所司法所 ！'}]}]" />
                  <a-input v-else disabled class="width" placeholder="请输入司法所司法所" v-decorator="['sfs', {rules: [{required: true, message: '请输入司法所司法所 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正开始日期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===3" v-decorator="['sqjzksrq', {rules: [{required: true, message: '请选择矫正开始日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['sqjzksrq', {rules: [{required: true, message: '请选择矫正开始日期 ！'}]}]" />
                  <!-- <a-input placeholder="请输入矫正开始日期" v-decorator="['sqjzksrq', {rules: [{required: true, message: '请输入 社区矫正案件编号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item
                  label="矫正结束日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===3" v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入矫正结束日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入矫正结束日期 ！'}]}]" />
                  <!-- <a-input placeholder="请输入矫正结束日期 " v-decorator="['sqjzjsrq', {rules: [{required: true, message: '请输入 统一赋号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正期限 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" class="width" placeholder="请输入矫正期限" v-decorator="['jzqx', {rules: [{required: true, message: '请输入矫正期限！'}]}]" />
                  <a-input v-else disabled class="width" placeholder="请输入矫正期限" v-decorator="['jzqx', {rules: [{required: true, message: '请输入矫正期限！'}]}]" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 矫正期间行为表现 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" style="width:400px" placeholder="请输入矫正期间行为表现 " v-decorator="['jzqjbxqk', {rules: [{required: true, message: '请输入矫正期间行为表现！'}]}]" />
                  <a-input v-else disabled style="width:400px" placeholder="请输入矫正期间行为表现 " v-decorator="['jzqjbxqk', {rules: [{required: true, message: '请输入矫正期间行为表现！'}]}]" />
                </a-form-item>
              </a-col>
            </a-row>
            <div
              style="font-family: '微软雅黑', sans-serif;
            font-weight: 400;font-style: normal;color: #1078C9;
            background-color: rgba(228, 243, 255, 1);
             margin-bottom: 20px;"
              class="bk">提请信息</div>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 法院 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['fy', {rules: [{required: true, message: '请选择法院'}]}]"
                    show-search
                    v-if="zt===3"
                    style="width: 200px"
                    :options="optionsFy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <a-select
                    v-decorator="['fy', {rules: [{required: true, message: '请选择法院'}]}]"
                    show-search
                    v-else
                    disabled
                    style="width: 200px"
                    :options="optionsFy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>
                  <!-- <a-input placeholder="请输入 统一赋号 " v-decorator="['tyfh', {rules: [{required: true, message: '请输入 统一赋号 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 征求检察院名称 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['jcy', {rules: [{required: true, message: '请选择征求检察院'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsJcy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                    v-if="zt===3"
                  >
                  </a-select>
                  <a-select
                    v-else
                    disabled
                    v-decorator="['jcy', {rules: [{required: true, message: '请选择征求检察院'}]}]"
                    show-search
                    style="width: 200px"
                    :options="optionsJcy"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                  >
                  </a-select>

                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="zt!==1" :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 建议文书号 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-input v-if="zt===3" placeholder="请输入建议文书号 " class="width" v-decorator="['jwswh', {rules: [{required: true, message: '请输入建议文书号 ！'}]}]" />
                  <a-input v-else disabled placeholder="请输入建议文书号 " class="width" v-decorator="['jwswh', {rules: [{required: true, message: '请输入建议文书号 ！'}]}]" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 提请日期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker v-if="zt===3" v-decorator="['tqrq', {rules: [{required: true, message: '请选择提请日期 ！'}]}]" />
                  <a-date-picker v-else disabled v-decorator="['tqrq', {rules: [{required: true, message: '请选择提请日期 ！'}]}]" />

                </a-form-item>
              </a-col>
              <a-col :md="18" :sm="18" :pull="2" >
                <a-form-item
                  label=" 提请理由 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-textarea
                    v-if="zt===3"
                    v-decorator="['tqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    placeholder="请输入提请理由"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <a-textarea
                    v-else
                    disabled
                    v-decorator="['tqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                    placeholder="请输入提请理由"
                    :auto-size="{ minRows: 5, maxRows: 5 }"
                  />
                  <!-- <a-input style="height:100px" placeholder="请输入提请理由" v-decorator="['tqly', {rules: [{required: true, message: '请输入提请理由 ！'}]}]" /> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="zt===3">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label=" 提请依据 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-select
                    v-decorator="['tqyj', {rules: [{required: true, message: '请选择提请依据 ！'}]}]"
                    show-search
                    :disabled="zt !== 3"
                    placeholder="请选择提请依据"
                    style="width: 200px">
                    <a-select-option v-for="(item,index) in tqyjDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
      </div>
    </template>
    <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
    <div style="margin-left:70%;margin-top:10px" v-if="current!==1">
      <div v-if="(current===0&&zt===1)||(current===2&&zt===3)">
        <a-button v-if="editbuttonflag" type="primary" @click="handleEdit(zt)" >暂存</a-button>
        <a-button v-else type="primary" @click="handleSubmit(zt)" >暂存</a-button>

        <a-button v-if="editbuttonflag" type="primary" @click="handleEdit(zt+1)" >提交</a-button>
        <a-button v-else type="primary" @click="handleSubmit(zt+1)" >提交</a-button>
        <a-button @click="handleCancel" type="dashed">取消</a-button>
      </div>

    </div>

  </a-modal>
</template>

<script>
  import chooseSqjzryRadio from '../correctionobjectinformation/chooseSqjzryRadio.vue'
  import moment from 'moment'
  import { revocationParoleAdd, revocationParoleEdit } from '@/api/modular/main/revocationparole/revocationParoleManage'
  import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
  import VDistpicker from 'v-distpicker/src/Distpicker'
  export default {
    data() {
      return {
        current: 0,
        stepStyle: { marginBottom: '10px', boxShadow: '0px -1px 0 0 #e8e8e8 inset' },
        hjdCode: undefined,
        xzdCode: undefined,
        id: undefined,
        editbuttonflag: false,
        jzdxdata: { xm: '' },
        jcyjwswh: undefined,
        cxhxjcyj: undefined,
        yjfksj: undefined,
        jcyjbz: undefined,
        handleChange: (value) => {
          console.log(`selected ${value}`)
        },
        handleBlur: () => {
          console.log('blur')
        },
        handleFocus: () => {
          console.log('focus')
        },
        zt: 1,
        showbuttonflag: true,
        value: undefined,
        hjdxz: '',
        sqjzzxd: '',
        jzdw: '',
        jzdwmc: '',
        optionsJcy: [],
        optionsFy: [],
        zjlxDropDown: [],
        xbDropDown: [],
        guojiDropDown: [],
        jzlbDropDown: [],
        tqyjDropDown: [],
        jzdareaCode: { province: '', city: '', area: '' },
        hjareaCode: { province: '', city: '', area: '' },
        xzdxz: '',
        labelCol: { xs: { span: 24 }, sm: { span: 9 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 15 } },
        csrqDateString: '',
        pjrqDateString: '',
        jzqxqrDateString: '',
        jzqxzrDateString: '',
        sqjzksrqDateString: '',
        sqjzjsrqDateString: '',
        zqyjrqDateString: '',
        tqrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created() {
      this.getData()
    },
    methods: {
      getData() {
        const dictOption = this.$options
        this.zjlxDropDown = dictOption.filters['dictData']('zjlx')
        this.xbDropDown = dictOption.filters['dictData']('xb')
        this.guojiDropDown = dictOption.filters['dictData']('guoji')
        this.jzlbDropDown = dictOption.filters['dictData']('jzlb')
        this.tqyjDropDown = dictOption.filters['dictData']('tqyj')

        extOrgInfoList({ type: 20, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.optionsFy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          })
        })
        extOrgInfoList({ type: 30, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.optionsJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          })
        })
      },
      jzdx(jzdx) {
        this.jzdxdata = jzdx
        this.hjareaCode.province = this.jzdxdata.hjszsName
        this.hjareaCode.city = this.jzdxdata.hjszdsName
        this.hjareaCode.area = this.jzdxdata.hjszxqName
        this.jzdareaCode.province = this.jzdxdata.gdjzdszsName
        this.jzdareaCode.city = this.jzdxdata.gdjzdszdsName
        this.jzdareaCode.area = this.jzdxdata.gdjzdszxqName
        this.jzdw = jzdx.jzjg
        this.jzdwmc = jzdx.jzjgName
        this.form.setFieldsValue(
          {
            hjdCode: '',
            xzdCode: '',
            hjdp: jzdx.hjszsName,
            hjdc: jzdx.hjszdsName,
            xzdp: jzdx.gdjzdszsName,
            xzdc: jzdx.gdjzdszxqName,
            zqyjjyswh: '',
            // zqyjrq: '',
            zqyjtqly: '',
            zqyjtqyj: '',
            gj: jzdx.gj === ('02' || '03') ? 'ZZZ' : 'CHN',
            jzqjbxqk: '', // ' 矫正期间行为表现 ',
            id: '',
            tyfh: '', // ' 统一赋号 ',
            sqjzajbh: '', // ' 社区矫正案件编号 ',
            zfbh: '', //  ' 罪犯编号 ',
            xm: jzdx.xm, //   ' 姓名 ',
            sqjzryId: '',
            xb: jzdx.xb, //  ' 性别 ',
            zjlx: '111', // ' 证件类型 ',
            zjhm: jzdx.sfzh, //  ' 证件号码 ',
            csrq: moment(jzdx.csrq.slice(0, 10), 'YYYY-MM-DD'), // ' 出生日期 ',
            hjd: jzdx.hjszxqName, // ' 户籍地 ',
            hjdxz: jzdx.hjszdmx, // ' 户籍地详址 ',
            xzd: jzdx.gdjzdszxqName, //  ' 现住地 ',
            xzdxz: jzdx.gdjzdmx, // ' 现住地详址 ',
            sxpjjg: '', // ' 生效判决机关 ',
            jwswh: '', // ' 判决文书文号 ',
            pjrq: undefined, //  ' 判决日期 ',
            pjzm: '', //   ' 判决罪名 ',
            pjqtzm: '', //  ' 判决其他罪名 ',
            zx: '', //  ' 主刑 ',
            fjx: '', //   ' 附加刑 ',
            jzlnr: '', // ' 禁止令内容 ',
            jzqxqr: undefined, // ' 禁止期限起日 ',
            jzqxzr: undefined, // ' 禁止期止限日 ',
            jzlb: jzdx.jzlb.slice(1, 2), // ' 矫正类别 ',
            jdjg: '', // ' 决定机关 ',
            sfs: jzdx.jzjgName, //  ' 司法所 ',
            sqjzksrq: moment(jzdx.sqjzksrq.slice(0, 10), 'YYYY-MM-DD'), // ' 社区矫正开始日期 ',
            sqjzjsrq: moment(jzdx.sqjzjsrq.slice(0, 10), 'YYYY-MM-DD'), // ' 社区矫正结束日期 ',
            jzqx: jzdx.sqjzqx, // ' 矫正期限 ',
            sqjzzxd: '', // ' 社区矫正执行地 ',
            jyswh: '', // ' 建议书文号 ',
            zqyjrq: undefined, // '征求建议日期',
            tqrq: undefined, //  ' 提请日期 ',
            tqly: '', //  ' 提请理由 ',
            tqyj: '',
            jcy: '', // '征求检察院',
            jcymc: '', // '征求检察院名称',
            fy: '', //  '提请法院',
            fymc: '', //  '提请法院名称',
            comment: '' // '提请撤销假释';
          })
      },
      chooseSqjzry() {
        // 关联社区矫正对象
        this.$refs.chooseSqjzryRadio.choose('stay')
      },
      // 初始化方法
      add (record) {
        this.current = 0
        this.visible = true
        if (record === undefined) {
          this.editbuttonflag = false
          this.showbuttonflag = true
          this.zt = 1
        } else {
          this.id = record.id
           this.editbuttonflag = true
          this.showbuttonflag = false
          this.zt = parseInt(record.zt)
          this.jcyjwswh = record.jcyjwswh
          this.cxhxjcyj = record.cxhxjcyj
          this.yjfksj = record.yjfksj
          this.jcyjbz = record.jcyjbz
          this.hjareaCode.province = record.hjdp
          this.hjareaCode.city = record.hjdc
          this.hjareaCode.area = record.hjd
          this.jzdareaCode.province = record.xzdp
          this.jzdareaCode.city = record.xzdc
          this.jzdareaCode.area = record.xzd
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                hjdCode: record.hjdCode,
                xzdCode: record.xzdCode,
                //  jyswh: record.jyswh,
                hjdp: record.hjdp,
                hjdc: record.hjdc,
                xzdp: record.xzdp,
                xzdc: record.xzdc,
                gj: record.gj,
                jzqjbxqk: record.jzqjbxqk, // ' 矫正期间行为表现 ',
                id: record.id,
                tyfh: '', // ' 统一赋号 ',
                sqjzajbh: '', // ' 社区矫正案件编号 ',
                zfbh: '', //  ' 罪犯编号 ',
                xm: record.xm, //   ' 姓名 ',
                sqjzryId: '',
                xb: record.xb, //  ' 性别 ',
                zjlx: record.zjlx, // ' 证件类型 ',
                zjhm: record.zjhm, //  ' 证件号码 ',
                csrq: moment(record.csrq.slice(0, 10), 'YYYY-MM-DD'), // ' 出生日期 ',
                hjd: record.hjd, // ' 户籍地 ',
                hjdxz: record.hjdxz, // ' 户籍地详址 ',
                xzd: record.xzd, //  ' 现住地 ',
                xzdxz: record.xzdxz, // ' 现住地详址 ',
                sxpjjg: '', // ' 生效判决机关 ',
                pjwswh: '', // ' 判决文书文号 ',
                pjrq: undefined, //  ' 判决日期 ',
                pjzm: '', //   ' 判决罪名 ',
                pjqtzm: '', //  ' 判决其他罪名 ',
                zx: '', //  ' 主刑 ',
                fjx: '', //   ' 附加刑 ',
                jzlnr: '', // ' 禁止令内容 ',
                jzqxqr: undefined, // ' 禁止期限起日 ',
                jzqxzr: undefined, // ' 禁止期止限日 ',
                jzlb: record.jzlb, // ' 矫正类别 ',
                jdjg: '', // ' 决定机关 ',
                sfs: record.sfs, //  ' 司法所 ',
                sqjzksrq: moment(record.sqjzksrq.slice(0, 10), 'YYYY-MM-DD'), // ' 社区矫正开始日期 ',
                sqjzjsrq: moment(record.sqjzjsrq.slice(0, 10), 'YYYY-MM-DD'), // ' 社区矫正结束日期 ',
                jzqx: record.jzqx, // ' 矫正期限 ',
                sqjzzxd: record.sqjzzxd, // ' 社区矫正执行地 ',
                // jyswh: record.jyswh, // ' 建议书文号 ',
                // zqyjrq: undefined, // '征求建议日期',
                // tqrq: moment(record.tqrq.slice(0, 10), 'YYYY-MM-DD'), //  ' 提请日期 ',
                // tqly: record.tqly, //  ' 提请理由 ',
                // tqyj: record.tqyj,
                jcy: record.jcy, // '征求检察院',
                jcymc: record.jcymc, // '征求检察院名称',
                fy: record.fy, //  '提请法院',
                fymc: record.fymc, //  '提请法院名称',
                zqyjjyswh: record.zqyjjyswh, // 征求意见建议书文号
                zqyjrq: moment(record.zqyjrq.slice(0, 10), 'YYYY-MM-DD'), // 征求意见日期
                zqyjtqly: record.zqyjtqly, // 征求意见提请理由
                zqyjtqyj: record.zqyjtqyj, // 征求意见提请依据
                comment: '', // '提请撤销假释';
                jyswh: record.jyswh, // 撤缓建议书文号
                tqrq: moment(record.tqrq.slice(0, 10), 'YYYY-MM-DD'), // 撤缓提请日期
                tqly: record.tqly, // 撤缓提请理由
                tqyj: record.tqyj // 撤缓提请依据
              }
            )
          }, 100)
        }
      },
      /**
       * 提交表单
       */
      changeChoseCityjzd: function (e) { // 后执行
        this.jzdareaCode.province = e.province.value
        this.jzdareaCode.city = e.city.value
        this.jzdareaCode.area = e.area.value
        console.log('e', e)
        this.xzdCode = e.area.code
        // if (!this.respSuccess) { // 区渲染失败
        //     this.choseCityMap({ code: e.city.code, value: e.city.value })
        // }
      },
      changeChoseCityhj: function (e) { // 后执行
        this.hjareaCode.province = e.province.value
        this.hjareaCode.city = e.city.value
        this.hjareaCode.area = e.area.value
        console.log('e', e)
        this.hjdCode = e.area.code
        // if (!this.respSuccess) { // 区渲染失败
        //     this.choseCityMap({ code: e.city.code, value: e.city.value })
        // }
      },
      handleSubmit(zt) {
        const { form: { validateFields } } = this
        this.confirmLoading = true

        validateFields((errors, values) => {
          if (!errors) {
            const FY = values.fy
            const jcy = values.jcy
            values.fymc = FY.split('|')[1]
            values.jcymc = jcy.split('|')[1]
            values.sqjzryId = this.jzdxdata.id
            values.tqrq = moment(values.tqrq).format('YYYY-MM-DD')
            values.zqyjrq = moment(values.zqyjrq).format('YYYY-MM-DD')
            values.csrq = moment(values.csrq).format('YYYY-MM-DD')
            values.sqjzjsrq = moment(values.sqjzjsrq).format('YYYY-MM-DD')
            values.sqjzksrq = moment(values.sqjzksrq).format('YYYY-MM-DD')
            values.tqrq = moment(values.tqrq).format('YYYY-MM-DD')
            values.hjdCode = this.hjdCode
            values.xzdCode = this.xzdCode
            values.zt = zt
            values.jzdw = this.jzdw
            values.jzdwmc = this.jzdwmc
            if (this.hjareaCode.province && this.hjareaCode.city && this.hjareaCode.area) {
              values.hjd = this.hjareaCode.area
              values.hjdp = this.hjareaCode.province
              values.hjdc = this.hjareaCode.city
            } else {
              this.$message.error('选择户籍省市区')
            }
            if (this.jzdareaCode.province && this.jzdareaCode.city && this.jzdareaCode.area) {
              values.xzd = this.jzdareaCode.area
              values.xzdp = this.jzdareaCode.province
              values.xzdc = this.jzdareaCode.city
            } else {
              this.$message.error('选择现住地省市区')
            }

            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }

            revocationParoleAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.form.resetFields()
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            console.log(errors)
            this.confirmLoading = false
          }
        })
      },
      handleEdit(zt) {
        const { form: { validateFields } } = this
        this.confirmLoading = true

        validateFields((errors, values) => {
          if (!errors) {
            const FY = values.fy
            const jcy = values.jcy
            values.id = this.id
            values.fymc = FY.split('|')[1]
            values.jcymc = jcy.split('|')[1]
            values.sqjzryId = this.jzdxdata.id
            values.tqrq = moment(values.tqrq).format('YYYY-MM-DD')
            values.zqyjrq = moment(values.zqyjrq).format('YYYY-MM-DD')
            values.csrq = moment(values.csrq).format('YYYY-MM-DD')
            values.sqjzjsrq = moment(values.sqjzjsrq).format('YYYY-MM-DD')
            values.sqjzksrq = moment(values.sqjzksrq).format('YYYY-MM-DD')
            values.tqrq = moment(values.tqrq).format('YYYY-MM-DD')
            values.zt = zt
            values.hjdCode = this.hjdCode
            values.xzdCode = this.xzdCode
            if (this.hjareaCode.province && this.hjareaCode.city && this.hjareaCode.area) {
              values.hjd = this.hjareaCode.area
              values.hjdp = this.hjareaCode.province
              values.hjdc = this.hjareaCode.city
            } else {
              this.$message.error('选择户籍省市区')
            }
            if (this.jzdareaCode.province && this.jzdareaCode.city && this.jzdareaCode.area) {
              values.xzd = this.jzdareaCode.area
              values.xzdp = this.jzdareaCode.province
              values.xzdc = this.jzdareaCode.city
            } else {
              this.$message.error('选择现住地省市区')
            }

            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }

            revocationParoleEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('修改成功')
                this.confirmLoading = false
                this.form.resetFields()
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('修改失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            console.log(errors)
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
          this.hjareaCode.province = ''
          this.hjareaCode.city = ''
          this.hjareaCode.area = ''
          this.jzdareaCode.province = ''
          this.jzdareaCode.city = ''
          this.jzdareaCode.area = ''
          this.form.resetFields()
          this.visible = false
      }
    },
    components: { VDistpicker, chooseSqjzryRadio }
  }
</script>
<style scoped>
.bk{
  border-style: solid;border-width: 1px;
  border-color: rgba(204, 204, 204, 1);
}
.boxLabel{
  font-family: '微软雅黑', sans-serif;font-weight: 400;
  font-style: normal;color: #1078C9;
  background-color: rgba(228, 243, 255, 1);
  margin-bottom: 20px;
}

.width{
  width: 200px
}
</style>

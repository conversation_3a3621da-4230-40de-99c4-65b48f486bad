<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('acceptParoleExecute:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据状态">
                <a-select v-model="queryParam.zt" placeholder="请选择状态" >
                  <a-select-option v-for="(item,index) in ztDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="送达时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <a-tree-select
                  v-model="queryParam.jsdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="数据来源(机构类型)">
                  <a-input v-model="queryParam.sjlylx" allow-clear placeholder="请输入数据来源(机构类型)"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="接收单位">
                  <a-input v-model="queryParam.jsdw" allow-clear placeholder="请输入接收单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="接收单位名称">
                  <a-input v-model="queryParam.jsdwmc" allow-clear placeholder="请输入接收单位名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="推送单位">
                  <a-input v-model="queryParam.tsdw" allow-clear placeholder="请输入推送单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="推送单位名称">
                  <a-input v-model="queryParam.tsdwmc" allow-clear placeholder="请输入推送单位名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="送达时间">
                  <a-date-picker style="width: 100%" placeholder="请选择送达时间" v-model="queryParam.sdsjDate" @change="onChangesdsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="统一赋号">
                  <a-input v-model="queryParam.tyfh" allow-clear placeholder="请输入统一赋号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="法院案件标识">
                  <a-input v-model="queryParam.fyajbs" allow-clear placeholder="请输入法院案件标识"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="案件名称">
                  <a-input v-model="queryParam.ajmc" allow-clear placeholder="请输入案件名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="罪犯编号">
                  <a-input v-model="queryParam.zfbh" allow-clear placeholder="请输入罪犯编号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="曾用名">
                  <a-input v-model="queryParam.cym" allow-clear placeholder="请输入曾用名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="民族">
                  <a-input v-model="queryParam.mz" allow-clear placeholder="请输入民族"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件类型">
                  <a-input v-model="queryParam.zjlx" allow-clear placeholder="请输入证件类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件号码">
                  <a-input v-model="queryParam.zjhm" allow-clear placeholder="请输入证件号码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="出生日期">
                  <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-model="queryParam.csrqDate" @change="onChangecsrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="犯罪时是否未成年">
                  <a-input v-model="queryParam.fzssfwcn" allow-clear placeholder="请输入犯罪时是否未成年"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="未成年">
                  <a-input v-model="queryParam.wcn" allow-clear placeholder="请输入未成年"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否有精神病">
                  <a-input v-model="queryParam.sfyjsb" allow-clear placeholder="请输入是否有精神病"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="鉴定机构">
                  <a-input v-model="queryParam.jdjg" allow-clear placeholder="请输入鉴定机构"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否有传染病">
                  <a-input v-model="queryParam.sfycrb" allow-clear placeholder="请输入是否有传染病"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="传染病类型">
                  <a-input v-model="queryParam.crblx" allow-clear placeholder="请输入传染病类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="文化程度">
                  <a-input v-model="queryParam.whcd" allow-clear placeholder="请输入文化程度"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="婚姻状况">
                  <a-input v-model="queryParam.hyzk" allow-clear placeholder="请输入婚姻状况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="职业">
                  <a-input v-model="queryParam.zy" allow-clear placeholder="请输入职业"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="就业就学情况">
                  <a-input v-model="queryParam.jyjxqk" allow-clear placeholder="请输入就业就学情况"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="现政治面貌">
                  <a-input v-model="queryParam.xzzmm" allow-clear placeholder="请输入现政治面貌"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="原政治面貌">
                  <a-input v-model="queryParam.yzzmm" allow-clear placeholder="请输入原政治面貌"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="原工作单位">
                  <a-input v-model="queryParam.ygzdw" allow-clear placeholder="请输入原工作单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="单位联系电话">
                  <a-input v-model="queryParam.dwlxdh" allow-clear placeholder="请输入单位联系电话"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人联系电话">
                  <a-input v-model="queryParam.grlxdh" allow-clear placeholder="请输入个人联系电话"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="国籍">
                  <a-input v-model="queryParam.gj" allow-clear placeholder="请输入国籍"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有无家庭成员及主要社会关系">
                  <a-input v-model="queryParam.ywjtcyjzyshgx" allow-clear placeholder="请输入有无家庭成员及主要社会关系"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="照片">
                  <a-input v-model="queryParam.zp" allow-clear placeholder="请输入照片"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="户籍地是否与居住地相同">
                  <a-input v-model="queryParam.hjdsfyjzdxt" allow-clear placeholder="请输入户籍地是否与居住地相同"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="住所地">
                  <a-input v-model="queryParam.zsd" allow-clear placeholder="请输入住所地"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="住所地详细地址">
                  <a-input v-model="queryParam.zsdxxdz" allow-clear placeholder="请输入住所地详细地址"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="户籍所在地">
                  <a-input v-model="queryParam.hjszd" allow-clear placeholder="请输入户籍所在地"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="户籍地址明细">
                  <a-input v-model="queryParam.hjdzmx" allow-clear placeholder="请输入户籍地址明细"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否有前科">
                  <a-input v-model="queryParam.sfyqk" allow-clear placeholder="请输入是否有前科"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否累犯">
                  <a-input v-model="queryParam.sflf" allow-clear placeholder="请输入是否累犯"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正类别">
                  <a-input v-model="queryParam.jzlb" allow-clear placeholder="请输入矫正类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="调查评估日期">
                  <a-date-picker style="width: 100%" placeholder="请选择调查评估日期" v-model="queryParam.dcpgrqDate" @change="onChangedcpgrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="调查评估意见">
                  <a-input v-model="queryParam.dcpgyj" allow-clear placeholder="请输入调查评估意见"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="生效判决书字号">
                  <a-input v-model="queryParam.sxpjszh" allow-clear placeholder="请输入生效判决书字号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="判决文书生效日期">
                  <a-date-picker style="width: 100%" placeholder="请选择判决文书生效日期" v-model="queryParam.pjwssxrqDate" @change="onChangepjwssxrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="犯罪类型">
                  <a-input v-model="queryParam.fzlx" allow-clear placeholder="请输入犯罪类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="具体罪名">
                  <a-input v-model="queryParam.jtzm" allow-clear placeholder="请输入具体罪名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否数罪并罚">
                  <a-input v-model="queryParam.sfszbf" allow-clear placeholder="请输入是否数罪并罚"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="判决刑种">
                  <a-input v-model="queryParam.pjxz" allow-clear placeholder="请输入判决刑种"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="判决刑期开始日期">
                  <a-date-picker style="width: 100%" placeholder="请选择判决刑期开始日期" v-model="queryParam.pjxqksrqDate" @change="onChangepjxqksrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="判决刑期结束日期">
                  <a-date-picker style="width: 100%" placeholder="请选择判决刑期结束日期" v-model="queryParam.pjxqjsrqDate" @change="onChangepjxqjsrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有期徒刑期限">
                  <a-input v-model="queryParam.yqtxqx" allow-clear placeholder="请输入有期徒刑期限"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="附加刑">
                  <a-input v-model="queryParam.fjx" allow-clear placeholder="请输入附加刑"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="审判机关名称">
                  <a-input v-model="queryParam.spjgmc" allow-clear placeholder="请输入审判机关名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="剥夺政治权利年限">
                  <a-input v-model="queryParam.bdzzqlnx" allow-clear placeholder="请输入剥夺政治权利年限"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="具体罚款金额">
                  <a-input v-model="queryParam.jtfkje" allow-clear placeholder="请输入具体罚款金额"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="驱逐出境">
                  <a-input v-model="queryParam.qzcj" allow-clear placeholder="请输入驱逐出境"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="没收财产">
                  <a-input v-model="queryParam.mscc" allow-clear placeholder="请输入没收财产"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否“五独”">
                  <a-input v-model="queryParam.sfwd" allow-clear placeholder="请输入是否“五独”"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否“五涉”">
                  <a-input v-model="queryParam.sfws" allow-clear placeholder="请输入是否“五涉”"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否有“四史”">
                  <a-input v-model="queryParam.sfyss" allow-clear placeholder="请输入是否有“四史”"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释裁定日期">
                  <a-date-picker style="width: 100%" placeholder="请选择假释裁定日期" v-model="queryParam.jscdrqDate" @change="onChangejscdrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释考验期">
                  <a-input v-model="queryParam.jskyq" allow-clear placeholder="请输入假释考验期"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释考验期起日">
                  <a-date-picker style="width: 100%" placeholder="请选择假释考验期起日" v-model="queryParam.jskyqqrDate" @change="onChangejskyqqr"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释考验期止日">
                  <a-date-picker style="width: 100%" placeholder="请选择假释考验期止日" v-model="queryParam.jskyqzrDate" @change="onChangejskyqzr"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释裁定书文号">
                  <a-input v-model="queryParam.jscdswh" allow-clear placeholder="请输入假释裁定书文号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="假释裁定机关">
                  <a-input v-model="queryParam.jscdjg" allow-clear placeholder="请输入假释裁定机关"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正机构">
                  <a-input v-model="queryParam.jzjg" allow-clear placeholder="请输入矫正机构"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="交付执行日期">
                  <a-date-picker style="width: 100%" placeholder="请选择交付执行日期" v-model="queryParam.jfzxrqDate" @change="onChangejfzxrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="执行通知书日期">
                  <a-date-picker style="width: 100%" placeholder="请选择执行通知书日期" v-model="queryParam.zxtzsrqDate" @change="onChangezxtzsrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="移交罪犯机关类型">
                  <a-input v-model="queryParam.yjzfjglx" allow-clear placeholder="请输入移交罪犯机关类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="移交罪犯机关名称">
                  <a-input v-model="queryParam.yjzfjgmc" allow-clear placeholder="请输入移交罪犯机关名称"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('acceptParoleExecute:edit')" >
          <a-button type="primary" v-if="hasPerm('acceptParoleExecute:edit')" :disabled="!hasAppoint" @click="zhipai">指派至司法所</a-button>
          <a-button type="primary" v-if="hasPerm('acceptParoleExecute:edit')" :disabled="!hasFeedback" icon="rollback" @click="fankui">反馈</a-button>
          <a-popconfirm v-if="hasPerm('acceptParoleExecute:delete')" placement="topRight" title="确认删除？" @confirm="() => acceptParoleExecuteDelete()">
            <a-button :disabled="!(selectedRowKeys.length === 1)" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('acceptParoleExecute:edit')" @click="$refs.detailFormRef.edit(record)">详情</a>
        </span>

        <span slot="zt" slot-scope="text">
          {{ 'accept_data_status' | dictType(text) }}
        </span>
        <span slot="sjlylx" slot-scope="text">
          {{ 'xtjgdm' | dictType(text) }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
      <detailForm ref="detailFormRef"/>
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { acceptParoleExecutePage, acceptParoleExecuteDelete } from '@/api/modular/main/acceptparoleexecute/acceptParoleExecuteManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import detailForm from './detailForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      detailForm,
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '数据状态',
            align: 'center',
            dataIndex: 'zt',
            scopedSlots: { customRender: 'zt' }
          },
          {
          ellipsis: true,
          title: '数据来源',
            align: 'center',
            dataIndex: 'sjlylx',
            scopedSlots: { customRender: 'sjlylx' }
          },
          {
          ellipsis: true,
          title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '接收单位',
            align: 'center',
            dataIndex: 'jsdwmc'
          },
          {
          ellipsis: true,
          title: '送达时间',
            align: 'center',
            dataIndex: 'sdsj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD HH:mm')
            }
          },
          {
          ellipsis: true,
          title: '反馈时间',
            align: 'center',
            dataIndex: 'fksj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD HH:mm')
            }
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return acceptParoleExecutePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        ztDropDown: [],
        selectedRowKeys: [],
        selectedRows: [],
        hasFeedback: false,
        hasAppoint: false
      }
    },
    created () {
      this.getOrgTree()
      this.sysDictTypeDropDown()
      if (this.hasPerm('acceptParoleExecute:edit') || this.hasPerm('acceptParoleExecute:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      acceptParoleExecuteDelete (record) {
        if (this.selectedRows.length === 1) {
          acceptParoleExecuteDelete(this.selectedRows[0]).then((res) => {
            if (res.success) {
              this.$message.success('删除成功')
              this.$refs.table.refresh()
            } else {
              this.$message.error('删除失败') // + res.message
            }
          })
        } else {
          this.$message.error('请选择一条记录')
        }
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.hasFeedback = false
        this.hasAppoint = false
        if (this.selectedRows.length === 1) {
          if (this.selectedRows[0].zt === '0') {
            // 待接收
            this.hasAppoint = true
          }
          if (this.selectedRows[0].zt === '1') {
            // 待反馈
            this.hasFeedback = true
          }
        }
      },
      zhipai () {
        if (this.selectedRows.length === 1) {
          this.$refs.editForm.edit(this.selectedRows[0], false, this.orgTree)
        } else {
          this.$message.error('请选择一条记录')
        }
        this.$refs.table.clearSelected()
      },
      fankui () {
        if (this.selectedRows.length === 1) {
          this.$refs.addForm.init(this.selectedRows[0], this.orgTree)
        } else {
          this.$message.error('请选择一条记录')
        }
        this.$refs.table.clearSelected()
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown () {
        this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

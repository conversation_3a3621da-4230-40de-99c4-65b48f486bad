<template>
  <a-modal
    title="假释执行反馈信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <!--案件信息-->
        <div
          class="border"
          style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
        >案件信息</div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
              <a-form-item
                label="案件赋号"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['tyfh']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="法院案件标识"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['fyajbs']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="案件名称"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['ajmc']" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--社区矫正对象信息-->
        <div
          class="border"
          style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
        >社区矫正对象信息</div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="罪犯编号"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['zfbh']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="姓名"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input placeholder="请输入姓名" v-decorator="['xm']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="证件类型"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select placeholder="请选择证件类型" v-decorator="['zjlx']">
                  <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="证件号码"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input placeholder="请输入证件号码" v-decorator="['zjhm']" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--送达回执-->
        <div
          class="border"
          style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
        >送达回执</div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="社区矫正机关"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-tree-select
                  :disabled="true"
                  v-decorator="['sqjzjg']"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="入矫日期"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                <a-date-picker style="width: 100%" v-decorator="['rjrq']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="执行通知书回执文号"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input placeholder="请输执行通知书回执文号" v-decorator="['zxtzshzwh']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="反馈结果"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-tree-select
                  v-decorator="['fkjg']"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="fkjgTree"
                  placeholder="请选择反馈结果"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="回执材料"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-upload
                  :multiple="false"
                  :showUploadList="true"
                  :file-list="fileList"
                  :remove="handleRemove"
                  :before-upload="beforeUpload">
                  <a-button> <a-icon type="upload" />上传文件</a-button>
                </a-upload>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="说明："
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                has-feedback
              >
                文书来源于电子卷宗已盖章文书
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--区县司法局反馈信息-->
        <div
          class="border"
          style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
        >区县司法局反馈信息</div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="反馈人"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['fkr']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="反馈时间"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-date-picker :disabled="true" style="width: 100%" v-decorator="['fksj']" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  acceptParoleExecuteDetail,
  acceptParoleExecuteEdit
} from '@/api/modular/main/acceptparoleexecute/acceptParoleExecuteManage'
  import { mapGetters } from 'vuex';
  import moment from 'moment';
import { sysFileInfoUpload } from '@/api/modular/system/fileManage';
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        orgTree: [],
        fksjDateString: '',
        zjlxDropDown: [],
        fkjgTree: [
          {
            children: [{
              children: [],
              id: '11',
              parentId: '1',
              title: '信息无误，确认接收',
              value: '11'

            }, {
              children: [],
              id: '12',
              parentId: '1',
              title: '存在部分信息错误，确认接收',
              value: '12'
            }],
            id: '1',
            parentId: '0',
            title: '正常接收',
            value: '1',
            disabled: true
          }, {
            children: [{
              children: [],
              id: '21',
              parentId: '2',
              title: '信息错误，非我区管辖矫正对象',
              value: '21'
            }, {
              children: [],
              id: '22',
              parentId: '2',
              title: '数据滞后，已办理手续',
              value: '22'
            }, {
              children: [],
              id: '23',
              parentId: '2',
              title: '数据重复发送',
              value: '23'
            }],
            id: '2',
            parentId: '0',
            title: '退回',
            value: '2',
            disabled: true
          }, {
            children: [],
            id: '3',
            parentId: '0',
            title: '其他',
            value: '3'
          }],
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        fileList: [],
        fileId: []
      }
    },
    computed: {
      ...mapGetters(['nickname'])
    },
    methods: {
      // 初始化方法
      init (record, orgTree) {
        this.visible = true
        this.orgTree = orgTree
        this.sysDictTypeDropDown()
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              taskId: record.taskId,
              zt: record.zt,
              sjlylx: record.sjlylx,
              jsdw: record.jsdw,
              jsdwmc: record.jsdwmc,
              tsdw: record.tsdw,
              tsdwmc: record.tsdwmc,
              tyfh: record.tyfh,
              fyajbs: record.fyajbs,
              ajmc: record.ajmc,
              zfbh: record.zfbh,
              xm: record.xm,
              cym: record.cym,
              xb: record.xb,
              mz: record.mz,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              fzssfwcn: record.fzssfwcn,
              wcn: record.wcn,
              sfyjsb: record.sfyjsb,
              jdjg: record.jdjg,
              sfycrb: record.sfycrb,
              crblx: record.crblx,
              whcd: record.whcd,
              hyzk: record.hyzk,
              zy: record.zy,
              jyjxqk: record.jyjxqk,
              xzzmm: record.xzzmm,
              yzzmm: record.yzzmm,
              ygzdw: record.ygzdw,
              dwlxdh: record.dwlxdh,
              grlxdh: record.grlxdh,
              gj: record.gj,
              ywjtcyjzyshgx: record.ywjtcyjzyshgx,
              zp: record.zp,
              hjdsfyjzdxt: record.hjdsfyjzdxt,
              zsd: record.zsd,
              zsdxxdz: record.zsdxxdz,
              hjszd: record.hjszd,
              hjdzmx: record.hjdzmx,
              sfyqk: record.sfyqk,
              sflf: record.sflf,
              jzlb: record.jzlb,
              dcpgyj: record.dcpgyj,
              sxpjszh: record.sxpjszh,
              fzlx: record.fzlx,
              jtzm: record.jtzm,
              sfszbf: record.sfszbf,
              pjxz: record.pjxz,
              yqtxqx: record.yqtxqx,
              fjx: record.fjx,
              spjgmc: record.spjgmc,
              bdzzqlnx: record.bdzzqlnx,
              jtfkje: record.jtfkje,
              qzcj: record.qzcj,
              mscc: record.mscc,
              sfwd: record.sfwd,
              sfws: record.sfws,
              sfyss: record.sfyss,
              jskyq: record.jskyq,
              jscdswh: record.jscdswh,
              jscdjg: record.jscdjg,
              jzjg: record.jzjg,
              yjzfjglx: record.yjzfjglx,
              yjzfjgmc: record.yjzfjgmc
            }
          )
        }, 100)
        // 加载附件
        acceptParoleExecuteDetail(record.id).then((res) => {
          if (res.success) {
            this.fileList = res.data.hzclList
          } else {
            this.$message.error('附件加载失败')// + res.message
          }
        })

        if (record.rjrq != null) {
          this.form.getFieldDecorator('rjrq', { initialValue: moment(record.rjrq, 'YYYY-MM-DD') })
        }
        this.form.getFieldDecorator('fkr', { initialValue: this.nickname })
        const now = moment()
        this.form.getFieldDecorator('fksj', { initialValue: now })
        this.fksjDateString = now.format('YYYY-MM-DD')
      },
      handleRemove(file) {
        const index = this.fileList.indexOf(file)
        const newFileList = this.fileList.slice()
        newFileList.splice(index, 1)
        this.fileList = newFileList
      },
      beforeUpload(file) {
        this.fileList = [...this.fileList, file]
        return false
      },
      sureUpload() {
        this.fileId = []
        const list = []
        console.log(this.fileList, '当前所有文件集合')
        if (this.fileList) {
          this.fileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.fileId = [...this.fileId, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.fileList = []
              })
            })
          }
          list.push(p1)
        })
        }

        return Promise.all(list).then(res => {
          this.uploadStatus = true
        })
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields(async (errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            await this.sureUpload()
            values.hzcl = this.fileId.join(',')
            values.zt = '4'
            values.fksj = this.fksjDateString
            acceptParoleExecuteEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown () {
        this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      }
    }
  }
</script>

<template>
  <a-modal
    title="编辑假释执行接收表"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <!-- 步骤条 -->
      <div style="margin-bottom:20px">
        <a-steps v-model:current="current" type="navigation" :style="stepStyle" >
          <a-step title="基本信息" />
          <a-step title="假释裁定信息" />
        </a-steps>
      </div>
      <!--基本信息-->
      <div v-show="current===0">
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <!--案件信息-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >案件信息</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                  <a-form-item
                    label="案件赋号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['tyfh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="法院案件标识"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['fyajbs']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="案件名称"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['ajmc']" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!--人员基本信息-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >人员基本信息</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="矫正类别"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入矫正类别" :disabled="linkDisabled" v-decorator="['jzlb']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="矫正机构"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入矫正机构" :disabled="linkDisabled" v-decorator="['jzjg']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="罪犯编号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['zfbh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="姓名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入姓名" :disabled="linkDisabled" v-decorator="['xm']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="曾用名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入曾用名" :disabled="linkDisabled" v-decorator="['cym']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="性别"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择性别" :disabled="linkDisabled" v-decorator="['xb']">
                      <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="民族"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择民族" :disabled="linkDisabled" v-decorator="['mz']">
                      <a-select-option v-for="(item,index) in mzDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="证件类型"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择证件类型" :disabled="linkDisabled" v-decorator="['zjlx']">
                      <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="证件号码"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入证件号码" :disabled="linkDisabled" v-decorator="['zjhm']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="出生日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入出生日期" :disabled="linkDisabled" v-decorator="['csrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="犯罪时是否未成年"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['fzssfwcn']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="未成年"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择未成年" :disabled="linkDisabled" v-decorator="['wcn']">
                      <a-select-option v-for="(item,index) in wcnDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否有精神病"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sfyjsb']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="鉴定机构"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入鉴定机构":disabled="linkDisabled" v-decorator="['jdjg']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否有传染病"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sfycrb']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="传染病类型"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择传染病类型" :disabled="linkDisabled" v-decorator="['crblx']">
                      <a-select-option v-for="(item,index) in crblxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="文化程度"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择文化程度" :disabled="linkDisabled" v-decorator="['whcd']">
                      <a-select-option v-for="(item,index) in whcdDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="婚姻状况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择婚姻状况" :disabled="linkDisabled" v-decorator="['hyzk']">
                      <a-select-option v-for="(item,index) in hyzkDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="职业"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入职业":disabled="linkDisabled" v-decorator="['zy']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="就业就学情况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择就业就学情况" :disabled="linkDisabled" v-decorator="['jyjxqk']">
                      <a-select-option v-for="(item,index) in jyjxqkDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="现政治面貌"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择政治面貌" :disabled="linkDisabled" v-decorator="['xzzmm']">
                      <a-select-option v-for="(item,index) in zzmmDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="原政治面貌"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择政治面貌" :disabled="linkDisabled" v-decorator="['yzzmm']">
                      <a-select-option v-for="(item,index) in zzmmDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" pull="3">
                  <a-form-item
                    label="原工作单位"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入原工作单位" :disabled="linkDisabled" v-decorator="['gzdw']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="单位联系电话"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入单位联系电话" :disabled="linkDisabled" v-decorator="['dwlxdh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="个人联系电话"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入个人联系电话" :disabled="linkDisabled" v-decorator="['grlxdh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="国籍"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择国籍" :disabled="linkDisabled" v-decorator="['gj']">
                      <a-select-option v-for="(item,index) in gjDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" pull="3">
                  <a-form-item
                    label="住所地"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入住所地" :disabled="linkDisabled" v-decorator="['zsd']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" pull="3">
                  <a-form-item
                    label="住所地详细地址"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入住所地详细地址" :disabled="linkDisabled" v-decorator="['zsdxxdz']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" pull="3">
                  <a-form-item
                    label="户籍所在地"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入户籍所在地" :disabled="linkDisabled" v-decorator="['hjszd']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" pull="3">
                  <a-form-item
                    label="户籍地址明细"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入户籍地址明细" :disabled="linkDisabled" v-decorator="['hjdzmx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="户籍地是否与居住地相同"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['hjdsfyjzdxt']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!--家庭成员及社会关系-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >家庭成员及社会关系</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <a-form-item
                    label="有无家庭成员及主要社会关系"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['ywjtcyjzyshgx']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <s-table
                    ref="table"
                    :columns="jtColumns"
                    :data="jtLoadData"
                    :alert="false"
                    :rowKey="(record) => record.code"
                  >
                  </s-table>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </a-spin>
      </div>
      <!--假释裁定信息-->
      <div v-show="current===1">
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <!--假释裁定信息-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >假释裁定信息</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="决定文书编号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="决定文书编号" :disabled="linkDisabled" v-decorator="['sxpjszh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="文书生效日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="文书生效日期" :disabled="linkDisabled" v-decorator="['pjwssxrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="执行通知书文号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入执行通知书文号" :disabled="linkDisabled" v-decorator="['zxtzswh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="执行通知书日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入执行通知书日期" :disabled="linkDisabled" v-decorator="['zxtzsrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="交付执行日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入交付执行日期" :disabled="linkDisabled" v-decorator="['jfzxrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="移交罪犯机关"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    has-feedback
                  >
                    <a-input placeholder="请输入移交罪犯机关类型" :disabled="linkDisabled" v-decorator="['yjzfjglx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="移交罪犯机关名称"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    has-feedback
                  >
                    <a-input placeholder="请输入移交罪犯机关名称" :disabled="linkDisabled" v-decorator="['yjzfjgmc']" />
                    <a-input placeholder="请输入移交罪犯机关类型" :disabled="linkDisabled" v-decorator="['yjzfjglx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="主要犯罪事实"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-textarea :rows="4" placeholder="请输入主要犯罪事实" :disabled="linkDisabled" v-decorator="['zyfzss']"></a-textarea>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="是否有前科"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    has-feedback
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sfyqk']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="是否累犯"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    has-feedback
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sflf']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="社区矫正开始日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-date-picker placeholder="请选择入社区矫正开始日期" :disabled="linkDisabled" @change="onChange" style="width: 100%" v-decorator="['sqjzksrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="社区矫正结束日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-date-picker placeholder="请选择入社区矫正结束日期" :disabled="linkDisabled" @change="onChange" style="width: 100%" v-decorator="['sqjzjsrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="社区矫正期限"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入社区矫正期限" :disabled="linkDisabled" v-decorator="['sqjzqx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="具体罪名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入具体罪名" :disabled="linkDisabled" v-decorator="['jtzm']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="管制期限"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入管制期限" :disabled="linkDisabled" v-decorator="['gzqx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="缓刑考验期限"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入缓刑考验期限" :disabled="linkDisabled" v-decorator="['hxkyqx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24" :pull="3">
                  <a-form-item
                    label="是否数罪并罚"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sfszbf']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="原羁押场所"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入原羁押场所" :disabled="linkDisabled" v-decorator="['yjycs']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="判决刑种"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入判决刑种" :disabled="linkDisabled" v-decorator="['pjxz']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="判决刑期开始日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-date-picker placeholder="请输入判决刑期开始日期" :disabled="linkDisabled" @change="onChange" style="width: 100%" v-decorator="['pjxqksrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="判决刑期结束日期"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-date-picker placeholder="请输入判决刑期结束日期" :disabled="linkDisabled" @change="onChange" style="width: 100%" v-decorator="['pjxqjsrq']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="有期徒刑期限"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入有期徒刑期限" :disabled="linkDisabled" v-decorator="['yqtxqx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="附加刑"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入附加刑" :disabled="linkDisabled" v-decorator="['fjx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="剥夺政治权利年限"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入剥夺政治权利年限" :disabled="linkDisabled" v-decorator="['bdzzqlnx']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="具体罚款金额"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入具体罚款金额" :disabled="linkDisabled" v-decorator="['jtfkje']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="驱逐出境"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入驱逐出境" :disabled="linkDisabled" v-decorator="['qzcj']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="没收财产"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入没收财产" :disabled="linkDisabled" v-decorator="['mscc']" />
                  </a-form-item>
                </a-col>
                <a-col :md="24" :sm="24">
                  <a-form-item
                    label="审判机关名称"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入审判机关名称" :disabled="linkDisabled" v-decorator="['spjgmc']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否“五独”"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入是否“五独”" :disabled="linkDisabled" v-decorator="['sfwd']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否“五涉”"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入是否“五涉”" :disabled="linkDisabled" v-decorator="['sfws']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否有“四史”"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入是否有“四史”" :disabled="linkDisabled" v-decorator="['sfyss']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                </a-col>
                <a-col :md="12" :sm="24">
                </a-col>
                <a-col :md="12" :sm="24">
                </a-col>
              </a-row>
            </div>
            <!--禁制令-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >禁制令</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <a-form-item
                    label="是否被宣告禁止令"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-radio-group :disabled="linkDisabled" v-decorator="['sfbxgjzl']" >
                      <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <s-table
                    ref="table"
                    :columns="jzlColumns"
                    :data="jzlLoadData"
                    :alert="false"
                    :rowKey="(record) => record.code"
                  >
                  </s-table>
                </a-col>
              </a-row>
            </div>
            <!--同案犯信息-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >同案犯信息</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <s-table
                    ref="table"
                    :columns="tafColumns"
                    :data="tafLoadData"
                    :alert="false"
                    :rowKey="(record) => record.code"
                  >
                  </s-table>
                </a-col>
              </a-row>
            </div>
            <!--文书信息-->
            <div
              class="border"
              style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
            >文书信息</div>
            <div class="border" style="">
              <a-row :gutter="24">
                <a-col :md="24" :sm="24">
                  <s-table
                    ref="table"
                    :columns="wsColumns"
                    :data="wsLoadData"
                    :alert="false"
                    :rowKey="(record) => record.code"
                  >
                  </s-table>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </a-spin>
      </div>

      <!--区县司法局审核信息-->
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >区县司法局审核信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="审核结果"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-tree-select
                    v-decorator="['shjg']"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="shjgTree"
                    placeholder="请选择审核结果"
                  >
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="说明："
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  如果此社区矫正对象已入矫，请选择信息之后退回。
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="审核人"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['shr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="审核时间"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker :disabled="true" style="width: 100%" v-decorator="['shsj']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="指派司法所"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-tree-select
                    :disabled="linkDisabled"
                    v-decorator="['shjzjg']"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择接收单位"
                  >
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="入矫时间"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  has-feedback
                >
                  <a-date-picker
                    style="width: 100%"
                    placeholder="请选择入矫时间"

                    v-decorator="['rjsj',{rules: [{ required: true, message: '请选择入矫时间！' }]}]"
                    @change="onChangerjsj"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-spin>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { acceptParoleExecuteEdit } from '@/api/modular/main/acceptparoleexecute/acceptParoleExecuteManage'
  import {
    acceptCorrectionAccomplicePage,
    acceptCorrectionDocPage,
    acceptCorrectionFamilyPage,
    acceptCorrectionForbidPage
  } from '@/api/modular/main/subList/subListManage';
  import { STable } from '@/components';
  export default {
    components: {
      STable
    },
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        orgTree: [],
        shjgTree: [
          {
            children: [{
              children: [],
              id: '11',
              parentId: '1',
              title: '信息无误，确认接收',
              value: '11'
            }, {
              children: [],
              id: '12',
              parentId: '1',
              title: '存在部分信息错误，确认接收',
              value: '12'
            }],
            id: '1',
            parentId: '0',
            title: '正常接收',
            disabled: true,
            value: '1'
          }, {
            children: [{
              children: [],
              id: '21',
              parentId: '2',
              title: '信息错误，非我区管辖矫正对象',
              value: '21'
            }, {
              children: [],
              id: '22',
              parentId: '2',
              title: '数据重复发送',
              value: '22'
            }, {
              children: [],
              id: '23',
              parentId: '2',
              title: '其他',
              value: '23'
            }],
            id: '2',
            parentId: '0',
            title: '退回',
            disabled: true,
            value: '2'
          }, {
            children: [{
              children: [],
              id: '31',
              parentId: '3',
              title: '数据滞后，已办理手续',
              value: '31'
            }],
            id: '3',
            parentId: '0',
            title: '其他',
            disabled: true,
            value: '3'
          }],
        ztDropDown: [],
        zjlxDropDown: [],
        crblxDropDown: [],
        whcdDropDown: [],
        hyzkDropDown: [],
        jyjxqkDropDown: [],
        zzmmDropDown: [],
        gjDropDown: [],
        xbDropDown: [],
        mzDropDown: [],
        wcnDropDown: [],
        visible: false,
        confirmLoading: false,
        current: 0,
        stepStyle: {
          marginBottom: '10px',
          boxShadow: '0px -1px 0 0 #e8e8e8 inset'
        },
        linkRequired: true,
        linkDisabled: true,
        shsjDateString: '',
        rjsjDateString: '',
        yesOrNoData: [
          { code: '1', name: '是' },
          { code: '0', name: '否' }
        ],
        // 查询参数
        queryParam: {},
        // 表头 家庭成员及社会关系
        jtColumns: [
          {
            ellipsis: true,
            title: '关系',
            dataIndex: 'gx'
          },
          {
            ellipsis: true,
            title: '姓名',
            dataIndex: 'xm'
          },
          {
            ellipsis: true,
            title: '性别',
            dataIndex: 'xb'
          },
          {
            ellipsis: true,
            title: '出生日期',
            dataIndex: 'csrq'
          },
          {
            ellipsis: true,
            title: '证件类型',
            dataIndex: 'lx'
          },
          {
            ellipsis: true,
            title: '证件号码',
            dataIndex: 'zjhm'
          },
          {
            ellipsis: true,
            title: '所在单位',
            dataIndex: 'szdw'
          },
          {
            ellipsis: true,
            title: '职务',
            dataIndex: 'zw'
          },
          {
            ellipsis: true,
            title: '家庭地址',
            dataIndex: 'jtzz'
          },
          {
            ellipsis: true,
            title: '联系电话',
            dataIndex: 'lxdh'
          }
        ],
        // 加载数据方法 必须为 Promise 对象
        jtLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionFamilyPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 文书
        wsColumns: [
          {
            ellipsis: true,
            title: '法律文书名称',
            dataIndex: 'ws'
          },
          {
            ellipsis: true,
            title: '附件',
            dataIndex: 'wsdm'
          }
        ],
        // 加载数据方法 必须为 Promise 对象
        wsLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionDocPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 禁止令
        jzlColumns: [
          {
            ellipsis: true,
            title: '禁止令类型',
            dataIndex: 'jzlxl'
          },
          {
            ellipsis: true,
            title: '禁止令内容',
            dataIndex: 'jzlnr'
          },
          {
            ellipsis: true,
            title: '禁止期限开始日期',
            dataIndex: 'jzqxksrq'
          },
          {
            ellipsis: true,
            title: '禁止期限结束日期',
            dataIndex: 'jzqxjsrq'
          },
          {
            ellipsis: true,
            title: '特定区域坐标',
            dataIndex: 'tdqyzb'
          }
        ],
        // 加载数据方法 必须为 Promise 对象
        jzlLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionForbidPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 同案犯信息
        tafColumns: [
          {
            ellipsis: true,
            title: '姓名',
            dataIndex: 'xm'
          },
          {
            ellipsis: true,
            title: '证件类型',
            dataIndex: 'zjlx'
          },
          {
            ellipsis: true,
            title: '证件号码',
            dataIndex: 'zjhm'
          },
          {
            ellipsis: true,
            title: '性别',
            dataIndex: 'xb'
          },
          {
            ellipsis: true,
            title: '出生日期',
            dataIndex: 'csrq'
          },
          {
            ellipsis: true,
            title: '罪名',
            dataIndex: 'zm'
          },
          {
            ellipsis: true,
            title: '被判处刑罚及所在监所',
            dataIndex: 'bpcxfjszjs'
          }
        ],
        // 加载数据方法 必须为 Promise 对象
        tafLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionAccomplicePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record, linkDisabled, orgTree) {
        this.visible = true
        this.recordId = record.id
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              taskId: record.taskId,
              zt: record.zt,
              sjlylx: record.sjlylx,
              jsdw: record.jsdw,
              jsdwmc: record.jsdwmc,
              tsdw: record.tsdw,
              tsdwmc: record.tsdwmc,
              tyfh: record.tyfh,
              fyajbs: record.fyajbs,
              ajmc: record.ajmc,
              zfbh: record.zfbh,
              xm: record.xm,
              cym: record.cym,
              xb: record.xb,
              mz: record.mz,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              fzssfwcn: record.fzssfwcn,
              wcn: record.wcn,
              sfyjsb: record.sfyjsb,
              jdjg: record.jdjg,
              sfycrb: record.sfycrb,
              crblx: record.crblx,
              whcd: record.whcd,
              hyzk: record.hyzk,
              zy: record.zy,
              jyjxqk: record.jyjxqk,
              xzzmm: record.xzzmm,
              yzzmm: record.yzzmm,
              ygzdw: record.ygzdw,
              dwlxdh: record.dwlxdh,
              grlxdh: record.grlxdh,
              gj: record.gj,
              ywjtcyjzyshgx: record.ywjtcyjzyshgx,
              zp: record.zp,
              hjdsfyjzdxt: record.hjdsfyjzdxt,
              zsd: record.zsd,
              zsdxxdz: record.zsdxxdz,
              hjszd: record.hjszd,
              hjdzmx: record.hjdzmx,
              sfyqk: record.sfyqk,
              sflf: record.sflf,
              jzlb: record.jzlb,
              dcpgyj: record.dcpgyj,
              sxpjszh: record.sxpjszh,
              fzlx: record.fzlx,
              jtzm: record.jtzm,
              sfszbf: record.sfszbf,
              pjxz: record.pjxz,
              yqtxqx: record.yqtxqx,
              fjx: record.fjx,
              spjgmc: record.spjgmc,
              bdzzqlnx: record.bdzzqlnx,
              jtfkje: record.jtfkje,
              qzcj: record.qzcj,
              mscc: record.mscc,
              sfwd: record.sfwd,
              sfws: record.sfws,
              sfyss: record.sfyss,
              jskyq: record.jskyq,
              jscdswh: record.jscdswh,
              jscdjg: record.jscdjg,
              jzjg: record.jzjg,
              yjzfjglx: record.yjzfjglx,
              yjzfjgmc: record.yjzfjgmc
            }
          )
        }, 100)
        // 时间单独处理
        if (record.sdsj != null) {
            this.form.getFieldDecorator('sdsj', { initialValue: moment(record.sdsj, 'YYYY-MM-DD') })
        }
        this.sdsjDateString = moment(record.sdsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.dcpgrq != null) {
            this.form.getFieldDecorator('dcpgrq', { initialValue: moment(record.dcpgrq, 'YYYY-MM-DD') })
        }
        this.dcpgrqDateString = moment(record.dcpgrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjwssxrq != null) {
            this.form.getFieldDecorator('pjwssxrq', { initialValue: moment(record.pjwssxrq, 'YYYY-MM-DD') })
        }
        this.pjwssxrqDateString = moment(record.pjwssxrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjxqksrq != null) {
            this.form.getFieldDecorator('pjxqksrq', { initialValue: moment(record.pjxqksrq, 'YYYY-MM-DD') })
        }
        this.pjxqksrqDateString = moment(record.pjxqksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjxqjsrq != null) {
            this.form.getFieldDecorator('pjxqjsrq', { initialValue: moment(record.pjxqjsrq, 'YYYY-MM-DD') })
        }
        this.pjxqjsrqDateString = moment(record.pjxqjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jscdrq != null) {
            this.form.getFieldDecorator('jscdrq', { initialValue: moment(record.jscdrq, 'YYYY-MM-DD') })
        }
        this.jscdrqDateString = moment(record.jscdrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jskyqqr != null) {
            this.form.getFieldDecorator('jskyqqr', { initialValue: moment(record.jskyqqr, 'YYYY-MM-DD') })
        }
        this.jskyqqrDateString = moment(record.jskyqqr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jskyqzr != null) {
            this.form.getFieldDecorator('jskyqzr', { initialValue: moment(record.jskyqzr, 'YYYY-MM-DD') })
        }
        this.jskyqzrDateString = moment(record.jskyqzr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jfzxrq != null) {
            this.form.getFieldDecorator('jfzxrq', { initialValue: moment(record.jfzxrq, 'YYYY-MM-DD') })
        }
        this.jfzxrqDateString = moment(record.jfzxrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zxtzsrq != null) {
            this.form.getFieldDecorator('zxtzsrq', { initialValue: moment(record.zxtzsrq, 'YYYY-MM-DD') })
        }
        this.zxtzsrqDateString = moment(record.zxtzsrq).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdsj = this.sdsjDateString
            values.csrq = this.csrqDateString
            values.dcpgrq = this.dcpgrqDateString
            values.pjwssxrq = this.pjwssxrqDateString
            values.pjxqksrq = this.pjxqksrqDateString
            values.pjxqjsrq = this.pjxqjsrqDateString
            values.jscdrq = this.jscdrqDateString
            values.jskyqqr = this.jskyqqrDateString
            values.jskyqzr = this.jskyqzrDateString
            values.jfzxrq = this.jfzxrqDateString
            values.zxtzsrq = this.zxtzsrqDateString
            acceptParoleExecuteEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdsj(date, dateString) {
        this.sdsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangedcpgrq(date, dateString) {
        this.dcpgrqDateString = dateString
      },
      onChangepjwssxrq(date, dateString) {
        this.pjwssxrqDateString = dateString
      },
      onChangepjxqksrq(date, dateString) {
        this.pjxqksrqDateString = dateString
      },
      onChangepjxqjsrq(date, dateString) {
        this.pjxqjsrqDateString = dateString
      },
      onChangejscdrq(date, dateString) {
        this.jscdrqDateString = dateString
      },
      onChangejskyqqr(date, dateString) {
        this.jskyqqrDateString = dateString
      },
      onChangejskyqzr(date, dateString) {
        this.jskyqzrDateString = dateString
      },
      onChangejfzxrq(date, dateString) {
        this.jfzxrqDateString = dateString
      },
      onChangezxtzsrq(date, dateString) {
        this.zxtzsrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

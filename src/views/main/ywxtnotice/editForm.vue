<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['userName']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['orgName']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="通知事项" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['noticeTypeName']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['sendTime']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="发送状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['sendStr']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="通知内容" :labelCol="{ xs: { span: 24 }, sm: { span: 3 } }" :wrapperCol="{ xs: { span: 24 }, sm: { span: 20 } }">
              <a-textarea disabled v-decorator="['msg']"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  export default {
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 5 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 15 } },
        noticeTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              userName: record.userName,
              orgName: record.orgName,
              noticeTypeName: record.noticeTypeName,
              sendTime: record.sendTime,
              sendStr: record.sendStr,
              msg: record.msg
            }
          )
        }, 100)
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

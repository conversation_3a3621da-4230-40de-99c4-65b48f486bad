<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="所属单位">
                <a-tree-select
                  v-model="queryParam.orgIds"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择所属单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.userName" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="通知事项">
                <a-select v-model="queryParam.noticeType" placeholder="请选择通知事项">
                  <a-select-option v-for="(item, index) in noticeTypeDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="通知时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <x-down
            ref="batchExport"
            @batchExport="batchExport"/>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
  import moment from 'moment'
import { ywxtNoticeExport, ywxtNoticeUserPage } from '@/api/modular/main/ywxt/ywxtManage'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      XDown,
      editForm
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          { title: '姓名', align: 'center', dataIndex: 'userName' },
          { title: '所属单位', align: 'center', dataIndex: 'orgName' },
          { title: '通知事项', align: 'center', dataIndex: 'noticeTypeName' },
          { title: '通知时间', align: 'center', dataIndex: 'sendTime' },
          { title: '发送状态', align: 'center', dataIndex: 'sendStr' },
          { title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return ywxtNoticeUserPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        noticeTypeDropDown: [
          { code: 'terminate', name: '解矫通知' },
          { code: 'add_correction', name: '入矫衔接' },
          { code: 'arrest', name: '提请逮捕' },
          { code: 'invest', name: '调查评估' },
          { code: 'recommit', name: '公安处罚协同' },
          { code: 'revocation_parole', name: '撤销假释' },
          { code: 'revocation_probation', name: '撤销缓刑' },
          { code: 'irs', name: '多跨协同' }
        ],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree();
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD')
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD')
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      getOrgTree() {
        return getOrgTree().then(res => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      batchExport () {
        ywxtNoticeExport(this.switchingDate()).then((res) => {
          this.$refs.batchExport.downloadfile(res)
        })
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <a-tree-select
                  v-model="queryParam.receiveOrgCode"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.correctionObject" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="案件类型">
                <sh-select v-model="queryParam.serverNum" allow-clear placeholder="请选择案件类型" dictType="server_num"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="消息类型">
                <sh-select v-model="queryParam.xxlx" allow-clear placeholder="请选择消息类型" dictType="xxlx"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.detailForm.open(record)">详情</a>
          <a-divider type="vertical"/>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => ywxtMessageLogDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
        <span slot="xxlx" slot-scope="text">
          {{ 'xxlx' | dictType(text) }}
        </span>
        <span slot="serverNum" slot-scope="text">
          {{ 'server_num' | dictType(text) }}
        </span>
      </s-table>
      <detail-form ref="detailForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { ywxtMessageLogPage, ywxtMessageLogDelete } from '@/api/modular/main/ywxt/ywxtMessageLogManage'
  import detailForm from './detailForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      detailForm
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          { title: '消息类型', align: 'center', dataIndex: 'xxlx', scopedSlots: { customRender: 'xxlx' } },
          { title: '消息内容', align: 'center', dataIndex: 'xxnr' },
          { title: '案件类型', align: 'center', dataIndex: 'serverNum', scopedSlots: { customRender: 'serverNum' } },
          { title: '姓名', align: 'center', dataIndex: 'correctionObject' },
          { title: '所属矫正单位', align: 'center', dataIndex: 'deptName' },
          { title: '消息时间', align: 'center', dataIndex: 'opTime' },
          { title: '消息发送单位', align: 'center', dataIndex: 'sendOrgName' },
          { title: '消息接收单位', align: 'center', dataIndex: 'receiveOrgName' },
          { title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return ywxtMessageLogPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        extOrgInfoData: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    methods: {
      moment,
      ywxtMessageLogDelete (record) {
        ywxtMessageLogDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

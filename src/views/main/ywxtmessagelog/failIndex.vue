<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <sh-correction-org-tree v-model="queryParam.jsdw" style="width: 100%"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.correctionObject" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="案件类型">
                <sh-select v-model="queryParam.typeName" allow-clear placeholder="请选择案件类型" dictType="server_num"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="openDetail(record)">详情</a>
        </span>
        <span slot="serverNum" slot-scope="text">
          {{ 'server_num' | dictType(text) }}
        </span>
        <span slot="failType" slot-scope="text">
          {{ 'failType' | dictType(text) }}
        </span>
      </s-table>
      <detail-form ref="detailForm" @ok="handleOk" />
    </a-card>
    <a-modal
      title="详情"
      :width="900"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @ok="handleCancel"
      @cancel="handleCancel">
      <a-spin :spinning="confirmLoading">
        <a-form>
          <a-form-item label="送达xml" :labelCol="{ span: 5 }" :wrapperCol="{ span: 15 }">
            <a-textarea :auto-size="{ minRows: 3, maxRows: 20 }" v-model="xml"/>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { ywxtWsLog } from '@/api/modular/main/ywxt/ywxtMessageLogManage'
  import detailForm from './detailForm.vue'
  export default {
    components: {
      STable,
      detailForm
    },
    data () {
      return {
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          { title: '案件类型', align: 'center', dataIndex: 'typeName', scopedSlots: { customRender: 'serverNum' } },
          { title: '失败类型', align: 'center', dataIndex: 'success', scopedSlots: { customRender: 'failType' } },
          { title: '姓名', align: 'center', dataIndex: 'correctionObject' },
          { title: '送达时间', align: 'center', dataIndex: 'opTime' },
          { title: '接收单位', align: 'center', dataIndex: 'jsdwmc' },
          { title: '发送单位', align: 'center', dataIndex: 'tsdwmc' }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return ywxtWsLog(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        visible: false,
        confirmLoading: false,
        xml: '',
        extOrgInfoData: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('sysPos:edit')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      openDetail(record) {
        this.xml = record.decodeXml
        this.visible = true
      },
      handleCancel() {
        this.visible = false
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

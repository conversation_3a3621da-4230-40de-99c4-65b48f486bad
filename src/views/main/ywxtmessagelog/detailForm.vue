<template>
  <a-drawer
    title="编辑一体化消息日志"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel"
  >
    <div style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <a-row :gutter="20">
          <a-form :form="form">
            <a-col :span="12">
              <a-form-item
                label="消息类型"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <sh-select disabled v-decorator="['xxlx']" dictType="xxlx"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="消息时间"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['opTime']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="姓名"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['correctionObject']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="所属矫正单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['deptName']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="消息发送单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['sendOrgName']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="消息接送单位"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['receiveOrgName']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="案件类型"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <sh-select disabled v-decorator="['serverNum']" dictType="server_num"/>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="消息内容"
                :labelCol="labelCol1"
                :wrapperCol="wrapperCol1">
                <a-textarea disabled v-decorator="['xxnr']" />
              </a-form-item>
            </a-col>
          </a-form>
        </a-row>
      </a-spin>
    </div>
    <div
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">
      <a-button style="margin-Right: 8px" @click="handleCancel">取消</a-button>
    </div>
  </a-drawer>
</template>

<script>
  export default {
    data () {
      return {
        drawerWidth: 1000,
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol1: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol1: { xs: { span: 24 }, sm: { span: 20 } },
        opTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created() {
      this.drawerWidth = window.innerWidth - 200
    },
    methods: {
      open (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              ...record
            }
          )
        }, 100)
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <div class="dynamic-menu">

    <a-menu
      mode="vertical"

      style="padding:0 5px;width: 180px"
      @click="selectMenuItem"
      :selectedKeys="selectedMenuKeys"
    >
      <a-menu-item :key="menuItem.key" v-for="menuItem in menuItems">
        {{ menuItem.name }}
      </a-menu-item>
    </a-menu>

    <div class="content-container">
      <component :is="selectedComponent"></component>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menuItems: [
        { key: 'irsindividualmedicalinsurance', name: '行政处罚行为数据信息' },
        { key: 'irsmarriageregistrationinformation', name: '行政处罚行为数据信息' },
        { key: 'irsextremepoorbasicinfo', name: '行政处罚行为数据信息' }
      ],
      selectedMenuKeys: [],
      selectedComponent: null
    };
  },
  mounted() {
    // 默认选择第一个菜单项
    this.selectMenuItem({ key: this.menuItems[0].key });
  },
  methods: {
    selectMenuItem({ key }) {
      this.selectedMenuKeys = [key];

      // 动态引入对应的组件
      import(`@/views/main/${key}/index.vue`)
        .then((module) => {
          this.selectedComponent = module.default;
        })
        .catch((error) => {
          console.error(`Failed to load component: ${error}`);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
  background: rgba(22, 144, 255, 1);
  border-radius: 4px 4px 4px 4px;
  color: #fff;
}
.dynamic-menu {
  display: flex;
}

.content-container {
  flex: 1;
  padding: 10px;
}
</style>

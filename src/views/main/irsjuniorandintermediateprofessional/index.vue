<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('irsJuniorAndIntermediateProfessional:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="备注">
                <a-input v-model="queryParam.bz" allow-clear placeholder="请输入备注"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="出生年月，yyyy年MM月dd日">
                  <a-input v-model="queryParam.csny" allow-clear placeholder="请输入出生年月，yyyy年MM月dd日"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证照数据">
                  <a-input v-model="queryParam.data" allow-clear placeholder="请输入证照数据"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照编码">
                  <a-input v-model="queryParam.elcLicenceCode" allow-clear placeholder="请输入电子证照编码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照来源部门">
                  <a-input v-model="queryParam.elcLicenceDept" allow-clear placeholder="请输入电子证照来源部门"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照文件数据">
                  <a-input v-model="queryParam.elcLicenceFile" allow-clear placeholder="请输入电子证照文件数据"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照文件地址">
                  <a-input v-model="queryParam.elcLicenceFileUrl" allow-clear placeholder="请输入电子证照文件地址"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照名称">
                  <a-input v-model="queryParam.elcLicenceName" allow-clear placeholder="请输入电子证照名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照结构化数据">
                  <a-input v-model="queryParam.elcLicenceStruct" allow-clear placeholder="请输入电子证照结构化数据"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证单位">
                  <a-input v-model="queryParam.fzdw" allow-clear placeholder="请输入发证单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证时间，yyyy年MM月dd日">
                  <a-input v-model="queryParam.fzsj" allow-clear placeholder="请输入发证时间，yyyy年MM月dd日"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="工作单位">
                  <a-input v-model="queryParam.gzdw" allow-clear placeholder="请输入工作单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="级别名称">
                  <a-input v-model="queryParam.jbmc" allow-clear placeholder="请输入级别名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="评委会名称">
                  <a-input v-model="queryParam.pwhmc" allow-clear placeholder="请输入评委会名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="取得方式">
                  <a-input v-model="queryParam.qdfs" allow-clear placeholder="请输入取得方式"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="取得资格时间，yyyy年MM月dd日">
                  <a-input v-model="queryParam.qdzgsj" allow-clear placeholder="请输入取得资格时间，yyyy年MM月dd日"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="签名证书">
                  <a-input v-model="queryParam.signCert" allow-clear placeholder="请输入签名证书"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="签名值">
                  <a-input v-model="queryParam.signValue" allow-clear placeholder="请输入签名值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所属系列">
                  <a-input v-model="queryParam.ssxl" allow-clear placeholder="请输入所属系列"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态的使用规则以数源部门解释为准">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态的使用规则以数源部门解释为准"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所学专业">
                  <a-input v-model="queryParam.sxzy" allow-clear placeholder="请输入所学专业"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所在地市、省级主管部门">
                  <a-input v-model="queryParam.szds" allow-clear placeholder="请输入所在地市、省级主管部门"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所在县（市、区）、市本级">
                  <a-input v-model="queryParam.szxs" allow-clear placeholder="请输入所在县（市、区）、市本级"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="时间戳签名值">
                  <a-input v-model="queryParam.tsa" allow-clear placeholder="请输入时间戳签名值"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="公布文号">
                  <a-input v-model="queryParam.wh" allow-clear placeholder="请输入公布文号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="资格名称">
                  <a-input v-model="queryParam.zgmc" allow-clear placeholder="请输入资格名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="最高学历">
                  <a-input v-model="queryParam.zgxl" allow-clear placeholder="请输入最高学历"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="最高学位">
                  <a-input v-model="queryParam.zgxw" allow-clear placeholder="请输入最高学位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件号码">
                  <a-input v-model="queryParam.zjhm" allow-clear placeholder="请输入证件号码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件类别">
                  <a-input v-model="queryParam.zjlb" allow-clear placeholder="请输入证件类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="照片路径">
                  <a-input v-model="queryParam.zplj" allow-clear placeholder="请输入照片路径"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书查询">
                  <a-input v-model="queryParam.zscx" allow-clear placeholder="请输入证书查询"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="专业名称">
                  <a-input v-model="queryParam.zymc" allow-clear placeholder="请输入专业名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="在职状态">
                  <a-input v-model="queryParam.zzzt" allow-clear placeholder="请输入在职状态"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.zsbh"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('irsJuniorAndIntermediateProfessional:add')" >
          <a-button type="primary" v-if="hasPerm('irsJuniorAndIntermediateProfessional:add')" icon="plus" @click="$refs.addForm.add()">新增省人社厅初中级职称证书</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('irsJuniorAndIntermediateProfessional:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('irsJuniorAndIntermediateProfessional:edit') & hasPerm('irsJuniorAndIntermediateProfessional:delete')"/>
          <a-popconfirm v-if="hasPerm('irsJuniorAndIntermediateProfessional:delete')" placement="topRight" title="确认删除？" @confirm="() => irsJuniorAndIntermediateProfessionalDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { irsJuniorAndIntermediateProfessionalPage, irsJuniorAndIntermediateProfessionalDelete } from '@/api/modular/main/irsjuniorandintermediateprofessional/irsJuniorAndIntermediateProfessionalManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import {getOrgTree} from "@/api/modular/system/orgManage";
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '毕业学校',
            align: 'center',
            dataIndex: 'byxx'
          },
          {
          ellipsis: true,
          title: '备注',
            align: 'center',
            dataIndex: 'bz'
          },
          {
          ellipsis: true,
          title: '出生年月，yyyy年MM月dd日',
            align: 'center',
            dataIndex: 'csny'
          },
          {
          ellipsis: true,
          title: '证照数据',
            align: 'center',
            dataIndex: 'data'
          },
          {
          ellipsis: true,
          title: '电子证照编码',
            align: 'center',
            dataIndex: 'elcLicenceCode'
          },
          {
          ellipsis: true,
          title: '电子证照来源部门',
            align: 'center',
            dataIndex: 'elcLicenceDept'
          },
          {
          ellipsis: true,
          title: '电子证照文件数据',
            align: 'center',
            dataIndex: 'elcLicenceFile'
          },
          {
          ellipsis: true,
          title: '电子证照文件地址',
            align: 'center',
            dataIndex: 'elcLicenceFileUrl'
          },
          {
          ellipsis: true,
          title: '电子证照名称',
            align: 'center',
            dataIndex: 'elcLicenceName'
          },
          {
          ellipsis: true,
          title: '电子证照结构化数据',
            align: 'center',
            dataIndex: 'elcLicenceStruct'
          },
          {
          ellipsis: true,
          title: '发证单位',
            align: 'center',
            dataIndex: 'fzdw'
          },
          {
          ellipsis: true,
          title: '发证时间，yyyy年MM月dd日',
            align: 'center',
            dataIndex: 'fzsj'
          },
          {
          ellipsis: true,
          title: '工作单位',
            align: 'center',
            dataIndex: 'gzdw'
          },
          {
          ellipsis: true,
          title: '级别名称',
            align: 'center',
            dataIndex: 'jbmc'
          },
          {
          ellipsis: true,
          title: '评委会名称',
            align: 'center',
            dataIndex: 'pwhmc'
          },
          {
          ellipsis: true,
          title: '取得方式',
            align: 'center',
            dataIndex: 'qdfs'
          },
          {
          ellipsis: true,
          title: '取得资格时间，yyyy年MM月dd日',
            align: 'center',
            dataIndex: 'qdzgsj'
          },
          {
          ellipsis: true,
          title: '签名证书',
            align: 'center',
            dataIndex: 'signCert'
          },
          {
          ellipsis: true,
          title: '签名值',
            align: 'center',
            dataIndex: 'signValue'
          },
          {
          ellipsis: true,
          title: '所属系列',
            align: 'center',
            dataIndex: 'ssxl'
          },
          {
          ellipsis: true,
          title: '状态的使用规则以数源部门解释为准',
            align: 'center',
            dataIndex: 'status'
          },
          {
          ellipsis: true,
          title: '所学专业',
            align: 'center',
            dataIndex: 'sxzy'
          },
          {
          ellipsis: true,
          title: '所在地市、省级主管部门',
            align: 'center',
            dataIndex: 'szds'
          },
          {
          ellipsis: true,
          title: '所在县（市、区）、市本级',
            align: 'center',
            dataIndex: 'szxs'
          },
          {
          ellipsis: true,
          title: '时间戳签名值',
            align: 'center',
            dataIndex: 'tsa'
          },
          {
          ellipsis: true,
          title: '公布文号',
            align: 'center',
            dataIndex: 'wh'
          },
          {
          ellipsis: true,
          title: '性别',
            align: 'center',
            dataIndex: 'xb'
          },
          {
          ellipsis: true,
          title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '资格名称',
            align: 'center',
            dataIndex: 'zgmc'
          },
          {
          ellipsis: true,
          title: '最高学历',
            align: 'center',
            dataIndex: 'zgxl'
          },
          {
          ellipsis: true,
          title: '最高学位',
            align: 'center',
            dataIndex: 'zgxw'
          },
          {
          ellipsis: true,
          title: '证件号码',
            align: 'center',
            dataIndex: 'zjhm'
          },
          {
          ellipsis: true,
          title: '证件类别',
            align: 'center',
            dataIndex: 'zjlb'
          },
          {
          ellipsis: true,
          title: '照片路径',
            align: 'center',
            dataIndex: 'zplj'
          },
          {
          ellipsis: true,
          title: '证书查询',
            align: 'center',
            dataIndex: 'zscx'
          },
          {
          ellipsis: true,
          title: '专业名称',
            align: 'center',
            dataIndex: 'zymc'
          },
          {
          ellipsis: true,
          title: '在职状态',
            align: 'center',
            dataIndex: 'zzzt'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return irsJuniorAndIntermediateProfessionalPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      if (this.hasPerm('irsJuniorAndIntermediateProfessional:edit') || this.hasPerm('irsJuniorAndIntermediateProfessional:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      irsJuniorAndIntermediateProfessionalDelete (record) {
        irsJuniorAndIntermediateProfessionalDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

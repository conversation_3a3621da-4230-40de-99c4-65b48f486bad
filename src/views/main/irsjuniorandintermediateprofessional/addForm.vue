<template>
  <a-modal
    title="新增省人社厅初中级职称证书"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="毕业学校"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入毕业学校" v-decorator="['byxx', {rules: [{required: true, message: '请输入毕业学校！'}]}]" />
        </a-form-item>
        <a-form-item
          label="备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入备注" v-decorator="['bz', {rules: [{required: true, message: '请输入备注！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生年月，yyyy年MM月dd日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生年月，yyyy年MM月dd日" v-decorator="['csny', {rules: [{required: true, message: '请输入出生年月，yyyy年MM月dd日！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证照数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证照数据" v-decorator="['data', {rules: [{required: true, message: '请输入证照数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照编码" v-decorator="['elcLicenceCode', {rules: [{required: true, message: '请输入电子证照编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照来源部门"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照来源部门" v-decorator="['elcLicenceDept', {rules: [{required: true, message: '请输入电子证照来源部门！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照文件数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照文件数据" v-decorator="['elcLicenceFile', {rules: [{required: true, message: '请输入电子证照文件数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照文件地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照文件地址" v-decorator="['elcLicenceFileUrl', {rules: [{required: true, message: '请输入电子证照文件地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照名称" v-decorator="['elcLicenceName', {rules: [{required: true, message: '请输入电子证照名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照结构化数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照结构化数据" v-decorator="['elcLicenceStruct', {rules: [{required: true, message: '请输入电子证照结构化数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证单位" v-decorator="['fzdw', {rules: [{required: true, message: '请输入发证单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证时间，yyyy年MM月dd日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证时间，yyyy年MM月dd日" v-decorator="['fzsj', {rules: [{required: true, message: '请输入发证时间，yyyy年MM月dd日！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工作单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工作单位" v-decorator="['gzdw', {rules: [{required: true, message: '请输入工作单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="级别名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入级别名称" v-decorator="['jbmc', {rules: [{required: true, message: '请输入级别名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评委会名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评委会名称" v-decorator="['pwhmc', {rules: [{required: true, message: '请输入评委会名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="取得方式"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入取得方式" v-decorator="['qdfs', {rules: [{required: true, message: '请输入取得方式！'}]}]" />
        </a-form-item>
        <a-form-item
          label="取得资格时间，yyyy年MM月dd日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入取得资格时间，yyyy年MM月dd日" v-decorator="['qdzgsj', {rules: [{required: true, message: '请输入取得资格时间，yyyy年MM月dd日！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签名证书"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入签名证书" v-decorator="['signCert', {rules: [{required: true, message: '请输入签名证书！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入签名值" v-decorator="['signValue', {rules: [{required: true, message: '请输入签名值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属系列"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属系列" v-decorator="['ssxl', {rules: [{required: true, message: '请输入所属系列！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态的使用规则以数源部门解释为准"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态的使用规则以数源部门解释为准" v-decorator="['status', {rules: [{required: true, message: '请输入状态的使用规则以数源部门解释为准！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所学专业"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所学专业" v-decorator="['sxzy', {rules: [{required: true, message: '请输入所学专业！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所在地市、省级主管部门"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所在地市、省级主管部门" v-decorator="['szds', {rules: [{required: true, message: '请输入所在地市、省级主管部门！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所在县（市、区）、市本级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所在县（市、区）、市本级" v-decorator="['szxs', {rules: [{required: true, message: '请输入所在县（市、区）、市本级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="时间戳签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入时间戳签名值" v-decorator="['tsa', {rules: [{required: true, message: '请输入时间戳签名值！'}]}]" />
        </a-form-item>
        <a-form-item
          label="公布文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入公布文号" v-decorator="['wh', {rules: [{required: true, message: '请输入公布文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="资格名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入资格名称" v-decorator="['zgmc', {rules: [{required: true, message: '请输入资格名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="最高学历"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入最高学历" v-decorator="['zgxl', {rules: [{required: true, message: '请输入最高学历！'}]}]" />
        </a-form-item>
        <a-form-item
          label="最高学位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入最高学位" v-decorator="['zgxw', {rules: [{required: true, message: '请输入最高学位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类别" v-decorator="['zjlb', {rules: [{required: true, message: '请输入证件类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="照片路径"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入照片路径" v-decorator="['zplj', {rules: [{required: true, message: '请输入照片路径！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书查询"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书查询" v-decorator="['zscx', {rules: [{required: true, message: '请输入证书查询！'}]}]" />
        </a-form-item>
        <a-form-item
          label="专业名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入专业名称" v-decorator="['zymc', {rules: [{required: true, message: '请输入专业名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="在职状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入在职状态" v-decorator="['zzzt', {rules: [{required: true, message: '请输入在职状态！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsJuniorAndIntermediateProfessionalAdd } from '@/api/modular/main/irsjuniorandintermediateprofessional/irsJuniorAndIntermediateProfessionalManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsJuniorAndIntermediateProfessionalAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

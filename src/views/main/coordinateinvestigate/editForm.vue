<template>
  <a-modal
    title="编辑调查评估协查"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="委托单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托单位" v-decorator="['wtdwmc']" />
        </a-form-item>
        <a-form-item
          label="委托单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托单位id" v-decorator="['wtdwId']" />
        </a-form-item>
        <a-form-item
          label="收到委托时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择收到委托时间" v-decorator="['sdwtsj']" @change="onChangesdwtsj"/>
        </a-form-item>
        <a-form-item
          label="委托编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托编号" v-decorator="['wtbh']" />
        </a-form-item>
        <a-form-item
          label="委托调查书"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托调查书" v-decorator="['wtdcs']" />
        </a-form-item>
        <a-form-item
          label="委托文书附件"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入委托文书附件" v-decorator="['wtwsfj']" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm']" />
        </a-form-item>
        <a-form-item
          label="拟适用社区矫正人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入拟适用社区矫正人员类型" v-decorator="['nsysqjzrylx']" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb']" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq']" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input
            placeholder="请输入身份证号"
            v-decorator="['sfzh',{rules: [{ required: false, message: '请输入身份证！'},{
              validator:IDValidator ,
            },]}]" />
        </a-form-item>
        <a-form-item
          label="居住地地址(省)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址(省)" v-decorator="['jzddzP']" />
        </a-form-item>
        <a-form-item
          label="居住地地址(市)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址(市)" v-decorator="['jzddzC']" />
        </a-form-item>
        <a-form-item
          label="居住地地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址" v-decorator="['jzddz']" />
        </a-form-item>
        <a-form-item
          label="是否有居住地地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有居住地地址明细" v-decorator="['sfyjzddzmx']" />
        </a-form-item>
        <a-form-item
          label="居住地地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地地址明细" v-decorator="['jzddzmx']" />
        </a-form-item>
        <a-form-item
          label="户籍是否与居住地相同"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍是否与居住地相同" v-decorator="['hjsfyjzdxt']" />
        </a-form-item>
        <a-form-item
          label="户籍地址(省)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址(省)" v-decorator="['hjdzP']" />
        </a-form-item>
        <a-form-item
          label="户籍地址(市)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址(市)" v-decorator="['hjdzC']" />
        </a-form-item>
        <a-form-item
          label="户籍地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址" v-decorator="['hjdz']" />
        </a-form-item>
        <a-form-item
          label="是否有户籍地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有户籍地址明细" v-decorator="['sfyhjdzmx']" />
        </a-form-item>
        <a-form-item
          label="户籍地址明细"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址明细" v-decorator="['hjdzmx']" />
        </a-form-item>
        <a-form-item
          label="工作单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工作单位" v-decorator="['hzdw']" />
        </a-form-item>
        <a-form-item
          label="拟适用矫正类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入拟适用矫正类别" v-decorator="['nsyjzlb']" />
        </a-form-item>
        <a-form-item
          label="是否有原判刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有原判刑期" v-decorator="['sfyypxq']" />
        </a-form-item>
        <a-form-item
          label="原判刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入原判刑期" v-decorator="['ypxq']" />
        </a-form-item>
        <a-form-item
          label="原判刑期开始日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原判刑期开始日期" v-decorator="['ypxqksrq']" @change="onChangeypxqksrq"/>
        </a-form-item>
        <a-form-item
          label="原判刑期结束日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原判刑期结束日期" v-decorator="['ypxqjsrq']" @change="onChangeypxqjsrq"/>
        </a-form-item>
        <a-form-item
          label="原判刑罚"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入原判刑罚" v-decorator="['ypxf']" />
        </a-form-item>
        <a-form-item
          label="附加刑"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附加刑" v-decorator="['fjx']" />
        </a-form-item>
        <a-form-item
          label="判决机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入判决机关" v-decorator="['pjjg']" />
        </a-form-item>
        <a-form-item
          label="判决日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择判决日期" v-decorator="['pjrq']" @change="onChangepjrq"/>
        </a-form-item>
        <a-form-item
          label="指派单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派单位" v-decorator="['zpwdmc']" />
        </a-form-item>
        <a-form-item
          label="指派单位ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派单位ID" v-decorator="['zpdwId']" />
        </a-form-item>
        <a-form-item
          label="指派人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派人" v-decorator="['zpr']" />
        </a-form-item>
        <a-form-item
          label="指派时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择指派时间" v-decorator="['zpsj']" @change="onChangezpsj"/>
        </a-form-item>
        <a-form-item
          label="指派备注"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入指派备注" v-decorator="['zpbz']" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { coordinateInvestigateEdit } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        sdwtsjDateString: '',
        csrqDateString: '',
        ypxqksrqDateString: '',
        ypxqjsrqDateString: '',
        pjrqDateString: '',
        zpsjDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,

      // 初始化方法
      IDValidator (rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
      // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              wtdwmc: record.wtdwmc,
              wtdwId: record.wtdwId,
              wtbh: record.wtbh,
              wtdcs: record.wtdcs,
              wtwsfj: record.wtwsfj,
              xm: record.xm,
              nsysqjzrylx: record.nsysqjzrylx,
              xb: record.xb,
              sfzh: record.sfzh,
              jzddzP: record.jzddzP,
              jzddzC: record.jzddzC,
              jzddz: record.jzddz,
              sfyjzddzmx: record.sfyjzddzmx,
              jzddzmx: record.jzddzmx,
              hjsfyjzdxt: record.hjsfyjzdxt,
              hjdzP: record.hjdzP,
              hjdzC: record.hjdzC,
              hjdz: record.hjdz,
              sfyhjdzmx: record.sfyhjdzmx,
              hjdzmx: record.hjdzmx,
              hzdw: record.hzdw,
              nsyjzlb: record.nsyjzlb,
              sfyypxq: record.sfyypxq,
              ypxq: record.ypxq,
              ypxf: record.ypxf,
              fjx: record.fjx,
              pjjg: record.pjjg,
              zpwdmc: record.zpwdmc,
              zpdwId: record.zpdwId,
              zpr: record.zpr,
              zpbz: record.zpbz
            }
          )
        }, 100)
        // 时间单独处理
        if (record.sdwtsj != null) {
            this.form.getFieldDecorator('sdwtsj', { initialValue: moment(record.sdwtsj, 'YYYY-MM-DD') })
        }
        this.sdwtsjDateString = moment(record.sdwtsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqksrq != null) {
            this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
        }
        this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqjsrq != null) {
            this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
        }
        this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zpsj != null) {
            this.form.getFieldDecorator('zpsj', { initialValue: moment(record.zpsj, 'YYYY-MM-DD') })
        }
        this.zpsjDateString = moment(record.zpsj).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdwtsj = this.sdwtsjDateString
            values.csrq = this.csrqDateString
            values.ypxqksrq = this.ypxqksrqDateString
            values.ypxqjsrq = this.ypxqjsrqDateString
            values.pjrq = this.pjrqDateString
            values.zpsj = this.zpsjDateString
            coordinateInvestigateEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdwtsj(date, dateString) {
        this.sdwtsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangeypxqksrq(date, dateString) {
        this.ypxqksrqDateString = dateString
      },
      onChangeypxqjsrq(date, dateString) {
        this.ypxqjsrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangezpsj(date, dateString) {
        this.zpsjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

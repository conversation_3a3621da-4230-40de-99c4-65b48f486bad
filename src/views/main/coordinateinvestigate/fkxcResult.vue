<template>
  <a-modal
    title="反馈协查结果"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    footer=""
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="title">委托协查信息
        </div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="委托编号">
                <a-input allow-clear placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="被告人（罪犯）姓名">
                <a-input allow-clear placeholder="" v-decorator="['xm', {rules: [{required: true, message: '被告人（罪犯）姓名 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="拟适用社区矫正人员类型">
                <a-select
                  v-decorator="['nsysqjzrylx', {rules: [{required: true, message: '请选择拟适用社区矫正人员类型 ！'}]}]"
                  show-search
                  placeholder="拟适用社区矫正人员类型"
                  style="width: 100%"
                  :options="nsysqjzrylxDropDown"

                >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="title" style=" margin-top:35px ;">协查信息</div>
        <div style="background: white;height: 300px;width: 100%;">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="家庭和社会关系" >
                <a-input allow-clear placeholder="" v-decorator="['jthshgx', {rules: [{required: true, message: '请选择拟适用社区矫正人员类型 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="社会危险性、对所居住社区的影响等情况">
                <a-input allow-clear placeholder="" v-decorator="['shyx', {rules: [{required: true, message: '社会危险性、对所居住社区的影响等情况 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="拟禁止的事项">
                <a-input allow-clear placeholder="" v-decorator="['njzdsx', {rules: [{required: true, message: '拟禁止的事项 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="犯罪行为的后果和影响">
                <a-input allow-clear placeholder="" v-decorator="['fzyx', {rules: [{required: true, message: '犯罪行为的后果和影响 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="居住地村（居）民委员会和被害人意见">
                <a-input allow-clear placeholder="" v-decorator="['bhryj', {rules: [{required: true, message: '居住地村（居）民委员会和被害人意见 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="调查评估意见">
                <a-input allow-clear placeholder="" v-decorator="['dcpgyj', {rules: [{required: true, message: '调查评估意见 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="调查人">
                <a-input allow-clear placeholder="" v-decorator="['dcr', {rules: [{required: true, message: '调查人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="附件下载">
                <a href=""> 调查意见书.pdf </a>
                <a href=""> 预览 </a>
                <a href=""> 下载 </a>
              </a-form-item>
            </a-col>

          </a-row>

        </div>
        <br>
        <div class="title" style=" margin-top:35px ;">协查单位</div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="调查单位">
                <a-tree-select
                  v-decorator="['dcdw2', {rules: [{required: true, message: '请选择调查单位 ！'}]}]"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择调查单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="指派单位">
                <a-select
                  v-decorator="['zpwdmc', {rules: [{required: true, message: '请选择指派单位 ！'}]}]"
                  show-search
                  placeholder="请选择指派单位"
                  style="width: 100%"
                  :options="extOrgJcy"

                >
                </a-select>

              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="调查意见审核人">
                <a-input allow-clear placeholder="" v-decorator="['dcyjshr', {rules: [{required: true, message: '请输入调查意见审核人 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

      </a-form>
    </a-spin>
    <div
      style="display:flex;
      margin-left: 34%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <span style="flex: 1;margin: 10px;">
        <a-button type="primary" @click="handleSubmit(1)">完整信息提交</a-button>
      </span>
      <span style="flex: 1;">
        <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleSubmit(0)">暂存并关闭</a-button>
      </span>
    </div>
  </a-modal>
</template>

<script>

import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';
import { sysDictTypeDropDown } from '@/api/modular/system/dictManage'
  import moment from 'moment'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  import { coordinateInvestigateEdit, coordinateInvestigateFeedback } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  export default {
    data () {
      return {
        extOrgJcy: [],
        orgTree: [],
        nsysqjzrylxDropDown: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        id: undefined,
        sdwtsjDateString: '',
        csrqDateString: '',
        ypxqksrqDateString: '',
        ypxqjsrqDateString: '',
        pjrqDateString: '',
        zpsjDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created () {
          this.getdata()
          this.getOrgTree()
      },
    methods: {
      moment,
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      // 初始化方法
      edit (record) {
        this.visible = true
        this.id = record.id
        setTimeout(() => {
          this.form.setFieldsValue(
            {

              jthshgx: record.jthshgx,
              shyx: record.shyx,
              njzdsx: record.njzdsx,
              fzyx: record.fzyx,
              bhryj: record.bhryj,
              dcpgyj: record.dcpgyj,
              dcr: record.dcr,
              dcdw2: record.dcdw2,
              dcyjshr: record.dcyjshr,

              wtdwmc: record.wtdwmc,
              wtdwId: record.wtdwId,
              wtbh: record.wtbh,
              wtdcs: record.wtdcs,
              wtwsfj: record.wtwsfj,
              xm: record.xm,
              nsysqjzrylx: record.nsysqjzrylx,
              xb: record.xb,
              sfzh: record.sfzh,
              jzddzP: record.jzddzP,
              jzddzC: record.jzddzC,
              jzddz: record.jzddz,
              sfyjzddzmx: record.sfyjzddzmx,
              jzddzmx: record.jzddzmx,
              hjsfyjzdxt: record.hjsfyjzdxt,
              hjdzP: record.hjdzP,
              hjdzC: record.hjdzC,
              hjdz: record.hjdz,
              sfyhjdzmx: record.sfyhjdzmx,
              hjdzmx: record.hjdzmx,
              hzdw: record.hzdw,
              nsyjzlb: record.nsyjzlb,
              sfyypxq: record.sfyypxq,
              ypxq: record.ypxq,
              ypxf: record.ypxf,
              fjx: record.fjx,
              pjjg: record.pjjg,
              zpwdmc: record.zpwdmc,
              zpdwId: record.zpdwId,
              zpr: record.zpr,
              zpbz: record.zpbz
            }
          )
        }, 100)
        // 时间单独处理
        if (record.sdwtsj != null) {
            this.form.getFieldDecorator('sdwtsj', { initialValue: moment(record.sdwtsj, 'YYYY-MM-DD') })
        }
        this.sdwtsjDateString = moment(record.sdwtsj).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqksrq != null) {
            this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
        }
        this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.ypxqjsrq != null) {
            this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
        }
        this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zpsj != null) {
            this.form.getFieldDecorator('zpsj', { initialValue: moment(record.zpsj, 'YYYY-MM-DD') })
        }
        this.zpsjDateString = moment(record.zpsj).format('YYYY-MM-DD')
      },

      getdata() {
        extOrgInfoList({ type: 30, orgName: '' }).then(res => {
        res.data.forEach(p => {
          this.extOrgJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
        })
      })
        sysDictTypeDropDown({ code: 'nsysqjzrylx' }).then(res => {
        res.data.forEach(p => {
              this.nsysqjzrylxDropDown.push({ value: p.code, label: p.value })
        })
      })
      },
      handleSubmit (zt) {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.zt = zt
            values.id = this.id
            coordinateInvestigateFeedback(values).then((res) => {
              if (res.success) {
                this.$message.success('反馈成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('反馈失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdwtsj(date, dateString) {
        this.sdwtsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangeypxqksrq(date, dateString) {
        this.ypxqksrqDateString = dateString
      },
      onChangeypxqjsrq(date, dateString) {
        this.ypxqjsrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangezpsj(date, dateString) {
        this.zpsjDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style scoped>
.title{
    border-width: 0;
    width: 100%;

    /* height: 35px; */
    background: inherit;
    background-color: rgba(242, 242, 242, 1);
    border: none;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    font-weight: 700;
    font-style: normal;
    text-align: left;
    line-height:35px;
}
</style>

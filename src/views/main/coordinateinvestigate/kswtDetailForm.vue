<template>
  <sh-drawer
    title="委托协查信息"
    :visible="visible"
    :footer="null"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"

  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">委托协查信息
        </div>
        <div style="background: white;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="委托编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :disabled="linkDisabled" allow-clear placeholder="请输入委托编号" v-decorator="['wtbh', {rules: [{required: true, message: '请输入委托编号 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="被告人（罪犯）姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :disabled="linkDisabled" allow-clear placeholder="" v-decorator="['xm', {rules: [{required: true, message: '被告人（罪犯）姓名 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-form-item label="拟适用社区矫正人员类型" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['nsylx', {rules: [{required: true, message: '请选择拟适用社区矫正人员类型 ！'}]}]"
                  show-search
                  placeholder="拟适用社区矫正人员类型"
                  style="width: 100%">
                  <a-select-option v-for="(item,index) in nsysqjzrylxDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="被告人（罪犯）身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :disabled="linkDisabled"
                  style="width: 100%"
                  placeholder="请输入身份证号"
                  v-decorator="['sfzh',{rules: [{ required: false, message: '请输入身份证！'},{
                    validator:IDValidator ,
                  },]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="被告人（罪犯）性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['xb', {rules: [{required: true, message: '请选择性别 ！'}]}]"
                  show-search
                  placeholder="请选择性别">
                  <a-select-option v-for="(item,index) in xbDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="被告人（罪犯）出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker style="width: 100%" :disabled="linkDisabled" placeholder="请选择被告人（罪犯）出生日期" v-decorator="['csrq']" @change="onChangecsrq"/>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="原判刑期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :disabled="linkDisabled" placeholder="用中文简体填写（例：两年六个月）" v-decorator="['ypxq', {rules: [{required: true, message: '请输入原判刑刑期 ！'}]}]" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="原判刑期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker style="width: 100%" :disabled="linkDisabled" placeholder="请选择原判刑期开始日期" v-decorator="['ypxqksrq']" @change="onChangeypxqksrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="原判刑期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker style="width: 100%" :disabled="linkDisabled" placeholder="请选择原判刑期结束日期" v-decorator="['ypxqjsrq']" @change="onChangeypxqjsrq"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="原判刑罚" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['ypxf', {rules: [{required: true, message: '请输入原判刑罚 ！'}]}]"
                  show-search
                  placeholder="请选择原判刑罚">
                  <a-select-option v-for="(item,index) in zxzlDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="附加刑" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['fjx', {rules: [{required: true, message: '请选择附加刑 ！'}]}]"
                  show-search
                  placeholder="请选择附加刑">
                  <a-select-option v-for="(item,index) in fjxDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="判决机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :disabled="linkDisabled" placeholder="请输入判决机关" v-decorator="['pjjg', {rules: [{required: true, message: '请输入判决机关 ！'}]}]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="判决日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker style="width: 100%" :disabled="linkDisabled" placeholder="请选择判决日期" v-decorator="['pjrq']" @change="onChangepjrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="附件下载" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a href=""> 调查意见书.pdf </a>
                <a href=""> 预览 </a>
                <a href=""> 下载 </a>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="cus-title-d" style=" margin-top:35px ;">委托协查单位</div>
        <div style="background: white;height: 100px;width: 100%;">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item label="委托协查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  :disabled="linkDisabled"
                  v-decorator="['wtxcdw', {rules: [{required: true, message: '请选择委托协查单位 ！'}]}]"
                  show-search
                  placeholder="请选择委托协查单位"
                  style="width: 100%">
                  <a-select-option v-for="(item,index) in sfjDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item label="建议协查完成返回期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker style="width: 100%" :disabled="linkDisabled" placeholder="请选择建议协查完成返回期限" v-decorator="['jyfhqx']" @change="onChangejyfhqx"/>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
    <!-- <div
      style="display:flex;
      margin-left: 34%;
      margin-top: 20px;
      width:300px;
      height:50px;
      /* background-color: red; */
      flex-wrap: nowrap;
      justify-content: space-around;">
      <span style="flex: 1;margin: 10px;" v-if="!linkDisabled">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
      </span>
      <span style="flex: 1;">
        <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel">关闭</a-button>
      </span>
    </div> -->
  </sh-drawer>
</template>

<script>
  import moment from 'moment'
  import { coordinateInvestigateSendadd } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  export default {
    data () {
      return {
        linkDisabled: false,
        fjxDropDown: [],
        zxzlDropDown: [],
        sfjDropDown: [],
        nsysqjzrylxDropDown: [],
        xbDropDown: [],
        id: undefined,
        labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
        csrqDateString: null,
        jyfhqxDateString: null,
        ypxqksrqDateString: null,
        ypxqjsrqDateString: null,
        pjrqDateString: null,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    created () {
        this.sysDictTypeDropDown()
    },
    methods: {
      moment,
      IDValidator (rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
      // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
      sysDictTypeDropDown() {
        const dictOption = this.$options
        this.sfjDropDown = dictOption.filters['dictData']('sfj')
        this.fjxDropDown = dictOption.filters['dictData']('fjx')
        this.zxzlDropDown = dictOption.filters['dictData']('zxzl')
        this.xbDropDown = dictOption.filters['dictData']('xb')
        this.nsysqjzrylxDropDown = dictOption.filters['dictData']('nsysqjzrylx')
      },
      // 初始化方法
      add () {
        this.visible = true
        this.linkDisabled = false
      },
      // 初始化方法
      open (record) {
        this.visible = true
        if (record) {
          this.linkDisabled = true
          this.id = record.id
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                id: record.id,
                wtbh: record.wtbh,
                xm: record.xm,
                nsylx: record.nsylx,
                xb: record.xb,
                sfzh: record.sfzh,
                ypxq: record.ypxq,
                ypxf: record.ypxf,
                fjx: record.fjx,
                pjjg: record.pjjg,
                wtxcdw: record.wtxcdw
              }
            )
          }, 100)
          // 时间单独处理 出生日期
          if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
            this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
          }
          // 时间单独处理 原判刑期开始日期
          if (record.ypxqksrq != null) {
            this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
            this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
          }
          // 时间单独处理 原判刑期结束日期
          if (record.ypxqjsrq != null) {
            this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
            this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
          }
          // 时间单独处理 判决日期
          if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
            this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
          }
          // 时间单独处理 建议协查完成返回期限
          if (record.jyfhqx != null) {
            this.form.getFieldDecorator('jyfhqx', { initialValue: moment(record.jyfhqx, 'YYYY-MM-DD') })
            this.jyfhqxDateString = moment(record.jyfhqx).format('YYYY-MM-DD')
          }
        }
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.id = this.id
            values.jyfhqx = this.jyfhqxDateString
            values.csrq = this.csrqDateString
            values.ypxqksrq = this.ypxqksrqDateString
            values.ypxqjsrq = this.ypxqjsrqDateString
            values.pjrq = this.pjrqDateString
            coordinateInvestigateSendadd(values).then((res) => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        if (dateString === '') {
          this.csrqDateString = null
        } else {
          this.csrqDateString = dateString
        }
      },
      onChangejyfhqx(date, dateString) {
        if (dateString === '') {
          this.jyfhqxDateString = null
        } else {
          this.jyfhqxDateString = dateString
        }
      },
      onChangepjrq(date, dateString) {
        if (dateString === '') {
          this.pjrqDateString = null
        } else {
          this.pjrqDateString = dateString
        }
      },
      onChangeypxqksrq(date, dateString) {
        if (dateString === '') {
          this.ypxqksrqDateString = null
        } else {
          this.ypxqksrqDateString = dateString
        }
      },
      onChangeypxqjsrq(date, dateString) {
        if (dateString === '') {
          this.ypxqjsrqDateString = null
        } else {
          this.ypxqjsrqDateString = dateString
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.id = ''
        this.jyfhqxDateString = null
        this.csrqDateString = null
        this.ypxqksrqDateString = null
        this.ypxqjsrqDateString = null
        this.pjrqDateString = null
        this.jyfhqx = null
        this.csrq = null
        this.ypxqksrq = null
        this.ypxqjsrq = null
        this.pjrq = null
        this.visible = false
      }
    }
  }
</script>

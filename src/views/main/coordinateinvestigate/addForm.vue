<template>
  <sh-drawer
    title="调查评估"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
    footer=""
  >
    <!-- 步骤条 -->
    <div style="margin-bottom:20px">
      <a-steps v-model:current="current" type="navigation">
        <a-step title="指派" :disabled="nowzt<0"/>
        <a-step title="接收" :disabled="nowzt<1"/>
        <a-step title="调查" :disabled="nowzt<2"/>
        <a-step title="初审/小组意见" :disabled="nowzt<3"/>
        <a-step title="初审/集体评议" :disabled="nowzt<4"/>
        <a-step title="审批" :disabled="nowzt<5"/>
      </a-steps>
    </div>
    <!-- 指派页面 -->
    <a-form :form="form">
      <div v-if="current===0">
        <a-spin :spinning="confirmLoading">
          <div class="border">
            <div class="cus-title-d">委托信息
            </div>
          </div>
          <div class="border">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input :disabled="zt!=='1'" style="width:100%" placeholder="请输入委托单位" v-decorator="['wtdwmc']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="收到委托时间"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请选择收到委托时间"
                    v-decorator="['sdwtsj']"
                    @change="onChangesdwtsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input :disabled="zt!=='1'" style="width:100%" placeholder="请输入委托编号" v-decorator="['wtbh']"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托调查书"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-upload
                    :multiple="false"
                    :showUploadList="true"
                    :file-list="wtdcsfileList"
                    :remove="handleRemove"
                    :before-upload="beforeUpload">
                    <a-button type="primary" :disabled="zt!=='1'">
                      <a-icon type="upload"/>
                      点击上传
                    </a-button>
                  </a-upload>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托文书附件"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-upload
                    :multiple="false"
                    :showUploadList="true"
                    :file-list="wtwsfjfileList"
                    :remove="handleRemove1"
                    :before-upload="beforeUpload1">
                    <a-button :disabled="zt!=='1'" type="primary">
                      <a-icon type="upload"/>
                      点击上传
                    </a-button>
                  </a-upload>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="cus-title-d">
            被调查对象基本信息
          </div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input style="width:100%" :disabled="zt!=='1'" placeholder="请输入姓名" v-decorator="['xm']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="拟适用社区矫正人员类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['nsysqjzrylx', {rules: [{required: true, message: '请选择拟适用社区矫正人员类型 ！'}]}]"
                    show-search
                    placeholder="拟适用社区矫正人员类型"
                    style="width:100%"
                    :options="nsysqjzrylxDropDown"
                    :disabled="zt!=='1'">
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['xb', {rules: [{required: true, message: '请选择性别 ！'}]}]"
                    show-search
                    placeholder="请选择性别"
                    style="width:100%"
                    :options="xbDropDown"
                    :disabled="zt!=='1'">
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请选择出生日期"
                    v-decorator="['csrq']"
                    @change="onChangecsrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="身份证号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请输入身份证号"
                    v-decorator="['sfzh',{rules: [{ required: false, message: '请输入身份证！'},{
                      validator:IDValidator ,
                    },]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 居住地地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <VDistpicker
                    :disabled="zt!=='1'"
                    :province="jzdareaCode.province"
                    :city="jzdareaCode.city"
                    :area="jzdareaCode.area"
                    @selected="changeChoseCityjzd"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-radio-group
                    name="radioGroup"
                    v-decorator="['sfyjzddzmx']"
                    v-model:value="sfyjzddzmx"
                    :disabled="zt!=='1'">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="sfyjzddzmx==='1'">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 居住地地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请输入居住地明细地址"
                    v-decorator="['jzddzmx', {rules: [{required: true, message: '请输入居住地明细地址！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 户籍是否与居住地相同 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-radio-group
                    v-decorator="['hjsfyjzdxt']"
                    :disabled="zt!=='1'"
                    name="radioGroup"
                    v-model:value="hjsfyjzdxt">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="hjsfyjzdxt==='2'">
              <a-col :md="12" :sm="24" :pull="0">
                <a-form-item
                  label=" 户籍地址 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">

                  <VDistpicker
                    :disabled="zt!=='1'"
                    :province="hjareaCode.province"
                    :city="hjareaCode.city"
                    :area="hjareaCode.area"
                    @selected="changeChoseCityhj"
                    style="display: inline-block"></VDistpicker>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有户籍地址明细"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-radio-group
                    :disabled="zt!=='1'"
                    v-decorator="['sfyhjdzmx']"
                    name="radioGroup"
                    v-model:value="sfyhjdzmx">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="hjsfyjzdxt==='2'&&sfyhjdzmx==='1'">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 户籍地址明细 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请输入户籍明细地址"
                    v-decorator="['hjdzmx', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 工作单位 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="如无工作单位,填写'不详'"
                    v-decorator="['hzdw', {rules: [{required: true, message: '请输入工作单位 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 具体罪名 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-button type="primary" :disabled="zt!=='1'" @click="$refs.zmSelectRef.open()"> 选择</a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-table :columns="columns2" :data-source="zmlist" style="width:100%;margin:0 auto"></a-table>
            </a-row>
            <a-row :gutter="24">

              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 是否有原判刑期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-radio-group
                    :disabled="zt!=='1'"
                    v-decorator="['sfyypxq']"
                    name="radioGroup"
                    v-model:value="sfyypxq">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="sfyypxq==='1'">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 原判刑刑期 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="用中文简体填写（例：两年六个月）"
                    v-decorator="['ypxq', {rules: [{required: true, message: '请输入原判刑刑期 ！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原判刑期开始日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请选择原判刑期开始日期"
                    v-decorator="['ypxqksrq']"
                    @change="onChangeypxqksrq"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原判刑期结束日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请选择原判刑期结束日期"
                    v-decorator="['ypxqjsrq']"
                    @change="onChangeypxqjsrq"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 判决机关 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请输入判决机关"
                    v-decorator="['pjjg', {rules: [{required: true, message: '请输入判决机关 ！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原判刑罚"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['ypxf', {rules: [{required: true, message: '请输入原判刑罚 ！'}]}]"
                    show-search
                    placeholder="请选择原判刑罚"
                    style="width:100%"
                    :options="zxzlDropDown"
                    :disabled="zt!=='1'">
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="附加刑"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['fjx', {rules: [{required: true, message: '请选择附加刑 ！'}]}]"
                    show-search
                    placeholder="请选择附加刑"
                    style="width:100%"
                    :options="fjxDropDown"
                    :disabled="zt!=='1'">
                  </a-select>
                  <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="判决日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='1'"
                    style="width:100%"
                    placeholder="请选择判决日期"
                    v-decorator="['pjrq']"
                    @change="onChangepjrq"/>
                  <!-- <a-input placeholder="请输入公、检、法,监狱单位编制委托编号" v-decorator="['wtdwmc']" /> -->
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="cus-title-d">
            指派信息
          </div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="指派单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-tree-select
                    v-decorator="['zpdwId', {rules: [{required: false, message: '请选择指派单位 ！'}]}]"
                    :disabled="zt!=='1'"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择指派单位">
                  </a-tree-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 指派人 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    disabled
                    style="width:100%"
                    :placeholder="username"
                    v-decorator="['zpr', {rules: [{required: false, message: '指派人 '}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 指派时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    disabled
                    style="width:100%"
                    :placeholder="defaulttime"
                    v-decorator="['zpsj', {rules: [{required: false, message: '指派时间 ！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="备注"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-textarea
                    :disabled="zt!=='1'"
                    v-decorator="['zpbz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-spin>
      </div>
      <!-- 接收页面 -->
      <div v-else-if="current===1">
        <div class="border">
          <div class="cus-title-d">接收信息
          </div>
        </div>
        <div class="border">

          <a-row :gutter="24">
            <a-col :md="24" :sm="24">
              <a-form-item
                label="接收状态"
                :labelCol="labelCol2"
                :wrapperCol="wrapperCol2">
                <a-select
                  v-decorator="['zt']"
                  :disabled="zt!=='2'"
                  show-search
                  placeholder="请选择接收状态"
                  style="width:100%"
                  :options="optionszt">
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 接收人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  style="width:100%"
                  :placeholder="username"
                  v-decorator="['jsr', {rules: [{required: false, message: '请输入接收人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 接收时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  style="width:100%"
                  :placeholder="defaulttime"
                  v-decorator="['jssj', {rules: [{required: false, message: '请输入接收时间 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-form-item
                label="备注"
                :labelCol="labelCol2"
                :wrapperCol="wrapperCol2">
                <a-textarea
                  :disabled="zt!=='2'"
                  v-decorator="['bz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
      <!-- 调查页面 -->
      <div v-else-if="current===2">
        <div class="border">
          <div class="cus-title-d">
            <span v-if="dcgc">调查过程</span>
            <span v-if="dcpgb">文书制作--浙江省社区矫正调查评估表</span>
            <span v-if="dcpgyjs">文书制作--调查评估意见书</span>
            <span v-else>调查信息</span>
          </div>
        </div>
        <div class="border" style="">
          <div class="dc" v-show="!qrdc&&!dcgc&&!dcpgb&&!dcpgyjs">
            <!-- 调查外部内容页 -->
            <div style="flex:1;text-align:center;margin:10px 0" @click="qrdc=!qrdc">
              <div class="box" style="background:red"/>
              <div class="title">
                确认调查
              </div>
              <div>
                <div v-if="parseInt(zt) >3" style="color:green">已提交</div>
                <div v-else>未提交</div>
              </div>
            </div>

            <div style="flex:1;text-align:center;margin:10px 0" @click="dcgc=!dcgc">
              <div class="box" style="background:yellow"/>
              <div class="title">
                调查过程
              </div>
              <div>
                <div v-if="parseInt(zt) >3&&parseInt(zt) !==31" style="color:green">已提交</div>
                <div v-else>未提交</div>
              </div>
            </div>

            <div style="flex:1;text-align:center;margin:10px 0" @click="dcpgb=!dcpgb">
              <div class="box" style="background:orange"/>
              <div class="title">
                浙江省矫正调查评估表
              </div>
              <div>
                <div v-if="zt.length === 1 &&parseInt(zt)>=4 || zt.length === 2&&parseInt(zt) >32" style="color:green">
                  已提交
                </div>
                <div v-else>未提交</div>
              </div>
            </div>

            <div style="flex:1;text-align:center;margin:10px 0" @click="dcpgyjs=!dcpgyjs">
              <div class="box" style="background:purple"/>
              <div class="title">
                调查评估意见书
              </div>
              <div>
                <div v-if="zt.length === 1 &&parseInt(zt)>=4" style="color:green">已提交</div>
                <div v-else>未提交</div>
              </div>
            </div>
          </div>
          <div v-if="qrdc">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否开展调查"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-select
                    :disabled="zt!=='3'"
                    v-decorator="['zt', {rules: [{required: true, message: '请选择是否开展调查 ！'}]}]"
                    show-search
                    placeholder="请选择是否开展调查"
                    style="width:100%"
                    :options="optionskzdc">
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查人 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    disabled
                    style="width:100%"
                    :placeholder="username"
                    v-decorator="['dcr', {rules: [{required: false, message: '请输入调查人 ！'}]}]"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    disabled
                    style="width:100%"
                    :placeholder="defaulttime"
                    v-decorator="['dcsj']"
                    @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="备注"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-textarea
                    :disabled="zt!=='3'"
                    v-decorator="['bz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div v-if="dcgc">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="调查单位（县区局）"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-tree-select
                    v-decorator="['dcdw1', {rules: [{required: false, message: '请选择接收单位 ！'}]}]"
                    :disabled="zt!=='31'"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择接收单位">
                  </a-tree-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="调查单位（司法所）"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-tree-select
                    v-decorator="['dcdw2', {rules: [{required: false, message: '请选择接收单位 ！'}]}]"
                    :disabled="zt!=='31'"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择接收单位">
                  </a-tree-select>
                </a-form-item>
              </a-col>
            </a-row>
            <div class="wsinfo" style="width:100px">年 月 日调查</div>
            <div class="border" >
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="被调查人姓名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='31'"
                      style="width:100%"
                      placeholder="直接获取，暂为空，审批通过之后自动显示"
                      v-decorator="['xm', {rules: [{required: true, message: '请输入被调查人姓名 ！'}]}]"/>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="与被告人（罪犯）关系"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-select
                      :disabled="zt!=='31'"
                      v-decorator="['gx', {rules: [{required: true, message: '请选择与被告人（罪犯）关系 ！'}]}]"
                      show-search
                      placeholder="YDS,只能选择直系亲属YDS01下子节点,且为打开状态"
                      style="width: 100%"
                      :options="yzfgxDropDown">
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label=" 调查时间 "
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="zt!=='31'"
                      style="width:100%"
                      placeholder="直接获取“收到委托时间”"
                      v-decorator="['dcsj']"
                      @change="onChangedcsj"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="调查地点"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='31'"
                      style="width:100%"
                      placeholder="直接获取，可修改"
                      v-decorator="['dcdd', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="调查事项"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='31'"
                      placeholder="直接获取，可修改"
                      v-decorator="['dcsx', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="调查人"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-button :disabled="zt!=='31'" type="primary" @click="$refs.userSelectRef.open()">请选择</a-button>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-table :columns="columns3" :data-source="dcrList" style="width:100%;margin:20px auto">
                  <span slot="sex" slot-scope="text">
                    {{ 'sex' | dictType(text) }}
                  </span>
                </a-table>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="上传笔录"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-upload
                      :multiple="false"
                      :showUploadList="true"
                      :file-list="blfileList"
                      :remove="handleRemove2"
                      :before-upload="beforeUpload2">
                      <a-button type="primary" :disabled="zt!=='31'">
                        <a-icon type="upload"/>
                        点击上传
                      </a-button>
                    </a-upload>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托调查材料"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-upload
                    :multiple="false"
                    :showUploadList="true"
                    :file-list="wtfileList"
                    :remove="handleRemove3"
                    :before-upload="beforeUpload3">
                    <a-button type="primary" :disabled="zt!=='31'">
                      <a-icon type="upload"/>
                      点击上传
                    </a-button>
                  </a-upload>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div v-if="dcpgb">
            <div class="wsinfo" style="width:70px">调查对象</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="民族"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-select
                      :disabled="zt!=='32'"
                      v-decorator="['mz', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                      show-search
                      placeholder="请选择民族"
                      style="width: 100%"
                      :options="mzDropDown">
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="别名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='32'"
                      style="width: 100%"
                      placeholder="请输入别名"
                      v-decorator="['bm', {rules: [{required: true, message: '请输入别名 ！'}]}]"/>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="曾用名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='32'"
                      style="width: 100%"
                      placeholder="请输入曾用名"
                      v-decorator="['cym', {rules: [{required: true, message: '请输入曾用名 ！'}]}]"/>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="籍贯"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='32'"
                      style="width: 100%"
                      placeholder="请输入籍贯"
                      v-decorator="['jg', {rules: [{required: true, message: '请输入籍贯 ！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="家庭住址"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='32'"
                      style="width:100%"
                      placeholder="请输入家庭住址"
                      v-decorator="['jtzz', {rules: [{required: true, message: '请输入家庭住址 ！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="联系电话"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='32'"
                      style="width: 100%"
                      placeholder="请输入联系电话"
                      v-decorator="['lxdh', {rules: [{required: true, message: '请输入联系电话 ！'}]}]"/>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="职业"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-select
                      :disabled="zt!=='32'"
                      v-decorator="['zy', {rules: [{required: true, message: '请选择职业 ！'}]}]"
                      show-search
                      placeholder="请选择职业"
                      style="width: 100%"
                      :options="zyDropDown">
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:110px">家庭和社会关系</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="有无家庭成员"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-radio-group
                      name="radioGroup"
                      :disabled="zt!=='32'"
                      v-decorator="['ywjtcy', {rules: [{required: false, message: '有无家庭成员 ！'}]}]">
                      <a-radio value="1">有</a-radio>
                      <a-radio value="2">无</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="社会交往情况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="社会交往情况"
                      v-decorator="['shjwqk', {rules: [{required: true, message: '社会交往情况 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="主要社会关系"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="主要社会关系"
                      v-decorator="['zyshgx', {rules: [{required: true, message: '主要社会关系 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="未成年对象的其他情况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['wcn', {rules: [{required: true, message: '未成年对象的其他情况 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:70px">个性特点</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="生理状况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['slzk', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="心理特征"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['xltz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="性格类型"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-select
                      :disabled="zt!=='32'"
                      v-decorator="['xglx', {rules: [{required: true, message: '请输入提请理由 ！'}]}]"
                      show-search
                      placeholder="请选择性格类型"
                      style="width: 100%"
                      :options="xglxDropDown">
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="爱好特长"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['ahtc', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:70px">一贯表现</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="工作（学习）表现"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['gzxxbx', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="遵纪守法情况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['zjsfqk', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="有无不良嗜好、行为恶习"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['blsh', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:140px">犯罪情况和悔罪表现</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="犯罪原因"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['fzyy', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="主观恶性"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['zgex', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="是否有犯罪前科"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['sfyfzqk', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="认罪悔罪态度"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['rzhztd', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:70px">社会反响</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="被害人或其亲属态度"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['bhrhqqstd', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="社会公众态度"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['shgztd', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:70px">监管条件</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="家庭成员态度"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['jtcytd', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="经济生活状况和环境"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['jjshzkhhj', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="基层组织意见"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="工作单位、就读学校和村（社区）"
                      v-decorator="['hjdxz', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div class="wsinfo" style="width:70px">审批意见</div>
            <div class="border" style="width:90%;margin:5% 5% 30px 5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="辖区公安派出所意见"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='32'"
                      placeholder="直接获取，可修改"
                      v-decorator="['gayj', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
          <div v-if="dcpgyjs">
            <div class="wsinfo">文书信息</div>
            <div class="border" style="width:90%;margin:5%;padding:20px">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="字"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      style="width:100%"
                      disabled
                      placeholder="直接获取，暂为空，审批通过之后自动显示"
                      v-decorator="['p6.zh', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="委托单位"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      style="width:100%"
                      disabled
                      placeholder="直接获取“委托单位"
                      v-decorator="['wtdw', {rules: [{required: false, message: '直接获取“委托单位 ！'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label=" 调查开始时间 "
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="zt!=='33'"
                      style="width:100%"
                      placeholder="直接获取“收到委托时间”"
                      v-decorator="['SDWTSJ']"
                      @change="onChangeSDWTSJ"/>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label=" 调查结束时间 "
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-date-picker
                      :disabled="zt!=='33'"
                      style="width:100%"
                      placeholder="直接获取“调查结束时间”"
                      v-decorator="['p6.dcjssj']"
                      @change="onChangep6dcjssj"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="姓名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      style="width:100%"
                      disabled
                      placeholder="直接获取，可修改"
                      v-decorator="['xm', {rules: [{required: false, message: '直接获取，可修改'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="有关情况"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-textarea
                      :disabled="zt!=='33'"
                      placeholder="直接获取，可修改"
                      v-decorator="['ygqk', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                      :auto-size="{ minRows: 5, maxRows: 5 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="评估意见"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol">
                    <a-input
                      :disabled="zt!=='33'"
                      style="width:100%"
                      placeholder="暂不显示，不可操作，区县司法局局长填写"
                      v-decorator="['p6.dcpgyj', {rules: [{required: false, message: '暂不显示，不可操作，区县司法局局长填写!'}]}]"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!-- <a-row :gutter="24">
              <a-col :md="12" :sm="24" >
                <a-form-item
                  label="调查评估意见书"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-button type="primary">文书打印</a-button>
                </a-form-item>
              </a-col>
            </a-row> -->
          </div>
        </div>
      </div>
      <!-- 初审小组意见 -->
      <div v-else-if="current===3">
        <div class="border">
          <div
            style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          >司法所负责人初审信息
          </div>
        </div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="初审结果"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-select
                  :disabled="zt!=='4'"
                  v-decorator="['zt', {rules: [{required: true, message: '请选择初审结果 ！'}]}]"
                  show-search
                  placeholder="请选择初审结果"
                  style="width:100%"
                  :options="optionssp">
                </a-select>
                <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 初审人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  style="width:100%"
                  :placeholder="username"
                  v-decorator="['csr', {rules: [{required: false, message: '请输入初审人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 初审时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="zt!=='4'"
                  style="width:100%"
                  placeholder="收到初审时间"
                  v-decorator="['cssj', {rules: [{required: true, message: '请输入初审时间 ！'}]}]"
                  @change="onChangecssj"/>
                <!-- <a-inputstyle="width:100%" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
              </a-form-item>
            </a-col>

          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="初审意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='4'"
                  v-decorator="['csyj', {rules: [{required: true, message: '请输入初审意见 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
        >
          司法所调查评估小组意见信息
        </div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 合议事项 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width:100%"
                  v-decorator="['hysx', {rules: [{required: true, message: '请输入合议事项 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 主持人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width:100%"
                  v-decorator="['zcr', {rules: [{required: true, message: '请输入主持人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 合议地点 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width:100%"
                  v-decorator="['hydd', {rules: [{required: true, message: '请输入合议地点 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 合议时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker :disabled="zt!=='4'"style="width:100%" v-decorator="['hysj']" @change="onChangehysj"/>
              </a-form-item>
            </a-col>

          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 合议人员 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width: 100%"
                  v-decorator="['hyry', {rules: [{required: true, message: '请输入合议人员 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 记录人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width:100%"
                  v-decorator="['jlr', {rules: [{required: true, message: '请输入记录人 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="合议情况"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='4'"
                  v-decorator="['hyqk', {rules: [{required: true, message: '请输入合议情况 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="合议意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='4'"
                  v-decorator="['hyyj', {rules: [{required: true, message: '请输入合议意见 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 负责人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='4'"
                  style="width:100%"
                  v-decorator="['fzr', {rules: [{required: true, message: '请输入负责人 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="备注"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='4'"
                  v-decorator="['bz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查评估小组意见表："
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-button type="primary">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row> -->
        </div>
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
          文书制作--调查评估意见书
        </div>
        <div class="border" style="">
          <div class="wsinfo">文书信息</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="字"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，暂为空，审批通过之后自动显示"
                    v-decorator="['z', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取“委托单位"
                    v-decorator="['wtdw', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查开始时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='4'"
                    style="width:100%"
                    placeholder="直接获取“收到委托时间”"
                    v-decorator="['zpsj']"
                    @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查结束时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='4'"
                    style="width:100%"
                    placeholder="直接获取“调查结束时间”"
                    v-decorator="['pyshsj']"
                    @change="onChangespsj"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，可修改"
                    v-decorator="['name', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="有关情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-textarea
                    :disabled="zt!=='4'"
                    placeholder="直接获取，可修改"
                    v-decorator="['ygqk', {rules: [{required: false, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="评估意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="暂不显示，不可操作，区县司法局局长填写"
                    v-decorator="['dcpgy', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!-- <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查评估意见书"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-button type="primary">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row> -->
        </div>
      </div>
      <!-- 集体评议 -->
      <div v-else-if="current===4">
        <div class="border">
          <div
            style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          >区县司法局初审信息
          </div>
        </div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="初审结果"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-select
                  v-decorator="['zt', {rules: [{required: true, message: '请选择初审结果 ！'}]}]"
                  show-search
                  placeholder="请选择初审结果"
                  style="width:100%"
                  :options="optionssp"

                  :disabled="zt!=='5'"
                >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 初审人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  style="width:100%"
                  :placeholder="username"
                  v-decorator="['csr', {rules: [{required: false, message: '初审人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 初审时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="zt!=='5'"
                  style="width:100%"
                  placeholder="请选择初审时间"
                  v-decorator="['cssj']"
                  @change="onChangecssj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="初审意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='5'"
                  v-decorator="['csyj', {rules: [{required: true, message: '请输入初审意见 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>

        </div>
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
          区县司法局评议审核信息
        </div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 评议审核事项 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width:100%"
                  v-decorator="['pyshsx', {rules: [{required: true, message: '请输入评议审核事项 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 主持人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width:100%"
                  v-decorator="['zcr', {rules: [{required: true, message: '请输入主持人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 评议审核地点 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width:100%"
                  v-decorator="['pyshdd', {rules: [{required: true, message: '请输入评议审核地点 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 评议审核时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="zt!=='5'"
                  style="width:100%"
                  placeholder="评议审核时间"
                  v-decorator="['pyshsj']"
                  @change="onChangepyshsj"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 评议审核人员 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width: 100%"
                  v-decorator="['pyshry', {rules: [{required: true, message: '请输入评议审核人员 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 记录人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width:100%"
                  v-decorator="['jlr', {rules: [{required: true, message: '请输入记录人 ！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 负责人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  :disabled="zt!=='5'"
                  style="width:100%"
                  v-decorator="['fzr', {rules: [{required: true, message: '请输入负责人 ！'}]}]"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="评议审核情况"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='5'"
                  v-decorator="['pyshqk', {rules: [{required: true, message: '请输入评议审核情况 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="评议审核意见"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='5'"
                  v-decorator="['pyshyj', {rules: [{required: true, message: '请输入评议审核意见 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="备注"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-textarea
                  :disabled="zt!=='5'"
                  v-decorator="['bz', {rules: [{required: true, message: '请输入备注 ！'}]}]"
                  :auto-size="{ minRows: 5, maxRows: 5 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查评估小组意见表："
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-button type="primary">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row> -->
        </div>
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
          文书制作--调查评估意见书
        </div>
        <div class="border">
          <div class="wsinfo">文书信息</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="字"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，暂为空，审批通过之后自动显示"
                    v-decorator="['hjdxz', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取“委托单位"
                    v-decorator="['hjdxz', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查开始时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='5'"
                    style="width:100%"
                    placeholder="直接获取“收到委托时间”"
                    v-decorator="['zpsj']"
                    @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label=" 调查结束时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='5'"
                    style="width:100%"
                    placeholder="直接获取“调查结束时间”"
                    v-decorator="['pyshsj']"
                    @change="onChangespsj"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，可修改"
                    v-decorator="['name', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="有关情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-textarea
                    :disabled="zt!=='5'"
                    placeholder="直接获取，可修改"
                    v-decorator="['ygqk', {rules: [{required: false, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="评估意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="暂不显示，不可操作，区县司法局局长填写"
                    v-decorator="['dcpgy', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!-- <a-row :gutter="24">
            <a-col :md="12" :sm="24" >
              <a-form-item
                label="调查评估意见书"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-button type="primary">文书打印</a-button>
              </a-form-item>
            </a-col>
          </a-row> -->
        </div>
      </div>
      <!-- 审批 -->
      <div v-else-if="current===5">
        <div class="border">
          <div
            style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          "
          >区县司法局负责人审批信息
          </div>
        </div>
        <div class="border" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="审批结果"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-select
                  v-decorator="['zt', {rules: [{required: true, message: '请选择审批结果 ！'}]}]"
                  show-search
                  placeholder="请选择审批结果"
                  style="width:100%"
                  :options="optionssp"

                  :disabled="zt!=='6'"
                >
                </a-select>
                <!-- <a-date-picker style="width: 100%" placeholder="收到委托时间" v-decorator="['zpsj']" @change="onChangezpsj"/> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 调查意见审批人 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  style="width:100%"
                  :placeholder="username"
                  v-decorator="['spr', {rules: [{required: false, message: '请输入调查意见审批人！'}]}]"/>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 审批时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker
                  disabled
                  style="width:100%"
                  :placeholder="defaulttime"
                  v-decorator="['spsj']"
                  @change="onChangespsj"/>

                <!-- <a-inputstyle="width:100%" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
              </a-form-item>
            </a-col>

          </a-row>

          <a-row>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="评估意见 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <!-- <a-input disabledstyle="width:100%" :placeholder="username" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
                <a-select
                  :disabled="zt!=='6'"
                  v-decorator="['dcpgyj', {rules: [{required: true, message: '请选择评估意见 ！'}]}]"
                  show-search
                  placeholder="请选择评估意见"
                  style="width:100%"
                  :options="dcpgyjDropDown">
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label=" 调查结束时间 "
                :labelCol="labelCol"
                :wrapperCol="wrapperCol">
                <a-date-picker
                  disabled
                  style="width:100%"
                  :placeholder="defaulttime"
                  v-decorator="['pyshsj']"
                  @change="onChangespsj"/>

                <!-- <a-inputstyle="width:100%" placeholder="请选择初审时间" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍明细地址 ！'}]}]" /> -->
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div
          style="font-family: '微软雅黑', sans-serif;font-weight: 400;
          font-style: normal;color: #1078C9;
          background-color: rgba(228, 243, 255, 1);
          /* margin-bottom: 20px; */
          ">
          文书制作--调查评估意见书
        </div>
        <div class="border">
          <div class="wsinfo">文书信息</div>
          <div class="border" style="width:90%;margin:5%;padding:20px">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="字"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，暂为空，审批通过之后自动显示"
                    v-decorator="['z', {rules: [{required: false, message: '直接获取，暂为空，审批通过之后自动显示 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="委托单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取“委托单位"
                    v-decorator="['wtdw', {rules: [{required: false, message: '委托单位 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item

                  label=" 调查开始时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='6'"
                    style="width:100%"
                    placeholder="直接获取“收到委托时间”"
                    @change="onChangezpsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item

                  label=" 调查结束时间 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-date-picker
                    :disabled="zt!=='6'"
                    style="width:100%"
                    placeholder="直接获取“调查结束时间”"
                    v-decorator="['pyshsj']"
                    @change="onChangespsj"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    style="width:100%"
                    disabled
                    placeholder="直接获取，可修改"
                    v-decorator="['name', {rules: [{required: false, message: '请输入户籍明细地址 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="有关情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-textarea
                    :disabled="zt!=='6'"
                    placeholder="直接获取，可修改"
                    v-decorator="['ygqk', {rules: [{required: true, message: '直接获取，可修改 ！'}]}]"
                    :auto-size="{ minRows: 5, maxRows: 5 }"/>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="评估意见"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol">
                  <a-input
                    :disabled="zt!=='6'"
                    style="width:100%"
                    placeholder="暂不显示，不可操作，区县司法局局长填写"
                    v-decorator="['dcpgy', {rules: [{required: true, message: '请输入评估意见 ！'}]}]"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </a-form>
    <div
      slot="footer"
    >
      <div v-show="current===0">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleEdit" v-show="zt==='1'">指派</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt==='1'" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===1">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit2" v-show="zt==='2'">提交</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt==='2'" @click="handleCancel">取消</a-button>
        </span>
      </div>
      <div v-show="current===2">
        <div v-show="qrdc">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="handleSubmit3('qrdc')" v-show="zt==='3'">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt==='3'" @click="qrdc=!qrdc">取消</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt!=='3'" @click="qrdc=!qrdc">返回</a-button>
          </span>
        </div>
        <div v-show="dcgc">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="handleSubmit3('dcgc')" v-show="zt==='31'">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt==='31'" @click="dcgc=!dcgc">取消</a-button>
          </span>
          <span style="flex: 1;">
            <a-button style="flex: 1;margin: 10px;" type="primary" v-show="zt!=='31'" @click="dcgc=!dcgc">返回</a-button>
          </span>
        </div>
        <div v-show="dcpgb">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="handleSubmit3('dcpgb')" v-show="zt==='32'">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button
              style="flex: 1;margin: 10px;"
              type="primary"
              @click="dcpgb=!dcpgb"
              v-show="zt==='32'">取消</a-button>
          </span>
          <span style="flex: 1;">
            <a-button
              style="flex: 1;margin: 10px;"
              type="primary"
              @click="dcpgb=!dcpgb"
              v-show="zt!=='32'">返回</a-button>
          </span>
        </div>
        <div v-show="dcpgyjs">
          <span style="flex: 1;margin: 10px;">
            <a-button type="primary" @click="handleSubmit3('dcpgyjs')" v-show="zt==='33'">保存</a-button>
          </span>
          <span style="flex: 1;">
            <a-button
              style="flex: 1;margin: 10px;"
              type="primary"
              @click="dcpgyjs=!dcpgyjs"
              v-show="zt==='33'">取消</a-button>
          </span>
          <span style="flex: 1;">
            <a-button
              style="flex: 1;margin: 10px;"
              type="primary"
              @click="dcpgyjs=!dcpgyjs"
              v-show="zt!=='33'">返回</a-button>
          </span>
        </div>
      </div>
      <div v-show="current===3">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit4" v-show="zt==='4'">提交并盖章</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel" v-show="zt==='4'">取消</a-button>
        </span>
      </div>
      <div v-show="current===4">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit5" v-show="zt==='5'">提交</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleSubmit5" v-show="zt==='5'">保存</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel" v-show="zt==='5'">取消</a-button>
        </span>
      </div>
      <div v-show="current===5">
        <span style="flex: 1;margin: 10px;">
          <a-button type="primary" @click="handleSubmit6" v-show="zt==='6'">提交</a-button>
        </span>
        <span style="flex: 1;">
          <a-button style="flex: 1;margin: 10px;" type="primary" @click="handleCancel" v-show="zt==='6'">取消</a-button>
        </span>
      </div>
    </div>
    <zmSelect ref="zmSelectRef" @ok="ok"/>
    <userSelect ref="userSelectRef" @ok1="ok1">
    </userselect>
  </sh-drawer>
</template>

<script>
import userSelect from './user/index.vue'
import zmSelect from '../chargeInfo/zmSelect.vue'
import moment from 'moment'
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import { sysDictTypeDropDown } from '@/api/modular/system/dictManage'
import {
  coordinateInvestigategetProcess, coordinateInvestigateAdd,
  CoordinateInvestigatedetail,
  coordinateInvestigateEdit,
  coordinateInvestigatep2,
  coordinateInvestigatep3,
  coordinateInvestigatelastP3,
  coordinateInvestigatep4,
  coordinateInvestigatep5,
  coordinateInvestigatep6
} from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
import VDistpicker from 'v-distpicker/src/Distpicker'

export default {
  components: { VDistpicker, zmSelect, userSelect },
  data() {
    return {
      jzdareaCode: { province: '', city: '', area: '' },
      hjareaCode: { province: '', city: '', area: '' },
      qrdc: false, // 确认调查展示状态
      dcgc: false,
      dcpgb: false,
      dcpgyjs: false,
      sfyypxq: 1,
      sfyhjdzmx: 1,
      hjsfyjzdxt: 1,
      sfyjzddzmx: 1,
      optionszt: [{ label: '接收', value: '1' }, { label: '退回', value: '0' }],
      optionssp: [{ label: '通过', value: '1' }, { label: '退回', value: '0' }],
      optionskzdc: [{ label: '开展调查', value: '1' }, { label: '退回', value: '0' }],
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
        wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      current: 0,
      dcsjDateString: null,
      spsjDateString: null,
      pyshsjDateString: null,
      cssjDateString: null,
      hysjDateString: null,
      sdwtsjDateString: null,
      csrqDateString: null,
      ypxqksrqDateString: null,
      ypxqjsrqDateString: null,
      pjrqDateString: null,
      zpsjDateString: null,
      p6dcjssjDateString: null,
      SDWTSJDateString: null,
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      blfileList: [],
      wtfileList: [],
      nsysqjzrylxDropDown: [],
      xbDropDown: [],
      zxzlDropDown: [],
      fjxDropDown: [],
      yzfgxDropDown: [],
      mzDropDown: [],
      zyDropDown: [],
      dcpgyjDropDown: [],
      xglxDropDown: [],
      orgTree: [],
      wtdcsfileList: [],
      wtdcs: [],
      bl: [],
      wt: [],
      wtwsfjfileList: [],
      wtwsfj: [],
      username: '',
      defaulttime: '',
      userid: '',
      id: '',
      nowzt: undefined,
      zt: '',
      lastP3: '',
      detail: '',
      record: undefined,
      historydata: undefined,
      sexDictTypeDropDown: [],
      zmlist: [],
      dcrList: [],
      columns2: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '罪名代码',
          align: 'center',
          dataIndex: 'chargeCode'

        },
        {
          title: '罪名',
          align: 'center',
          dataIndex: 'charge'
        }

      ],
      columns3: [
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex',
          scopedSlots: { customRender: 'sex' }
        }, {
          title: '手机',
          align: 'center',
          dataIndex: 'phone'
        }
      ]
    }
  },
  created() {
    this.defaulttime = moment().format('YYYY-MM-DD hh:mm:ss')
    this.userid = localStorage.getItem('USER_ID')
    this.username = localStorage.getItem('USER_NAME')
    this.getdata()
  },
  watch: {
    current: {
      immediate: true,
      handler(newValue) {
        if (newValue !== 2) {
          this.qrdc = false
          this.dcgc = false
          this.dcpgb = false
          this.dcpgyjs = false
        }
        if (newValue === 0 && this.record) {
          this.sfyjzddzmx = this.record.sfyjzddzmx
          this.sfyypxq = this.record.sfyypxq
          this.hjsfyjzdxt = this.record.hjsfyjzdxt
          console.log(this.record)
          this.$nextTick(() => {
            setTimeout(() => {
              this.form.setFieldsValue(this.record)
            }, 50);
          })
          this.zmlist = JSON.parse(this.record.zm)
          this.sdwtsjDateString = this.record.sdwtsj
          this.csrqDateString = this.record.csrq
          this.ypxqksrqDateString = this.record.ypxqksrq
          this.ypxqjsrqDateString = this.record.ypxqjsrq
          this.pjrqDateString = this.record.pjrq

          this.jzdareaCode.province = this.record.jzddzP
          this.jzdareaCode.city = this.record.jzddzC
          this.jzdareaCode.area = this.record.jzddz

          this.hjareaCode.province = this.record.hjdzP
          this.hjareaCode.city = this.record.hjdzC
          this.hjareaCode.area = this.record.hjdz

          this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(this.record.ypxqksrq, 'YYYY-MM-DD hh:mm:ss') })
          this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(this.record.ypxqjsrq, 'YYYY-MM-DD hh:mm:ss') })
          this.form.getFieldDecorator('pjrq', { initialValue: moment(this.record.pjrq, 'YYYY-MM-DD hh:mm:ss') })
          this.form.getFieldDecorator('sdwtsj', { initialValue: moment(this.record.sdwtsj, 'YYYY-MM-DD hh:mm:ss') })
          this.form.getFieldDecorator('csrq', { initialValue: moment(this.record.csrq, 'YYYY-MM-DD hh:mm:ss') })
        }
        if (newValue === 1 && this.historydata && this.historydata.p2) {
          console.log(this.historydata.p2)
          this.historydata.p2.jssj = moment(this.historydata.p2.jssj).format('YYYY-MM-DD hh:mm:ss')

          this.$nextTick(() => {
            setTimeout(() => {
              this.form.setFieldsValue(this.historydata.p2)
            }, 50);
          })
        }

        if (newValue === 3 && this.historydata && this.historydata.p4) {
          console.log(this.historydata.p4)

          this.$nextTick(() => {
            setTimeout(() => {
              this.form.setFieldsValue(this.historydata.p4)
            }, 50);
          })
          if (this.historydata.p4.cssj) {
            this.form.getFieldDecorator('cssj', { initialValue: moment(this.historydata.p4.cssj, 'YYYY-MM-DD hh:mm:ss') })
          }
          if (this.historydata.p4.hysj) {
            this.form.getFieldDecorator('hysj', { initialValue: moment(this.historydata.p4.hysj, 'YYYY-MM-DD hh:mm:ss') })
          }
        }

        if (newValue === 4 && this.historydata && this.historydata.p5) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.form.setFieldsValue(this.historydata.p5)
            }, 50);
          })

          if (this.historydata.p5.cssj) {
            this.form.getFieldDecorator('cssj', { initialValue: moment(this.historydata.p5.cssj, 'YYYY-MM-DD hh:mm:ss') })
          }
          if (this.historydata.p5.hysj) {
            this.form.getFieldDecorator('hysj', { initialValue: moment(this.historydata.p5.hysj, 'YYYY-MM-DD hh:mm:ss') })
          }
          if (this.historydata.p5.pyshsj) {
            this.form.getFieldDecorator('pyshsj', { initialValue: moment(this.historydata.p5.pyshsj, 'YYYY-MM-DD hh:mm:ss') })
          }
        }

        if (newValue === 5 && this.historydata && this.historydata.p6) {
          console.log(this.historydata.p6)

          this.$nextTick(() => {
            setTimeout(() => {
              this.form.setFieldsValue(this.historydata.p6)
            }, 50);
          })
          if (this.historydata.p6.pyshsj) {
            this.form.getFieldDecorator('pyshsj', { initialValue: moment(this.historydata.p6.pyshsj, 'YYYY-MM-DD hh:mm:ss') })
          }
          if (this.historydata.p6.spsj) {
            this.form.getFieldDecorator('spsj', { initialValue: moment(this.historydata.p6.spsj, 'YYYY-MM-DD hh:mm:ss') })
          }
        }
      }
    },
    qrdc: {
      handler() {
        console.log(this.detail)
        this.$nextTick(() => {
          setTimeout(() => {
            this.form.setFieldsValue(this.detail)
          }, 50);
        })
        this.form.getFieldDecorator('dcsj', { initialValue: moment(this.record.sdwtsj, 'YYYY-MM-DD hh:mm:ss') })
      }
    },
    dcgc: {
      handler() {
        console.log(this.detail)
        this.dcrList = JSON.parse(this.historydata.p3.records[0].dcr)
        this.blfileList = this.historydata.p3.records[0].dcblFileList
        this.wtfileList = this.historydata.p3.wtdcclFileList
        this.$nextTick(() => {
          setTimeout(() => {
            this.form.setFieldsValue(this.detail)
            this.form.setFieldsValue(this.historydata.p3.records[0])
          }, 50);
        })
        this.form.getFieldDecorator('dcsj', { initialValue: moment(this.record.sdwtsj, 'YYYY-MM-DD hh:mm:ss') })
      }
    },
    dcpgb: {
      handler() {
        console.log(this.detail)
        this.$nextTick(() => {
          setTimeout(() => {
            this.form.setFieldsValue(this.detail)
          }, 50);
        })
      }
    },
    dcpgyjs: {
      handler() {
        console.log(this.detail)
        this.$nextTick(() => {
          setTimeout(() => {
            this.form.setFieldsValue(this.detail)
          }, 50);
        })
      }
    }
  },
  methods: {
    moment,
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    ok(data) {
      this.zmlist = data
    },
    ok1(data) {
      this.dcrList = data
    },
    handleRemove(file) {
      const index = this.wtdcsfileList.indexOf(file)
      const newFileList = this.wtdcsfileList.slice()
      newFileList.splice(index, 1)
      this.wtdcsfileList = newFileList
    },
    beforeUpload(file) {
      this.wtdcsfileList = [...this.wtdcsfileList, file]
      return false
    },
    sureUpload() {
      this.wtdcs = []
      const list = []
      console.log(this.wtdcsfileList, '当前所有文件集合')
      if (this.wtdcsfileList != null) {
        this.wtdcsfileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('ext', 1)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.wtdcs = [...this.wtdcs, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.wtdcsfileList = []
              })
            })
          }
          list.push(p1)
        })
      }
      return Promise.all(list).then(res => {
        this.uploadStatus = true
      })
    },
    handleRemove2(file) {
      const index = this.blfileList.indexOf(file)
      const newFileList = this.blfileList.slice()
      newFileList.splice(index, 1)
      this.blfileList = newFileList
    },
    beforeUpload2(file) {
      this.blfileList = [...this.blfileList, file]
      return false
    },
    sureUpload2() {
      this.bl = []
      const list = []
      console.log(this.blfileList, '当前所有文件集合')
      if (this.blfileList != null) {
        this.blfileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('ext', 1)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.bl = [...this.bl, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.blfileList = []
              })
            })
          }
          list.push(p1)
        })
      }
      return Promise.all(list).then(res => {
        this.uploadStatus = true
      })
    },
    handleRemove3(file) {
      const index = this.wtfileList.indexOf(file)
      const newFileList = this.wtfileList.slice()
      newFileList.splice(index, 1)
      this.wtfileList = newFileList
    },
    beforeUpload3(file) {
      this.wtfileList = [...this.wtfileList, file]
      return false
    },
    sureUpload3() {
      this.wt = []
      const list = []
      console.log(this.wtfileList, '当前所有文件集合')
      if (this.wtfileList != null) {
        this.wtfileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('ext', 1)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.wt = [...this.wt, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.wtfileList = []
              })
            })
          }
          list.push(p1)
        })
      }
      return Promise.all(list).then(res => {
        this.uploadStatus = true
      })
    },
    handleRemove1(file) {
      const index = this.wtwsfjfileList.indexOf(file)
      const newFileList = this.wtwsfjfileList.slice()
      newFileList.splice(index, 1)
      this.wtwsfjfileList = newFileList
    },
    beforeUpload1(file) {
      this.wtwsfjfileList = [...this.wtwsfjfileList, file]
      return false
    },
    sureUpload1() {
      this.wtwsfj = []
      const list = []
      console.log(this.wtwsfjfileList, '当前所有文件集合')
      if (this.wtwsfjfileList != null) {
        this.wtwsfjfileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('ext', 1)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.wtwsfj = [...this.wtwsfj, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.wtwsfjfileList = []
              })
            })
          }
          list.push(p1)
        })
      }
      return Promise.all(list).then(() => {
        this.uploadStatus = true
      })
    },
    changeChoseCityjzd: function (e) { // 后执行
      this.jzdareaCode.province = e.province.value
      this.jzdareaCode.city = e.city.value
      this.jzdareaCode.area = e.area.value
    },
    changeChoseCityhj: function (e) { // 后执行
      this.hjareaCode.province = e.province.value
      this.hjareaCode.city = e.city.value
      this.hjareaCode.area = e.area.value
    },
    getdata() {
      sysDictTypeDropDown({ code: 'dcpgyj' }).then(res => {
        res.data.forEach(p => {
          this.dcpgyjDropDown.push({ value: p.code, label: p.value })
        })
      })

      sysDictTypeDropDown({ code: 'xglx' }).then(res => {
        res.data.forEach(p => {
          this.xglxDropDown.push({ value: p.code, label: p.value })
        })
      })

      sysDictTypeDropDown({ code: 'zy' }).then(res => {
        res.data.forEach(p => {
          this.zyDropDown.push({ value: p.code, label: p.value })
        })
      })

      sysDictTypeDropDown({ code: 'mz' }).then(res => {
        res.data.forEach(p => {
          this.mzDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'yzfgx' }).then(res => {
        res.data.forEach(p => {
          this.yzfgxDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'nsysqjzrylx' }).then(res => {
        res.data.forEach(p => {
          this.nsysqjzrylxDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'xb' }).then(res => {
        res.data.forEach(p => {
          this.xbDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'zxzl' }).then(res => {
        res.data.forEach(p => {
          this.zxzlDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'fjx' }).then(res => {
        res.data.forEach(p => {
          this.fjxDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'sex' }).then((res) => {
        this.sexDictTypeDropDown = res.data
      })
    },
    // 初始化方法
    async add(record, orgTree) {
      this.orgTree = orgTree
      this.record = record
      console.log(record.zt)
      this.zt = record.zt
      if (record.zt.slice(0, 1) === '3') {
        this.nowzt = parseInt(record.zt.slice(0, 1)) - 1
        this.current = parseInt(record.zt.slice(0, 1)) - 1
      } else if (record.zt.slice(0, 1) === '7') {
        // this.nowzt = parseInt(record.zt.slice(0, 1)) - 2
        this.nowzt = 999
        this.current = undefined
        var that = this
        setTimeout(() => {
          that.$nextTick(() => {
            that.current = 0
          })
        }, 100);
        // this.current = parseInt(record.zt.slice(0, 1)) - 2
      } else {
        this.nowzt = parseInt(record.zt) - 1
        this.current = parseInt(record.zt) - 1
      }

      CoordinateInvestigatedetail({ id: record.id }).then((res) => {
        if (res.success) {
          console.log(res.data)
          this.wtdcsfileList = res.data.wtdcsList
          this.wtwsfjfileList = res.data.wtwsfjList
        } else {
          this.$message.error('附件加载失败')// + res.message
        }
      })

      if (parseInt(record.zt) > 3) {
        coordinateInvestigatelastP3({ id: record.id }).then(res => {
          if (res.success) {
            this.detail = res.data

            this.lastP3 = res.data.id
            console.log('3333', res)
          }
        })
      }

      await coordinateInvestigategetProcess({ id: record.id }).then(res => {
        if (res.success) {
          this.historydata = res.data

          console.log('444', res)

          //     if (record.zt.slice(0, 1) === '3') {
          //   this.nowzt = parseInt(record.zt.slice(0, 1)) - 1
          // this.current = parseInt(record.zt.slice(0, 1)) - 1
          // } else if (record.zt.slice(0, 1) === '7') {
          //   // this.nowzt = parseInt(record.zt.slice(0, 1)) - 2
          //   this.current = 1
          //   var that = this
          //    setTimeout(() => {
          //     that.current = 0
          //    }, 1);
          // // this.current = parseInt(record.zt.slice(0, 1)) - 2
          // } else {
          //   this.nowzt = parseInt(record.zt) - 1
          // this.current = parseInt(record.zt) - 1
          // }
          // this.zt = record.zt

          this.id = record.id
          this.visible = true
        }
      })
    },

    /**
     * 提交表单
     */
    handleSubmit() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.id = this.id
          values.sdwtsj = this.sdwtsjDateString
          values.csrq = this.csrqDateString
          values.ypxqksrq = this.ypxqksrqDateString
          values.ypxqjsrq = this.ypxqjsrqDateString
          values.pjrq = this.pjrqDateString
          values.zpsj = this.defaulttime
          values.zpr = this.userid

          console.log(values)

          coordinateInvestigateAdd(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit1() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.sdwtsj = this.sdwtsjDateString
          values.csrq = this.csrqDateString
          values.ypxqksrq = this.ypxqksrqDateString
          values.ypxqjsrq = this.ypxqjsrqDateString
          values.pjrq = this.pjrqDateString
          values.zpsj = this.zpsjDateString
          coordinateInvestigateAdd(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit2() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.jssj = this.defaulttime
          values.jsr = this.userid
          values.pid = this.id
          console.log(values)

          // values.sdwtsj = this.sdwtsjDateString
          // values.csrq = this.csrqDateString
          // values.ypxqksrq = this.ypxqksrqDateString
          // values.ypxqjsrq = this.ypxqjsrqDateString
          // values.pjrq = this.pjrqDateString
          // values.zpsj = this.zpsjDateString
          coordinateInvestigatep2(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit3(key) {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }

          values.dcsj = this.defaulttime
          values.dcr = this.userid
          values.pid = this.id
          if (key === 'qrdc') {
            this.qrdc = !this.qrdc
          } else if (key === 'dcgc') {
            await this.sureUpload2()
            await this.sureUpload3()
            values.id = this.lastP3
            values.wtdccl = this.wt.join(',')
            // values.wtwsfj = this.wtwsfj.join(',')
            values.dcbl = this.bl.join(',')
            values.records = [{
              dcr: JSON.stringify(this.dcrList),
              dcbl: values.dcbl,
              xm: values.xm,
              gx: values.gx,
              dcsj: values.dcsj,
              dcdd: values.dcdd,
              dcsx: values.dcsx

            }]
            values.zt = '31'

            this.dcgc = !this.dcgc
          } else if (key === 'dcpgb') {
            values.id = this.lastP3
            values.zt = '32'
            this.dcpgb = !this.dcpgb
          } else if (key === 'dcpgyjs') {
            values['p6.dcjssj'] = this.p6dcjssjDateString
            values.SDWTSJ = this.SDWTSJDateString
            values.zt = '33'
            values.id = this.lastP3
            this.dcpgyjs = !this.dcpgyjs
          }

          console.log(values)

          // values.sdwtsj = this.sdwtsjDateString
          // values.csrq = this.csrqDateString
          // values.ypxqksrq = this.ypxqksrqDateString
          // values.ypxqjsrq = this.ypxqjsrqDateString
          // values.pjrq = this.pjrqDateString
          // values.zpsj = this.zpsjDateString
          coordinateInvestigatep3(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit4() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.cssj = this.cssjDateString
          values.hysj = this.hysjDateString

          values.csr = this.userid
          values.pid = this.id
          console.log(values)

          coordinateInvestigatep4(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit5() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.cssj = this.cssjDateString
          values.pyshsj = this.pyshsjDateString

          values.csr = this.userid
          values.pid = this.id
          console.log(values)

          coordinateInvestigatep5(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit6() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.cssj = this.cssjDateString
          values.pyshsj = this.pyshsjDateString
          values.spsj = this.spsjDateString
          values.spr = this.userid
          values.pid = this.id
          console.log(values)

          coordinateInvestigatep6(values).then((res) => {
            if (res.success) {
              this.$message.success('新增成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('新增失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleEdit() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          await this.sureUpload()
          await this.sureUpload1()

          values.hjdz = this.hjareaCode.area
          values.jzddz = this.jzdareaCode.area
          values.jzddzC = this.jzdareaCode.city
          values.jzddzP = this.jzdareaCode.province
          values.hjdzC = this.hjareaCode.province
          values.hjdzP = this.hjareaCode.province
          values.zm = JSON.stringify(this.zmlist)
          values.wtdcs = this.wtdcs.join(',')
          values.wtwsfj = this.wtwsfj.join(',')
          values.id = this.id
          values.sdwtsj = this.sdwtsjDateString
          values.csrq = this.csrqDateString
          values.ypxqksrq = this.ypxqksrqDateString
          values.ypxqjsrq = this.ypxqjsrqDateString
          values.pjrq = this.pjrqDateString
          values.zpsj = this.defaulttime
          values.zpr = this.userid
          console.log(values)

          coordinateInvestigateEdit(values).then((res) => {
            if (res.success) {
              this.$message.success('编辑成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('编辑失败')// + res.message
            }
          }).finally((res) => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    onChangedcsj(date, dateString) {
      if (dateString === '') {
        this.dcsjDateString = null
      } else {
        this.dcsjDateString = dateString
      }
    },
    onChangespsj(date, dateString) {
      if (dateString === '') {
        this.spsjDateString = null
      } else {
        this.spsjDateString = dateString
      }
    },
    onChangehysj(date, dateString) {
      if (dateString === '') {
        this.hysjDateString = null
      } else {
        this.hysjDateString = dateString
      }
    },
    onChangepyshsj(date, dateString) {
      if (dateString === '') {
        this.pyshsjDateString = null
      } else {
        this.pyshsjDateString = dateString
      }
    },
    onChangecssj(date, dateString) {
      if (dateString === '') {
        this.cssjDateString = null
      } else {
        this.cssjDateString = dateString
      }
    },
    onChangesdwtsj(date, dateString) {
      if (dateString === '') {
        this.sdwtsjDateString = null
      } else {
        this.sdwtsjDateString = dateString
      }
    },
    onChangecsrq(date, dateString) {
      if (dateString === '') {
        this.csrqDateString = null
      } else {
        this.csrqDateString = dateString
      }
    },
    onChangeypxqksrq(date, dateString) {
      if (dateString === '') {
        this.ypxqksrqDateString = null
      } else {
        this.ypxqksrqDateString = dateString
      }
    },
    onChangeypxqjsrq(date, dateString) {
      if (dateString === '') {
        this.ypxqjsrqDateString = null
      } else {
        this.ypxqjsrqDateString = dateString
      }
    },
    onChangepjrq(date, dateString) {
      if (dateString === '') {
        this.pjrqDateString = null
      } else {
        this.pjrqDateString = dateString
      }
    },
    onChangezpsj(date, dateString) {
      if (dateString === '') {
        this.zpsjDateString = null
      } else {
        this.zpsjDateString = dateString
      }
    },
    onChangeSDWTSJ(date, dateString) {
      if (dateString === '') {
        this.SDWTSJDateString = null
      } else {
        this.SDWTSJDateString = dateString
      }
    },
    onChangep6dcjssj(date, dateString) {
      if (dateString === '') {
        this.p6dcjssjDateString = null
      } else {
        this.p6dcjssjDateString = dateString
      }
    },
    handleCancel() {
      this.form.resetFields()
      this.jzdareaCode = { province: '', city: '', area: '' }
      this.hjareaCode = { province: '', city: '', area: '' }
      this.blfileList = []
      this.wtfileList = []
      this.zmlist = []
      this.dcrList = []
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
.title{
  font-size:16px;
  margin:5px 0;
  // position: relative;top:97px;left:60px
}

.dc{
   display:flex;
   justify-content: space-around;
  }
// .border{
//     border-style: solid;border-width: 1px;
//         border-color: rgba(204, 204, 204, 1);
// }
.box{

  // width:150px;
  height:100px;
  margin:20px 50px;
  // background-color: red;
}
.wsinfo{
  z-index:auto;
  position:relative;
  left:90px;
  top:70px;
  background: white;
  width:70px;
  padding-left:5px
}
</style>

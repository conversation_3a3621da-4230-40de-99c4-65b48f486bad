<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.zpdwId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-show="bsflag===0" v-model="queryParam.zt" placeholder="请选择状态" >
                  <a-select-option v-for="(item,index) in ztDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
                <a-select v-show="bsflag===1" v-model="queryParam.xtzt" placeholder="请选择状态" >
                  <a-select-option v-for="(item,index) in xtztDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="10" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  :show-time="{
                    hideDisabledOptions: true,
                    defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
                  }"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="handleSearch" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <div style="margin:10px">
      <a-button v-if="bsflag" @click="bsflag=0">外省委托协查</a-button>
      <a-button type="primary" v-if="!bsflag" >外省委托协查</a-button>
      <a-button v-if="!bsflag" @click="bsflag=1">本省发起委托</a-button>
      <a-button type="primary" v-if="bsflag" @click="bsflag=1">本省发起委托</a-button>
    </div>
    <a-card :bordered="false">
      <div v-show="bsflag===0">
        <!--外省委托协查-->
        <s-table
          ref="table"
          :columns="columnsInvAccept"
          :data="loadDataInvAccept"
          :alert="false"
          :rowKey="(record) => record.id"
          :rowSelection="{ type: 'radio' ,selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
          <template class="table-operator" slot="operator" >
            <a-button type="primary" :disabled="disabledAccept" icon="plus" @click="$refs.acceptForm.add(selectedRows[0])">受理</a-button>
            <a-button type="primary" :disabled="disabledInvestigate" icon="plus" @click="$refs.addForm.add(selectedRows[0],orgTree)">跨省评估协查</a-button>
            <a-button type="primary" :disabled="disabledFeedBack" icon="plus" @click="$refs.fkxcResult.edit(selectedRows[0])">协查意见反馈</a-button>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="$refs.detailForm.add(record)">详情</a>
          </span>
          <span slot="timeFormat" slot-scope="text">
            {{ text === null ? '' : moment(text).format('YYYY-MM-DD HH:mm') }}
          </span>
          <span slot="zt" slot-scope="text">
            {{ 'dcpgzt' | dictType(text) }}
          </span>
        </s-table>
        <add-form ref="addForm" @ok="handleOk" />
        <edit-form ref="editForm" @ok="handleOk" />
        <accept-form ref="acceptForm" @ok="handleOk"/>
      </div>
      <div v-show="bsflag===1">
        <!--本省发起委托-->
        <s-table
          ref="table1"
          :columns="columnsInvSend"
          :data="loadDataInvSend"
          :alert="false"
          :rowKey="(record) => record.id"
          :rowSelection="{ type: 'radio' ,selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template class="table-operator" slot="operator" >
            <a-button type="primary" icon="plus" @click="$refs.kswtAddForm.add()">发起跨省委托</a-button>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="$refs.kswtDetailForm.open(record)">详情</a>
          </span>
          <span slot="sfj" slot-scope="text">
            {{ 'sfj' | dictType(text) }}
          </span>
          <span slot="xtzt" slot-scope="text">
            {{ 'xtzt' | dictType(text) }}
          </span>
          <span slot="timeFormat" slot-scope="text">
            {{ text === null ? '' : moment(text).format('YYYY-MM-DD HH:mm') }}
          </span>
        </s-table>
        <detailForm ref="detailForm" @ok="handleOk"/>
        <add-form ref="addForm" @ok="handleOk" />
        <edit-form ref="editForm" @ok="handleOk" />
        <FkxcResult ref="fkxcResult" @ok="handleOk" />
        <kswtAddForm ref="kswtAddForm" @ok="handleOk" />
        <kswtDetailForm ref="kswtDetailForm" @ok="handleOk" />
      </div>
    </a-card>
  </div>
</template>
<script>

  import { STable } from '@/components'
  import moment from 'moment'
  import {
    coordinateInvestigatePage,
    coordinateInvestigateSendPage
  } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
  import addForm from './addForm.vue'
  import detailForm from './detailForm.vue'
  import editForm from './editForm.vue'
  import FkxcResult from './fkxcResult.vue'
  import kswtAddForm from './kswtAddForm.vue'
  import kswtDetailForm from './kswtDetailForm.vue'
  import acceptForm from './acceptForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      detailForm,
      FkxcResult,
      kswtAddForm,
      kswtDetailForm,
      STable,
      addForm,
      editForm,
      acceptForm
    },
    data () {
      return {
        onChange: current => {
          console.log(current)
        },
        bsflag: 0,
        // 查询参数
        queryParam: {},
        // 表头
        columnsInvAccept: [
         { ellipsis: true, title: '协同状态', align: 'center', dataIndex: 'zt', scopedSlots: { customRender: 'zt' } },
         { ellipsis: true, title: '调查期限', align: 'center', dataIndex: 'dcqx' },
         { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
         { ellipsis: true, title: '省(市)', align: 'center', dataIndex: 'jzddzP' },
         { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'wtdwmc' },
         { ellipsis: true, title: '协查意见', align: 'center', dataIndex: 'zpbz' },
         { ellipsis: true, title: '申请时间', align: 'center', dataIndex: 'sqsj', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '反馈时间', align: 'center', dataIndex: 'fksj', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        columnsInvSend: [
         { ellipsis: true, title: '协同状态', align: 'center', dataIndex: 'xtzt', scopedSlots: { customRender: 'xtzt' } },
         { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
         { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jzdw' },
         { ellipsis: true, title: '委托省(市)', align: 'center', dataIndex: 'wtxcdw', scopedSlots: { customRender: 'sfj' } },
         { ellipsis: true, title: '协查意见', align: 'center', dataIndex: 'xcyj' },
         { ellipsis: true, title: '受理结果', align: 'center', dataIndex: 'sljg' },
         { ellipsis: true, title: '申请时间', align: 'center', dataIndex: 'sqsj', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '操作', width: '150px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadDataInvAccept: parameter => {
          return coordinateInvestigatePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        loadDataInvSend: parameter => {
          return coordinateInvestigateSendPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        ztDropDown: [],
        xtztDropDown: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      this.sysDictTypeDropDown()
    },
    watch: {
      bsflag: {
        handler() {
          this.selectedRows = []
          this.selectedRowKeys = []
          this.$refs.table.clearSelected()
          this.$refs.table1.clearSelected()
          this.$refs.table.refresh()
          this.$refs.table1.refresh()
        }
      }
    },
    computed: {
      disabledAccept () {
        return (!this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].zt !== '0');
      },
      disabledInvestigate () {
        return (!this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].zt === '0');
      },
      disabledFeedBack () {
        return (!this.selectedRows.length || this.selectedRows.length !== 1 || this.selectedRows[0].zt !== '7');
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD HH:mm:ss')
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD HH:mm:ss')
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      handleSearch () {
        if (this.bsflag === 0) {
          this.$refs.table.refresh(true)
        }
        if (this.bsflag === 1) {
          this.$refs.table1.refresh(true)
        }
      },
      handleOk () {
        this.selectedRows = []
        this.selectedRowKeys = []
        this.$refs.table.clearSelected()
        this.$refs.table1.clearSelected()
        this.$refs.table.refresh()
        this.$refs.table1.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        console.log(this.selectedRows)
      },
      sysDictTypeDropDown () {
        this.ztDropDown = this.$options.filters['dictData']('zt')
        this.xtztDropDown = this.$options.filters['dictData']('xtzt')
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style scoped>
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }

</style>

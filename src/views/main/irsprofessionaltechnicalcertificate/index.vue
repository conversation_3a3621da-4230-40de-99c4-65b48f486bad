<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('irsProfessionalTechnicalCertificate:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="报考专业名称">
                <a-input v-model="queryParam.bkzymc" allow-clear placeholder="请输入报考专业名称"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照编码">
                  <a-input v-model="queryParam.elcLicenceCode" allow-clear placeholder="请输入电子证照编码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照来源部门">
                  <a-input v-model="queryParam.elcLicenceDept" allow-clear placeholder="请输入电子证照来源部门"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照文件地址">
                  <a-input v-model="queryParam.elcLicenceFileUrl" allow-clear placeholder="请输入电子证照文件地址"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照名称">
                  <a-input v-model="queryParam.elcLicenceName" allow-clear placeholder="请输入电子证照名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="电子证照结构化数据">
                  <a-input v-model="queryParam.elcLicenceStruct" allow-clear placeholder="请输入电子证照结构化数据"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="发证单位">
                  <a-input v-model="queryParam.fzdw" allow-clear placeholder="请输入发证单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="合格年份">
                  <a-input v-model="queryParam.hgnf" allow-clear placeholder="请输入合格年份"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="考试科目">
                  <a-input v-model="queryParam.kskm" allow-clear placeholder="请输入考试科目"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="考试时间，yyyy年MM月dd日">
                  <a-input v-model="queryParam.kssj" allow-clear placeholder="请输入考试时间，yyyy年MM月dd日"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="考生姓名">
                  <a-input v-model="queryParam.ksxm1" allow-clear placeholder="请输入考生姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="签发时间，yyyy年MM月dd日">
                  <a-input v-model="queryParam.qfsj" allow-clear placeholder="请输入签发时间，yyyy年MM月dd日"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态的使用规则以数源部门解释为准">
                  <a-input
                    v-model="queryParam.status"
                    allow-clear
                    placeholder="请输入状态的使用规则以数源部门解释为准"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效年度数">
                  <a-input v-model="queryParam.yxnds" allow-clear placeholder="请输入有效年度数"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期截止">
                  <a-input v-model="queryParam.yxqjz" allow-clear placeholder="请输入有效期截止"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期起始">
                  <a-input v-model="queryParam.yxqqs" allow-clear placeholder="请输入有效期起始"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="有效期起始年度">
                  <a-input v-model="queryParam.yxqqsnd" allow-clear placeholder="请输入有效期起始年度"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件号码">
                  <a-input v-model="queryParam.zjhm" allow-clear placeholder="请输入证件号码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件类别">
                  <a-input v-model="queryParam.zjlb" allow-clear placeholder="请输入证件类别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="照片路径">
                  <a-input v-model="queryParam.zplj" allow-clear placeholder="请输入照片路径"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书查询">
                  <a-input v-model="queryParam.zscx" allow-clear placeholder="请输入证书查询"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证书管理号">
                  <a-input v-model="queryParam.zsglh" allow-clear placeholder="请输入证书管理号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证照名称">
                  <a-input v-model="queryParam.zzname" allow-clear placeholder="请输入证照名称"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.zzbh"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('irsProfessionalTechnicalCertificate:add')">
          <a-button
            type="primary"
            v-if="hasPerm('irsProfessionalTechnicalCertificate:add')"
            icon="plus"
            @click="$refs.addForm.add()">新增省人社厅专业技术人员资格证书
          </a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('irsProfessionalTechnicalCertificate:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider
            type="vertical"
            v-if="hasPerm('irsProfessionalTechnicalCertificate:edit') & hasPerm('irsProfessionalTechnicalCertificate:delete')"/>
          <a-popconfirm
            v-if="hasPerm('irsProfessionalTechnicalCertificate:delete')"
            placement="topRight"
            title="确认删除？"
            @confirm="() => irsProfessionalTechnicalCertificateDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import {
  irsProfessionalTechnicalCertificatePage,
  irsProfessionalTechnicalCertificateDelete
} from '@/api/modular/main/irsprofessionaltechnicalcertificate/irsProfessionalTechnicalCertificateManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '报考级别名称',
          align: 'center',
          dataIndex: 'bkjbmc'
        },
        {
          ellipsis: true,
          title: '报考专业名称',
          align: 'center',
          dataIndex: 'bkzymc'
        },
        {
          ellipsis: true,
          title: '证照数据',
          align: 'center',
          dataIndex: 'data'
        },
        {
          ellipsis: true,
          title: '电子证照编码',
          align: 'center',
          dataIndex: 'elcLicenceCode'
        },
        {
          ellipsis: true,
          title: '电子证照来源部门',
          align: 'center',
          dataIndex: 'elcLicenceDept'
        },
        {
          ellipsis: true,
          title: '电子证照文件数据',
          align: 'center',
          dataIndex: 'elcLicenceFile'
        },
        {
          ellipsis: true,
          title: '电子证照文件地址',
          align: 'center',
          dataIndex: 'elcLicenceFileUrl'
        },
        {
          ellipsis: true,
          title: '电子证照名称',
          align: 'center',
          dataIndex: 'elcLicenceName'
        },
        {
          ellipsis: true,
          title: '电子证照结构化数据',
          align: 'center',
          dataIndex: 'elcLicenceStruct'
        },
        {
          ellipsis: true,
          title: '发证单位',
          align: 'center',
          dataIndex: 'fzdw'
        },
        {
          ellipsis: true,
          title: '合格年份',
          align: 'center',
          dataIndex: 'hgnf'
        },
        {
          ellipsis: true,
          title: '考试科目',
          align: 'center',
          dataIndex: 'kskm'
        },
        {
          ellipsis: true,
          title: '考试时间，yyyy年MM月dd日',
          align: 'center',
          dataIndex: 'kssj'
        },
        {
          ellipsis: true,
          title: '考生姓名',
          align: 'center',
          dataIndex: 'ksxm1'
        },
        {
          ellipsis: true,
          title: '签发时间，yyyy年MM月dd日',
          align: 'center',
          dataIndex: 'qfsj'
        },
        {
          ellipsis: true,
          title: '签名证书',
          align: 'center',
          dataIndex: 'signCert'
        },
        {
          ellipsis: true,
          title: '签名值',
          align: 'center',
          dataIndex: 'signValue'
        },
        {
          ellipsis: true,
          title: '状态的使用规则以数源部门解释为准',
          align: 'center',
          dataIndex: 'status'
        },
        {
          ellipsis: true,
          title: '时间戳签名值',
          align: 'center',
          dataIndex: 'tsa'
        },
        {
          ellipsis: true,
          title: '有效年度数',
          align: 'center',
          dataIndex: 'yxnds'
        },
        {
          ellipsis: true,
          title: '有效期截止',
          align: 'center',
          dataIndex: 'yxqjz'
        },
        {
          ellipsis: true,
          title: '有效期起始',
          align: 'center',
          dataIndex: 'yxqqs'
        },
        {
          ellipsis: true,
          title: '有效期起始年度',
          align: 'center',
          dataIndex: 'yxqqsnd'
        },
        {
          ellipsis: true,
          title: '证件号码',
          align: 'center',
          dataIndex: 'zjhm'
        },
        {
          ellipsis: true,
          title: '证件类别',
          align: 'center',
          dataIndex: 'zjlb'
        },
        {
          ellipsis: true,
          title: '照片路径',
          align: 'center',
          dataIndex: 'zplj'
        },
        {
          ellipsis: true,
          title: '证书查询',
          align: 'center',
          dataIndex: 'zscx'
        },
        {
          ellipsis: true,
          title: '证书管理号',
          align: 'center',
          dataIndex: 'zsglh'
        },
        {
          ellipsis: true,
          title: '证照名称',
          align: 'center',
          dataIndex: 'zzname'
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return irsProfessionalTechnicalCertificatePage(Object.assign(parameter, this.queryParam)).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
    if (this.hasPerm('irsProfessionalTechnicalCertificate:edit') || this.hasPerm('irsProfessionalTechnicalCertificate:delete')) {
      this.columns.push({
        title: '操作',
        width: '150px',
        dataIndex: 'action',
        scopedSlots: { customRender: 'action' }
      })
    }
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    irsProfessionalTechnicalCertificateDelete(record) {
      irsProfessionalTechnicalCertificateDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

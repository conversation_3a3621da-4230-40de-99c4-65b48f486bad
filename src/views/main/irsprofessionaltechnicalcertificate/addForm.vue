<template>
  <a-modal
    title="新增省人社厅专业技术人员资格证书
"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="报考级别名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入报考级别名称" v-decorator="['bkjbmc', {rules: [{required: true, message: '请输入报考级别名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="报考专业名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入报考专业名称" v-decorator="['bkzymc', {rules: [{required: true, message: '请输入报考专业名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证照数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="电子证照编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照编码" v-decorator="['elcLicenceCode', {rules: [{required: true, message: '请输入电子证照编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照来源部门"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照来源部门" v-decorator="['elcLicenceDept', {rules: [{required: true, message: '请输入电子证照来源部门！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照文件数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="电子证照文件地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照文件地址" v-decorator="['elcLicenceFileUrl', {rules: [{required: true, message: '请输入电子证照文件地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照名称" v-decorator="['elcLicenceName', {rules: [{required: true, message: '请输入电子证照名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="电子证照结构化数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入电子证照结构化数据" v-decorator="['elcLicenceStruct', {rules: [{required: true, message: '请输入电子证照结构化数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证单位" v-decorator="['fzdw', {rules: [{required: true, message: '请输入发证单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="合格年份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入合格年份" v-decorator="['hgnf', {rules: [{required: true, message: '请输入合格年份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="考试科目"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入考试科目" v-decorator="['kskm', {rules: [{required: true, message: '请输入考试科目！'}]}]" />
        </a-form-item>
        <a-form-item
          label="考试时间，yyyy年MM月dd日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入考试时间，yyyy年MM月dd日" v-decorator="['kssj', {rules: [{required: true, message: '请输入考试时间，yyyy年MM月dd日！'}]}]" />
        </a-form-item>
        <a-form-item
          label="考生姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入考生姓名" v-decorator="['ksxm1', {rules: [{required: true, message: '请输入考生姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签发时间，yyyy年MM月dd日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入签发时间，yyyy年MM月dd日" v-decorator="['qfsj', {rules: [{required: true, message: '请输入签发时间，yyyy年MM月dd日！'}]}]" />
        </a-form-item>
        <a-form-item
          label="签名证书"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="状态的使用规则以数源部门解释为准"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态的使用规则以数源部门解释为准" v-decorator="['status', {rules: [{required: true, message: '请输入状态的使用规则以数源部门解释为准！'}]}]" />
        </a-form-item>
        <a-form-item
          label="时间戳签名值"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="有效年度数"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效年度数" v-decorator="['yxnds', {rules: [{required: true, message: '请输入有效年度数！'}]}]" />
        </a-form-item>
        <a-form-item
          label="有效期截止"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效期截止" v-decorator="['yxqjz', {rules: [{required: true, message: '请输入有效期截止！'}]}]" />
        </a-form-item>
        <a-form-item
          label="有效期起始"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效期起始" v-decorator="['yxqqs', {rules: [{required: true, message: '请输入有效期起始！'}]}]" />
        </a-form-item>
        <a-form-item
          label="有效期起始年度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入有效期起始年度" v-decorator="['yxqqsnd', {rules: [{required: true, message: '请输入有效期起始年度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类别" v-decorator="['zjlb', {rules: [{required: true, message: '请输入证件类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="照片路径"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入照片路径" v-decorator="['zplj', {rules: [{required: true, message: '请输入照片路径！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书查询"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书查询" v-decorator="['zscx', {rules: [{required: true, message: '请输入证书查询！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书管理号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书管理号" v-decorator="['zsglh', {rules: [{required: true, message: '请输入证书管理号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证照名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证照名称" v-decorator="['zzname', {rules: [{required: true, message: '请输入证照名称！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsProfessionalTechnicalCertificateAdd } from '@/api/modular/main/irsprofessionaltechnicalcertificate/irsProfessionalTechnicalCertificateManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsProfessionalTechnicalCertificateAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

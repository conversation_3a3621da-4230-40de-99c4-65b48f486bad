<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="所属单位">
                <a-tree-select
                  v-model="queryParam.deptId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择所属单位">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="设备品牌">
                <a-select
                  placeholder="请选择设备品牌"
                  v-model="queryParam.deviceBrand">
                  <a-select-option v-for="(item,index) in deviceBrandDropDown" :key="index" :value="item.code">{{
                      item.name
                    }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="设备类型">
                <a-select
                  placeholder="请选择设备类型"
                  v-model="queryParam.deviceType">
                  <a-select-option value="flat">平板</a-select-option>
                  <a-select-option value="phone">手机</a-select-option>
                  <a-select-option value="other">其他</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="绑定状态">
                <a-select
                  placeholder="请选择绑定状态"
                  v-model="queryParam.isBand">
                  <a-select-option value="1">已绑定</a-select-option>
                  <a-select-option value="0">未绑定</a-select-option>
                  <a-select-option value="">全部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="设备编号">
                <a-input v-model="queryParam.deviceNumber" allow-clear placeholder="请输入设备编号"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button
                  style="margin-left: 8px"
                  @click="
                    queryParam = {}
                    $refs.table.refresh(true)
                  "
                >重置</a-button
                >
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.addForm.add(orgTree)">新增</a-button>
          <a-popconfirm
            :disabled="!selectedRowKeys.length"
            placement="topRight"
            title="确认删除？"
            @confirm="() => investigationDeviceDelete()"
          >
            <a-button :disabled="!selectedRowKeys.length" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
          <x-down ref="batchExport" @batchExport="batchExport"/>
        </template>

        <span slot="deviceType" slot-scope="text, record">
          <span v-if="record.deviceType==='flat'">平板</span>
          <span v-else-if="record.deviceType==='phone'">手机</span>
           <span v-else>其他</span>
        </span>
        <span slot="status" slot-scope="text, record">
         <span v-if="record.status==='0'">启用</span>
           <span v-else>禁用</span>
        </span>
        <span slot="deviceBrand" slot-scope="text">
          {{ 'deviceBrand' | dictType(text) }}
        </span>

        <span slot="action" slot-scope="text, record">
          <div v-if="record.deptId">
               <a-popconfirm v-if="record.status==='0'" placement="topRight" title="确认禁用？"
                             @confirm="() => investigationDeviceEdit(record,1)">
            <a>禁用</a>
          </a-popconfirm>
            <a-popconfirm v-else placement="topRight" title="确认启用？" @confirm="() => investigationDeviceEdit(record,0)">
            <a>启用</a>
          </a-popconfirm>
          <a-divider type="vertical"/>
          <a-popconfirm placement="topRight" title="确认重置密码？" @confirm="() => resetPassword(record)">
            <a>重置密码</a>
          </a-popconfirm>
             <a-divider type="vertical"/>
          <a-popconfirm placement="topRight" title="确认解绑？" @confirm="() => unbind(record)">
            <a>解绑</a>
          </a-popconfirm>
          </div>
       <a-button v-else type="primary" icon="plus" @click="$refs.editForm.edit(record,orgTree)">绑定单位</a-button>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>

</template>
<script>
import {STable, XDown, Ellipsis} from '@/components'
import {
  investigationDevicePage,
  investigationDeviceEdit,
  investigationDeviceDelete, investigationDeviceExport, unbind
} from '@/api/modular/main/investigationdevice/investigationdeviceManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import {getOrgTree} from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm,
    Ellipsis,
    XDown
  },
  data() {
    return {
      // 查询参数
      queryParam: {
      },
      // 表头
      columns: [
        {
          title: '序号',
          align: 'center',
          dataIndex: '',
          width: '70px',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '所属单位',
          align: 'center',
          dataIndex: 'deptName',
          width: '12%',
          scopedSlots: {customRender: 'deptName'}
        },
        {
          title: '设备名称',
          align: 'center',
          dataIndex: 'deviceName',
          width: '12%',
          scopedSlots: {customRender: 'deviceName'}
        },
        {
          title: '设备品牌',
          align: 'center',
          dataIndex: 'deviceBrand',
          width: '12%',
          scopedSlots: {customRender: 'deviceBrand'}
        },
        {
          title: '设备类型',
          align: 'center',
          width: '12%',
          dataIndex: 'deviceType',
          scopedSlots: {customRender: 'deviceType'}
        },
        {
          title: '设备型号',
          align: 'center',
          width: '12%',
          dataIndex: 'deviceModel'
        },
        {
          title: '设备编号',
          align: 'center',
          width: '12%',
          dataIndex: 'deviceNumber'
        },
        {
          title: '状态',
          align: 'center',
          width: '12%',
          dataIndex: 'status',
          scopedSlots: {customRender: 'status'}
        },
        {
          title: '备注',
          align: 'center',
          width: '12%',
          dataIndex: 'remark'
        },
        {
          title: '操作',
          align: 'center',
          dataIndex: 'action',
          width: '180px',
          scopedSlots: {customRender: 'action'}
        }
      ],
      tstyle: {'padding-bottom': '0px', 'margin-bottom': '10px'},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return investigationDevicePage(Object.assign(parameter, this.queryParam)).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      orgTree: [],
      deviceBrandDropDown: []
    }
  },
  created() {
    this.getOrgTree()
    const options = this.$options
    this.deviceBrandDropDown = options.filters['dictData']('deviceBrand')
  },
  methods: {

    investigationDeviceDelete() {
      investigationDeviceDelete({'idList': this.selectedRowKeys})
        .then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            this.selectedRowKeys = []
            this.selectedRows = []
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
        .finally(res => {
        })
    },

    investigationDeviceEdit(record, status) {
      record.status = status
      investigationDeviceEdit(record)
        .then(res => {
          if (res.success) {
            this.$message.success(status === 0 ? '已启用' : '已禁用')
            this.$refs.table.refresh()
          } else {
            this.$message.error('操作失败') // + res.message
          }
        })
        .finally(res => {
        })
    },

    resetPassword(record) {
      record.password = 'admin123456';
      investigationDeviceEdit(record)
        .then(res => {
          if (res.success) {
            this.$message.success('重置成功！')
            this.$refs.table.refresh()
          } else {
            this.$message.error('操作失败') // + res.message
          }
        })
        .finally(res => {
        })
    },

    unbind(record) {
      unbind(record)
        .then(res => {
          if (res.success) {
            this.$message.success('解绑成功！')
            this.$refs.table.refresh()
          } else {
            this.$message.error('操作失败') // + res.message
          }
        })
        .finally(res => {
        })
    },
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    batchExport() {
      investigationDeviceExport(this.queryParam).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

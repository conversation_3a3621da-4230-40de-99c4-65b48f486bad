<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-item v-show="false">
        <a-input v-decorator="['id']"/>
      </a-form-item>
      <a-form :form="form">
        <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            style="width: 100%"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="orgTree"
            placeholder="请选择所属单位"
            v-decorator="['deptId', { rules: [{ required: true, message: '请选择所属单位！' }] }]"
          >
          </a-tree-select>
        </a-form-item>
        <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入设备名称"
            v-decorator="['deviceName', { rules: [{ required: true, message: '请输入设备名称！' }] }]"
          />
        </a-form-item>
        <a-form-item
          label="设备品牌"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          class="indicator-options"
        >
          <a-select v-decorator="['deviceBrand']">
            <a-select-option v-for="(item,index) in deviceBrandDropDown" :key="index" :value="item.code">{{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="设备类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择设备类型" v-decorator="['deviceType',{initialValue: 'flat'}]">
            <a-select-option value="flat">平板</a-select-option>
            <a-select-option value="phone">手机</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备型号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入设备型号"
            v-decorator="['deviceModel', { rules: [{ required: false, message: '请输入设备型号！' }] }]"
          />
        </a-form-item>
        <a-form-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入设备编号"
            v-decorator="['deviceNumber', { rules: [{ required: true, message: '请输入设备编号！'}] }]"
          />
        </a-form-item>
        <a-form-item label="设备密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入设备密码"
            v-decorator="['password', { rules: [{ required: true, message: '请输入设备密码！'}],initialValue: 'admin123456' }]"
          />
        </a-form-item>
        <a-form-item label="设备状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择设备状态" v-decorator="['status',{initialValue: '0'}]">
            <a-select-option value="0">启用</a-select-option>
            <a-select-option value="1">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item label="总分" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入总分"
            v-decorator="['topicScore', { rules: [{ required: true, message: '请输入总分！' }] }]"
          />
        </a-form-item> -->
        <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入备注"
            v-decorator="['remark', { rules: [{ required: false, message: '请输入备注！' }] }]"
          />
        </a-form-item>

        <!-- <a-form-item label="删除状态， 0：未删除  1：已删除" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入删除状态， 0：未删除  1：已删除"
            v-decorator="[
              'delFlag',
              { rules: [{ required: true, message: '请输入删除状态， 0：未删除  1：已删除！' }] }
            ]"
          />
        </a-form-item> -->
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  investigationDeviceAdd,
  investigationDeviceEdit,
} from '@/api/modular/main/investigationdevice/investigationdeviceManage'
import {getOrgList} from "@/api/modular/system/orgManage";

export default {
  data() {
    return {
      labelCol: {
        xs: {span: 24},
        sm: {span: 5}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 15}
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      indicatorOptions: [
        {
          content: '',
          itemScore: ''
        }
      ],
      title: '新增设备',
      id: '',
      orgTree: [],
      deviceBrandDropDown: [],
      orgList: []
    }
  },
  created() {
    const options = this.$options
    this.deviceBrandDropDown = options.filters['dictData']('deviceBrand')
    getOrgList().then((res) => {
      this.orgList = res.data
    })
  },
  methods: {
    // 初始化方法
    add(orgTree) {
      this.visible = true
      this.title = '新增设备'
      this.id = ''
      this.orgTree = orgTree
    },
    edit(record) {
      this.visible = true
      this.title = '新增设备'
      this.id = record.id
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      const {
        form: {validateFields}
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof values[key] === 'object' && values[key]) {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.deptName = this.orgList.find(item => values.deptId === item.id).name
          if (this.id === '') {
            investigationDeviceAdd(values)
              .then(res => {
                if (res.success) {
                  this.$message.success('新增成功')
                  this.confirmLoading = false
                  this.handleCancel()
                } else {
                  this.$message.error(res.message) // + res.message
                }
              })
              .finally(res => {
                this.confirmLoading = false
              })
          } else {
            values.id = this.id
            investigationDeviceEdit(values)
              .then(res => {
                if (res.success) {
                  this.$message.success('编辑成功')
                  this.confirmLoading = false

                  this.handleCancel()
                } else {
                  this.$message.error('编辑失败') // + res.message
                }
              })
              .finally(res => {
              })
          }
          this.confirmLoading = false
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.$emit('ok')
      this.id = ''
      this.indicatorOptions = [
        {
          content: '',
          itemScore: ''
        }
      ]
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.indicator-options {
  display: flex;
  position: relative;
  align-items: center;

  /deep/ .ant-form-item-children {
    display: flex;
    align-items: center;

    .score-input {
      width: 20%;
      margin-left: 10px;
    }

    .add-icon {
      position: absolute;
      right: -40px;
      color: #1690ff;
      font-size: 20px;
      cursor: pointer;
      margin-right: 10px;
    }

    .delete-icon {
      position: absolute;
      right: -40px;
      color: #b1b1b1;
      font-size: 20px;
      cursor: pointer;
    }
  }
}
</style>

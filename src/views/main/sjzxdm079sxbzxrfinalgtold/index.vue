<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.bzxrmc" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.sfzhm" allow-clear placeholder="请输入身份证号码"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="被执行人性质">
                <a-select v-model="queryParam.bzxrxz" allow-clear placeholder="请选择被执行人性质">
                  <a-select-option value="自然人">
                    自然人
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.sxid"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  sjzxDm079SxbzxrFinalGtOldPage,
  sjzxDm079SxbzxrFinalGtOldDelete
} from '@/api/modular/main/sjzxdm079sxbzxrfinalgtold/sjzxDm079SxbzxrFinalGtOldManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'bzxrmc'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        // {
        //   ellipsis: true,
        //   title: '性别',
        //   align: 'center',
        //   dataIndex: 'xb'
        // },
        {
          ellipsis: true,
          title: '身份证号码',
          align: 'center',
          dataIndex: 'sfzhm'
        },
        {
          ellipsis: true,
          title: '被执行人性质',
          align: 'center',
          dataIndex: 'bzxrxz'
        },
        {
          ellipsis: true,
          title: '失信被执行人行为情况',
          align: 'center',
          dataIndex: 'sxbzxrxwqk'
        },
        // {
        //   ellipsis: true,
        //   title: '法定代表人或者负责人姓名',
        //   align: 'center',
        //   dataIndex: 'fddbr'
        // },
        {
          ellipsis: true,
          title: '执行法院',
          align: 'center',
          dataIndex: 'zxfy'
        },
        // {
        //   ellipsis: true,
        //   title: '执行依据文号',
        //   align: 'center',
        //   dataIndex: 'zxyjwh'
        // },
        // {
        //   ellipsis: true,
        //   title: '做出执行依据单位',
        //   align: 'center',
        //   dataIndex: 'zczxyjdw'
        // },
        {
          ellipsis: true,
          title: '发布时间',
          align: 'center',
          dataIndex: 'fbrq'
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          width: '50px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return sjzxDm079SxbzxrFinalGtOldPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const queryParamcsrq = this.queryParam.csrqDate
      if (queryParamcsrq != null) {
        this.queryParam.csrq = moment(queryParamcsrq).format('YYYY-MM-DD')
        if (queryParamcsrq.length < 1) {
          delete this.queryParam.csrq
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    sjzxDm079SxbzxrFinalGtOldDelete(record) {
      sjzxDm079SxbzxrFinalGtOldDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onChangecsrq(date, dateString) {
      this.csrqDateString = dateString
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

<template>
  <a-modal
    title="新增社区矫正失信被执行人信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="数据采集时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据采集时间" v-decorator="['cdcColTime', {rules: [{required: true, message: '请输入数据采集时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据同步DM结果表时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据同步DM结果表时间" v-decorator="['cdcInsTime', {rules: [{required: true, message: '请输入数据同步DM结果表时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="法院代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入法院代码" v-decorator="['fydm', {rules: [{required: true, message: '请输入法院代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="搜案年号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入搜案年号" v-decorator="['sanh', {rules: [{required: true, message: '请输入搜案年号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="搜案编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入搜案编号" v-decorator="['sabh', {rules: [{required: true, message: '请输入搜案编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="被执行人序号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入被执行人序号" v-decorator="['bzxrxh', {rules: [{required: true, message: '请输入被执行人序号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="被执行人姓名/名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入被执行人姓名/名称" v-decorator="['bzxrmc', {rules: [{required: true, message: '请输入被执行人姓名/名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号码" v-decorator="['sfzhm', {rules: [{required: true, message: '请输入身份证号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="组织机构代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入组织机构代码" v-decorator="['zzjgdm', {rules: [{required: true, message: '请输入组织机构代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="法定代表人或者负责人姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入法定代表人或者负责人姓名" v-decorator="['fddbr', {rules: [{required: true, message: '请输入法定代表人或者负责人姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行法院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行法院" v-decorator="['zxfy', {rules: [{required: true, message: '请输入执行法院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="执行依据文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入执行依据文号" v-decorator="['zxyjwh', {rules: [{required: true, message: '请输入执行依据文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="立案时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入立案时间" v-decorator="['larq', {rules: [{required: true, message: '请输入立案时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="案号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入案号" v-decorator="['ah', {rules: [{required: true, message: '请输入案号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="做出执行依据单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入做出执行依据单位" v-decorator="['zczxyjdw', {rules: [{required: true, message: '请输入做出执行依据单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="生效法律文书确定的义务"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入生效法律文书确定的义务" v-decorator="['sxflwsqdyw', {rules: [{required: true, message: '请输入生效法律文书确定的义务！'}]}]" />
        </a-form-item>
        <a-form-item
          label="被执行人的履行情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="发布时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发布时间" v-decorator="['fbrq', {rules: [{required: true, message: '请输入发布时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="失信被执行人行为情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入失信被执行人行为情况" v-decorator="['sxbzxrxwqk', {rules: [{required: true, message: '请输入失信被执行人行为情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="已履行部分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入已履行部分" v-decorator="['ylxbf', {rules: [{required: true, message: '请输入已履行部分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="未履行部分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入未履行部分" v-decorator="['wlxbf', {rules: [{required: true, message: '请输入未履行部分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="被执行人性质"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入被执行人性质" v-decorator="['bzxrxz', {rules: [{required: true, message: '请输入被执行人性质！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { sjzxDm079SxbzxrFinalGtOldAdd } from '@/api/modular/main/sjzxdm079sxbzxrfinalgtold/sjzxDm079SxbzxrFinalGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        csrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.csrq = this.csrqDateString
            sjzxDm079SxbzxrFinalGtOldAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

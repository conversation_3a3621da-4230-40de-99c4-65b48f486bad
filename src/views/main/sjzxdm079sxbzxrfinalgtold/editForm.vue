<template>
  <a-modal
    title="详情"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-card>
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">基本信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['bzxrmc', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="矫正单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jzjgName', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="状态"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zhuangtai', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['sfzhm', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">失信被执行人信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="被执行人性质"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['bzxrxz', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="失信被执行人行为情况"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea disabled v-decorator="['sxbzxrxwqk', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col><a-col :span="12">
            <a-form-item
              label="法定代表人或负责人姓名"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['fddbr', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="执行法院"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zxfy', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col><a-col :span="12">
            <a-form-item
              label="执行依据文号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zxyjwh', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="做出执行依据单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['zczxyjdw', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col><a-col :span="12">
            <a-form-item
              label="发布时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['fbrq', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="更新时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['updateTime', {rules: [{ message: ''}]}]" />
            </a-form-item>
          </a-col>
        </a-form>
      </a-card>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { sjzxDm079SxbzxrFinalGtOldEdit } from '@/api/modular/main/sjzxdm079sxbzxrfinalgtold/sjzxDm079SxbzxrFinalGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 10 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 14 }
        },
        csrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        if (record.zhuangtai === '200') {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '在矫'
              }
            )
          }, 100)
        } else {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '解矫'
              }
            )
          }, 100)
        }
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              sxid: record.sxid,
              cdcColTime: record.cdcColTime,
              cdcInsTime: record.cdcInsTime,
              fydm: record.fydm,
              sanh: record.sanh,
              sabh: record.sabh,
              bzxrxh: record.bzxrxh,
              bzxrmc: record.bzxrmc,
              sfzhm: record.sfzhm,
              zzjgdm: record.zzjgdm,
              fddbr: record.fddbr,
              zxfy: record.zxfy,
              zxyjwh: record.zxyjwh,
              larq: record.larq,
              ah: record.ah,
              zczxyjdw: record.zczxyjdw,
              sxflwsqdyw: record.sxflwsqdyw,
              bzxrlxqk: record.bzxrlxqk,
              fbrq: record.fbrq,
              sxbzxrxwqk: record.sxbzxrxwqk,
              ylxbf: record.ylxbf,
              wlxbf: record.wlxbf,
              xb: record.xb,
              bzxrxz: record.bzxrxz,
              updateTime: record.updateTime,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName
            }
          )
        }, 100)
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.csrq = this.csrqDateString
            sjzxDm079SxbzxrFinalGtOldEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

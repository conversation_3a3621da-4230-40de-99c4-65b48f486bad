<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading" style="height: 500px;">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="cus-title-d">基本信息</div>
          </a-col>
        </a-row>
        <a-col :span="12">
          <a-form-item
            label="姓名"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['psnname', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="矫正单位"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['jzjgName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="性别"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['gend', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="证件号码"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['certno', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="状态"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['zhuangtai', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="联系电话"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['mob', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="cus-title-d">医保信息</div>
          </a-col>
        </a-row>
        <a-col :span="12">
          <a-form-item
            label="险种类型名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['insutypeName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="人员参保信息"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['empName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="人员参保状态"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['psnInsuStas', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="离退休状态"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['psnType', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="户口类型"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['empForm', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="更新时间"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['updateTime', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        brdyDateString: '',
        pausinsudateDateString: '',
        psninsudateDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        if (record.zhuangtai === '200') {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '在矫'
              }
            )
          }, 100)
        } else {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '解矫'
              }
            )
          }, 100)
        }
        const psnInfo = JSON.parse(record.psninsuredinfo)
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              alis: record.alis,
              certno: record.certno,
              code: record.code,
              data: record.data,
              empfom: record.empfom,
              empname: record.empname,
              gend: record.gend,
              insuadmdvs: record.insuadmdvs,
              insutypename: record.insutypename,
              insutyperetrflag: record.insutyperetrflag,
              message: record.message,
              mob: record.mob,
              naty: record.naty,
              psncerttype: record.psncerttype,
              psninsuredinfo: record.psninsuredinfo,
              psninsustas: record.psninsustas,
              psnmgtcode: record.psnmgtcode,
              psnname: record.psnname,
              psnno: record.psnno,
              psntype: record.psntype,
              retrtype: record.retrtype,
              tel: record.tel,
              type: record.type,
              uscc: record.uscc,
              ver: record.ver,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              insutypeName: psnInfo.insutypeName,
              psnType: psnInfo.psnType,
              empName: psnInfo.empName,
              psnInsuStas: psnInfo.psnInsuStas,
              empForm: psnInfo.empForm,
              updateTime: record.updateTime
            }
          )
        }, 100)
        // 时间单独处理
        if (record.brdy != null) {
            this.form.getFieldDecorator('brdy', { initialValue: moment(record.brdy, 'YYYY-MM-DD') })
        }
        this.brdyDateString = moment(record.brdy).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pausinsudate != null) {
            this.form.getFieldDecorator('pausinsudate', { initialValue: moment(record.pausinsudate, 'YYYY-MM-DD') })
        }
        this.pausinsudateDateString = moment(record.pausinsudate).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.psninsudate != null) {
            this.form.getFieldDecorator('psninsudate', { initialValue: moment(record.psninsudate, 'YYYY-MM-DD') })
        }
        this.psninsudateDateString = moment(record.psninsudate).format('YYYY-MM-DD')
      },
      handleSubmit () {
        this.handleCancel()
      },
      onChangebrdy(date, dateString) {
        this.brdyDateString = dateString
      },
      onChangepausinsudate(date, dateString) {
        this.pausinsudateDateString = dateString
      },
      onChangepsninsudate(date, dateString) {
        this.psninsudateDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

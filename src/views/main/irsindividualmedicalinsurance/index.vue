<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.psnname" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.certno" allow-clear placeholder="请输入身份证号"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="险种类型">
                <a-input v-model="queryParam.insutypename" allow-clear placeholder="请输入险种类型"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.psnno"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  irsIndividualMedicalInsurancePage,
  irsIndividualMedicalInsuranceDelete
} from '@/api/modular/main/irsindividualmedicalinsurance/irsIndividualMedicalInsuranceManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'psnname'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '手机号码',
          align: 'center',
          dataIndex: 'mob'
        },
        // {
        //   ellipsis: true,
        //   title: '人员证件类型',
        //   align: 'center',
        //   dataIndex: 'psncerttype'
        // },
        {
          ellipsis: true,
          title: '证件号码',
          align: 'center',
          dataIndex: 'certno'
        },
        // {
        //   ellipsis: true,
        //   title: '险种类型名称',
        //   align: 'center',
        //   dataIndex: 'insutypename'
        // },
        // {
        //   ellipsis: true,
        //   title: '人员参保信息',
        //   align: 'center',
        //   dataIndex: 'psninsuredinfo'
        // },
        // {
        //   ellipsis: true,
        //   title: '人员参保状态',
        //   align: 'center',
        //   dataIndex: 'psnInsuStas'
        // },
        {
          ellipsis: true,
          title: '离退休类型',
          align: 'center',
          dataIndex: 'retrtype'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          width: '50px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return irsIndividualMedicalInsurancePage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const queryParambrdy = this.queryParam.brdyDate
      if (queryParambrdy != null) {
        this.queryParam.brdy = moment(queryParambrdy).format('YYYY-MM-DD')
        if (queryParambrdy.length < 1) {
          delete this.queryParam.brdy
        }
      }
      const queryParampausinsudate = this.queryParam.pausinsudateDate
      if (queryParampausinsudate != null) {
        this.queryParam.pausinsudate = moment(queryParampausinsudate).format('YYYY-MM-DD')
        if (queryParampausinsudate.length < 1) {
          delete this.queryParam.pausinsudate
        }
      }
      const queryParampsninsudate = this.queryParam.psninsudateDate
      if (queryParampsninsudate != null) {
        this.queryParam.psninsudate = moment(queryParampsninsudate).format('YYYY-MM-DD')
        if (queryParampsninsudate.length < 1) {
          delete this.queryParam.psninsudate
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    irsIndividualMedicalInsuranceDelete(record) {
      irsIndividualMedicalInsuranceDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onChangebrdy(date, dateString) {
      this.brdyDateString = dateString
    },
    onChangepausinsudate(date, dateString) {
      this.pausinsudateDateString = dateString
    },
    onChangepsninsudate(date, dateString) {
      this.psninsudateDateString = dateString
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

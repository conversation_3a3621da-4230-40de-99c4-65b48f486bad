<template>
  <a-modal
    title="新增智慧医保-医保个人参保信息查询
"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="别名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入别名" v-decorator="['alis', {rules: [{required: true, message: '请输入别名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['brdy',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangebrdy"/>
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['certno', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入代码" v-decorator="['code', {rules: [{required: true, message: '请输入代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据" v-decorator="['data', {rules: [{required: true, message: '请输入数据！'}]}]" />
        </a-form-item>
        <a-form-item
          label="用工形式"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入用工形式" v-decorator="['empfom', {rules: [{required: true, message: '请输入用工形式！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位名称" v-decorator="['empname', {rules: [{required: true, message: '请输入单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['gend', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="参保所属医保区划"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入参保所属医保区划" v-decorator="['insuadmdvs', {rules: [{required: true, message: '请输入参保所属医保区划！'}]}]" />
        </a-form-item>
        <a-form-item
          label="险种类型名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入险种类型名称" v-decorator="['insutypename', {rules: [{required: true, message: '请输入险种类型名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="险种离退休标志"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入险种离退休标志" v-decorator="['insutyperetrflag', {rules: [{required: true, message: '请输入险种离退休标志！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信息"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信息" v-decorator="['message', {rules: [{required: true, message: '请输入信息！'}]}]" />
        </a-form-item>
        <a-form-item
          label="手机号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入手机号码" v-decorator="['mob', {rules: [{required: true, message: '请输入手机号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民族"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民族" v-decorator="['naty', {rules: [{required: true, message: '请输入民族！'}]}]" />
        </a-form-item>
        <a-form-item
          label="暂停参保日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择暂停参保日期" v-decorator="['pausinsudate',{rules: [{ required: true, message: '请选择暂停参保日期！' }]}]" @change="onChangepausinsudate"/>
        </a-form-item>
        <a-form-item
          label="人员证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员证件类型" v-decorator="['psncerttype', {rules: [{required: true, message: '请输入人员证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人参保日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择个人参保日期" v-decorator="['psninsudate',{rules: [{ required: true, message: '请选择个人参保日期！' }]}]" @change="onChangepsninsudate"/>
        </a-form-item>
        <a-form-item
          label="人员参保信息"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员参保信息" v-decorator="['psninsuredinfo', {rules: [{required: true, message: '请输入人员参保信息！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员参保状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员参保状态" v-decorator="['psninsustas', {rules: [{required: true, message: '请输入人员参保状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员编号" v-decorator="['psnmgtcode', {rules: [{required: true, message: '请输入人员编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员姓名" v-decorator="['psnname', {rules: [{required: true, message: '请输入人员姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员类型" v-decorator="['psntype', {rules: [{required: true, message: '请输入人员类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="离退休类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入离退休类型" v-decorator="['retrtype', {rules: [{required: true, message: '请输入离退休类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="联系电话"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入联系电话" v-decorator="['tel', {rules: [{required: true, message: '请输入联系电话！'}]}]" />
        </a-form-item>
        <a-form-item
          label="类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入类型" v-decorator="['type', {rules: [{required: true, message: '请输入类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社会统一信用代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社会统一信用代码" v-decorator="['uscc', {rules: [{required: true, message: '请输入社会统一信用代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="版本号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入版本号" v-decorator="['ver', {rules: [{required: true, message: '请输入版本号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正单位id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsIndividualMedicalInsuranceAdd } from '@/api/modular/main/irsindividualmedicalinsurance/irsIndividualMedicalInsuranceManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        brdyDateString: '',
        pausinsudateDateString: '',
        psninsudateDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.brdy = this.brdyDateString
            values.pausinsudate = this.pausinsudateDateString
            values.psninsudate = this.psninsudateDateString
            irsIndividualMedicalInsuranceAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangebrdy(date, dateString) {
        this.brdyDateString = dateString
      },
      onChangepausinsudate(date, dateString) {
        this.pausinsudateDateString = dateString
      },
      onChangepsninsudate(date, dateString) {
        this.psninsudateDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

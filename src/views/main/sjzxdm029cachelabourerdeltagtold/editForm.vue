<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading" style="height: 600px;">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="cus-title-d">基本信息</div>
          </a-col>
        </a-row>
        <a-col :span="12">
          <a-form-item
            label="姓名"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['realName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="矫正单位"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['jzjgName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="性别"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select disabled v-decorator="['gender', {rules: [{ message: ''}]}]">
              <a-select-option value="1">
                男
              </a-select-option>
              <a-select-option value="2">
                女
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="身份证号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['idCardNo', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="状态"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['zhuangtai', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="联系电话"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['mobilephone', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="cus-title-d">农民工信息</div>
          </a-col>
        </a-row>
        <a-col :span="12">
          <a-form-item
            label="不良行为"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select disabled v-decorator="['badStatus', {rules: [{ message: ''}]}]">
              <a-select-option value="0">
                无不良行为
              </a-select-option>
              <a-select-option value="1">
                有不良行为
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="人员类型"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select disabled v-decorator="['laborType', {rules: [{ message: ''}]}]">
              <a-select-option value="1">
                民工
              </a-select-option>
              <a-select-option value="2">
                项目部管理人员
              </a-select-option>
              <a-select-option value="3">
                企业职员
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="行业类型"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select disabled v-decorator="['industryType', {rules: [{ message: ''}]}]">
              <a-select-option value="1">
                住建行业
              </a-select-option>
              <a-select-option value="2">
                交通行业
              </a-select-option>
              <a-select-option value="3">
                水利行业
              </a-select-option>
              <a-select-option value="4">
                其他行业
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="职位名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['positionName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            label="所属企业"
            :labelCol="bigLabelCol"
            :wrapperCol="bigWrapperCol"
          >
            <a-input disabled v-decorator="['currentCompanyName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="项目类型"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['projectType', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="工种编号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['ahcp0006', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="工种名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['worktypeName', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="更新时间"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['updateTime', {rules: [{ message: ''}]}]" />
          </a-form-item>
        </a-col>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { sjzxDm029CacheLabourerDeltaGtOldEdit } from '@/api/modular/main/sjzxdm029cachelabourerdeltagtold/sjzxDm029CacheLabourerDeltaGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        bigLabelCol: {
          xs: { span: 24 },
          sm: { span: 3 }
        },
        bigWrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 }
        },
        lastModifyTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        if (record.zhuangtai === '200') {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '在矫'
              }
            )
          }, 100)
        } else {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                zhuangtai: '解矫'
              }
            )
          }, 100)
        }
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              realName: record.realName,
              idCardNo: record.idCardNo,
              worktypeNo: record.worktypeNo,
              worktypeName: record.worktypeName,
              industryType: record.industryType,
              projectType: record.projectType,
              provinceCode: record.provinceCode,
              cityCode: record.cityCode,
              lid: record.lid,
              currentPid: record.currentPid,
              currentProjectName: record.currentProjectName,
              currentCid: record.currentCid,
              currentCompanyName: record.currentCompanyName,
              isJob: record.isJob,
              mobilephone: record.mobilephone,
              badStatus: record.badStatus,
              isDeleted: record.isDeleted,
              countyCode: record.countyCode,
              gender: record.gender,
              positionName: record.positionName,
              laborType: record.laborType,
              age: record.age,
              lastModifyUser: record.lastModifyUser,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              updateTime: record.updateTime
            }
          )
        }, 100)
        // 时间单独处理
        if (record.lastModifyTime != null) {
            this.form.getFieldDecorator('lastModifyTime', { initialValue: moment(record.lastModifyTime, 'YYYY-MM-DD') })
        }
        this.lastModifyTimeDateString = moment(record.lastModifyTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.lastModifyTime = this.lastModifyTimeDateString
            sjzxDm029CacheLabourerDeltaGtOldEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangelastModifyTime(date, dateString) {
        this.lastModifyTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

<template>
  <a-modal
    title="新增矫正对象农民工信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="民工姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民工姓名" v-decorator="['realName', {rules: [{required: true, message: '请输入民工姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证" v-decorator="['idCardNo', {rules: [{required: true, message: '请输入身份证！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工种编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工种编号" v-decorator="['worktypeNo', {rules: [{required: true, message: '请输入工种编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工种名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工种名称" v-decorator="['worktypeName', {rules: [{required: true, message: '请输入工种名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="行业类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行业类型" v-decorator="['industryType', {rules: [{required: true, message: '请输入行业类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="项目类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入项目类型" v-decorator="['projectType', {rules: [{required: true, message: '请输入项目类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属省"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属省" v-decorator="['provinceCode', {rules: [{required: true, message: '请输入所属省！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属市"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属市" v-decorator="['cityCode', {rules: [{required: true, message: '请输入所属市！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民工lid"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民工lid" v-decorator="['lid', {rules: [{required: true, message: '请输入民工lid！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属项目PID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属项目PID" v-decorator="['currentPid', {rules: [{required: true, message: '请输入所属项目PID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属项目"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属项目" v-decorator="['currentProjectName', {rules: [{required: true, message: '请输入所属项目！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属企业cid"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属企业cid" v-decorator="['currentCid', {rules: [{required: true, message: '请输入所属企业cid！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属企业"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属企业" v-decorator="['currentCompanyName', {rules: [{required: true, message: '请输入所属企业！'}]}]" />
        </a-form-item>
        <a-form-item
          label="就职状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入就职状态" v-decorator="['isJob', {rules: [{required: true, message: '请输入就职状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="联系方式"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入联系方式" v-decorator="['mobilephone', {rules: [{required: true, message: '请输入联系方式！'}]}]" />
        </a-form-item>
        <a-form-item
          label="不良行为"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入不良行为" v-decorator="['badStatus', {rules: [{required: true, message: '请输入不良行为！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否删除"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否删除" v-decorator="['isDeleted', {rules: [{required: true, message: '请输入是否删除！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属区"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属区" v-decorator="['countyCode', {rules: [{required: true, message: '请输入所属区！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['gender', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职位名称" v-decorator="['positionName', {rules: [{required: true, message: '请输入职位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员类型" v-decorator="['laborType', {rules: [{required: true, message: '请输入人员类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年龄"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年龄" v-decorator="['age', {rules: [{required: true, message: '请输入年龄！'}]}]" />
        </a-form-item>
        <a-form-item
          label="最后修改人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入最后修改人" v-decorator="['lastModifyUser', {rules: [{required: true, message: '请输入最后修改人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="最后修改时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择最后修改时间" v-decorator="['lastModifyTime',{rules: [{ required: true, message: '请选择最后修改时间！' }]}]" @change="onChangelastModifyTime"/>
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { sjzxDm029CacheLabourerDeltaGtOldAdd } from '@/api/modular/main/sjzxdm029cachelabourerdeltagtold/sjzxDm029CacheLabourerDeltaGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        lastModifyTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.lastModifyTime = this.lastModifyTimeDateString
            sjzxDm029CacheLabourerDeltaGtOldAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangelastModifyTime(date, dateString) {
        this.lastModifyTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

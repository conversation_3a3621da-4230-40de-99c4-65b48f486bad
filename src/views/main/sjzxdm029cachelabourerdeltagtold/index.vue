<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.realName" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证">
                <a-input v-model="queryParam.idCardNo" allow-clear placeholder="请输入身份证"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="人员类型">
                <a-select v-model="queryParam.laborType" allow-clear placeholder="请选择人员类型">
                  <a-select-option value="1">
                    民工
                  </a-select-option>
                  <a-select-option value="2">
                    项目部管理人员
                  </a-select-option>
                  <a-select-option value="3">
                    企业职员
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="blxw" slot-scope="text, record">
          <span v-if="record.badStatus==='0'">无不良行为</span>
          <span v-if="record.badStatus==='1'">有不良行为</span>
        </span>
        <span slot="xb" slot-scope="text, record">
          <span v-if="record.gender==='1'">男</span>
          <span v-if="record.gender==='2'">女</span>
        </span>
        <span slot="jzzt" slot-scope="text, record">
          <span v-if="record.isJob==='0'">离职</span>
          <span v-if="record.isJob==='1'">在职</span>
        </span>
        <span slot="rylx" slot-scope="text, record">
          <span v-if="record.laborType==='1'">民工</span>
          <span v-if="record.laborType==='2'">项目部管理人员</span>
          <span v-if="record.laborType==='3'">企业职员</span>
        </span>
        <span slot="hylx" slot-scope="text, record">
          <span v-if="record.industryType==='1'">住建行业</span>
          <span v-if="record.industryType==='2'">交通行业</span>
          <span v-if="record.industryType==='3'">水利行业</span>
          <span v-if="record.industryType==='4'">其他行业</span>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk"/>
      <edit-form ref="editForm" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import {
  sjzxDm029CacheLabourerDeltaGtOldPage,
  sjzxDm029CacheLabourerDeltaGtOldDelete
} from '@/api/modular/main/sjzxdm029cachelabourerdeltagtold/sjzxDm029CacheLabourerDeltaGtOldManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
import { getOrgTree } from '@/api/modular/system/orgManage';

export default {
  components: {
    STable,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '姓名',
          align: 'center',
          dataIndex: 'realName'
        },
        {
          ellipsis: true,
          title: '矫正单位',
          align: 'center',
          dataIndex: 'jzjgName'
        },
        {
          ellipsis: true,
          title: '状态',
          align: 'center',
          dataIndex: 'zhuangtai',
          scopedSlots: { customRender: 'zhuangtai' }
        },
        // {
        //   ellipsis: true,
        //   title: '性别',
        //   align: 'center',
        //   dataIndex: 'gender',
        //   scopedSlots: { customRender: 'xb' }
        // },
        {
          ellipsis: true,
          title: '联系方式',
          align: 'center',
          dataIndex: 'mobilephone'
        },
        {
          ellipsis: true,
          title: '身份证号',
          align: 'center',
          dataIndex: 'idCardNo'
        },
        {
          ellipsis: true,
          title: '不良行为',
          align: 'center',
          dataIndex: 'badStatus',
          scopedSlots: { customRender: 'blxw' }
        },
        {
          ellipsis: true,
          title: '人员类型',
          align: 'center',
          dataIndex: 'laborType',
          scopedSlots: { customRender: 'rylx' }
        },
        {
          ellipsis: true,
          title: '行业类型',
          align: 'center',
          dataIndex: 'industryType',
          scopedSlots: { customRender: 'hylx' }
        },
        {
          ellipsis: true,
          title: '所属企业',
          align: 'center',
          dataIndex: 'currentCompanyName'
        },
        // {
        //   ellipsis: true,
        //   title: '职位名称',
        //   align: 'center',
        //   dataIndex: 'positionName'
        // },
        // {
        //   ellipsis: true,
        //   title: '项目类型',
        //   align: 'center',
        //   dataIndex: 'projectType'
        // },
        // {
        //   ellipsis: true,
        //   title: '工种编号',
        //   align: 'center',
        //   dataIndex: 'worktypeNo'
        // },
        {
          ellipsis: true,
          title: '工种名称',
          align: 'center',
          dataIndex: 'worktypeName'
        },
        // {
        //   ellipsis: true,
        //   title: '职位名称',
        //   align: 'center',
        //   dataIndex: 'positionName'
        // },
        {
          ellipsis: true,
          title: '就职状态',
          align: 'center',
          dataIndex: 'isJob',
          scopedSlots: { customRender: 'jzzt' }
        },
        {
          ellipsis: true,
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          width: '50px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return sjzxDm029CacheLabourerDeltaGtOldPage(Object.assign(parameter, this.switchingDate())).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: []
    }
  },
  created() {
    this.getOrgTree()
  },
  methods: {
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const queryParamlastModifyTime = this.queryParam.lastModifyTimeDate
      if (queryParamlastModifyTime != null) {
        this.queryParam.lastModifyTime = moment(queryParamlastModifyTime).format('YYYY-MM-DD')
        if (queryParamlastModifyTime.length < 1) {
          delete this.queryParam.lastModifyTime
        }
      }
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    sjzxDm029CacheLabourerDeltaGtOldDelete(record) {
      sjzxDm029CacheLabourerDeltaGtOldDelete(record).then((res) => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onChangelastModifyTime(date, dateString) {
      this.lastModifyTimeDateString = dateString
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

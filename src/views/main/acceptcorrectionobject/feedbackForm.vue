<template>
  <a-drawer
    title="交付衔接协同"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <section class="accept-wrapper">
          <div class="accept-left">
            <!--案件信息-->
            <div class="cus-title-d" style="margin-top: 20px;">案件信息 </div>
            <div class="" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                  <a-form-item
                    label="案件赋号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['tyfh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="法院案件标识"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['fyajbs']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="案件名称"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['ajmc']" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!--社区矫正对象信息-->
            <div class="cus-title-d" style="margin-top: 20px;">社区矫正对象信息 </div>
            <div class="" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="公安嫌疑人编号"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input :disabled="true" v-decorator="['gaxyrbh']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="姓名"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input disabled placeholder="请输入姓名" v-decorator="['xm']" />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="证件类型"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select placeholder="请选择证件类型" v-decorator="['zjlx']" disabled>
                      <a-select-option v-for="(item,index) in zjlxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="证件号码"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input placeholder="请输入证件号码" v-decorator="['zjhm']" disabled />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!--送达回执-->
            <div class="cus-title-d" style="margin-top: 20px;">送达回执 </div>
            <div class="" style="">
              <a-row :gutter="24">
                <a-col :md="12" :sm="24">
                  <a-form-item
                    label="入矫结果"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select
                      :disabled="linkDisabled"
                      v-decorator="['fkjg', {rules: [{ required: true, message: '请选择入矫结果！' }]}]"
                      style="width: 100%"
                      :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                      @change="changerjjg">
                      <a-select-option value="1">已入矫</a-select-option>
                      <a-select-option value="0">未入矫</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24" v-if="xtbh === 'XTBH4052_1'">
                  <a-form-item label="反馈法院" :labelCol="labelCol" :wrapperCol="wrapperCol" >
                    <a-input placeholder="反馈法院" v-decorator="['tsdwmc']" disabled />
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24" v-if="false" >
                  <a-form-item label="反馈检察院" :labelCol="labelCol" :wrapperCol="wrapperCol" >
                    <a-select
                      :disabled="linkDisabled"
                      v-decorator="['fkJcyCode', {rules: [{required: true, message: '请选择检察院'}]}]"
                      show-search
                      style="width: 100%"
                      :options="extOrgJcy" >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24" v-if="xtbh === 'XTBH4072_1'">
                  <a-form-item label="反馈公安" :labelCol="labelCol" :wrapperCol="wrapperCol" >
                    <a-select
                      :disabled="linkDisabled"
                      v-decorator="['fkGaCode', {rules: [{required: true, message: '请选择公安'}]}]"
                      show-search
                      style="width: 100%"
                      :options="extOrgGa" >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="12" :sm="24" v-if="false">
                  <a-form-item label="反馈看守所" :labelCol="labelCol" :wrapperCol="wrapperCol" >
                    <a-select
                      :disabled="linkDisabled"
                      v-decorator="['fkKssCode', {rules: [{required: true, message: '请选择看守所'}]}]"
                      show-search
                      style="width: 100%"
                      :options="extOrgKss" >
                    </a-select>
                  </a-form-item>
                </a-col>
                <div v-if="fkjg==='1'">
                  <a-col :md="12" :sm="24">
                    <a-form-item
                      label="社区矫正机关"
                      :labelCol="labelCol"
                      :wrapperCol="wrapperCol"
                    >
                      <a-tree-select
                        :disabled="linkDisabled"
                        v-decorator="['sqjzjg']"
                        style="width: 100%"
                        :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                        :treeData="orgTree">
                      </a-tree-select>
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24">
                    <a-form-item
                      label="入矫日期"
                      :labelCol="labelCol"
                      :wrapperCol="wrapperCol"
                    >
                      <a-date-picker :disabled="true" style="width: 100%" v-decorator="['rjrq']" />
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24">
                    <a-form-item
                      label="执行通知书回执文号"
                      :labelCol="labelCol"
                      :wrapperCol="wrapperCol"
                    >
                      <a-input :disabled="linkDisabled" placeholder="请输执行通知书回执文号" v-decorator="['zxtzshzwh', {rules: [{ required: true, message: '请输执行通知书回执文号！' }]}]" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item
                      label="回执材料"
                      :labelCol="{ span: 3 }"
                      :wrapperCol="{ span: 20 }">
                      <a-upload
                        :disabled="linkDisabled"
                        :multiple="false"
                        :showUploadList="true"
                        v-decorator="['fileList', { rules: [{ required: true, message: '请上传附件！' }] }]"
                        :file-list="fileList"
                        :remove="handleRemove"
                        :before-upload="beforeUpload">
                        <a-button> <a-icon type="upload" />上传文件</a-button>
                      </a-upload>
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24">
                    <a-form-item
                      label="说明："
                      :labelCol="labelCol"
                      :wrapperCol="wrapperCol">
                      文书来源于电子卷宗已盖章文书
                    </a-form-item>
                  </a-col>
                  <a-col :md="24" :sm="24">
                    <a-form-item
                      label="备注"
                      :labelCol="{ span: 3 }"
                      :wrapperCol="{ span: 20 }">
                      <a-textarea :disabled="linkDisabled" :rows="4" placeholder="请输入备注" v-decorator="['fkbz']"></a-textarea>
                    </a-form-item>
                  </a-col>
                </div>
                <div v-else-if="fkjg==='0'">
                  <a-col :md="24" :sm="24" >
                    <a-form-item
                      label="未入矫原因"
                      :labelCol="{ span: 3 }"
                      :wrapperCol="{ span: 20 }">

                      <a-textarea :disabled="linkDisabled" :rows="4" placeholder="未入矫原因" v-decorator="['wnrjyy', {rules: [{ required: true, message: '请选择未入矫原因！' }]}]"></a-textarea>
                    </a-form-item>
                  </a-col>
                  <a-col :md="24" :sm="24">
                    <a-form-item
                      label="回执材料"
                      :labelCol="{ span: 3 }"
                      :wrapperCol="{ span: 20 }">

                      <a-upload
                        :disabled="linkDisabled"
                        :multiple="false"
                        :showUploadList="true"
                        :file-list="fileList"
                        :remove="handleRemove"
                        :before-upload="beforeUpload">
                        <a-button> <a-icon type="upload" />上传文件</a-button>
                      </a-upload>
                    </a-form-item>
                  </a-col>

                </div>
              </a-row>
            </div>
          </div>
          <div class="accept-right">
            <StepVue :steps="steps"/>
          </div>
        </section>
        <!--区县司法局反馈信息-->
        <div class="cus-title-d" style="margin-top: 20px;">区县司法局反馈信息 </div>
        <div class="" style="">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24">
              <a-form-item
                label="反馈人"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['fkr']" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24">
              <a-form-item
                label="反馈时间"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input :disabled="true" v-decorator="['fksj']" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
    <div
      v-if="!linkDisabled"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-button style="margin-right: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import {
  acceptTemporarilyOutsidePrisonDetail,
  acceptTemporarilyOutsidePrisonFeedback
} from '@/api/modular/main/accepttemporarilyoutsideprison/acceptTemporarilyOutsidePrisonManage'
import {
  acceptBaseStep,
  acceptCorrectionObjectDetail,
  acceptCorrectionObjectFeedback
} from '@/api/modular/main/acceptcorrectionobject/acceptCorrectionObjectManage'
import moment from 'moment'
import StepVue from '@/views/main/acceptcorrectionobject/components/Step.vue';
import { mapGetters } from 'vuex'
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';

export default {
  components: { StepVue },
  data() {
    return {
      drawerWidth: 1000,
      xtbh: undefined,
      fkjg: '1',
      sqjzjg: '',
      labelCol: { xs: { span: 24 }, sm: { span: 5 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 19 } },
      linkDisabled: false,
      visible: false,
      confirmLoading: false,
      steps: [],
      orgTree: [],
      extOrgFy: [],
      extOrgJcy: [],
      extOrgGa: [],
      extOrgKss: [],
      rjsjDateString: '',
      zjlxDropDown: [],
      form: this.$form.createForm(this),
      fileList: [],
      fileId: []
    }
  },
  created() {
    this.drawerWidth = window.innerWidth - 200
    this.getExtOrgData()
    this.sysDictTypeDropDown()
  },
  computed: {
    ...mapGetters(['nickname'])
  },
  methods: {
    getExtOrgData() {
      extOrgInfoList().then((res) => {
        res.data.forEach((p) => {
          if (p.type === 20) {
            this.extOrgFy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          } else if (p.type === 30) {
            this.extOrgJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          } else if (p.type === 40) {
            this.extOrgGa.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          } else if (p.type === 41) {
            this.extOrgKss.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
          }
        })
      })
    },
    changerjjg(e) {
      this.fkjg = e
      this.$nextTick(() => {
        this.form.setFieldsValue({ sqjzjg: this.sqjzjg })
      })
    },
    // 初始化方法
    init(record, orgTree, xtbh, disable) {
      console.log(record, 'record')
      this.xtbh = xtbh
      this.visible = true
      this.linkDisabled = disable
      this.orgTree = orgTree

      switch (this.xtbh) {
        case 'XTBH4003':
          this.loadCorrectionObjectData(record.id);
          break;
        case 'XTBH4022_1':
          this.loadTemporarilyOutsidePrisonData(record.id);
          break;
        case 'XTBH4052_1':
          this.loadTemporarilyOutsidePrisonData(record.id);
          break;
        case 'XTBH4072_1':
          this.loadTemporarilyOutsidePrisonData(record.id);
          break;
        default:
          break;
      }

      acceptBaseStep({ id: record.id, xtbh: xtbh }).then(res => {
        if (res.success) {
          this.steps = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload(file) {
      this.fileList = [...this.fileList, file]
      return false
    },
    sureUpload() {
      this.fileId = []
      const list = []
      console.log(this.fileList, '当前所有文件集合')
      if (this.fileList != null) {
        this.fileList.forEach(file => {
          if (file.size != null) {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('ext', 1)
            var p1 = new Promise((resolve, reject) => {
              sysFileInfoUpload(Object.assign(formData)).then(res => {
                if (res.success) {
                  this.fileId = [...this.fileId, res.data.id]
                  resolve(res.result)
                } else {
                  this.$message.error(res.message)
                  this.uploadStatus = false
                  reject(res)
                }
                this.fileList = []
              })
            })
          }
          list.push(p1)
        })
      }
      return Promise.all(list).then(() => {
        this.uploadStatus = true
      })
    },
    loadCorrectionObjectData(id) {
      acceptCorrectionObjectDetail({ id: id }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                id: res.data.id,
                tyfh: res.data.tyfh,
                xm: res.data.xm,
                fyajbs: res.data.fyajbs || res.data.ajbs,
                ajmc: res.data.ajmc,
                gaxyrbh: res.data.gaxyrbh,
                zjlx: '111',
                zjhm: res.data.zjhm,
                sqjzjg: res.data.jzjg,
                fkjg: res.data.fkjg || '1',
                fkbz: res.data.fkbz || '',
                wnrjyy: res.data.wnrjyy || '',
                zxtzshzwh: res.data.zxtzshzwh || ''
              }
            )

            if (res.data.rjrq != null) {
              this.form.getFieldDecorator('rjrq', { initialValue: moment(res.data.rjrq, 'YYYY-MM-DD') })
            } else {
              this.form.getFieldDecorator('rjrq', { initialValue: null })
            }
            if (res.data.fksj != null) {
              this.form.getFieldDecorator('fksj', { initialValue: moment(res.data.fksj).format('YYYY-MM-DD HH:mm') })
            } else {
              this.form.getFieldDecorator('fksj', { initialValue: moment().format('YYYY-MM-DD HH:mm') })
            }
            if (res.data.fkr == null) {
              this.form.getFieldDecorator('fkr', { initialValue: this.nickname })
            } else {
              this.form.getFieldDecorator('fkr', { initialValue: res.data.fkr })
            }
          }, 100)
          this.sqjzjg = res.data.jzjg
          this.fileList = res.data.hzclList
        }
      })
    },
    loadTemporarilyOutsidePrisonData(id) {
      acceptTemporarilyOutsidePrisonDetail({ id: id }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                fkjg: res.data.fkjg || '1',
                fkbz: res.data.fkbz || '',
                wnrjyy: res.data.wnrjyy || '',
                zxtzshzwh: res.data.zxtzshzwh || '',
                tsdwmc: res.data.tsdwmc || '',
                // fkKssCode: res.data.fkKssCode || '',
                // fkJcyCode: res.data.fkJcyCode || '',
                // fkGaCode: res.data.fkGaCode || '',

                id: res.data.id,
                tyfh: res.data.tyfh,
                xm: res.data.xm,
                fyajbs: res.data.fyajbs || res.data.ajbs,
                ajmc: res.data.ajmc,
                gaxyrbh: res.data.gaxyrbh,
                zjlx: '111',
                zjhm: res.data.zjhm,
                sqjzjg: res.data.jzjg
              }
            )
            if (res.data.rjrq != null) {
              this.form.getFieldDecorator('rjrq', { initialValue: moment(res.data.rjrq, 'YYYY-MM-DD') })
            } else {
              this.form.getFieldDecorator('rjrq', { initialValue: null })
            }
            if (res.data.fksj != null) {
              this.form.getFieldDecorator('fksj', { initialValue: moment(res.data.fksj).format('YYYY-MM-DD HH:mm') })
            } else {
              this.form.getFieldDecorator('fksj', { initialValue: moment().format('YYYY-MM-DD HH:mm') })
            }
            if (res.data.fkr == null) {
              this.form.getFieldDecorator('fkr', { initialValue: this.nickname })
            } else {
              this.form.getFieldDecorator('fkr', { initialValue: res.data.fkr })
            }
          }, 100)

          this.sqjzjg = res.data.jzjg
          this.fileList = res.data.hzclList
        }
      })
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      switch (this.xtbh) {
        case 'XTBH4003':
          this.handleSubmit1();
          break;
        case 'XTBH4022_1':
          this.handleSubmit2();
          break;
        case 'XTBH4052_1':
          this.handleSubmit2();
          break;
        case 'XTBH4072_1':
          this.handleSubmit2();
          break;
        default:
          break;
      }
    },
    handleSubmit2() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          await this.sureUpload()
          values.hzcl = this.fileId.join(',')
          values.zt = '4'
          delete values.rjrq
          delete values.fileList
          acceptTemporarilyOutsidePrisonFeedback(values).then((res) => {
            if (res.success) {
              this.$message.success('提交成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('提交失败')// + res.message
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleSubmit1() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          await this.sureUpload()
          delete values.fileList
          delete values.rjrq
          values.hzcl = this.fileId.join(',')
          values.zt = '4'

          acceptCorrectionObjectFeedback(values).then((res) => {
            if (res.success) {
              this.$message.success('提交成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('提交失败')// + res.message
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    },
    /**
     * 获取字典数据
     */
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
    }
  }
}
</script>

<style lang="less" scoped>
.accept-wrapper{
  display: flex;
  width: 100%;
  .accept-left{
    flex:1;
    overflow: auto;
    overflow-x: hidden;
  }
  .accept-right{
    border-left: 1px solid #e8e8e8;
    position: relative;
    max-width: 300px;
  }
}
/deep/ .ant-drawer-body {
  padding-bottom: 78px;
}

.border{
  border-style: solid;border-width: 1px;
  border-color: rgba(204, 204, 204, 1);
}

.title{
  font-family: '微软雅黑', sans-serif;
  font-weight: 400;
  font-style: normal;color: #1078C9;
  background-color: rgba(228, 243, 255, 1);
  padding:5px 20px;
}
::v-deep .ant-row{
  margin: 10px 0;
}

</style>

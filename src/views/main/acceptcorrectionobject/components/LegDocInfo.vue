<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="cus-title-d">执行通知书</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="决定文书文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['sxpjszh', { rules: [{ required: false, message: '请输入！' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="文书生效日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['pjwssxrq', { rules: [{ required: false, message: '请选择！' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="执行通知书文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['zxtzswh', { rules: [{ required: false, message: '请输入！' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="执行通知书日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['zxtzsrq', { rules: [{ required: false, message: '请选择！' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="交付执行日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['jfzxrq', { rules: [{ required: false, message: '请选择！' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="移交罪犯机关" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-select
            dictType="yjzfjglx"
            :disabled="readOnly"
            style="width: 320px;"
            placeholder="请选择"
            v-decorator="['yjzfjglx', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="移交罪犯机关名称" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-space>
            <a-input :disabled="readOnly" v-decorator="['yjzfjgmc', { rules: [{ required: false, message: '请输入' }] }]"/>
            <sh-select
              dictType="yjzfjglx"
              :disabled="readOnly"
              style="width: 320px;"
              placeholder="请选择"
              v-decorator="['yjzfjglx', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"/>
          </a-space>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="主要犯罪事实" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            :disabled="readOnly"
            v-decorator="['zyfzss', { rules: [{ required: false, message: '请输入' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">文书信息</div>
        <a-button
          v-if="baseObj.docList && baseObj.docList.length > 0"
          style="float: right;margin-right: 55px;margin-top: -50px;"
          type="primary"
          icon="download"
          @click="downloadDocList"
          :loading="exportBatchLoading">
          打包下载
        </a-button>
      </a-col>
      <a-col :span="23">
        <DocList :list="baseObj.docList"/>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
// 文书信息
import DocList from '@/views/main/acceptcorrectionobject/components/DocList'
import { docBatchDownLoad } from '@/api/modular/main/acceptDocManage';

export default {
  inject: ['parentData'],
  props: {
    readOnly: { default: false, type: Boolean },
    baseObj: { default: () => {}, type: Object }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  components: {
    DocList
  },
  data() {
    return {
      exportBatchLoading: false,
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      form: this.$form.createForm(this)
    }
  },
  methods: {
    setValues() {
      const allData = this.form.getFieldsValue()
      const values = {}
      Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })
      this.form.setFieldsValue({
        ...values
      })
    },
    downloadDocList() {
      this.exportBatchLoading = true
      docBatchDownLoad({ contactId: this.baseObj.id, xm: this.baseObj.xm }).then((res) => {
        this.downloadFile(res)
      }).catch((err) => {
        this.exportBatchLoading = false
        this.$message.error('下载错误：获取文件流错误 ' + err)
      })
    },
    downloadFile(res) {
      this.exportBatchLoading = false
      var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
      var contentDisposition = res.headers['content-disposition']
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var result = patt.exec(contentDisposition)
      var filename = result[1]
      var downloadElement = document.createElement('a')
      var href = window.URL.createObjectURL(blob) // 创建下载的链接
      var reg = /^["](.*)["]$/g
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-card {
  // background: #f5f6fa;
}

/deep/ .ant-card-body {
  padding-left: 0;
  padding-right: 0;
}
</style>

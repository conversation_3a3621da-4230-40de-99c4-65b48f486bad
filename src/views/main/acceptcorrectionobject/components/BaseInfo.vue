<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="cus-title-d">案件信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['tyfh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="法院案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['fyajbs', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['ajmc', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">人员基本信息</div>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['jzlb', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree
            :disabled="true"
            v-decorator="['jsdw', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="公安嫌疑人编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['gaxyrbh', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['xm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="曾用名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['cym', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="sex"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['xb', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="mz"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['mz', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zjlx"
            disabled
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['zjlx', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['zjhm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            :disabled="readOnly"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['csrq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否成年" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group
            :disabled="readOnly"
            v-decorator="['sfcn', { rules: [{ required: false, message: '请输入' }] }]"
          >
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="未成年" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="wcn"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['wcn', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="文化程度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="whcd"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['whcd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="婚姻状况" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="hyzk"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['hyzk', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="职业" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_BQZY"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['zy', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="就业就学情况" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="jyjxqk"
            style="width: 100%;"
            :disabled="readOnly"
            placeholder="请选择"
            v-decorator="['jyjxqk', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="现政治面貌" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zzmm"
            style="width: 100%;"
            :disabled="readOnly"
            placeholder="请选择"
            v-decorator="['xzzmm', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原政治面貌" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zzmm"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['yzzmm', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原工作单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['gzdw', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="单位电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['dwlxdh', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="个人联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="readOnly"
            placeholder="可填多个联系电话，用逗号分开"
            v-decorator="['grlxdh', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="guoji"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['gj', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="户籍是否与居住地相同" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group
            :disabled="readOnly"
            @change="handleChange"
            v-decorator="['hjdsfyjzdxt', { rules: [{ required: false, message: '请输入' }] }]"
          >
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="住所地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-cascader-distpicker
            :disabled="readOnly"
            v-decorator="['zsd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="住所地详细地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['zsdxxdz', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍所在地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-cascader-distpicker
            :disabled="readOnly"
            v-decorator="['hjszd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍地址明细" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="readOnly"
            placeholder="请输入"
            v-decorator="['hjdzmx', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24">
        <div class="cus-title-d">家庭成员及社会关系</div>
      </a-col>
      <a-col :span="24">
        <a-form-item label="有无家庭成员及主要社会关系" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
          <a-radio-group
            :disabled="readOnly"
            v-decorator="['ywjtcyjzyshgx', { rules: [{ required: false, message: '请输入' }] }]"
          >
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="23">
        <MembersList :list="baseObj.familyList" />
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
// 成员列表
import MembersList from '@/views/main/acceptcorrectionobject/components/MembersList'

export default {
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {},
      type: Object
    }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  components: { MembersList },
  data() {
    return {
      isShow: false,
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      form: this.$form.createForm(this)
    }
  },
  mounted() {},
  methods: {
    handleChange(e) {
      console.log(e)
      if (e.target.value === '1') {
        const a = this.form.getFieldsValue()
        this.form.setFieldsValue({
          hjdzmx: a.zsdxxdz,
          hjszd: a.zsd
        })
      }
    },
    setValues() {
      this.isShow = !!(this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y');
      const allData = this.form.getFieldsValue()

      const values = {}
      Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })
      this.form.setFieldsValue({
        ...values
      })
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-card {
  // background: #f5f6fa;
}

/deep/.ant-card-body {
  padding-left: 0;
  padding-right: 0;
}
</style>

<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="审核结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            v-decorator="['shjg', { rules: [{ required: true, message: '请选择审核结果！' }] }]"
            style="width: 100%"
            :disabled="readOnly"
            :treeDefaultExpandAll="true"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="shjgTreeDropDown"
            @change="handleChange"
            placeholder="请选择审核结果">
          </a-tree-select>
        </a-form-item>
      </a-col>
      <a-col :span="12" v-if="!showUpload">
        <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['jzlb', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12" v-if="!showUpload">
        <a-form-item label="矫正机构" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree :params="{level:4}" :disabled="readOnly" v-decorator="['jzjg', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12" v-if="!showUpload">
        <a-form-item label="入矫时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            :disabled="readOnly"
            placeholder="请选择"
            v-decorator="['rjrq', {initialValue:moment().format('YYYY-MM-DD'), rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            v-if="!showUpload"
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['shbz', { rules: [{ required: false, message: '请输入' }] }]" />
          <a-textarea
            v-else
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['shbz', { rules: [{ required: true, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24" v-show="showUpload">
        <a-form-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-file-upload
            v-if="!readOnly"
            v-decorator="['hzcl']"
            :btnText="'附件上传'"
            uploadUrl="/api/sysFileInfo/uploadOss">
          </sh-file-upload>
          <sh-file-upload
            v-else
            disabled
            v-model="hzclList"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="审核人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['shry', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            disabled
            v-decorator="['shsj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>

    </a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'
import { sysDictDataTree } from '@/api/modular/system/dictDataManage';
import { fileList } from '@/api/modular/system/fileManage';
export default {
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => { },
      type: Object
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    showUpload() {
      return this.shjgFlag !== '1'
    }
  },
  data() {
    return {
      moment,
      shjgFlag: '1',
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      shjgTreeDropDown: [],
      hzclList: [],
      form: this.$form.createForm(this)
    }
  },
  created() {
    sysDictDataTree({ dictTypeId: '1732298638446571522' }).then(res => {
      if (res.success) {
        this.shjgTreeDropDown = res.data
      } else {
        this.$message.warning(res.message)
      }
    })
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  methods: {
    handleChange() {
      this.$nextTick(() => {
        this.shjgFlag = this.form.getFieldValue('shjg')
        setTimeout(() => {
          this.form.setFieldsValue({
          shry: this.userInfo.name,
          shsj: moment().format('YYYY-MM-DD HH:mm:ss')
        })
        }, 300)
      })
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    },
    setValues() {
      if (this.baseObj.shjg) {
        this.shjgFlag = this.baseObj.shjg
        this.$nextTick(() => {
          this.form.setFieldsValue({
            shjg: this.baseObj.shjg,
            jzlb: this.baseObj.jzlb,
            jzjg: this.baseObj.jzjg,
            rjrq: this.baseObj.rjrq,
            shry: this.baseObj.shry,
            shsj: this.baseObj.shsj,
            shbz: this.baseObj.shbz
          })
        })
      } else {
        this.shjgFlag = '1'
        this.$nextTick(() => {
          this.form.setFieldsValue({
            shjg: '1',
            shry: this.userInfo.name,
            shsj: moment().format('YYYY-MM-DD HH:mm:ss')
          })
        })
      }
      console.log(this.baseObj.hzcl);
      if (this.baseObj.hzcl) {
        const a = this.baseObj.hzcl
        fileList({ ids: a }).then(res => {
          this.hzclList = res.data.map(item => {
            return {
              name: item.name,
              url: item.url,
              uid: item.id,
              ...item
            }
          })
        })
      }
    }
  }

}
</script>

<style lang="less" scoped></style>

<template>
  <!-- <a-row :gutter="24"> -->
  <a-form :form="form" >
    <a-row :gutter="24">

      <a-col :span="12">
        <a-form-item label="社区矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker :disabled="readOnly" style="width: 100%" placeholder="请选择" v-decorator="['sqjzksrq',{rules: [{ required: false, message: '请选择！' }]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="社区矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker :disabled="readOnly" style="width: 100%" placeholder="请选择" v-decorator="['sqjzjsrq',{rules: [{ required: false, message: '请选择！' }]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="社区矫正期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['sqjzqx', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="具体罪名" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <SpecificCharges :disabled="readOnly" :list="baseObj.chargeList" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="管制期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['gzqx', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="缓刑考验期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['hxkyqx', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="是否数罪并罚" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-radio-group :disabled="readOnly" v-decorator="['sfszbf', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原羁押场所" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="yjzfjglx"
            style="width: 320px;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['yjycs', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="判决刑种" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zxzl"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['pjxz', {initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="判决刑期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker :disabled="readOnly" style="width: 100%" placeholder="请选择" v-decorator="['pjxqksrq',{rules: [{ required: false, message: '请选择！' }]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="判决刑期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker :disabled="readOnly" style="width: 100%" placeholder="请选择" v-decorator="['pjxqjsrq',{rules: [{ required: false, message: '请选择！' }]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="有期徒刑期限" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['yqtxqx', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="附加刑" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-select
            dictType="fjx"
            mode="multiple"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['fjxList', { initialValue: [] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="剥夺政治权利年限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['bdzzqlnx', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="具体罚款金额" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['jtfkje', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="驱逐出境" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group :disabled="readOnly" v-decorator="['qzcj', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否“五独“" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group :disabled="readOnly" v-decorator="['sfwd', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否“五涉“" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group :disabled="readOnly" v-decorator="['sfws', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否有“四史“" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group :disabled="readOnly" v-decorator="['sfyss', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="没收财产" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" placeholder="请输入" v-decorator="['mscc', {rules: [{required: false, message: '请输入！'}]}]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原审判机关名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-space>
            <sh-select
              :options="parentData.extOrgInfoData.fy"
              labelKey="label"
              valueKey="value"
              :disabled="readOnly"
              style="width: 320px;"
              placeholder="请选择"
              v-decorator="['spjgmc', {initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
          </a-space>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="cus-title-d">禁止令</div>
      </a-col>
      <a-col :span="24">
        <a-form-item label="是否被宣告禁止令" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-radio-group disabled v-decorator="['sfbxgjzl', { rules: [{ required: false, message: '请输入' }] }]" >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="23">
        <JunctionList :list="baseObj.forbidList"/>
      </a-col>
      <a-col :span="24">
        <div style="margin-top: 20px;" class="cus-title-d">同案犯信息</div>
      </a-col>
      <a-col :span="23">
        <AccompliceList :list="baseObj.accompliceList"/>
      </a-col>
    </a-row>
  </a-form>
  <!-- </a-row> -->
</template>

<script>

// 具体罪名列表
import SpecificCharges from '@/views/main/acceptcorrectionobject/components/SpecificCharges'
// 禁制令列表
import JunctionList from '@/views/main/acceptcorrectionobject/components/JunctionList'
// 同案犯列表
import AccompliceList from '@/views/main/acceptcorrectionobject/components/AccompliceList'
export default {
  inject: ['parentData'],
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {},
      type: Object
    }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setValues()
        })
      },
      deep: true
    }
  },
  components: {
    SpecificCharges,
    JunctionList,
    AccompliceList
  },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      form: this.$form.createForm(this)
    }
  },
  methods: {
    setValues() {
      // 家庭成员及社会关系
      if (this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y') {
        this.isShow = true;
      } else {
        this.isShow = false;
      }
      const allData = this.form.getFieldsValue()
      const values = {}
      Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })

      values.fjxList = this.baseObj.fjx ? this.baseObj.fjx.split(',') : []
      values.sfwdList = this.baseObj.sfwd ? this.baseObj.sfwd.split(',') : []
      values.sfwsList = this.baseObj.sfws ? this.baseObj.sfws.split(',') : []
      values.sfyssList = this.baseObj.sfyss ? this.baseObj.sfyss.split(',') : []
      this.form.setFieldsValue({
        ...values
      })
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-card{
    // background: #f5f6fa;
}
/deep/.ant-card-body{
    padding-left: 0;
    padding-right: 0;
}

</style>

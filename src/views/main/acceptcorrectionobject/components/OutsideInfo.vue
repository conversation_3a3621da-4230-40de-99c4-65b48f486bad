<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="决定文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['jdswh', { rules: [{ required: false, message: '请输入' }] }]" />

        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否决定暂予监外执行" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            :options="yesOrNoData"
            style="width: 100%;"
            :disabled="readOnly"
            placeholder="请选择"
            v-decorator="['sfjdzyjwzx', {initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="决定（不）暂予监外执行日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            disabled
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['jdzyjwzxrq', { rules: [{ required: false, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="监外执行决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            :options="parentData.extOrgInfoData.fy"
            labelKey="orgName"
            valueKey="orgCode"
            :disabled="readOnly"
            style="width: 320px;"
            placeholder="请选择"
            v-decorator="['zyjwzxjdjg', {initialValue: '', rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="（不）暂予监外执行原因" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
          <a-textarea
            disabled
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['zyjwzxyy', { rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-correction-org-tree
            :disabled="readOnly"
            v-decorator="['jsdwId', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="交付执行日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['jfzxrq', { rules: [{ required: false, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="移交罪犯机关类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="yjzfjglx"
            style="width: 100%;"
            :disabled="readOnly"
            placeholder="请选择"
            v-decorator="['yjzfjglx', { initialValue: '',rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="移交罪犯机关名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            :options="[
              ...parentData.extOrgInfoData.jcy,
              ...parentData.extOrgInfoData.ga,
              ...parentData.extOrgInfoData.fy
            ]"
            labelKey="orgName"
            valueKey="orgCode"
            :disabled="readOnly"
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['yjzfjgmc', { initialValue: '',rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" v-decorator="['jzqx', { rules: [{ required: false, message: '请输入' }] }]" />

        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="暂予监外执行起日" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['zyjwzxqr', { rules: [{ required: false, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="暂予监外执行止日" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['zyjwzxzr', { rules: [{ required: false, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
export default {
  inject: ['parentData'],
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => { },
      type: Object

    }
  },
  watch: {
    baseObj: {
      handler() {
        this.$nextTick(() => {
          this.setVals()
        })
      },
      deep: true
    }
  },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      },
      yesOrNoData: [
        { code: '1', name: '是' },
        { code: '0', name: '否' }
      ],
      form: this.$form.createForm(this)
    }
  },
  methods: {
    setVals() {
      // 家庭成员及社会关系
      if (this.baseObj.ywjtcyjzyshgx && this.baseObj.ywjtcyjzyshgx === 'Y') {
        this.isShow = true;
      } else {
        this.isShow = false;
      }
      const allData = this.form.getFieldsValue()
      const values = {}
       Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })
      // values.zsd = addres
      // values.hjszd = addres2
      this.form.setFieldsValue({
       ...values
      })
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('暂予监外信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    }
  }

}
</script>

<style lang="scss" scoped></style>

<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    bordered>
    <template slot="ossUrl" slot-scope="text,record">
      <a-button @click="handleClick(record)" type="link">{{ record.ws }}</a-button>
    </template>
  </a-table>
</template>
<script>

import { reLoadFileFromOss } from '@/api/modular/main/acceptDocManage';

export default {
  props: {
    list: {
      default: () => [],
      type: Array
    }
  },
  data() {
    return {
      columns: [
        { title: '序号', dataIndex: '', width: 60, key: 'rowIndex', align: 'center', customRender: function (t, r, index) { return parseInt(index) + 1 } },
        { title: '法律文书名称',
          dataIndex: 'ws',
          customRender: function (text) {
          return text.replace(/\.[^.\s]*$/, '')
        } },
        { title: '附件', className: 'column-money', dataIndex: 'ossUrl', scopedSlots: { customRender: 'ossUrl' } }
      ]
    };
  },
  methods: {
    async handleClick(record) {
      const url = await this.reLoadFileFromOss(record.id, record.ossUrl);
      if (url === 'NoSuchKey') {
        this.$message.error('该文件不存在，请联系管理员！')
      } else {
        this.$PdfDrawer.viewPdf({ title: '详情', url: url })
      }
    },
    reLoadFileFromOss(id, url) {
      return new Promise((resolve, reject) => {
        if (url == null || url === '' || url === 'NoSuchKey') {
          reLoadFileFromOss({ id: id }).then(res => {
            if (res.success) {
              resolve(res.data.ossUrl)
            } else {
              reject(res.message)
            }
          })
        } else {
          resolve(url)
        }
      })
    }
  }
};
</script>
<style lang="less" scoped>
th.column-money,
td.column-money {
  text-align: right !important;
}

/deep/ .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

/deep/ .ant-table-body .ant-table-thead > tr > th {
  background: #fafafa !important;
}

</style>
<style lang="less"></style>

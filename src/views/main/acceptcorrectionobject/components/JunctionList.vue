<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    bordered>
    <template slot="name" slot-scope="text">
      <a>{{ text }}</a>
    </template>
  </a-table>
</template>
<script>
export default {
  props: {
    list: {
      default: () => [],
      type: Array
    }
  },
  data() {
    return {
      columns: [
        { title: '序号', dataIndex: '', width: 60, key: 'rowIndex', align: 'center', customRender: function (t, r, index) { return parseInt(index) + 1 } },
        { title: '禁止令类型', dataIndex: 'jzllx', scopedSlots: { customRender: 'jzllx' } },
        { title: '禁止令内容', className: 'column-money', dataIndex: 'jzlnr' },
        { title: '禁止期限开始日期', className: 'column-money', dataIndex: 'jzqxksrq' },
        { title: '禁止期限结束日期', className: 'column-money', dataIndex: 'jzqxjsrq' },
        { title: '特定区域坐标', className: 'column-money', dataIndex: 'tdqyzb' }
      ]
    };
  }
};
</script>
<style lang="less" scoped>
th.column-money,
td.column-money {
  text-align: right !important;
}

/deep/ .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

/deep/ .ant-table-body .ant-table-thead > tr > th {
  background: #fafafa !important;
}

</style>
<style lang="less"></style>

<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    bordered>
    <template slot="jtzm" slot-scope="text,record">
      <a-input v-if="!readOnly" @change="handleChange" v-model="record.jtzm"/>
      <div v-else>{{ record.jtzm }}</div>
    </template>

  </a-table>
</template>
  <script>
  export default {
    props: {
      list: {
        default: () => [],
        type: Array
      },
      readOnly: {
        default: false,
        type: Boolean
      }
    },
    data() {
      return {
        columns: [
          {
            title: '序号',
            dataIndex: '',
            width: 60,
            key: 'rowIndex',
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '罪名',
            dataIndex: 'zmName'
          },
          {
            title: '犯罪类型',
            className: 'column-money',
            dataIndex: 'fzlxName'
          },
          {
            title: '具体罪名',
            className: 'column-money',
            dataIndex: 'jtzm',
            scopedSlots: { customRender: 'jtzm' }
          }
        ]
      };
    },
    methods: {
      handleChange() {
        this.$emit('改变', this.list)
        this.$emit('input', this.list)
        this.$emit('onChange', this.list)
        console.log(this.list)
      }
    }
  };
  </script>
  <style lang="less" scoped>
  th.column-money,
  td.column-money {
    text-align: right !important;
  }
 /deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin:  0 ;
  }

  /deep/.ant-table-body .ant-table-thead > tr > th{
    background: #fafafa !important;
  }

  </style>
  <style lang="less"></style>

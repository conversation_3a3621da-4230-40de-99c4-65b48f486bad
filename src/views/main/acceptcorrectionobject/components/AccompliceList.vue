<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    bordered>
    <template slot="name" slot-scope="text">
      <a>{{ text }}</a>
    </template>
    <span slot="dateFormat" slot-scope="text">
      {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
    </span>
    <span slot="xb" slot-scope="text">
      {{ 'xb' | dictType(text) }}
    </span>
    <span slot="zm" slot-scope="text">
      {{ 'SQJZ_FZLX_NEW' | dictType(text) }}
    </span>
  </a-table>
</template>
  <script>

  import moment from 'moment';

  export default {
    props: {
      list: {
        default: () => [],
        type: Array
      }
    },
    data() {
      return {
        columns: [
          {
            title: '序号',
            dataIndex: '',
            width: 60,
            key: 'rowIndex',
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          { title: '姓名', dataIndex: 'xm', scopedSlots: { customRender: 'xm' } },
          { title: '证件类型',
            dataIndex: 'zjlx',
            customRender: function (t, r, index) {
              if (r.zjhm) {
                return '身份证'
              } else {
                return ''
              }
            }
          },
          { title: '证件号码', dataIndex: 'zjhm' },
          { title: '性别', dataIndex: 'xb', scopedSlots: { customRender: 'xb' } },
          { title: '出生日期', dataIndex: 'csrq', scopedSlots: { customRender: 'dateFormat' } },
          { title: '罪名', dataIndex: 'zm', scopedSlots: { customRender: 'zm' } },
          { title: '被判处刑罚及所在监所', dataIndex: 'bpcxfjszjs' }
        ]
      };
    },
    methods: {
      moment
    }
  };
  </script>
  <style lang="less" scoped>
  th.column-money,
  td.column-money {
    text-align: right !important;
  }
 /deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin:  0 ;
  }

  /deep/.ant-table-body .ant-table-thead > tr > th{
    background: #fafafa !important;
  }

  </style>
  <style lang="less"></style>

<template>
  <a-table
    size="small"
    :rowKey="(record) => record.id"
    :pagination="false"
    :columns="columns"
    :data-source="list"
    bordered>
    <span slot="gx" slot-scope="text">
      {{ 'SQJZ_CW2' | dictType(text) }}
    </span>
    <span slot="zjlx" slot-scope="text">
      {{ 'zjlx' | dictType(text) }}
    </span>
    <span slot="xb" slot-scope="text">
      {{ 'xb' | dictType(text) }}
    </span>
  </a-table>
</template>
<script>

import moment from 'moment';

export default {
  props: {
    list: {
      default: () => [],
      type: Array
    }
  },
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: '',
          width: 60,
          key: 'rowIndex',
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '关系',
          dataIndex: 'gx',
          scopedSlots: { customRender: 'gx' }
        },
        {
          title: '姓名',
          dataIndex: 'xm'
        },
        {
          title: '性别',
          dataIndex: 'xb',
          scopedSlots: { customRender: 'xb' }
        },
        {
          title: '出生日期',
          dataIndex: 'csrq',
          customRender: (val) => {
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          title: '证件类型',
          dataIndex: 'lx',
          scopedSlots: { customRender: 'zjlx' }
        },
        {
          title: '证件号码',
          dataIndex: 'zjhm'
        },
        {
          title: '所在单位',
          dataIndex: 'szdw'
        },
        {
          title: '职务',
          dataIndex: 'zw'
        },
        {
          title: '家庭地址',
          dataIndex: 'jtzz'
        },
        {
          title: '联系电话',
          dataIndex: 'lxdh'
        }
      ]
    };
  },
  methods: {
    moment
  }
};
</script>
<style lang="less" scoped>
th.column-money,
td.column-money {
  text-align: right !important;
}

/deep/ .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

/deep/ .ant-table-body .ant-table-thead > tr > th {
  background: #fafafa !important;
}

</style>

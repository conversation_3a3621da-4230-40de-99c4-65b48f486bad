<template>
  <a-drawer
    title="反馈信息"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <div v-if="visible" style="padding-bottom: 0;">

      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper class1" >
          <div class="accept-left">
            <a-form :form="form">
              <a-row :gutter="24">
                <div class="cus-title" style="margin-top: 10px;">案件信息</div>
                <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                <a-col :span="24">
                  <a-form-item label="案件赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['tyfh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="法院案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['fyajbs']" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['ajmc']" />
                  </a-form-item>
                </a-col>
                <div class="cus-title" style="margin-top: 10px;">社区矫正对象信息</div>
                <a-col :span="24">
                  <a-form-item label="公安嫌疑人编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['gaxyrbh']" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['xm']" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <sh-select
                      dictType="zjlx"
                      disabled
                      style="width: 100%;"
                      v-decorator="['zjlx']" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input disabled v-decorator="['zjhm']" />
                  </a-form-item>
                </a-col>
                <div class="cus-title" style="margin-top: 10px;">送达回执信息</div>
                <a-col :span="24">
                  <a-form-item label="入矫结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select
                      :disabled="readOnly"
                      v-decorator="['fkjg', {rules: [{ required: true, message: '请选择入矫结果！' }]}]"
                      style="width: 100%"
                      :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                      @change="handleChangeFkjg">
                      <a-select-option value="1">已入矫</a-select-option>
                      <a-select-option value="0">未入矫</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24" v-if="fkjg==='1'">
                <a-col :span="24">
                  <a-form-item label="社区矫正机构" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-tree-select
                      :disabled="readOnly"
                      v-decorator="['sqjzjg', {rules: [{ required: true, message: '请选择社区矫正机构！' }]}]"
                      style="width: 100%"
                      :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                      :treeData="orgTree">
                    </a-tree-select>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="执行通知书日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker :disabled="readOnly" style="width: 100%" valueFormat="YYYY-MM-DD HH:mm:ss" v-decorator="['jdwssxrq', {rules: [{ required: true, message: '请选择决定文书生效日期！' }]}]" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="入矫日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-date-picker :disabled="readOnly" style="width: 100%" valueFormat="YYYY-MM-DD HH:mm:ss" v-decorator="['rjrq', {rules: [{ required: true, message: '请输选择决定文书生效日期！' }]}]" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="执行通知书回执文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input :disabled="readOnly" placeholder="请输执行通知书回执文号" v-decorator="['zxtzshzwh', {rules: [{ required: true, message: '请输执行通知书回执文号！' }]}]" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-textarea :disabled="readOnly" :rows="4" placeholder="请输入备注" v-decorator="['fkbz']"></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="未入矫原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-textarea :disabled="readOnly" :rows="4" placeholder="未入矫原因" v-decorator="['wnrjyy', {rules: [{ required: true, message: '请输入未入矫原因！' }]}]"></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
            <a-alert style="margin-left: 10%;" message="说明:以上信息均为自动抓取的信息，如需调整请自行进行修改，修改后可点击生成文书，即可按修改后的信息自动生成回执文书。" type="info" show-icon />

          </div>
          <div class="accept-right" >
            <div class="cus-title" >回执文书信息</div>
            <div style="margin-top: 10px;">
              <span style="margin-right: 4px;color: #f5222d;font-size: 14px;">*</span>盖章上传:
              <sh-file-upload
                v-model="hzws"
                :disabled="readOnly"
                :btnText="'附件上传'"
                accept=".pdf"
                uploadUrl="/api/sysFileInfo/uploadOss" >
              </sh-file-upload>
            </div>
            <div v-show="!readOnly" style="color: rgba(255, 0, 0, 1);margin: 10px 0">
              说明：系统提供默认的回执样表供在线签章和样表下载使用，如需修改文书请修改后点击生成文书即可。已授权电子签章用户点击智能签章即可完成附件上传。无法使用电子签章用户可使用样表下载后，线下盖章手动上传pdf格式的盖章附件。如协同单位有特殊回执文件要求，以对方要求为主。
            </div>
            <div class="right-con">
              <a-space style="margin-bottom: 10px;" v-show="!readOnly">
                <a-button :loading="loading" @click="getNoSignPdf()" type="">
                  生成文书
                </a-button>
                <a-button :loading="loading" @click="getSignedPdf()" type="primary" >
                  智能盖章
                </a-button>
                <a-button :loading="loading" @click="getNoSignPdf()" type="">
                  撤销
                </a-button>
                <a-button :loading="loading" @click="handleClickDownLoad2('1769556076487897090')" type="">
                  样表下载
                </a-button>
                <sh-select
                  v-model="sealType"
                  :options="[{name:'矫正机构章',code:'1'},{name:'司法局章',code:'2'}]"
                  style="width: 100%;"
                  :disabled="readOnly"/>
              </a-space>
              <div>
                <embed v-if="pdfUrl" :src="pdfUrl" type="application/pdf" style="width:100%;height:892px" />
              </div>
            </div>
          </div>
        </section>
      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
import pdf from 'vue-pdf';
import moment from 'moment';
import { mapGetters } from 'vuex';
import {
  acceptCorrectionObjectDetail,
  acceptCorrectionObjectFeedback, getHzcl,
  getNoSignPdf4003, getSignedPdf4003
} from '@/api/modular/main/acceptcorrectionobject/acceptCorrectionObjectManage';

export default {
  components: { pdf },
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      fkjg: '1',
      orgTree: [],
      hzws: [],
      pdfUrl: '',
      sealType: '2',
      loading: false,
      isOk: false,
      drawerWidth: 1000,
      readOnly: false,
      record: {},
      detailObj: {},
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    this.drawerWidth = window.innerWidth - 200
  },
  methods: {
    async init(record, orgTree, type) {
      this.visible = true
      this.orgTree = orgTree
      this.hzcl = []
      this.pdfUrl = ''
      this.sealType = window.localStorage.getItem('sealType4003')
      this.record = record
      this.readOnly = !!type;
      this.loadCorrectionObjectData(record.id)
    },
    handleChangeFkjg(e) {
      this.fkjg = e
      this.$nextTick(() => {
        this.loadCorrectionObjectData(this.record.id)
      })
    },
    async handleClickDownLoad2() {
        this.loading = true
        const { form: { validateFields } } = this
        validateFields((errors, values) => {
          if (!errors) {
            const allData = this.form.getFieldsValue()
            allData.rjrq = moment(allData.rjrq).format('YYYY-MM-DD HH:mm:ss')
            allData.jdwssxrq = moment(allData.jdwssxrq).format('YYYY-MM-DD HH:mm:ss')

            getNoSignPdf4003(allData).then((res) => {
              this.loading = false
              this.isOk = false
              // 将 ArrayBuffer 转换为 Blob
              // const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
              // // 通过 Blob 创建一个可用于访问该 Blob 的 URL
              // const pdfUrl = URL.createObjectURL(pdfBlob);
              var blob = new Blob([res.data], { type: 'application/pdf' });
              var filename = '样表'
              var downloadElement = document.createElement('a')
              var href = window.URL.createObjectURL(blob) // 创建下载的链接
              var reg = /^["](.*)["]$/g
              downloadElement.style.display = 'none'
              downloadElement.href = href
              downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
              document.body.appendChild(downloadElement)
              downloadElement.click() // 点击下载
              document.body.removeChild(downloadElement) // 下载完成移除元素
              window.URL.revokeObjectURL(href)
            })
          } else {
            this.loading = false
          }
        })
    },
    async getNoSignPdf() {
      this.loading = true
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          const allData = this.form.getFieldsValue()
          allData.rjrq = moment(allData.rjrq).format('YYYY-MM-DD HH:mm:ss')
          allData.jdwssxrq = moment(allData.jdwssxrq).format('YYYY-MM-DD HH:mm:ss')
          getNoSignPdf4003(allData).then((res) => {
            this.loading = false
            this.isOk = false
            const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
            this.pdfUrl = URL.createObjectURL(pdfBlob)
          })
        } else {
          this.loading = false
        }
      })
    },
    async getSignedPdf() {
      this.loading = true
      const { form: { validateFields } } = this
      validateFields((errors, values) => {
        if (!errors) {
          const allData = this.form.getFieldsValue()
          allData.rjrq = moment(allData.rjrq).format('YYYY-MM-DD HH:mm:ss')
          allData.jdwssxrq = moment(allData.jdwssxrq).format('YYYY-MM-DD HH:mm:ss')
          allData.sealType = this.sealType

          window.localStorage.setItem('sealType4003', this.sealType)
          getSignedPdf4003(allData).then((res) => {
            this.loading = false
            this.isOk = false
            const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
            this.pdfUrl = URL.createObjectURL(pdfBlob)
            getHzcl({ id: allData.id }).then((res) => {
              this.hzws = res.data
            })
          }).finally(() => {
            this.loading = false
          })
        } else {
          this.loading = false
        }
      })
    },
    loadCorrectionObjectData(id) {
      acceptCorrectionObjectDetail({ id: id }).then((res) => {
        if (res.success) {
          setTimeout(() => {
            this.form.setFieldsValue(
              {
                id: res.data.id,
                tyfh: res.data.tyfh,
                xm: res.data.xm,
                fyajbs: res.data.fyajbs || res.data.ajbs,
                ajmc: res.data.ajmc,
                gaxyrbh: res.data.gaxyrbh,
                zjlx: '111',
                zjhm: res.data.zjhm,
                sqjzjg: res.data.jzjg,
                fkjg: res.data.fkjg || this.fkjg,
                fkbz: res.data.fkbz || '',
                wnrjyy: res.data.wnrjyy || '',
                zxtzshzwh: res.data.zxtzshzwh || res.data.sxpjszh
              }
            )

            if (res.data.rjrq != null) {
              this.form.getFieldDecorator('rjrq', { initialValue: moment(res.data.rjrq, 'YYYY-MM-DD') })
            } else {
              this.form.getFieldDecorator('rjrq', { initialValue: moment() })
            }
            if (res.data.jdwssxrq != null) {
              this.form.getFieldDecorator('jdwssxrq', { initialValue: moment(res.data.jdwssxrq, 'YYYY-MM-DD') })
            } else if (res.data.sdsj != null) {
              this.form.getFieldDecorator('jdwssxrq', { initialValue: moment(res.data.sdsj, 'YYYY-MM-DD') })
            } else {
              this.form.getFieldDecorator('jdwssxrq', { initialValue: moment() })
            }
            this.getNoSignPdf()
          }, 100)
        }
      })
    },
    handleSubmit() {
      if (!this.hzws.length) {
        this.$message.error('请上传盖章文件')
        return false
      }
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          values.fkr = this.userInfo.name
          values.fksj = moment().format('YYYY-MM-DD HH:mm:ss')
          values.rjrq = moment(values.rjrq).format('YYYY-MM-DD HH:mm:ss')
          values.jdwssxrq = moment(values.jdwssxrq).format('YYYY-MM-DD HH:mm:ss')
          for (const key in values) {
            if (typeof (values[key]) === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          if (this.hzws?.length) {
            values.hzcl = this.hzws.map(item => item.id).toString()
          }
          acceptCorrectionObjectFeedback(values).then((res) => {
            if (res.success) {
              this.$message.success('提交成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            } else {
              this.$message.error('提交失败')// + res.message
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.detailObj = null
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
// /deep/.ant-drawer-body{
//   padding: ;
// }
.accept-wrapper {
  display: flex;
  width: 100%;
  flex-flow: column;

  &.class1 {
    flex-flow: row;

    .accept-left {
      flex: 1;
      overflow: hidden;
    }

    .accept-right {
      flex: 1;
      overflow: hidden;
      border-left: 1px solid #e8e8e8;

      .right-con {
        min-height: 500px;
        background: #EEEDED;
        padding: 10px;
      }
    }
  }

  .accept-left {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
    padding: 20px;
  }

  .accept-right {
    padding: 20px;
    // margin-top: 44px;
    position: relative;
    //   max-width: 300px;
    // padding-top: 20px;

    // .titlesd{
    //   position: absolute;
    //   top: -44px;
    //   left: -1px;
    //   line-height: 43px;
    //   border-bottom: 1px solid #e8e8e8;
    //   width: 100%;
    //   font-size: 16px;
    // font-family: Microsoft YaHei, Microsoft YaHei;
    // font-weight: bold;
    // color: #1890FF;
    // padding-left: 20px;
    // }
  }
}

/deep/ .ant-drawer-body {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 53px;
}

.cus-title-d {
  font-size: 20px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  // padding: 0 0 10px 0;
}

.cus-title {

  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  // margin-bottom: 20px;
  padding-left: 10px;
  font-size: 20px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  color: #333333;

  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

<template>
  <a-drawer
    title="交付衔接协同"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel"
  >
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper">
          <div class="accept-left">
            <a-tabs default-active-key="1">
              <a-tab-pane key="1" tab="基本信息">
                <BaseInfo :readOnly="true" ref="BaseInfo" :baseObj="detailObj" />
              </a-tab-pane>
              <a-tab-pane key="2" tab="法律文书信息" force-render>
                <LegDocInfo :readOnly="true" ref="LegDocInfo" :baseObj="detailObj" />
              </a-tab-pane>
              <a-tab-pane key="3" tab="刑罚执行信息" force-render>
                <PenaltyExecuInfo :readOnly="true" ref="PenaltyExecuInfo" :baseObj="detailObj" />
              </a-tab-pane>
              <a-tab-pane
                key="4"
                v-if="['XTBH4022_1', 'XTBH4052_1', 'XTBH4072_1'].includes(record.xtbh)"
                tab="暂予监外信息"
                force-render
              >
                <OutsideInfo :readOnly="true" ref="OutsideInfo" :baseObj="detailObj" />
              </a-tab-pane>
            </a-tabs>
          </div>
          <div class="accept-right">
            <!-- <div class="titlesd">流程记录</div> -->
            <StepVue :steps="steps" />
          </div>
        </section>

        <div class="cus-title-d" style="margin-top: 20px;">区县司法局审核信息</div>
        <AduitForm :readOnly="readOnly" :baseObj="detailObj" ref="AduitForm" />
      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px'
      }"
    >
      <a-button style="marginRight: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
// 基本信息
import BaseInfo from '@/views/main/acceptcorrectionobject/components/BaseInfo'
// 法律文书信息
import LegDocInfo from '@/views/main/acceptcorrectionobject/components/LegDocInfo'
// 刑罚执行信息
import PenaltyExecuInfo from '@/views/main/acceptcorrectionobject/components/PenaltyExecuInfo'
// 暂予监外信息
import OutsideInfo from '@/views/main/acceptcorrectionobject/components/OutsideInfo'
// 审核信息
import AduitForm from '@/views/main/acceptcorrectionobject/components/AduitForm'
// 步骤
import StepVue from './components/Step.vue'
import {
  acceptBaseDetail,
  acceptBaseEdit,
  acceptBaseStep,
  checkPsn
} from '@/api/modular/main/acceptcorrectionobject/acceptCorrectionObjectManage'

export default {
  components: { BaseInfo, LegDocInfo, PenaltyExecuInfo, OutsideInfo, AduitForm, StepVue },
  data() {
    return {
      drawerWidth: 1000,
      steps: [],
      readOnly: false,
      extOrgInfoData: {}, // 矫正决定机关
      record: {},
      detailObj: {},
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  provide() {
    return {
      parentData: this.$data
    }
  },
  created() {
    this.drawerWidth = window.innerWidth - 200
    this.getExtOrgInfoList()
  },
  methods: {
    // 外部单位信息列表
    getExtOrgInfoList() {
      extOrgInfoList().then(res => {
        // 法院
        const fy = []
        // 公安
        const ga = []
        // 检察院
        const jcy = []
        res.data.forEach(v => {
          if (v.type === 20) {
            fy.push(v)
          } else if (v.type === 40) {
            ga.push(v)
          } else if (v.type === 30) {
            jcy.push(v)
          }
        })
        this.extOrgInfoData = { fy, ga, jcy }
      })
    },
    // 初始化方法
    add(record, type) {
      this.record = record
      this.readOnly = !!type
      this.visible = true
      this.getDetail()
    },
    async getDetail() {
      this.detailObj = {}
      acceptBaseDetail({ id: this.record.id, xtbh: this.record.xtbh }).then(res => {
        if (res.success) {
          this.detailObj = res.data
          if (res.data.forbidList && res.data.forbidList.length) {
            this.detailObj.sfbxgjzl = res.data.forbidList[0].sfbxgjzl
          } else {
            this.detailObj.sfbxgjzl = '0'
          }
        } else {
          this.$message.error(res.message)
        }
      })
      acceptBaseStep({ id: this.record.id, xtbh: this.record.xtbh }).then(res => {
        if (res.success) {
          this.steps = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /**
     * 提交表单
     */
    async handleSubmit() {
      // 基本信息
      const data1 = await this.$refs.BaseInfo.handleSubmit()

      // 法律文书信息
      const data2 = await this.$refs.LegDocInfo.handleSubmit()
      // 刑罚执行信息
      const data3 = await this.$refs.PenaltyExecuInfo.handleSubmit()
      // 审核表单
      const data = await this.$refs.AduitForm.handleSubmit()
      data.hzcl = data.hzcl ? data.hzcl.map(item2 => item2.id).toString() : ''

      let sendWd = ''
      if (data.shjg === '1') {
        // 审核通过的 检查是否已推送万达
        sendWd = await this.check(data1.zjhm)
      }

      data.shjgName = this.$options.filters['dictType']('shjg', data.shjg)
      this.confirmLoading = true
      let postData = {}
      postData.id = this.record.id

      if (['XTBH4022_1', 'XTBH4052_1', 'XTBH4072_1'].includes(this.record.xtbh)) {
        // 暂予监外信息
        const data4 = await this.$refs.OutsideInfo.handleSubmit()
        postData = {
          xtbh: this.record.xtbh,
          id: this.record.id,
          sendWd: sendWd,
          top: {
            ...data1,
            ...data2,
            ...data3,
            ...data4,
            ...data
          }
        }
      } else {
        postData = {
          xtbh: this.record.xtbh,
          id: this.record.id,
          sendWd: sendWd,
          obj: {
            ...data1,
            ...data2,
            ...data3,
            ...data
          }
        }
      }

      acceptBaseEdit(postData)
        .then(res => {
          if (res.success) {
            this.$message.success('操作成功')
            this.confirmLoading = false
            this.$emit('ok', postData)
            this.handleCancel()
          } else {
            this.$message.error('操作失败' + res.message) // + res.message
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    check(zjhm) {
      return new Promise((resolve, reject) => {
        checkPsn({ zjhm: zjhm })
          .then(res => {
            if (res.data) {
              this.$confirm({
                title: '提示',
                content: '该社区矫正矫正对象已在执法办案系统入矫，无法进行二次入矫，是否需要更新一体化协同文书 ?',
                okText: '确定',
                cancelText: '不需要',
                onOk: () => {
                  resolve(res.data)
                },
                onCancel() {
                  resolve('')
                }
              })
            } else {
              resolve('all')
            }
            // eslint-disable-next-line handle-callback-err
          })
          .catch(error => {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject('all')
          })
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.accept-wrapper {
  display: flex;
  width: 100%;
  .accept-left {
    flex: 1;
    overflow: auto;
  }
  .accept-right {
    border-left: 1px solid #e8e8e8;
    // margin-top: 44px;
    position: relative;
    max-width: 300px;
  }
}
/deep/ .ant-drawer-body {
  padding-top: 0;
  padding-bottom: 78px;
}

.cus-title {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;

  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

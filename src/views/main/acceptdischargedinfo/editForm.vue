<template>
  <a-modal
    title="编辑释放信息接收表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <!-- 步骤条 -->
    <div style="margin-bottom:20px">
      <a-steps v-model:current="current" type="navigation" :style="stepStyle" >
        <a-step title="个人基本信息" />
        <a-step title="案件信息" />
      </a-steps>
    </div>
    <!--基本信息-->
    <div v-show="current===0">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <!--个人信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >个人信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                <a-form-item
                  label="嫌疑人编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zfbh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="曾用名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['cym']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xb']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="民族"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['mz']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['csrq']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="证件类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zjlx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="证件号码"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zjhm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="犯罪时是否未成年"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fzssfwcn']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="未成年"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['wcn']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否有精神病"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfyjsb']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="鉴定机构"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['jdjg']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否有传染病"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfycrb']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="传染病类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['crblx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否累犯"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sflf']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否残疾"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfcj']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否暴力犯"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfblf']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否病犯"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfbf']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否老年犯"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sflnf']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="文化程度"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['whcd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="婚姻状况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['hyzk']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="职业"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zy']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="职务"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zw']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="职级"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zj']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="政治面貌"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zzmm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="工作单位/所在学校"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['gzdwszxx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="单位联系电话"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['dwlxdh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="单位/学校所在地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['dwxxszd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="个人联系电话"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['grlxdh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="国籍"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['gj']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出生地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['csd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现住地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xzd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现住地详址"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xzdxz']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="户籍地"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['hjd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="户籍地详址"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['hjdxz']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--家庭成员及社会关系-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >家庭成员及社会关系</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <a-form-item
                  label="有无家庭成员及主要社会关系"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-radio-group :disabled="true" v-decorator="['ywjtcyjzyshgx']" >
                    <a-radio v-for="(item,index) in yesOrNoData" :key="index" :value="item.code">{{ item.name }}</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <s-table
                  ref="table"
                  :columns="jtColumns"
                  :data="jtLoadData"
                  :alert="false"
                  :rowKey="(record) => record.code"
                >
                </s-table>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-spin>
    </div>
    <!--案件信息-->
    <div v-show="current===1">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <!--案件信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >案件信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="统一赋号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['tyfh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="监狱案件编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['jyajbh']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--同案犯信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >同案犯信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <s-table
                  ref="table"
                  :columns="tafColumns"
                  :data="tafLoadData"
                  :alert="false"
                  :rowKey="(record) => record.code"
                >
                </s-table>
              </a-col>
            </a-row>
          </div>
          <!--前科信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >前科信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <s-table
                  ref="table"
                  :columns="qkColumns"
                  :data="qkLoadData"
                  :alert="false"
                  :rowKey="(record) => record.code"
                >
                </s-table>
              </a-col>
            </a-row>
          </div>
          <!--判决信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >判决信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="生效判决书字号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['pjwswh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="判决文书生效日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['pjrq']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="生效判决机关"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sxpjjg']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="有期徒刑期限"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['ypxq']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="判决罪名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['pjzm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="判决其他罪名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['pjqtzm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否数罪并罚"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfszbf']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="主刑"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原刑期起日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['yxqqr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="原刑期止日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['yxqzr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24">
                <a-form-item
                  label="附加刑"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fjx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="附加刑具体情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fjxjtqk']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="缓刑考验期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['hxkyq']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="财产性判项"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['ccxpx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="具体罚款金额"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fjjewy']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="没收财产金额"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['msccjewy']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="其他财产性判项金额"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['qtccxpxjewy']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否“五独”"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfwd']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否“五涉”"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfws']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否有“四史”"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfyss']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--刑罚执行信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >刑罚执行信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="刑罚执行日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['xfzxrq']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="入监日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['rjrq']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现刑期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xxq']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现刑期起日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['xxqqr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现刑期止日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['xxqzr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="已服刑期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['yfxq']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="剩余刑期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['syxq']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--释放信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >释放信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="释放类别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sflb']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="假释考验期起日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['jskyqqr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="假释考验期止日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['jskyqzr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否剥政"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfbz']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现剥政期限"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xbzqx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现剥政起日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['xbzqr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="现剥政止日"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['xbzzr']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="技术特长及等级证书"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['jstcjdjzs']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="服刑期间奖惩情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fxqjjcqk']" />
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24">
                <a-form-item
                  label="服刑期间刑种、刑期变动情况"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fxxqbdqk']" />
                </a-form-item>
              </a-col>
              <a-col :md="24" :sm="24">
                <a-form-item
                  label="服刑期间表现"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['fxqjbx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="释放后住址"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfhzz']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="安置帮教工作办公室"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['azbjgzbgs']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="帮教人员类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['bjrylx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="出监日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['cjrq']"/>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--文书信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >文书信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <s-table
                  ref="table"
                  :columns="wsColumns"
                  :data="wsLoadData"
                  :alert="false"
                  :rowKey="(record) => record.code"
                >
                </s-table>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-spin>
    </div>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { STable } from '@/components'
  import {
    acceptCorrectionAccomplicePage,
    acceptCorrectionDocPage,
    acceptCorrectionFamilyPage,
    acceptCriminalRecordPage
  } from '@/api/modular/main/subList/subListManage';
  export default {
    components: {
      STable
    },
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        current: 0,
        stepStyle: {
          marginBottom: '10px',
          boxShadow: '0px -1px 0 0 #e8e8e8 inset'
        },
        yesOrNoData: [
          { code: '1', name: '是' },
          { code: '0', name: '否' }
        ],
        // 表头 家庭成员及社会关系
        jtColumns: [
          {
          ellipsis: true,
          title: '关系',
            dataIndex: 'gx'
          },
          {
          ellipsis: true,
          title: '姓名',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '性别',
            dataIndex: 'xb'
          },
          {
          ellipsis: true,
          title: '出生日期',
            dataIndex: 'csrq'
          },
          {
          ellipsis: true,
          title: '证件类型',
            dataIndex: 'lx'
          },
          {
          ellipsis: true,
          title: '证件号码',
            dataIndex: 'zjhm'
          },
          {
          ellipsis: true,
          title: '所在单位',
            dataIndex: 'szdw'
          },
          {
          ellipsis: true,
          title: '职务',
            dataIndex: 'zw'
          },
          {
          ellipsis: true,
          title: '家庭地址',
            dataIndex: 'jtzz'
          },
          {
          ellipsis: true,
          title: '联系电话',
            dataIndex: 'lxdh'
          }
        ],
        jtLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionFamilyPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 同案犯信息
        tafColumns: [
          {
          ellipsis: true,
          title: '姓名',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '证件类型',
            dataIndex: 'zjlx'
          },
          {
          ellipsis: true,
          title: '证件号码',
            dataIndex: 'zjhm'
          },
          {
          ellipsis: true,
          title: '性别',
            dataIndex: 'xb'
          },
          {
          ellipsis: true,
          title: '出生日期',
            dataIndex: 'csrq'
          },
          {
          ellipsis: true,
          title: '罪名',
            dataIndex: 'zm'
          },
          {
          ellipsis: true,
          title: '刑种',
            dataIndex: 'xz'
          },
          {
          ellipsis: true,
          title: '刑期',
            dataIndex: 'xq'
          },
          {
          ellipsis: true,
          title: '被判处刑罚及所在监所',
            dataIndex: 'bpcxfjszjs'
          }
        ],
        tafLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionAccomplicePage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 前科信息
        qkColumns: [
          {
          ellipsis: true,
          title: '刑种',
            dataIndex: 'xz'
          },
          {
          ellipsis: true,
          title: '判决日期',
            dataIndex: 'pjrq'
          },
          {
          ellipsis: true,
          title: '判决法院',
            dataIndex: 'pjfy'
          },
          {
          ellipsis: true,
          title: '罪名',
            dataIndex: 'zm'
          },
          {
          ellipsis: true,
          title: '原判刑期',
            dataIndex: 'ypxq'
          },
          {
          ellipsis: true,
          title: '执行机关',
            dataIndex: 'zxjg'
          },
          {
          ellipsis: true,
          title: '执行刑期',
            dataIndex: 'zxxq'
          },
          {
          ellipsis: true,
          title: '备注',
            dataIndex: 'bz'
          }
        ],
        qkLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCriminalRecordPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        // 表头 文书
        wsColumns: [
          {
          ellipsis: true,
          title: '法律文书名称',
            dataIndex: 'ws'
          },
          {
          ellipsis: true,
          title: '附件',
            dataIndex: 'wsdm'
          }
        ],
        wsLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionDocPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        this.recordId = record.id
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              taskId: record.taskId,
              zt: record.zt,
              sjlylx: record.sjlylx,
              jsdw: record.jsdw,
              jsdwmc: record.jsdwmc,
              tsdw: record.tsdw,
              tsdwmc: record.tsdwmc,
              tyfh: record.tyfh,
              jyajbh: record.jyajbh,
              zfbh: record.zfbh,
              xm: record.xm,
              cym: record.cym,
              xb: record.xb,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              fzssfwcn: record.fzssfwcn,
              wcn: record.wcn,
              sfyjsb: record.sfyjsb,
              jdjg: record.jdjg,
              sfycrb: record.sfycrb,
              crblx: record.crblx,
              gj: record.gj,
              mz: record.mz,
              gzdwszxx: record.gzdwszxx,
              dwxxszd: record.dwxxszd,
              dwlxdh: record.dwlxdh,
              grlxdh: record.grlxdh,
              csd: record.csd,
              hjd: record.hjd,
              hjdxz: record.hjdxz,
              xzd: record.xzd,
              xzdxz: record.xzdxz,
              whcd: record.whcd,
              hyzk: record.hyzk,
              sf: record.sf,
              zzmm: record.zzmm,
              zy: record.zy,
              zw: record.zw,
              zj: record.zj,
              sflf: record.sflf,
              sfblf: record.sfblf,
              sflnf: record.sflnf,
              sfcj: record.sfcj,
              sfbf: record.sfbf,
              sxpjjg: record.sxpjjg,
              pjwswh: record.pjwswh,
              pjzm: record.pjzm,
              pjqtzm: record.pjqtzm,
              sfszbf: record.sfszbf,
              zx: record.zx,
              ypxq: record.ypxq,
              fjx: record.fjx,
              fjxjtqk: record.fjxjtqk,
              hxkyq: record.hxkyq,
              ccxpx: record.ccxpx,
              fjjewy: record.fjjewy,
              msccjewy: record.msccjewy,
              qtccxpxjewy: record.qtccxpxjewy,
              bdzzqlqx: record.bdzzqlqx,
              sfwd: record.sfwd,
              sfws: record.sfws,
              sfyss: record.sfyss,
              xxq: record.xxq,
              yfxq: record.yfxq,
              syxq: record.syxq,
              sflb: record.sflb,
              sfbz: record.sfbz,
              xbzqx: record.xbzqx,
              jstcjdjzs: record.jstcjdjzs,
              fxqjjcqk: record.fxqjjcqk,
              fxxqbdqk: record.fxxqbdqk,
              fxqjbx: record.fxqjbx,
              sfhzz: record.sfhzz,
              azbjgzbgs: record.azbjgzbgs,
              bjrylx: record.bjrylx
            }
          )
        }, 100)
        // 时间单独处理
        if (record.csrq != null) {
          this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        if (record.pjrq != null) {
          this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        if (record.yxqqr != null) {
          this.form.getFieldDecorator('yxqqr', { initialValue: moment(record.yxqqr, 'YYYY-MM-DD') })
        }
        if (record.yxqzr != null) {
          this.form.getFieldDecorator('yxqzr', { initialValue: moment(record.yxqzr, 'YYYY-MM-DD') })
        }
        if (record.xfzxrq != null) {
          this.form.getFieldDecorator('xfzxrq', { initialValue: moment(record.xfzxrq, 'YYYY-MM-DD') })
        }
        if (record.rjrq != null) {
          this.form.getFieldDecorator('rjrq', { initialValue: moment(record.rjrq, 'YYYY-MM-DD') })
        }
        if (record.xxqqr != null) {
          this.form.getFieldDecorator('xxqqr', { initialValue: moment(record.xxqqr, 'YYYY-MM-DD') })
        }
        if (record.xxqzr != null) {
          this.form.getFieldDecorator('xxqzr', { initialValue: moment(record.xxqzr, 'YYYY-MM-DD') })
        }
        if (record.jskyqqr != null) {
          this.form.getFieldDecorator('jskyqqr', { initialValue: moment(record.jskyqqr, 'YYYY-MM-DD') })
        }
        if (record.jskyqzr != null) {
          this.form.getFieldDecorator('jskyqzr', { initialValue: moment(record.jskyqzr, 'YYYY-MM-DD') })
        }
        if (record.xbzqr != null) {
          this.form.getFieldDecorator('xbzqr', { initialValue: moment(record.xbzqr, 'YYYY-MM-DD') })
        }
        if (record.xbzzr != null) {
          this.form.getFieldDecorator('xbzzr', { initialValue: moment(record.xbzzr, 'YYYY-MM-DD') })
        }
        if (record.cjrq != null) {
          this.form.getFieldDecorator('cjrq', { initialValue: moment(record.cjrq, 'YYYY-MM-DD') })
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

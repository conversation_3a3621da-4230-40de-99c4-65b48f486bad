<template>
  <a-modal
    title="新增释放信息接收表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label=""
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入" v-decorator="['taskId', {rules: [{required: true, message: '请输入！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据状态" v-decorator="['zt', {rules: [{required: true, message: '请输入数据状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据来源(机构类型)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据来源(机构类型)" v-decorator="['sjlylx', {rules: [{required: true, message: '请输入数据来源(机构类型)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="接收单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入接收单位" v-decorator="['jsdw', {rules: [{required: true, message: '请输入接收单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="接收单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入接收单位名称" v-decorator="['jsdwmc', {rules: [{required: true, message: '请输入接收单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="推送单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入推送单位" v-decorator="['tsdw', {rules: [{required: true, message: '请输入推送单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="推送单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入推送单位名称" v-decorator="['tsdwmc', {rules: [{required: true, message: '请输入推送单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="送达时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择送达时间" v-decorator="['sdsj',{rules: [{ required: true, message: '请选择送达时间！' }]}]" @change="onChangesdsj"/>
        </a-form-item>
        <a-form-item
          label="统一赋号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入统一赋号" v-decorator="['tyfh', {rules: [{required: true, message: '请输入统一赋号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="监狱案件编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入监狱案件编号" v-decorator="['jyajbh', {rules: [{required: true, message: '请输入监狱案件编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="罪犯编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入罪犯编号" v-decorator="['zfbh', {rules: [{required: true, message: '请输入罪犯编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="曾用名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入曾用名" v-decorator="['cym', {rules: [{required: true, message: '请输入曾用名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类型" v-decorator="['zjlx', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出生日期" v-decorator="['csrq',{rules: [{ required: true, message: '请选择出生日期！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label="犯罪时是否未成年"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入犯罪时是否未成年" v-decorator="['fzssfwcn', {rules: [{required: true, message: '请输入犯罪时是否未成年！'}]}]" />
        </a-form-item>
        <a-form-item
          label="未成年"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入未成年" v-decorator="['wcn', {rules: [{required: true, message: '请输入未成年！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有精神病"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有精神病" v-decorator="['sfyjsb', {rules: [{required: true, message: '请输入是否有精神病！'}]}]" />
        </a-form-item>
        <a-form-item
          label="鉴定机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入鉴定机构" v-decorator="['jdjg', {rules: [{required: true, message: '请输入鉴定机构！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有传染病"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有传染病" v-decorator="['sfycrb', {rules: [{required: true, message: '请输入是否有传染病！'}]}]" />
        </a-form-item>
        <a-form-item
          label="传染病类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入传染病类型" v-decorator="['crblx', {rules: [{required: true, message: '请输入传染病类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="国籍"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入国籍" v-decorator="['gj', {rules: [{required: true, message: '请输入国籍！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民族"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民族" v-decorator="['mz', {rules: [{required: true, message: '请输入民族！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工作单位/所在学校"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工作单位/所在学校" v-decorator="['gzdwszxx', {rules: [{required: true, message: '请输入工作单位/所在学校！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位/学校所在地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位/学校所在地" v-decorator="['dwxxszd', {rules: [{required: true, message: '请输入单位/学校所在地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位联系电话"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位联系电话" v-decorator="['dwlxdh', {rules: [{required: true, message: '请输入单位联系电话！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人联系电话"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人联系电话" v-decorator="['grlxdh', {rules: [{required: true, message: '请输入个人联系电话！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生地" v-decorator="['csd', {rules: [{required: true, message: '请输入出生地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地" v-decorator="['hjd', {rules: [{required: true, message: '请输入户籍地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地详址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地详址" v-decorator="['hjdxz', {rules: [{required: true, message: '请输入户籍地详址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现住地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入现住地" v-decorator="['xzd', {rules: [{required: true, message: '请输入现住地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现住地详址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入现住地详址" v-decorator="['xzdxz', {rules: [{required: true, message: '请输入现住地详址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文化程度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文化程度" v-decorator="['whcd', {rules: [{required: true, message: '请输入文化程度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻状况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻状况" v-decorator="['hyzk', {rules: [{required: true, message: '请输入婚姻状况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份" v-decorator="['sf', {rules: [{required: true, message: '请输入身份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="政治面貌"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入政治面貌" v-decorator="['zzmm', {rules: [{required: true, message: '请输入政治面貌！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职业"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职业" v-decorator="['zy', {rules: [{required: true, message: '请输入职业！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职务"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职务" v-decorator="['zw', {rules: [{required: true, message: '请输入职务！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职级" v-decorator="['zj', {rules: [{required: true, message: '请输入职级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否累犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否累犯" v-decorator="['sflf', {rules: [{required: true, message: '请输入是否累犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否暴力犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否暴力犯" v-decorator="['sfblf', {rules: [{required: true, message: '请输入是否暴力犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否老年犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否老年犯" v-decorator="['sflnf', {rules: [{required: true, message: '请输入是否老年犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否残疾"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否残疾" v-decorator="['sfcj', {rules: [{required: true, message: '请输入是否残疾！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否病犯"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否病犯" v-decorator="['sfbf', {rules: [{required: true, message: '请输入是否病犯！'}]}]" />
        </a-form-item>
        <a-form-item
          label="生效判决机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入生效判决机关" v-decorator="['sxpjjg', {rules: [{required: true, message: '请输入生效判决机关！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决文书文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入判决文书文号" v-decorator="['pjwswh', {rules: [{required: true, message: '请输入判决文书文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择判决日期" v-decorator="['pjrq',{rules: [{ required: true, message: '请选择判决日期！' }]}]" @change="onChangepjrq"/>
        </a-form-item>
        <a-form-item
          label="判决罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入判决罪名" v-decorator="['pjzm', {rules: [{required: true, message: '请输入判决罪名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="判决其他罪名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入判决其他罪名" v-decorator="['pjqtzm', {rules: [{required: true, message: '请输入判决其他罪名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否数罪并罚"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否数罪并罚" v-decorator="['sfszbf', {rules: [{required: true, message: '请输入是否数罪并罚！'}]}]" />
        </a-form-item>
        <a-form-item
          label="主刑"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入主刑" v-decorator="['zx', {rules: [{required: true, message: '请输入主刑！'}]}]" />
        </a-form-item>
        <a-form-item
          label="原判刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入原判刑期" v-decorator="['ypxq', {rules: [{required: true, message: '请输入原判刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="原刑期起日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原刑期起日" v-decorator="['yxqqr',{rules: [{ required: true, message: '请选择原刑期起日！' }]}]" @change="onChangeyxqqr"/>
        </a-form-item>
        <a-form-item
          label="原刑期止日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择原刑期止日" v-decorator="['yxqzr',{rules: [{ required: true, message: '请选择原刑期止日！' }]}]" @change="onChangeyxqzr"/>
        </a-form-item>
        <a-form-item
          label="附加刑"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附加刑" v-decorator="['fjx', {rules: [{required: true, message: '请输入附加刑！'}]}]" />
        </a-form-item>
        <a-form-item
          label="附加刑具体情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入附加刑具体情况" v-decorator="['fjxjtqk', {rules: [{required: true, message: '请输入附加刑具体情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="缓刑考验期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入缓刑考验期" v-decorator="['hxkyq', {rules: [{required: true, message: '请输入缓刑考验期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="财产性判项"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入财产性判项" v-decorator="['ccxpx', {rules: [{required: true, message: '请输入财产性判项！'}]}]" />
        </a-form-item>
        <a-form-item
          label="罚金金额（万元）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入罚金金额（万元）" v-decorator="['fjjewy', {rules: [{required: true, message: '请输入罚金金额（万元）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="没收财产金额（万元）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入没收财产金额（万元）" v-decorator="['msccjewy', {rules: [{required: true, message: '请输入没收财产金额（万元）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="其他财产性判项金额（万元）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入其他财产性判项金额（万元）" v-decorator="['qtccxpxjewy', {rules: [{required: true, message: '请输入其他财产性判项金额（万元）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="剥夺政治权利期限"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入剥夺政治权利期限" v-decorator="['bdzzqlqx', {rules: [{required: true, message: '请输入剥夺政治权利期限！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否“五独”"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否“五独”" v-decorator="['sfwd', {rules: [{required: true, message: '请输入是否“五独”！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否“五涉”"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否“五涉”" v-decorator="['sfws', {rules: [{required: true, message: '请输入是否“五涉”！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否有“四史”"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否有“四史”" v-decorator="['sfyss', {rules: [{required: true, message: '请输入是否有“四史”！'}]}]" />
        </a-form-item>
        <a-form-item
          label="刑罚执行日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择刑罚执行日期" v-decorator="['xfzxrq',{rules: [{ required: true, message: '请选择刑罚执行日期！' }]}]" @change="onChangexfzxrq"/>
        </a-form-item>
        <a-form-item
          label="入监日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择入监日期" v-decorator="['rjrq',{rules: [{ required: true, message: '请选择入监日期！' }]}]" @change="onChangerjrq"/>
        </a-form-item>
        <a-form-item
          label="现刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入现刑期" v-decorator="['xxq', {rules: [{required: true, message: '请输入现刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现刑期起日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择现刑期起日" v-decorator="['xxqqr',{rules: [{ required: true, message: '请选择现刑期起日！' }]}]" @change="onChangexxqqr"/>
        </a-form-item>
        <a-form-item
          label="现刑期止日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择现刑期止日" v-decorator="['xxqzr',{rules: [{ required: true, message: '请选择现刑期止日！' }]}]" @change="onChangexxqzr"/>
        </a-form-item>
        <a-form-item
          label="已服刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入已服刑期" v-decorator="['yfxq', {rules: [{required: true, message: '请输入已服刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="剩余刑期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入剩余刑期" v-decorator="['syxq', {rules: [{required: true, message: '请输入剩余刑期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="释放类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入释放类别" v-decorator="['sflb', {rules: [{required: true, message: '请输入释放类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="假释考验期起日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择假释考验期起日" v-decorator="['jskyqqr',{rules: [{ required: true, message: '请选择假释考验期起日！' }]}]" @change="onChangejskyqqr"/>
        </a-form-item>
        <a-form-item
          label="假释考验期止日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择假释考验期止日" v-decorator="['jskyqzr',{rules: [{ required: true, message: '请选择假释考验期止日！' }]}]" @change="onChangejskyqzr"/>
        </a-form-item>
        <a-form-item
          label="是否剥政"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否剥政" v-decorator="['sfbz', {rules: [{required: true, message: '请输入是否剥政！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现剥政期限"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入现剥政期限" v-decorator="['xbzqx', {rules: [{required: true, message: '请输入现剥政期限！'}]}]" />
        </a-form-item>
        <a-form-item
          label="现剥政起日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择现剥政起日" v-decorator="['xbzqr',{rules: [{ required: true, message: '请选择现剥政起日！' }]}]" @change="onChangexbzqr"/>
        </a-form-item>
        <a-form-item
          label="现剥政止日"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择现剥政止日" v-decorator="['xbzzr',{rules: [{ required: true, message: '请选择现剥政止日！' }]}]" @change="onChangexbzzr"/>
        </a-form-item>
        <a-form-item
          label="技术特长及等级证书"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入技术特长及等级证书" v-decorator="['jstcjdjzs', {rules: [{required: true, message: '请输入技术特长及等级证书！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服刑期间奖惩情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服刑期间奖惩情况" v-decorator="['fxqjjcqk', {rules: [{required: true, message: '请输入服刑期间奖惩情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服刑期间刑种、刑期变动情况"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服刑期间刑种、刑期变动情况" v-decorator="['fxxqbdqk', {rules: [{required: true, message: '请输入服刑期间刑种、刑期变动情况！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服刑期间表现"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服刑期间表现" v-decorator="['fxqjbx', {rules: [{required: true, message: '请输入服刑期间表现！'}]}]" />
        </a-form-item>
        <a-form-item
          label="释放后住址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入释放后住址" v-decorator="['sfhzz', {rules: [{required: true, message: '请输入释放后住址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="安置帮教工作办公室"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入安置帮教工作办公室" v-decorator="['azbjgzbgs', {rules: [{required: true, message: '请输入安置帮教工作办公室！'}]}]" />
        </a-form-item>
        <a-form-item
          label="帮教人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入帮教人员类型" v-decorator="['bjrylx', {rules: [{required: true, message: '请输入帮教人员类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出监日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择出监日期" v-decorator="['cjrq',{rules: [{ required: true, message: '请选择出监日期！' }]}]" @change="onChangecjrq"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { acceptDischargedInfoAdd } from '@/api/modular/main/acceptdischargedinfo/acceptDischargedInfoManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        sdsjDateString: '',
        csrqDateString: '',
        pjrqDateString: '',
        yxqqrDateString: '',
        yxqzrDateString: '',
        xfzxrqDateString: '',
        rjrqDateString: '',
        xxqqrDateString: '',
        xxqzrDateString: '',
        jskyqqrDateString: '',
        jskyqzrDateString: '',
        xbzqrDateString: '',
        xbzzrDateString: '',
        cjrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdsj = this.sdsjDateString
            values.csrq = this.csrqDateString
            values.pjrq = this.pjrqDateString
            values.yxqqr = this.yxqqrDateString
            values.yxqzr = this.yxqzrDateString
            values.xfzxrq = this.xfzxrqDateString
            values.rjrq = this.rjrqDateString
            values.xxqqr = this.xxqqrDateString
            values.xxqzr = this.xxqzrDateString
            values.jskyqqr = this.jskyqqrDateString
            values.jskyqzr = this.jskyqzrDateString
            values.xbzqr = this.xbzqrDateString
            values.xbzzr = this.xbzzrDateString
            values.cjrq = this.cjrqDateString
            acceptDischargedInfoAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdsj(date, dateString) {
        this.sdsjDateString = dateString
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangeyxqqr(date, dateString) {
        this.yxqqrDateString = dateString
      },
      onChangeyxqzr(date, dateString) {
        this.yxqzrDateString = dateString
      },
      onChangexfzxrq(date, dateString) {
        this.xfzxrqDateString = dateString
      },
      onChangerjrq(date, dateString) {
        this.rjrqDateString = dateString
      },
      onChangexxqqr(date, dateString) {
        this.xxqqrDateString = dateString
      },
      onChangexxqzr(date, dateString) {
        this.xxqzrDateString = dateString
      },
      onChangejskyqqr(date, dateString) {
        this.jskyqqrDateString = dateString
      },
      onChangejskyqzr(date, dateString) {
        this.jskyqzrDateString = dateString
      },
      onChangexbzqr(date, dateString) {
        this.xbzqrDateString = dateString
      },
      onChangexbzzr(date, dateString) {
        this.xbzzrDateString = dateString
      },
      onChangecjrq(date, dateString) {
        this.cjrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

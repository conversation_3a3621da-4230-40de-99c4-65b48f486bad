<template>
  <div>
    <a-drawer title="详情" width="85%" :visible="visible" @close="onClose" :footer="null">
      <a-tabs v-model="activeTab" @change="changeTab">
        <a-tab-pane key="1" tab="迁入审核信息">
          <auditDetail ref="auditDetail" />
        </a-tab-pane>
        <a-tab-pane key="2" tab="移送档案及入矫信息" v-if="detailData.processStatus > 2">
          <file ref="file" :disabled="disabled" />
        </a-tab-pane>
      </a-tabs>
    </a-drawer>
  </div>
</template>
<script>
import auditDetail from './components/auditDetail.vue'
import file from './components/file.vue'
import { placechangeTransProvDetail } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
export default {
  components: {
    auditDetail,
    file
  },
  data() {
    return {
      visible: false,
      disabled: true,
      activeTab: '1',
      detailData: {}
    }
  },
  methods: {
    changeTab(val) {
      console.log(val)
      if (val === '1') {
        this.$nextTick(() => {
          this.$refs.auditDetail.initFormData(this.detailData)
        })
      } else if (val === '2') {
        this.$nextTick(() => {
          this.$refs.file.initFormData(this.detailData)
        })
      }
    },
    open(record) {
      this.visible = true
      placechangeTransProvDetail({ id: record.id }).then(res => {
        this.detailData = res.data
        this.$nextTick(() => {
          this.$refs.auditDetail.initFormData(this.detailData)
        })
      })
    },
    onClose() {
      this.visible = false
      this.detailData = {}
      this.activeTab = '1'
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-input-disabled,
/deep/.ant-cascader-picker-disabled {
  color: #222;
}
</style>

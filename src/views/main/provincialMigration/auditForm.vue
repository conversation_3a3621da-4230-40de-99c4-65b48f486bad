<template>
  <a-drawer title="跨省迁入审核" width="80%" :visible="visible" :confirmLoading="confirmLoading" @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <detail ref="detail" />
        <div class="cus-title-d">外省迁入审核信息</div>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="接收结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                placeholder="请选择"
                v-decorator="['opinionResult', { rules: [{ required: true, message: '请选择 ！' }] }]"
                :options="resultOption"
              >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :span="24">
            <a-form-item label="情况说明" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <a-input
                placeholder="请输入"
                v-decorator="[
                  'opinionRemark',
                  {
                    rules: [
                      { required: form.getFieldValue('opinionResult') === '2' ? true : false, message: '请输入 ！' }
                    ]
                  }
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['deptContactPsn', { rules: [{ required: true, message: '请输入 ！' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['deptContactTel', { rules: [{ required: true, message: '请输入 ！' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['deptName', { rules: [{ required: true, message: '请输入 ！' }] }]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="意见反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                placeholder="请选择"
                style="width: 100%"
                v-decorator="['opinionDate', { rules: [{ required: true, message: '请选择 ！' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="执行地变更复函" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <sh-file-upload
                placeholder="请选择"
                uploadUrl="/api/sysFileInfo/uploadOss"
                :fileList="form.getFieldValue('fileList')"
                accept=".pdf"
                v-decorator="['fileList', { rules: [{ required: true, message: '请选择 ！' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            说明：审核结果会通过跨部门办案系统告知迁出单位
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <div
      v-if="!disabled"
      :style="{
        position: 'absolute',
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff'
      }"
    >
      <a-button style="margin-right: 10px;" @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleOk">提交</a-button>
    </div>
  </a-drawer>
</template>
<script>
import moment from 'moment'
import detail from '@/views/main/executeRelocation/components/detail'
import {
  placechangeTransProvDetail,
  placechangeTransProvAudit
} from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
export default {
  components: { detail },
  data() {
    return {
      moment,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      resultOption: [
        { label: '同意接收', value: '1' },
        { label: '不同意接收', value: '2' }
      ],
      detailData: {}
    }
  },
  methods: {
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    },
    open(record) {
      this.visible = true
      placechangeTransProvDetail({ id: record.id }).then(res => {
        this.detailData = res.data
        this.$nextTick(() => {
          this.form.setFieldsValue({
            ...this.detailData,
            opinionDate: moment().format('YYYY-MM-DD')
          })
          this.$refs.detail.initFormData(this.detailData)
        })
      })
    },
    handleOk() {
      this.form.validateFieldsAndScroll((err, values) => {
        if (!err) {
          this.confirmLoading = true
          const params = { ...values, id: this.detailData.id }
          params.files =
            values.fileList && values.fileList.length > 0 ? Array.from(values.fileList, item => item.id).toString() : ''
          placechangeTransProvAudit(params)
            .then(res => {
              this.confirmLoading = false
              this.$message.success('提交成功')
              this.handleCancel()
              this.$emit('ok')
            })
            .catch(() => {
              this.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-input-disabled,
/deep/.ant-cascader-picker-disabled {
  color: #222;
}
</style>

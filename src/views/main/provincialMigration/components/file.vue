<template>
  <div>
    <a-form :form="form">
      <div class="cus-title-d">
        社矫对象信息
<!--        <span class="title-right">-->
<!--          目前该社区矫正跨省迁出只支持贵州省-->
<!--        </span>-->
      </div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input allow-clear disabled v-decorator="['uniformCode']" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input allow-clear disabled v-decorator="['correctionObjName']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['nation']">
              <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['certType']">
              <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['certNum']" />
          </a-form-item>
        </a-col>
      </a-row>
      <div class="cus-title-d">移送档案信息</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="移送单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['extDeptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactPsn']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactTel']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="移送日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['transDate']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="移送档案目录" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <!-- <sh-file-upload disabled v-decorator="['fileList']" /> -->
            <a-table
              ref="fileTable"
              :columns="columns"
              :data-source="fileList"
              :pagination="false"
              :rowKey="record => record.id"
            >
              <span slot="fileOriginName" slot-scope="text, record">
                <a href="javascript:;" @click="openFile(record.url)">
                  {{ record.name }}
                </a>
              </span>
            </a-table>
          </a-form-item>
        </a-col>
      </a-row>
      <div class="cus-title-d">外省迁入报到情况反馈</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="入矫报到情况" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select
              :disabled="disabled"
              placeholder="请选择"
              v-decorator="['registrationResult', { rules: [{ required: true, message: '请选择 ！' }] }]"
              :options="resultOption"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="入矫日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              :disabled="disabled"
              placeholder="请选择"
              format="YYYY-MM-DD"
              v-decorator="['correctionStartAt', , { rules: [{ required: true, message: '请选择 ！' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="现矫正机构" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-tree-select
              :disabled="disabled"
              v-decorator="['deptId', { rules: [{ required: true, message: '请选择矫正机构 ！' }] }]"
              style="width: 100%"
              :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
              :treeData="orgTree"
              placeholder="请选择"
            >
            </a-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              :disabled="disabled"
              placeholder="请选择"
              format="YYYY-MM-DD"
              v-decorator="['opinionDate', { rules: [{ required: true, message: '请选择 ！' }] }]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="是否需同步执法办案" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select
              :disabled="disabled"
              placeholder="请选择"
              v-decorator="['sync', { rules: [{ required: true, message: '请选择 ！' }] }]"
              :options="syncOption"
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="24">
          说明：提交后，报到信息会反馈至外省迁出单位。选择同步“执法办案”系统后，迁入对象的文书和信息会自动同步至“执法办案”的入矫模块。
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'
import { getOrgTree } from '@/api/modular/system/orgManage'
import { mapGetters } from 'vuex'
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      orgTree: [],
      zjlxDropDown: [],
      mzDropDown: [],
      fileList: [],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      syncOption: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      resultOption: [
        { label: '在规定时间内报到', value: '1' },
        { label: '超出规定时限报到', value: '2' },
        { label: '未报到且下落不明', value: '3' },
        { label: '其它', value: '4' }
      ],
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          align: 'center',
          width: 80,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '文书名称',
          dataIndex: 'fileOriginName',
          align: 'center',
          scopedSlots: { customRender: 'fileOriginName' }
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.getOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    openFile(url) {
      window.open(url, '_blank')
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    initFormData(data) {
      this.$nextTick(() => {
        this.id = data.id
        this.form.setFieldsValue(data)
        this.form.setFieldsValue({
          opinionDate: moment().format('YYYY-MM-DD'),
          deptCode: this.userInfo.loginEmpInfo.orgId,
          correctionStartAt: moment().format('YYYY-MM-DD')
        })
        const fileList =
          data.files46003 && data.files46003.length > 0
            ? data.files46003.map(item => {
                return { uid: item.id, name: item.fileOriginName, status: 'done', url: item.filePath }
              })
            : []
        this.fileList = fileList
      })
    },
    resetForm() {
      this.form.resetFields()
      this.fileList = []
    },
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.mzDropDown = this.$options.filters['dictData']('mz')
    },
    getValues() {
      let formData = null
      this.form.validateFields((err, values) => {
        if (!err) {
          values.id = this.id
          formData = values
        } else {
          formData = null
        }
      })
      return formData
    }
  }
}
</script>
<style lang="less" scoped>
.cus-title-d {
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

<template>
  <div>
    <a-form :form="form">
      <detail ref="detail" />
      <div class="cus-title-d">外省迁入审核信息</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="接收结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['opinionResult']" :options="resultOption"> </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="24">
          <a-form-item label="情况说明" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-input disabled v-decorator="['opinionRemark']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactPsn']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactTel']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="意见反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['opinionDate']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="执行地变更复函" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-file-upload disabled v-decorator="['fileList']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          说明：审核结果会通过跨部门办案系统告知迁出单位
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>
<script>
import detail from '@/views/main/executeRelocation/components/detail'
export default {
  components: { detail },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      resultOption: [
        { label: '同意接收', value: '1' },
        { label: '不同意接收', value: '2' }
      ]
    }
  },
  mounted() {},
  methods: {
    initFormData(data) {
      if (data.processStatus === '1') {
        data.deptContactPsn = ''
        data.deptContactTel = ''
        data.deptName = ''
      }
      this.form.setFieldsValue({
        ...data
      })
      const fileList =
        data.files46002 && data.files46002.length > 0
          ? data.files46002.map(item => {
              return { uid: item.id, name: item.fileOriginName, status: 'done', url: item.filePath }
            })
          : []
      this.form.setFieldsValue({
        fileList
      })
      this.$refs.detail.initFormData(data)
    }
  }
}
</script>
<style lang="less" scoped></style>

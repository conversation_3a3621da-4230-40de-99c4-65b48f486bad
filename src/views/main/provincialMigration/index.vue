<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.deptId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select style="width: 100%" v-model="queryParam.processStatus" placeholder="请选择状态">
                  <a-select-option v-for="(item, index) in dcpgztData" :key="index" :value="item.code">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.correctionObjName" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="handleSearch">查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}; $refs.tableEmigration.refresh(true) ">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <!--跨省迁出-->
      <s-table
        ref="tableEmigration"
        :columns="columnsEmigration"
        :data="loadEmigrationData"
        :alert="false"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <x-down ref="batchExport" @batchExport="batchExport" />
          <span style="margin-left:16px;">说明：待接收迁入档案状态无需操作，外省移送档案后自动更改状态为“待反馈入矫情况”</span>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="record.processStatus === '1'" @click="$refs.auditForm.open(record)">审核</a>
          <a-divider v-if="record.processStatus === '1'" type="vertical" />
          <a v-if="record.processStatus === '3'" @click="clickFeedback(record)">反馈</a>
          <a-divider v-if="record.processStatus === '3'" type="vertical" />
          <a @click="$refs.detailsForm.open(record, true)">详情</a>
        </span>
        <span slot="processStatus" slot-scope="text">
          {{ 'place_change_imm_status' | dictType(text) }}
        </span>
        <span slot="changeReason" slot-scope="text">
          <ellipsis :length="30" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="extDeptName" slot-scope="text">
          <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="deptName" slot-scope="text">
          <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="registrationResult" slot-scope="text, record">
          <span v-if="record.registrationResult === '1'">在规定时间内报到</span>
          <span v-if="record.registrationResult === '2'">超出规定时限报到</span>
          <span v-if="record.registrationResult === '3'">未报到且下落不明</span>
          <span v-if="record.registrationResult === '4'">其他</span>
        </span>
      </s-table>
    </a-card>
    <auditForm ref="auditForm" @ok="$refs.tableEmigration.refresh(true)" />
    <detailsForm ref="detailsForm" />
    <a-drawer
      title="跨省迁入反馈"
      width="80%"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @close="handleCancel"
    >
      <file ref="file" />
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff'
        }"
      >
        <a-button style="margin-right: 10px;" @click="handleCancel">关闭</a-button>
        <a-button type="primary" @click="handleSubmit">提交</a-button>
      </div>
    </a-drawer>
  </div>
</template>
<script>
import file from './components/file.vue'
import auditForm from './auditForm.vue'
import detailsForm from './detailsForm.vue'
import { STable, XDown, Ellipsis } from '@/components'
import moment from 'moment'
import {
  placechangeTransProvList,
  placechangeTransProvFeedback,
  placechangeTransProvDetail, placechangeTransProvExport
} from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
export default {
  components: {
    STable,
    XDown,
    Ellipsis,
    auditForm,
    detailsForm,
    file
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      // 查询参数
      queryParam: {},
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      selectedRowKeys: [],
      selectedRows: [],
      // 表头 跨省迁出
      columnsEmigration: [
        {
          title: '状态',
          align: 'center',
          dataIndex: 'processStatus',
          scopedSlots: { customRender: 'processStatus' }
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'correctionObjName'
        },
        {
          title: '迁出单位',
          align: 'center',
          dataIndex: 'extDeptName',
          scopedSlots: { customRender: 'extDeptName' }
        },
        {
          title: '迁入单位',
          align: 'center',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'deptName' }
        },
        {
          title: '申请时间',
          align: 'center',
          dataIndex: 'applicationDate',
          customRender: val => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          title: '审核结果',
          align: 'center',
          dataIndex: 'changeReason',
          scopedSlots: { customRender: 'changeReason' }
        },
        {
          title: '入矫情况',
          align: 'center',
          dataIndex: 'registrationResult',
          scopedSlots: { customRender: 'registrationResult' }
        },
        {
          title: '迁出省(市)',
          align: 'center',
          dataIndex: 'destinationProvName'
        },
        {
          title: '送达时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          // width: '150px',
          align: 'center',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 跨省迁出 加载数据方法 必须为 Promise 对象
      loadEmigrationData: parameter => {
        return placechangeTransProvList(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      orgTree: [],
      dcpgztData: [],
      bdztData: []
    }
  },
  created() {
    this.getOrgTree()
    this.dataTypeItem()
  },
  methods: {
    moment,
    /**
     * 批量导出
     */
    batchExport() {
      placechangeTransProvExport(this.switchingDate()).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      this.queryParam.transType = '1'
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD ')
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD ')
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      delete this.queryParam.dates
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    handleSearch() {
      this.$refs.tableEmigration.refresh(true)
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /**
     * 获取字典数据
     */
    dataTypeItem() {
      this.dcpgztData = this.$options.filters['dictData']('place_change_imm_status')
    },
    handleCancel() {
      this.visible = false
      this.confirmLoading = false
      this.$refs.file.resetForm()
    },
    handleSubmit() {
      this.confirmLoading = true
      const formData = this.$refs.file.getValues()
      console.log(formData, 22222)
      if (formData) {
        placechangeTransProvFeedback(formData).then(res => {
          if (res.success) {
            this.$message.success(res.message)
            this.$refs.tableEmigration.refresh(true)
            this.confirmLoading = false
            this.visible = false
          } else {
            this.$message.warning(res.message)
            this.confirmLoading = false
          }
        })
      }
    },
    clickFeedback(record) {
      this.visible = true
      placechangeTransProvDetail({ id: record.id }).then(res => {
        if (res.success) {
          this.$nextTick(() => {
            this.$refs.file.initFormData(res.data)
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-input-disabled,
/deep/.ant-cascader-picker-disabled {
  color: #222;
}
/deep/.ant-drawer-body {
  height: calc(100% - 100px);
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
</style>

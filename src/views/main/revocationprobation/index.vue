<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label=" 姓名 ">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名 "/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="征求建议日期">
                <a-date-picker style="width: 100%" placeholder="请选择征求建议日期" v-model="queryParam.zqyjrqDate" @change="onChangezqyjrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label=" 提请日期 ">
                <a-date-picker style="width: 100%" placeholder="请选择 提请日期 " v-model="queryParam.tqrqDate" @change="onChangetqrq"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="提请法院名称">
                <a-input v-model="queryParam.fymc" allow-clear placeholder="请输入提请法院名称"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="提请状态">
                <a-select v-model="queryParam.zt" placeholder="提请状态" >
                  <a-select-option v-for="(item,index) in ztDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"

        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" >
          <a-button v-if="hasRole(['SFSYWRY','SFSSZ'])" type="primary" icon="plus" @click="$refs.addFormNew.add('','xz')">新增提请撤销缓刑</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => revocationProbationDelete(record)">
            <a>删除</a>
          </a-popconfirm>
          <a-divider type="vertical"></a-divider>
          <a @click="$refs.addFormNew.view(record)">详情</a>
          <a-divider type="vertical"></a-divider>
          <!-- 新增	xz
          司法所合议审核	sh
          区县局评议审核	sh1
          区县局审批	sp1
          市局评议审核	sh2
          市局审批	sp2
          省厅评议审核	sh3
          省厅审批	sp3
          征求检查建议	zqjcjy"SFSWRY
          接收检查建议	jsjcjy		 -->

          <a v-if="(record.zt==='xz'||record.zt==='sh') && hasRole(['SFSYWRY','SFSSZ'])" @click="$refs.addFormNew.add(record,'xz')">编辑</a>
          <a-divider v-if="record.zt==='sh' && hasRole(['SFSYWRY','SFSSZ'])" type="vertical"></a-divider>
          <a v-if="record.zt==='sh' && hasRole(['SFSSZ'])" @click="$refs.addFormNew.add(record)">审核</a>
          <a v-if="record.zt==='sh1' && hasRole(['QXSFJKZ'])" @click="$refs.addFormNew.add(record)">区县局评议审核</a>
          <a v-if="record.zt==='sp1' && hasRole(['QXSFJJLD'])" @click="$refs.addFormNew.add(record)">区县局审批</a>
          <a v-if="record.zt==='sh2' && hasRole(['SSFJYWRY','SSFJYWSHRY'])" @click="$refs.addFormNew.add(record)">市局评议审核		</a>
          <a v-if="record.zt==='sp2' && hasRole(['SSFJJLD'])" @click="$refs.addFormNew.add(record)">市局审批</a>
          <a v-if="record.zt==='sh3' && hasRole(['SSFTYWRY','SSFTYWSHRY'])" @click="$refs.addFormNew.add(record)">省厅评议审核		</a>
          <a v-if="record.zt==='sp3' && hasRole(['SSFTTLD'])" @click="$refs.addFormNew.add(record)">省厅审批</a>
          <a v-if="record.zt==='zqjcjy' && hasRole(['QXSFJKZ','QXSFJJLD'])" @click="$refs.addFormNew.add(record)">征求检查建议</a>
          <a v-if="record.zt==='tqcx' && hasRole(['QXSFJKZ','QXSFJJLD'])" @click="$refs.addFormNew.add(record)">提请撤销缓刑</a>
        </span>
        <span slot="timeFormat" slot-scope="text">
          {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
        </span>
        <span slot="zt" slot-scope="text">
          {{ 'cxtqjd' | dictType(text) }}
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
      <addFormNew ref="addFormNew" @ok="handleOk"/>
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { revocationProbationPage, revocationProbationDelete } from '@/api/modular/main/revocationprobation/revocationProbationManage'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  import addFormNew from './addFormNew.vue'
  export default {
    components: {
      STable,
      editForm,
      addFormNew
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
         { ellipsis: true, title: '提请状态', align: 'center', dataIndex: 'zt', scopedSlots: { customRender: 'zt' } },
         { ellipsis: true, title: ' 姓名 ', align: 'center', dataIndex: 'xm' },
         { ellipsis: true, title: '矫正单位', align: 'center', dataIndex: 'jzdwmc' },
         { ellipsis: true, title: '征求建议日期', align: 'center', dataIndex: 'zqyjrq', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: ' 提请日期 ', align: 'center', dataIndex: 'tqrq', scopedSlots: { customRender: 'timeFormat' } },
         { ellipsis: true, title: '征求检察院名称', align: 'center', dataIndex: 'jcymc' },
         { ellipsis: true, title: '提请法院名称', align: 'center', dataIndex: 'fymc' },
         { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return revocationProbationPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        ztDropDown: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
     if (this.$route.query.zt) {
        this.queryParam.zt = this.$route.query.zt
      }
      this.getOrgTree()
      this.sysDictTypeDropDown()
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamzqyjrq = this.queryParam.zqyjrqDate
        if (queryParamzqyjrq != null) {
            this.queryParam.zqyjrq = moment(queryParamzqyjrq).format('YYYY-MM-DD')
            if (queryParamzqyjrq.length < 1) {
                delete this.queryParam.zqyjrq
            }
        }
        const queryParamtqrq = this.queryParam.tqrqDate
        if (queryParamtqrq != null) {
            this.queryParam.tqrq = moment(queryParamtqrq).format('YYYY-MM-DD')
            if (queryParamtqrq.length < 1) {
                delete this.queryParam.tqrq
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      revocationProbationDelete (record) {
        revocationProbationDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      onChangezqyjrq(date, dateString) {
        this.zqyjrqDateString = dateString
      },
      onChangetqrq(date, dateString) {
        this.tqrqDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      sysDictTypeDropDown () {
        this.ztDropDown = this.$options.filters['dictData']('cxtqjd')
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

<template>
  <a-drawer
    title="提请撤销缓刑"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer custom-wrapper"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible">
      <a-spin :spinning="confirmLoading">
        <StepBar
          :currentNode="detailObj.zt"
          :process-nodes="processNodes"
          v-if="record && record.id"
          v-model="currentStep" />
        <section class="section-wrapper">
          <div class="section-left">
            <!-- 新增 -->
            <addFormPage :readOnly="isDisabled('xz')" v-if="currentStep === 'xz'" ref="addFormPage" />
            <!-- 初审合议 -->
            <InitialJudicialReviewFormPage :disabled="isDisabled('sh')" v-if="currentStep === 'sh'" ref="InitialJudicialReviewFormPage" />
            <!-- 区 -->
            <!-- 初审评议  -->
            <InitialReviewEvaluationFormPage
              :disabled="isDisabled('sh1')"
              :detail="detailObj.phase.sh1"
              v-if="currentStep === 'sh1'"
              ref="InitialReviewEvaluationFormPage" />
            <!-- 审批 -->
            <ApprovalForm :disabled="isDisabled('sp1')" :detail="detailObj.phase.sp1" v-if="currentStep === 'sp1'" ref="ApprovalForm" />
            <!-- 市 -->
            <!-- 初审评议  -->
            <InitialReviewEvaluationFormPage :disabled="isDisabled('sh2')" :detail="detailObj.phase.sh2" v-if="currentStep === 'sh2'" ref="sh2" />
            <!-- 审批 -->
            <ApprovalForm :disabled="isDisabled('sp2')" :detail="detailObj.phase.sp2" v-if="currentStep === 'sp2'" ref="sp2" />
            <!-- 省 -->
            <!-- 初审评议  -->
            <InitialReviewEvaluationFormPage :disabled="isDisabled('sh3')" :detail="detailObj.phase.sh3" v-if="currentStep === 'sh3'" ref="sh3" />
            <!-- 审批 -->
            <ApprovalForm :disabled="isDisabled('sp3')" :detail="detailObj.phase.sp3" v-if="currentStep === 'sp3'" ref="sp3" />

            <!-- 征求检察建议 -->
            <SolicitingProsecutorialAdvice
              :disabled="isDisabled('zqjcjy')"
              v-if="currentStep === 'zqjcjy'"
              ref="SolicitingProsecutorialAdvice" />
            <!-- 接收检察建议 -->
            <ReceiveProsecutorialAdvice
              :disabled="isDisabled('jsjcjy')"
              v-if="currentStep === 'jsjcjy'"
              ref="ReceiveProsecutorialAdvice" />
            <!-- 提请撤销缓刑 -->
            <MotionToRevokeProbation
              :disabled="isDisabled('tqcx')"
              v-if="currentStep === 'tqcx'"
              ref="MotionToRevokeProbation" />
            <!-- 处理结果 -->
            <ProcessingResult v-if="currentStep === 'cljg'" ref="ProcessingResult" />
            <!-- 文书打印 -->
            <DocumentPrinting v-if="currentStep === 9" ref="DocumentPrinting" />
          </div>
          <div class="section-right">
            <StepVue :steps="taskInfoList"></StepVue>
          </div>

        </section>

      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-space>
        <a-button @click="handleCancel">
          取消
        </a-button>

        <a-button v-if="!['cljg','jsjcjy'].includes(currentStep)" :loading="confirmLoading" type="primary" @click="handleSubmit">
          提交
        </a-button>
      </a-space>

    </div>
  </a-drawer>
</template>

<script>
// 步骤
import StepVue from './components/Step.vue';
import StepBar from '@/views/main/revocationprobation/components/StepBar'
// 新增
import addFormPage from '@/views/main/revocationprobation/addFormPage/index';
// 初审合议
import InitialJudicialReviewFormPage from '@/views/main/revocationprobation/InitialJudicialReviewFormPage/index';
// 初审评议
import InitialReviewEvaluationFormPage from '@/views/main/revocationprobation/InitialReviewEvaluationFormPage/index';
// 区县局审批
import ApprovalForm from '@/views/main/revocationprobation/ApprovalForm'
// 征求检察建议
import SolicitingProsecutorialAdvice from '@/views/main/revocationprobation/SolicitingProsecutorialAdvice'
// 接收检察建议

import ReceiveProsecutorialAdvice from '@/views/main/revocationprobation/ReceiveProsecutorialAdvice'
// 提请撤销缓刑
import MotionToRevokeProbation from '@/views/main/revocationprobation/MotionToRevokeProbation'

// 处理结果
import ProcessingResult from '@/views/main/revocationprobation/ProcessingResult'
// 文书打印

import DocumentPrinting from '@/views/main/revocationprobation/DocumentPrinting'
import { fileList } from '@/api/modular/system/fileManage'

export default {
  components: { StepBar, StepVue, addFormPage, InitialJudicialReviewFormPage, InitialReviewEvaluationFormPage, ApprovalForm, SolicitingProsecutorialAdvice, ReceiveProsecutorialAdvice, ProcessingResult, MotionToRevokeProbation, DocumentPrinting },
  data() {
    return {
      currentStep: 'xz',
      drawetWidth: 1000,
      readOnly: false,
      record: {},
      detailObj: {},
      visible: false,
      taskInfoList: [],
      processNodes: '',
      stepList: [],
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  provide() {
    return {
      parentData: this.$data
    }
  },
  created() {
    this.drawetWidth = window.innerWidth
  },
  methods: {
    isDisabled(code) {
      if (this.stepList.indexOf(code) < this.stepList.indexOf(this.detailObj.zt)) {
        return true
      } else {
        return false
      }
    },
    view(record, type = '') {
      this.readOnly = true
      if (record.processInstanceId == null) {
        type = 'xz'
      }
      this.add(record, type)
    },
    // 初始化方法
    async add(record, type) {
      // currentStep: 'xz',
      // drawetWidth: 1000,
      // readOnly: false,
      // record: {},
      // detailObj: {},
      // visible: false,
      // taskInfoList: [],
      // processNodes: '',

      this.currentStep = 'xz'
      this.detailObj = {}
      this.taskInfoList = []
      this.processNodes = ''
      this.stepList = []

      this.record = record

      if (record) {
        const data = await this.getDetail()
        this.processNodes = data.processNodes
        this.stepList = data.processNodes.split(',')
        this.taskInfoList = data.taskInfoList
        console.log(this.processNodes)
        // 需要转换的字段
        // postData.files1 = postData.files1.map(item => item.id).toString()
        //   postData.tqyjP1 = postData.tqyjP1.toString()
        data.files1 && (data.files1 = await this.getFileList(data.files1))
        data.files2 && (data.files2 = await this.getFileList(data.files2))
        data.files3 && (data.files3 = await this.getFileList(data.files3))
        data.files4 && (data.files4 = await this.getFileList(data.files4))
        data.files5 && (data.files5 = await this.getFileList(data.files5))
        data.tqyjP1 && (data.tqyjP1 = data.tqyjP1.split(','))
        data.tqyj && (data.tqyj = data.tqyj.split(','))

        const phase = data.phase
        // 使用Object.entries方法获取对象的键值对数组，然后对数组进行解构赋值
        for (const [key, obj] of Object.entries(phase)) {
          // 使用Object.entries方法获取对象的键值对数组，然后对数组进行解构赋值
          for (const [fileKey, fileValue] of Object.entries(obj)) {
            if (fileKey.startsWith('file') && fileValue && fileValue.trim() !== '') {
              // 将文件属性拆分并存储为数组
              obj[fileKey] = await this.getFileList(fileValue)
            }
          }
        }
        if (type) {
          this.currentStep = type
        } else {
          this.currentStep = data.zt
        }
        this.detailObj = data
      }

      this.visible = true
    },
    async getFileList(ids) {
      const { data } = await fileList({ ids })
      return Promise.resolve(data.map(item => {
        return {
          uid: -item.id,
          status: 'done',
          ...item
        }
      }
      ))
      // this.form.setFieldsValue({
      //   ws: data.map(item => {
      //     return { ...item, id: item.uid }
      //   })
      // })
    },
    async getDetail() {
      const { data } = await this.$http({
        url: '/revocationProbation/detail',
        params: {
          id: this.record.id
        }
      })
      if (data.zt === 'ywc') {
        data.zt = 'cljg'
      }
      return Promise.resolve(data)
    },
    handleSuccess(res) {
      if (res.success) {
        this.$message.success('操作成功')
        this.confirmLoading = false
        this.$emit('ok')
        this.handleCancel()
      } else {
        this.$message.error('操作失败' + res.message)// + res.message
      }
    },
    /**
     * 提交表单
     */
    async handleSubmit() {
      let postData = {}

      if (!this.record || !this.record.id || this.currentStep === 'xz') {
        postData = await this.$refs.addFormPage.handleSubmit()
        this.confirmLoading = true
        postData.files1 && (postData.files1 = postData.files1.map(item => item.id).toString())
        postData.tqyjP1 = postData.tqyjP1.toString()
        delete postData.xm
        delete postData.jzlb
        delete postData.jzjg
        delete postData.sfzh
        let url = '/revocationProbation/add'
        if (this.record && this.currentStep === 'xz') {
          url = '/revocationProbation/edit'
        }
        this.record && (postData.id = this.record.id)
        this.$http({
          url,
          method: 'post',
          data: postData
        }).then(res => this.handleSuccess(res)).finally((res) => {
          this.confirmLoading = false
        })
        return false
      }
      // 新增	xz
      //     司法所合议审核	sh
      //     区县局评议审核	sh1
      //     区县局审批	sp1
      //     市局评议审核	sh2
      //     市局审批	sp2
      //     省厅评议审核	sh3
      //     省厅审批	sp3
      //     征求检查建议	zqjcjy
      //     接收检查建议	jsjcjy
      // if()
      if (this.detailObj.zt === 'sh') {
        postData = await this.$refs.InitialJudicialReviewFormPage.handleSubmit()
        postData.file1 && (postData.file1 = postData.file1.map(item => item.id).toString())
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())

        // InitialJudicialReviewFormPage
      }
      if (this.detailObj.zt === 'sh1') {
        postData = await this.$refs.InitialReviewEvaluationFormPage.handleSubmit()
        postData.file1 && (postData.file1 = postData.file1.map(item => item.id).toString())
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())

        // InitialJudicialReviewFormPage
      }
      if (this.detailObj.zt === 'sp1') {
        postData = await this.$refs.ApprovalForm.handleSubmit()
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())
      }
      if (this.detailObj.zt === 'sh2') {
        postData = await this.$refs.sh2.handleSubmit()
        postData.file1 && (postData.file1 = postData.file1.map(item => item.id).toString())
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())

        // InitialJudicialReviewFormPage
      }
      if (this.detailObj.zt === 'sp2') {
        postData = await this.$refs.sp2.handleSubmit()
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())
      }
      if (this.detailObj.zt === 'sh3') {
        postData = await this.$refs.sh3.handleSubmit()
        postData.file1 && (postData.file1 = postData.file1.map(item => item.id).toString())
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())

        // InitialJudicialReviewFormPage
      }
      if (this.detailObj.zt === 'sp3') {
        postData = await this.$refs.sp3.handleSubmit()
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())
      }
      let _url = '/revocationProbation/audit'
      // zqjcjy,jsjcjy,tqcx
      if (['zqjcjy', 'jsjcjy', 'tqcx'].includes(this.detailObj.zt)) {
        _url = '/revocationProbation/submit'
      }
      //
      if (this.detailObj.zt === 'zqjcjy') {
        postData = await this.$refs.SolicitingProsecutorialAdvice.handleSubmit()
        postData.file2 && (postData.file2 = postData.file2.map(item => item.id).toString())
      }
      if (this.detailObj.zt === 'jsjcjy') {
        postData = await this.$refs.ReceiveProsecutorialAdvice.handleSubmit()
        postData.file3 && (postData.file3 = postData.file3.map(item => item.id).toString())
      }
      if (this.detailObj.zt === 'tqcx') {
        postData = await this.$refs.MotionToRevokeProbation.handleSubmit()
        postData.file4 && (postData.file4 = postData.file4.map(item => item.id).toString())
      }

      postData.id = this.record.id
      postData.tqyj && (postData.tqyj = postData.tqyj.toString())

      this.confirmLoading = true
      this.$http({
        url: _url,
        method: 'post',
        data: postData
      }).then(res => this.handleSuccess(res)).finally((res) => {
        this.confirmLoading = false
      })
    },

    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.readOnly = false
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.has-footer /deep/ .ant-drawer-body {

  padding-bottom: 78px;
}

.cus-title {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;

  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}

.section-wrapper {
  display: flex;

  .section-right {
    width: 300px;
  }

  .section-left {
    flex: 1;
    overflow: hidden;
  }
}
</style>

<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="社区矫正对象" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['xm', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="撤销缓刑决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            style="width: 100%;"
            api="/extOrgInfo/list?type=20"
            disabled
            placeholder="请选择"
            labelKey="orgName"
            valueKey="orgCode"
            v-decorator="['cxhxjdjg', {initialValue:'', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="是否撤销缓刑" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            disabled
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['sfjdcxhx', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="决定原因" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            disabled
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['cxhxyy', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="文书" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-file-upload
            disabled
            v-decorator="['files5', {initialValue:[], rules: [{ required: false, message: '请输入' }] }]"
            uploadUrl="/api/sysFileInfo/uploadOss" />
        </a-form-item>
      </a-col>
      <!-- <a-col :span="12">
        <a-form-item label="裁定时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
           disabled
            v-decorator="['shsj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="裁定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
           disabled
            v-decorator="['shsj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="抄送" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
           disabled
            v-decorator="['shsj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col> -->

    </a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

  <script>

  import { mapGetters } from 'vuex'
  import moment from 'moment'
  function isEmpty(obj) {
      if (!obj) {
          return false
      } else {
          return Object.keys(obj).length === 0;
      }
  }
  export default {
      components: {},
      props: {
          readOnly: {
              default: false,
              type: Boolean
          },
          baseObj: {
              default: () => { },
              type: Object

          }
      },
      computed: {
          ...mapGetters(['userInfo'])
      },
      data() {
          return {
              flagType: false,
              labelCol: {
                  xs: { span: 24 },
                  sm: { span: 6 }
              },
              wrapperCol: {
                  xs: { span: 24 },
                  sm: { span: 16 }
              },
              labelCol2: {
                  xs: { span: 24 },
                  sm: { span: 3 }
              },
              wrapperCol2: {
                  xs: { span: 24 },
                  sm: { span: 20 }
              },
              shjgTree: [
                  {
                      children: [{
                          children: [],
                          id: '11',
                          parentId: '1',
                          title: '信息无误，确认接收',
                          value: '11'
                      }, {
                          children: [],
                          id: '12',
                          parentId: '1',
                          title: '存在部分信息错误，确认接收',
                          value: '12'
                      }],
                      id: '1',
                      parentId: '0',
                      title: '正常接收',
                      disabled: true,
                      value: '1'
                  }, {
                      children: [{
                          children: [],
                          id: '21',
                          parentId: '2',
                          title: '信息错误，非我区管辖矫正对象',
                          value: '21'
                      }, {
                          children: [],
                          id: '22',
                          parentId: '2',
                          title: '数据滞后，已办理手续',
                          value: '22'
                      }, {
                          children: [],
                          id: '23',
                          parentId: '2',
                          title: '数据重复发送',
                          value: '23'
                      }],
                      id: '2',
                      parentId: '0',
                      title: '退回',
                      disabled: true,
                      value: '2'
                  }, {
                      children: [],
                      id: '3',
                      parentId: '0',
                      title: '其他',
                      value: '3'

                  }],
              form: this.$form.createForm(this)
          }
      },
       async mounted() {
          if (!isEmpty(this.baseObj)) {
              this.setVals()
          } else {
              this.form.setFieldsValue({
                  shry: this.userInfo.name,
                  shsj: moment().format('YYYY-MM-DD HH:mm:ss')
              })
          }
      },
      watch: {
          baseObj: {
              handler() {
                  if (this.readOnly) {
                      this.$nextTick(() => {
                          // alert(2)
                          this.setVals()
                      })
                  }
              },
              deep: true
          }
      },
      methods: {
          handleChange(e) {
              console.log(e)
              if (e === '21') {
                  this.flagType = true
              } else {
                  this.flagType = false
              }
              this.$emit('change', this.flagType)
          },
          chooseSqjzry() {
              // 关联社区矫正对象
              this.$refs.chooseSqjzryRadio.choose('stay')
          },
          jzdx(e) {
              console.log(e)
          },
          handleSubmit() {
              return new Promise((resolve, reject) => {
                  this.form.validateFieldsAndScroll((err, values) => {
                      if (!err) {
                          resolve(values)
                      } else {
                          this.$message.error('信息未填写完成,请检查')
                          reject(err)
                      }
                  })
              })
          },
          setVals() {
              const allData = this.form.getFieldsValue()
              const values = {}
              Object.keys(allData).map(key => {
                  if (this.baseObj[key]) {
                      values[key] = this.baseObj[key]
                  } else {
                      values[key] = null
                  }
              })
              // values.zsd = addres
              // values.hjszd = addres2
              this.form.setFieldsValue({
                  ...values
              })
          }
      }

  }
  </script>

  <style lang="less" scoped></style>

<template>
  <div>
    <a-alert
      style="margin: 15px 0;"
      message="矫正对象基本信息（如有部分同步信息缺失，请手动补全）"
      type="info"
      show-icon />
    <BaseInfo :readOnly="parentData.readOnly || disabled" :baseObj="parentData.detailObj" ref="BaseInfo"/>
    <a-alert
      style="margin: 15px 0;"
      message="征求检察建议信息"
      type="info"
      show-icon />
    <AdviceForm :readOnly="parentData.readOnly || disabled" :baseObj="parentData.detailObj" ref="AdviceForm"/>
    <div>
      <a-button @click="showSign">
        生成文书
      </a-button>
    </div>
    <SignPdf ref="SignPdf"/>

  </div>
</template>

<script>
import BaseInfo from '@/views/main/revocationprobation/SolicitingProsecutorialAdvice/components/BaseInfo.vue'
import AdviceForm from '@/views/main/revocationprobation/SolicitingProsecutorialAdvice/components/AdviceForm.vue'
import SignPdf from '@/views/main/revocationprobation/SolicitingProsecutorialAdvice/components/SignPdf.vue'

    export default {
        components: { BaseInfo, AdviceForm, SignPdf },
        inject: ['parentData'],
        props: {
          disabled: {
            default: false
          }
        },
        methods: {
          showSign() {
            this.$refs.SignPdf.add()
          },
          async  handleSubmit() {
          const postData1 = await this.$refs.AdviceForm.handleSubmit();
          const postDAta2 = {}
          return Promise.resolve({
            ...postData1, ...postDAta2
          })
        }
         }
    }
</script>

<style lang="less" scoped>

</style>

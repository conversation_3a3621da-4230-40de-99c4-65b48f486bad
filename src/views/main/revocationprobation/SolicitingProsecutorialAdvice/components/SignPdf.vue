<template>
  <a-drawer
    title="反馈信息"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 0px;">
      <a-spin :spinning="confirmLoading">
        <section class="accept-wrapper " :class="{ class1: modelType === 'a' }">
          <!-- <div class="accept-left">

            <a-radio-group v-show="!readOnly" v-model="modelType" button-style="solid">
              <a-radio-button value="a">
                云签章模式
              </a-radio-button>
            </a-radio-group>
            <div v-show="!readOnly" class="cus-title" style="margin-top: 10px;">数据录入区域</div>
            <BaseInfo :typeModel="modelType === 'a'" :readOnly="readOnly" ref="BaseInfo" :baseObj="detailObj" />
          </div> -->
          <div class="accept-right" v-if="modelType === 'a'">
            <div v-show="!readOnly" class="cus-title" >文书制作区域</div>
            <p v-show="!readOnly" style="color: rgba(255, 0, 0, 1);">说明：文书将根据右侧录入数据自动更新，确认无误后点击盖章即可自动完成盖章。如需自定义盖章位置，请点击自定义盖章，盖章完成后将无法修改文书信息。
            </p>
            <div class="right-con">
              <a-space style="margin-bottom: 10px;" v-show="!readOnly">
                <!-- <a-button type="primary">
                    生成文书
                  </a-button> -->
                <a-button :loading="loading" @click="getSignedPdf()" type="primary" >
                  智能盖章
                </a-button>
                <!-- <a-button type="primary">
                    自定义盖章
                  </a-button> -->
                <a-button :loading="loading" @click="getNoSignPdf()" type="">
                  撤销
                </a-button>
              </a-space>
              <div>
                <embed v-if="pdfUrl" :src="pdfUrl" type="application/pdf" style="width:100%;height:892px" />

                <!-- <pdf :src="pdfUrl" :page="currentPage" :show-all="true"></pdf> -->
              </div>
            </div>
            <!-- <StepVue :steps="steps"/> -->
          </div>
        </section>

      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-button style="marginRight: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
        提交
      </a-button>
    </div>
  </a-drawer>
</template>

  <script>
  import pdf from 'vue-pdf';

  import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
  import { fileList } from '@/api/modular/system/fileManage'
  // 基本信息

  // 审核信息
  import AduitForm from '@/views/main/acceptinvestinfo/components/AduitFormFeed'
  // 步骤
  import StepVue from '@/views/main/acceptcorrectionobject/components/Step.vue';
  import { getNoSignPdf44002, getSignedPdf44002 } from '@/api/modular/main/acceptrecommit/acceptRecommitManage';

  export default {
    components: { AduitForm, StepVue, pdf },
    data() {
      return {
        pdfUrl: '',
        loading: false,
        currentPage: 1,
        modelType: 'a',
        isOk: false,
        drawetWidth: 1000,
        steps: [],
        readOnly: false,
        extOrgInfoData: {}, // 矫正决定机关
        extOrgJcy: [], // 检察院下拉框
        record: {},
        detailObj: {},
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    provide() {
      return {
        parentData: this.$data
      }
    },
    created() {
      this.drawetWidth = window.innerWidth - 200
      this.getExtOrgInfoList()
      this.getExtOrgInfoList2()
    },
    methods: {
      // 外部单位信息列表
      getExtOrgInfoList2() {
        extOrgInfoList().then((res) => {
          // 社区矫正决定机关
          const sqjz = []
          //
          const ga = []
          // 检察院
          const jcy = []
          res.data.forEach((v, i) => {
            if (v.type === 20) {
              sqjz.push(v)
            } else if (v.type === 40) {
              ga.push(v)
            } else if (v.type === 30) {
              jcy.push(v)
            }
          })
          this.extOrgInfoData = {
            sqjz,
            ga,
            jcy
          }
          extOrgInfoList({ type: 30, orgName: '' }).then(res => {
            // extOrgInfoList({ type: '', orgName: '' }).then(res => {
            res.data.forEach(p => {
              this.extOrgJcy.push({ value: p.orgCode + '|' + p.orgName + '|' + p.id, label: p.orgName })
            })
          })
        })
      },
      // 外部单位信息列表
      getExtOrgInfoList() {
        extOrgInfoList({ type: 30, orgName: '' }).then(res => {
          res.data.forEach(p => {
            this.extOrgJcy.push(p)
          })
        })
      },
      distpickerValidator(ule, value, callback) {
        // "selectedProvince": "330000", "selectedCity": "330100", "selectedDistrict": "330108"
        if (value.selectedProvince && value.selectedCity && value.selectedDistrict) {
          callback()
        } else {
          callback(new Error('请选择'))
        }
      },
      parseAdministrativeCode(code) {
        if (code.length !== 6) {
          return null; // 行政区划代码必须是6位
        }

        const provinceCode = code.slice(0, 2) + '0000'; // 获取省级代码
        const cityCode = code.slice(0, 4) + '00'; // 获取市级代码

        return {
          selectedProvince: provinceCode,
          selectedCity: cityCode,
          selectedDistrict: code
        };
      },
      async getFileList() {
        console.log(this.record)
        const { data } = await fileList({ ids: this.record.hzws })
        this.detailObj.hzws = data.map(item => { return { ...item, id: item.uid } })
        // this.form.setFieldsValue({
        //   hzcl: data.map(item => { return { ...item, id: item.uid } })
        // })
        // console.log(this.detailObj)
        return Promise.resolve()
        // this.$refs.BaseInfo.setVals()
      },
      // 初始化方法
      async add(record, type) {
        this.visible = true
        this.pdfUrl = ''
        this.modelType = 'a'
        this.record = record
        this.readOnly = !!type;
        if (this.readOnly) {
          if (record.feedbackStatus === 2) {
            this.modelType = 'b'
          } else {
            this.modelType = 'a'
            this.getSignedPdf()
          }
        } else {
          this.getNoSignPdf()
        }
        this.getDetail()
      },
      async getNoSignPdf() {
        this.loading = true
        getNoSignPdf44002({ id: this.record.id }).then((res) => {
          this.loading = false
          this.isOk = false
          // 将 ArrayBuffer 转换为 Blob
          const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
          // 通过 Blob 创建一个可用于访问该 Blob 的 URL
          const pdfUrl = URL.createObjectURL(pdfBlob);
          // 现在，pdfUrl 包含一个可以在前端使用的 URL
          // 可以将其用于显示 PDF 或其他相关操作
          // 注意：使用完毕后，最好在适当的时候释放该 URL
          // 在这个例子中，可以通过调用 revokeObjectURL 方法来释放资源
          // URL.revokeObjectURL(pdfUrl);
          this.pdfUrl = pdfUrl

          console.log(pdfUrl)
        })
      },
      async getSignedPdf() {
        this.loading = true
        getSignedPdf44002({ id: this.record.id }).then((res) => {
          this.loading = false
          this.isOk = false
          // 将 ArrayBuffer 转换为 Blob
          const pdfBlob = new Blob([res.data], { type: 'application/pdf' });
          // 通过 Blob 创建一个可用于访问该 Blob 的 URL
          const pdfUrl = URL.createObjectURL(pdfBlob);
          // 现在，pdfUrl 包含一个可以在前端使用的 URL
          // 可以将其用于显示 PDF 或其他相关操作
          // 注意：使用完毕后，最好在适当的时候释放该 URL
          // 在这个例子中，可以通过调用 revokeObjectURL 方法来释放资源
          // URL.revokeObjectURL(pdfUrl);
          this.pdfUrl = pdfUrl

          console.log(pdfUrl)
        }).finally(() => {
          this.loading = false
        })
      },
      async getDetail() {
        this.detailObj = {}
        // const { data } = await acceptInvestInfoDetail({
        //   id: this.record.id
        // })
        const { data } = await this.$http({
          url: '/acceptRecommit/getFeedbackInfo',
          params: {
            id: this.record.id
          }
        })
        await this.getFileList()
        this.detailObj = { ...data, ...this.detailObj }

        this.$nextTick(() => {
          this.$refs.BaseInfo.setVals()
        })
      },

      edit(record, type) {
        if (record) {
          if (type) {
            this.readOnly = true
          }
          this.visible = true
          this.record = { ...record }
          this.getFileList()
        }
      },
      /**
       * 提交表单
       */
      async handleSubmit() {
        // 基本信息
        const data1 = await this.$refs.BaseInfo.handleSubmit()
        let url = '/acceptRecommit/commitFeedbackByHand'
          if (this.modelType === 'a') {
            // 云
            url = '/acceptRecommit/commitFeedback'
          } else {
            // 手动
            url = '/acceptRecommit/commitFeedbackByHand'
          }
        // // 审核表单
        // const data = await this.$refs.AduitForm.handleSubmit()
        this.confirmLoading = true
        const postData = {
          ...data1,
          id: this.record.id
        }
        if (postData.hzws?.length) {
          postData.hzws = postData.hzws.map(item => item.id).toString()
        }
        // postData.id = this.record.id

        this.$http({
          url: url,
          method: 'POST',
          data: {
            ...postData
          }
        }).then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.confirmLoading = false
            this.$emit('ok', postData)
            this.handleCancel()
          } else {
            this.$message.error('操作失败' + res.message)// + res.message
          }
        }).finally(() => {
          this.confirmLoading = false
        })
      },
      handleCancel() {
        this.form.resetFields()
        this.record = null
        this.visible = false
      }
    }
  }
  </script>
  <style lang="less" scoped>
  // /deep/.ant-drawer-body{
  //   padding: ;
  // }
  .accept-wrapper {
    display: flex;
    width: 100%;
    flex-flow: column;

    &.class1 {
      flex-flow: row;

      .accept-left {
        flex: 1;
        overflow: hidden;
      }

      .accept-right {
        flex: 1;
        overflow: hidden;
        border-left: 1px solid #e8e8e8;

        .right-con {
          min-height: 500px;
          background: #EEEDED;
          padding: 10px;
        }
      }
    }

    .accept-left {
      flex: 1;
      overflow: auto;
      overflow-x: hidden;
      padding: 20px;
    }

    .accept-right {
      padding: 20px;
      // margin-top: 44px;
      position: relative;
      //   max-width: 300px;
      // padding-top: 20px;

      // .titlesd{
      //   position: absolute;
      //   top: -44px;
      //   left: -1px;
      //   line-height: 43px;
      //   border-bottom: 1px solid #e8e8e8;
      //   width: 100%;
      //   font-size: 16px;
      // font-family: Microsoft YaHei, Microsoft YaHei;
      // font-weight: bold;
      // color: #1890FF;
      // padding-left: 20px;
      // }
    }
  }

  /deep/ .ant-drawer-body {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 53px;
  }

  .cus-title-d {
    font-size: 20px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    color: #333333;
    // padding: 0 0 10px 0;
  }

  .cus-title {

    overflow: hidden;
    /* 字体大小 */
    font-weight: bold;
    /* 字体粗细 */
    color: #333;
    /* 字体颜色 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    /* 文本阴影 */
    position: relative;
    // margin-bottom: 20px;
    padding-left: 10px;
    font-size: 20px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    color: #333333;

    &::after {
      display: block;
      content: '';
      width: 6px;
      height: 60%;
      border-radius: 5px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      background: #1990ff;
    }
  }
  </style>

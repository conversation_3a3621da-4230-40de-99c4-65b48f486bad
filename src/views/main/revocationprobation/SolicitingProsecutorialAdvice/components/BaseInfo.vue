<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['xm', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="zjlx"
            disabled
            style="width: 100%;"
            placeholder="请选择"
            value="111"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="sex"
            style="width: 100%;"
            placeholder="请选择"
            disabled
            v-decorator="['xb', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            disabled
            valueFormat="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择"
            v-decorator="['csrq', { initialValue: undefined, rules: [{ required: false, message: '请选择！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['zjhm', { rules: [{ required: false, message: '请输入！' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="guoji"
            disabled
            style="width: 100%;"
            placeholder="请选择"
            v-decorator="['gj', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="居住地地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-cascader-distpicker
            disabled
            v-decorator="['xzd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="居住地地址明细" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['xzdxz', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-cascader-distpicker
            disabled
            v-decorator="['hjd', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="户籍地址明细" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-input
            disabled
            placeholder="请输入"
            v-decorator="['hjdxz', { rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            style="width: 100%;"
            placeholder="请选择"
            disabled
            v-decorator="['jzlb', { initialValue: '', rules: [{ required: false, message: '请输入' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="社区矫正执行地" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['sqjzzxd', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="司法所" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['sfs', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['sqjzksrq', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['sqjzjsrq', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['jzqx', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="矫正期间行为表现" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            disabled
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['jzqjbxqk', { rules: [{ required: true, message: '请输入' }] }]"/>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>

import { mapGetters } from 'vuex'
import moment from 'moment'

function isEmpty(obj) {
  if (!obj) {
    return false
  } else {
    return Object.keys(obj).length === 0;
  }
}

export default {
  components: {},
  props: {
    readOnly: {
      default: false,
      type: Boolean
    },
    baseObj: {
      default: () => {
      },
      type: Object

    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      flagType: false,
      labelCol: { xs: { span: 24 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      form: this.$form.createForm(this)
    }
  },
  async mounted() {
    if (!isEmpty(this.baseObj)) {
      this.setVals()
    } else {
      this.form.setFieldsValue({
        shry: this.userInfo.name,
        shsj: moment().format('YYYY-MM-DD HH:mm:ss')
      })
    }
  },
  watch: {
    baseObj: {
      handler() {
        if (this.readOnly) {
          this.$nextTick(() => {
            // alert(2)
            this.setVals()
          })
        }
      },
      deep: true
    }
  },
  methods: {
    handleChange(e) {
      console.log(e)
      if (e === '21') {
        this.flagType = true
      } else {
        this.flagType = false
      }
      this.$emit('change', this.flagType)
    },
    chooseSqjzry() {
      // 关联社区矫正对象
      this.$refs.chooseSqjzryRadio.choose('stay')
    },
    jzdx(e) {
      console.log(e)
    },
    handleSubmit() {
      return new Promise((resolve, reject) => {
        this.form.validateFieldsAndScroll((err, values) => {
          if (!err) {
            resolve(values)
          } else {
            this.$message.error('信息未填写完成,请检查')
            reject(err)
          }
        })
      })
    },
    setVals() {
      const allData = this.form.getFieldsValue()
      const values = {}
      Object.keys(allData).map(key => {
        if (this.baseObj[key]) {
          values[key] = this.baseObj[key]
        } else {
          values[key] = null
        }
      })
      this.form.setFieldsValue({
        ...values
      })
    }
  }

}
</script>

<style lang="less" scoped></style>

<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-form-item label="社区矫正决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            style="width: 100%;"
            api="/extOrgInfo/list?type=20"
            :disabled="readOnly"
            placeholder="请选择"
            labelKey="orgName"
            valueKey="orgCode"
            v-decorator="['fy', {initialValue:'', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="征求检察院名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            style="width: 100%;"
            api="/extOrgInfo/list?type=30"
            :disabled="readOnly"
            placeholder="请选择"
            labelKey="orgName"
            valueKey="orgCode"
            v-decorator="['jcy', {initialValue:'', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="建议文书号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled="readOnly"
            v-decorator="['zqyjjyswh', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="提请日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择"
            valueFormat="YYYY-MM-DD"
            :disabled="readOnly"
            v-decorator="['zqyjrq', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="提请依据" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-tree-select
            tree-checkable
            :disabled="readOnly"
            @onChange="handleChange2"
            v-decorator="['zqyjtqyj', {initialValue:[], rules: [{ required: true, message: '请输入' }] }]"
            apiURL="/sysDictData/tree?dictTypeId=1531931283775598594"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="提请理由" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['zqyjtqly', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="文书上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-file-upload
            :disabled="readOnly"
            v-decorator="['files2', { initialValue: [], rules: [{ required: false, message: '请输入' }] }]"
            uploadUrl="/api/sysFileInfo/uploadOss" />
          说明：上传（《撤销缓刑建议书》或者《撤销缓刑报告》等其他已签章的PDF材料
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>

import { mapGetters } from 'vuex'
import moment from 'moment'
function isEmpty(obj) {
    if (!obj) {
        return true
    } else {
        return Object.keys(obj).length === 0;
    }
}
export default {
    components: {},
    props: {
        readOnly: {
            default: false,
            type: Boolean
        },
        baseObj: {
            default: () => { },
            type: Object

        }
    },
    computed: {
        ...mapGetters(['userInfo'])
    },
    data() {
        return {
            flagType: false,
            labelCol: { xs: { span: 24 }, sm: { span: 6 } },
            wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
            labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
            wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
            form: this.$form.createForm(this)
        }
    },
      async mounted() {
          if (!isEmpty(this.baseObj)) {
              this.setVals()
        } else {
            this.form.setFieldsValue({
                shry: this.userInfo.name,
                shsj: moment().format('YYYY-MM-DD HH:mm:ss')
            })
        }
    },
    watch: {
        baseObj: {
            handler() {
                if (this.readOnly) {
                    this.$nextTick(() => {
                        // alert(2)
                        this.setVals()
                    })
                }
            },
            deep: true
        }
    },
    methods: {
        handleChange(e) {
            console.log(e)
            if (e === '21') {
                this.flagType = true
            } else {
                this.flagType = false
            }
            this.$emit('change', this.flagType)
        },
        handleChange2({ value, label, extra }) {
            console.log(value, label, extra)
            this.form.setFieldsValue({
                tqly: label.toString()
            })
        },
        chooseSqjzry() {
            // 关联社区矫正对象
            this.$refs.chooseSqjzryRadio.choose('stay')
        },
        jzdx(e) {
            console.log(e)
        },
        handleSubmit() {
            return new Promise((resolve, reject) => {
                this.form.validateFieldsAndScroll((err, values) => {
                    if (!err) {
                        resolve(values)
                    } else {
                        this.$message.error('信息未填写完成,请检查')
                        reject(err)
                    }
                })
            })
        },
        setVals() {
            const allData = this.form.getFieldsValue()
            const values = {}
            Object.keys(allData).map(key => {
                if (this.baseObj[key]) {
                    values[key] = this.baseObj[key]
                } else {
                    values[key] = null
                }
            })
            this.form.setFieldsValue({
                ...values
            })
        }
    }

}
</script>

<style lang="less" scoped></style>

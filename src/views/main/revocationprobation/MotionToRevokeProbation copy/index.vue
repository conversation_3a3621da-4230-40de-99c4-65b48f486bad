<template>
  <div>
    <a-alert style="margin: 15px 0;" message="检察建议" type="info" show-icon />
    <ProsecutorialAdviceFrom :readOnly="parentData.readOnly" :baseObj="parentData.detailObj" @change="handleChange" ref="ProsecutorialAdviceFrom" />

  </div>
</template>

<script>
// 检察建议

import ProsecutorialAdviceFrom from './components/ProsecutorialAdviceFrom.vue'
export default {
    components: { ProsecutorialAdviceFrom },
    inject: ['parentData'],
    data() {
        return {
            flagType: false
        }
    },
    methods: {
        handleChange(code) {
            this.flagType = code
        }
    }

}
</script>

<style lang="scss" scoped></style>

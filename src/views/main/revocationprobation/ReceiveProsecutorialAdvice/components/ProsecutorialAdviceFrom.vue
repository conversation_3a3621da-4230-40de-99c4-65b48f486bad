<template>
  <a-form :form="form">
    <a-row :gutter="24">

      <a-col :span="24">
        <a-form-item label="撤销缓刑检察建议" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['cxhxjcyj', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['jcyjbz', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="文书文号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :disabled="readOnly" v-decorator="['jcyjwswh', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="反馈时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled="readOnly"
            v-decorator="['yjfksj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="文书" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <sh-file-upload
            :disabled="readOnly"

            v-decorator="['files3', {initialValue:[], rules: [{ required: false, message: '请输入' }] }]"
            uploadUrl="/api/sysFileInfo/uploadOss" />
        </a-form-item>
      </a-col>

    </a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

  <script>

  import { mapGetters } from 'vuex'
  import moment from 'moment'
  function isEmpty(obj) {
      if (!obj) {
          return false
      } else {
          return Object.keys(obj).length === 0;
      }
  }
  export default {
      components: {},
      props: {
          readOnly: {
              default: false,
              type: Boolean
          },
          baseObj: {
              default: () => { },
              type: Object

          }
      },
      computed: {
          ...mapGetters(['userInfo'])
      },
      data() {
          return {
              flagType: false,
              labelCol: {
                  xs: { span: 24 },
                  sm: { span: 6 }
              },
              wrapperCol: {
                  xs: { span: 24 },
                  sm: { span: 16 }
              },
              labelCol2: {
                  xs: { span: 24 },
                  sm: { span: 3 }
              },
              wrapperCol2: {
                  xs: { span: 24 },
                  sm: { span: 20 }
              },
              shjgTree: [
                  {
                      children: [{
                          children: [],
                          id: '11',
                          parentId: '1',
                          title: '信息无误，确认接收',
                          value: '11'
                      }, {
                          children: [],
                          id: '12',
                          parentId: '1',
                          title: '存在部分信息错误，确认接收',
                          value: '12'
                      }],
                      id: '1',
                      parentId: '0',
                      title: '正常接收',
                      disabled: true,
                      value: '1'
                  }, {
                      children: [{
                          children: [],
                          id: '21',
                          parentId: '2',
                          title: '信息错误，非我区管辖矫正对象',
                          value: '21'
                      }, {
                          children: [],
                          id: '22',
                          parentId: '2',
                          title: '数据滞后，已办理手续',
                          value: '22'
                      }, {
                          children: [],
                          id: '23',
                          parentId: '2',
                          title: '数据重复发送',
                          value: '23'
                      }],
                      id: '2',
                      parentId: '0',
                      title: '退回',
                      disabled: true,
                      value: '2'
                  }, {
                      children: [],
                      id: '3',
                      parentId: '0',
                      title: '其他',
                      value: '3'

                  }],
              form: this.$form.createForm(this)
          }
      },
       async mounted() {
          if (!isEmpty(this.baseObj)) {
              this.setVals()
          } else {
              this.form.setFieldsValue({
                jcyjwswh: this.userInfo.name,
                  yjfksj: moment().format('YYYY-MM-DD HH:mm:ss')
              })
          }
      },
      watch: {
          baseObj: {
              handler() {
                  if (this.readOnly) {
                      this.$nextTick(() => {
                          // alert(2)
                          this.setVals()
                      })
                  }
              },
              deep: true
          }
      },
      methods: {
          handleChange(e) {
              console.log(e)
              if (e === '21') {
                  this.flagType = true
              } else {
                  this.flagType = false
              }
              this.$emit('change', this.flagType)
          },
          chooseSqjzry() {
              // 关联社区矫正对象
              this.$refs.chooseSqjzryRadio.choose('stay')
          },
          jzdx(e) {
              console.log(e)
          },
          handleSubmit() {
              return new Promise((resolve, reject) => {
                  this.form.validateFieldsAndScroll((err, values) => {
                      if (!err) {
                          resolve(values)
                      } else {
                          this.$message.error('信息未填写完成,请检查')
                          reject(err)
                      }
                  })
              })
          },
          setVals() {
              const allData = this.form.getFieldsValue()
              const values = {}
              Object.keys(allData).map(key => {
                  if (this.baseObj[key]) {
                      values[key] = this.baseObj[key]
                  } else {
                      values[key] = null
                  }
              })
              // values.zsd = addres
              // values.hjszd = addres2
              this.form.setFieldsValue({
                  ...values
              })
          }
      }

  }
  </script>

  <style lang="less" scoped></style>

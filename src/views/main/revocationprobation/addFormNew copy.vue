<template>
  <a-drawer
    title="交付衔接协同"
    :width="drawetWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel">
    <!-- :confirmLoading="confirmLoading" -->
    <div v-if="visible" style="padding-bottom: 54px;">
      <a-spin :spinning="confirmLoading">
        222

      </a-spin>
    </div>
    <div
      v-if="!readOnly"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px',
      }">

      <a-space>
        <a-button @click="handleCancel">
          取消
        </a-button>
        <a-button :loading="confirmLoading" type="primary" @click="handleSubmit">
          提交
        </a-button>
      </a-space>

    </div>
  </a-drawer>
</template>

  <script>

  export default {
    components: { },
    data() {
      return {
        drawetWidth: 1000,
        steps: [],
        readOnly: false,
        record: {},
        detailObj: {},

        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    provide() {
      return {
        parentData: this.$data
      }
    },
    created() {
      this.drawetWidth = window.innerWidth
    },
    methods: {

      // 初始化方法
      add(record, type) {
        this.record = record
        if (type) {
          this.readOnly = true
        } else {
          this.readOnly = false
        }
        this.visible = true
      },

      /**
       * 提交表单
       */
      async handleSubmit() {
        // 基本信息
        // const data1 = await this.$refs.BaseInfo.handleSubmit()

        // return false
        // this.$http({
        //   url: '/acceptBaseInfo/edit',
        //   method: 'POST',
        //   data: {
        //     ...postData
        //   }
        // }).then((res) => {
        //       if (res.success) {
        //         this.$message.success('操作成功')
        //         this.confirmLoading = false
        //         this.$emit('ok', postData)
        //         this.handleCancel()
        //       } else {
        //         this.$message.error('操作失败' + res.message)// + res.message
        //       }
        //     }).finally((res) => {
        //       this.confirmLoading = false
        //     })
      },

      handleCancel() {
        this.form.resetFields()
        this.record = null
        this.visible = false
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .accept-wrapper{
    display: flex;
    width: 100%;
    .accept-left{
      flex:1;
      overflow: auto;
    }
    .accept-right{
      border-left: 1px solid #e8e8e8;
    // margin-top: 44px;
    position: relative;
    max-width: 500px;
    // padding-top: 20px;

    // .titlesd{
    //   position: absolute;
    //   top: -44px;
    //   left: -1px;
    //   line-height: 43px;
    //   border-bottom: 1px solid #e8e8e8;
    //   width: 100%;
    //   font-size: 16px;
    // font-family: Microsoft YaHei, Microsoft YaHei;
    // font-weight: bold;
    // color: #1890FF;
    // padding-left: 20px;
    // }
    }
  }
  /deep/ .ant-drawer-body {
    padding-top: 0;
    padding-bottom: 78px;
  }

  .cus-title {
    font-size: 18px;
    overflow: hidden;
    /* 字体大小 */
    font-weight: bold;
    /* 字体粗细 */
    color: #333;
    /* 字体颜色 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    /* 文本阴影 */
    position: relative;
    margin-bottom: 20px;
    padding-left: 10px;

    &::after {
      display: block;
      content: '';
      width: 6px;
      height: 60%;
      border-radius: 5px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      background: #1990ff;
    }
  }
  </style>

<template>
  <div style="height: 100%">
    {{ selectedFiles }}
    <FileTree
      :treeData.sync="fileList"
      v-model="selectedFiles"

    >
      <!-- :editable="false" -->
      <template #operations>
        <a-button type="primary">组卷</a-button>
        <a-button type="primary">补充材料</a-button>
        <a-button type="danger" >批量删除</a-button>
      </template>
    </FileTree>
  </div>
</template>

<script>
import FileTree from '@/components/FileTree.vue'

export default {
  components: {
    FileTree
  },
  data() {
    return {
      fileList: [
        {
          id: '1',
          name: '指派委托材料',
          isFile: false,
          children: [
            {
              id: '1-1',
              name: '委托调查函',
              isFile: true
            },
            {
              id: '1-2',
              name: '基本信息表',
              isFile: false,
              children: [
                {
                  id: '1-2-1',
                  name: '个人基本信息',
                  isFile: true
                },
                {
                  id: '1-2-2',
                  name: '家庭成员信息',
                  isFile: true
                },
                {
                  id: '1-2-3',
                  name: '其他材料',
                  isFile: false,
                  children: [
                    {
                      id: '1-2-3-1',
                      name: '补充说明',
                      isFile: true
                    },
                    {
                      id: '1-2-3-2',
                      name: '附件资料',
                      isFile: true
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: '2',
          name: '调查材料',
          isFile: false,
          children: [
            {
              id: '2-1',
              name: '调查笔录',
              isFile: true
            },
            {
              id: '2-2',
              name: '走访记录',
              isFile: false,
              children: [
                {
                  id: '2-2-1',
                  name: '邻居走访',
                  isFile: true
                },
                {
                  id: '2-2-2',
                  name: '单位走访',
                  isFile: true
                }
              ]
            }
          ]
        },
        {
          id: '3',
          name: '其他材料',
          isFile: false,
          children: [
            {
              id: '3-1',
              name: '房产证',
              isFile: true
            },
            {
              id: '3-2',
              name: '居住证',
              isFile: true
            },
            {
              id: '3-3',
              name: '证明文件',
              isFile: false,
              children: [
                {
                  id: '3-3-1',
                  name: '工作证明',
                  isFile: true
                },
                {
                  id: '3-3-2',
                  name: '社保证明',
                  isFile: true
                }
              ]
            }
          ]
        }
      ],
      selectedFiles: [ { 'id': '1-2-1', 'name': '个人基本信息', 'isFile': true }, { 'id': '2-2-1', 'name': '邻居走访', 'isFile': true }, { 'id': '3-2', 'name': '居住证', 'isFile': true } ]
      // 选中的文件列表
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

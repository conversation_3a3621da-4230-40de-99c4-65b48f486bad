<template>
  <a-modal
    title="编辑提请撤销缓刑"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label=" 统一赋号 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 统一赋号 " v-decorator="['tyfh', {rules: [{required: true, message: '请输入 统一赋号 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 社区矫正案件编号 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 社区矫正案件编号 " v-decorator="['sqjzajbh', {rules: [{required: true, message: '请输入 社区矫正案件编号 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 罪犯编号 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 罪犯编号 " v-decorator="['zfbh', {rules: [{required: true, message: '请输入 罪犯编号 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 姓名 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 姓名 " v-decorator="['xm', {rules: [{required: true, message: '请输入 姓名 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 性别 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 性别 " v-decorator="['xb', {rules: [{required: true, message: '请输入 性别 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 证件类型 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 证件类型 " v-decorator="['zjlx', {rules: [{required: true, message: '请输入 证件类型 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 证件号码 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 证件号码 " v-decorator="['zjhm', {rules: [{required: true, message: '请输入 证件号码 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 出生日期 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 出生日期 " v-decorator="['csrq',{rules: [{ required: true, message: '请选择 出生日期 ！' }]}]" @change="onChangecsrq"/>
        </a-form-item>
        <a-form-item
          label=" 户籍地 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 户籍地 " v-decorator="['hjd', {rules: [{required: true, message: '请输入 户籍地 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 户籍地详址 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 户籍地详址 " v-decorator="['hjdxz', {rules: [{required: true, message: '请输入 户籍地详址 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 现住地 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 现住地 " v-decorator="['xzd', {rules: [{required: true, message: '请输入 现住地 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 现住地详址 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 现住地详址 " v-decorator="['xzdxz', {rules: [{required: true, message: '请输入 现住地详址 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 生效判决机关 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 生效判决机关 " v-decorator="['sxpjjg', {rules: [{required: true, message: '请输入 生效判决机关 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 判决文书文号 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 判决文书文号 " v-decorator="['pjwswh', {rules: [{required: true, message: '请输入 判决文书文号 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 判决日期 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 判决日期 " v-decorator="['pjrq',{rules: [{ required: true, message: '请选择 判决日期 ！' }]}]" @change="onChangepjrq"/>
        </a-form-item>
        <a-form-item
          label=" 判决罪名 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 判决罪名 " v-decorator="['pjzm', {rules: [{required: true, message: '请输入 判决罪名 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 判决其他罪名 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 判决其他罪名 " v-decorator="['pjqtzm', {rules: [{required: true, message: '请输入 判决其他罪名 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 主刑 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 主刑 " v-decorator="['zx', {rules: [{required: true, message: '请输入 主刑 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 附加刑 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 附加刑 " v-decorator="['fjx', {rules: [{required: true, message: '请输入 附加刑 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 禁止令内容 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 禁止令内容 " v-decorator="['jzlnr', {rules: [{required: true, message: '请输入 禁止令内容 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 禁止期限起日 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 禁止期限起日 " v-decorator="['jzqxqr',{rules: [{ required: true, message: '请选择 禁止期限起日 ！' }]}]" @change="onChangejzqxqr"/>
        </a-form-item>
        <a-form-item
          label=" 禁止期止限日 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 禁止期止限日 " v-decorator="['jzqxzr',{rules: [{ required: true, message: '请选择 禁止期止限日 ！' }]}]" @change="onChangejzqxzr"/>
        </a-form-item>
        <a-form-item
          label=" 矫正类别 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 矫正类别 " v-decorator="['jzlb', {rules: [{required: true, message: '请输入 矫正类别 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 决定机关 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 决定机关 " v-decorator="['jdjg', {rules: [{required: true, message: '请输入 决定机关 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 司法所 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 司法所 " v-decorator="['sfs', {rules: [{required: true, message: '请输入 司法所 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 社区矫正开始日期 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 社区矫正开始日期 " v-decorator="['sqjzksrq',{rules: [{ required: true, message: '请选择 社区矫正开始日期 ！' }]}]" @change="onChangesqjzksrq"/>
        </a-form-item>
        <a-form-item
          label=" 社区矫正结束日期 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 社区矫正结束日期 " v-decorator="['sqjzjsrq',{rules: [{ required: true, message: '请选择 社区矫正结束日期 ！' }]}]" @change="onChangesqjzjsrq"/>
        </a-form-item>
        <a-form-item
          label=" 矫正期限 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 矫正期限 " v-decorator="['jzqx', {rules: [{required: true, message: '请输入 矫正期限 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 社区矫正执行地 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 社区矫正执行地 " v-decorator="['sqjzzxd', {rules: [{required: true, message: '请输入 社区矫正执行地 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 建议书文号 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 建议书文号 " v-decorator="['jyswh', {rules: [{required: true, message: '请输入 建议书文号 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label="征求建议日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择征求建议日期" v-decorator="['zqyjrq',{rules: [{ required: true, message: '请选择征求建议日期！' }]}]" @change="onChangezqyjrq"/>
        </a-form-item>
        <a-form-item
          label=" 提请日期 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择 提请日期 " v-decorator="['tqrq',{rules: [{ required: true, message: '请选择 提请日期 ！' }]}]" @change="onChangetqrq"/>
        </a-form-item>
        <a-form-item
          label=" 提请理由 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 提请理由 " v-decorator="['tqly', {rules: [{required: true, message: '请输入 提请理由 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label=" 提请依据 "
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入 提请依据 " v-decorator="['tqyj', {rules: [{required: true, message: '请输入 提请依据 ！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正单位" v-decorator="['jzdw', {rules: [{required: true, message: '请输入矫正单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="征求检察院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入征求检察院" v-decorator="['jcy', {rules: [{required: true, message: '请输入征求检察院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="征求检察院名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入征求检察院名称" v-decorator="['jcymc', {rules: [{required: true, message: '请输入征求检察院名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提请法院"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入提请法院" v-decorator="['fy', {rules: [{required: true, message: '请输入提请法院！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提请法院名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入提请法院名称" v-decorator="['fymc', {rules: [{required: true, message: '请输入提请法院名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="提请状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入提请状态" v-decorator="['zt', {rules: [{required: true, message: '请输入提请状态！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { revocationProbationEdit } from '@/api/modular/main/revocationprobation/revocationProbationManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        csrqDateString: '',
        pjrqDateString: '',
        jzqxqrDateString: '',
        jzqxzrDateString: '',
        sqjzksrqDateString: '',
        sqjzjsrqDateString: '',
        zqyjrqDateString: '',
        tqrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              tyfh: record.tyfh,
              sqjzajbh: record.sqjzajbh,
              zfbh: record.zfbh,
              xm: record.xm,
              xb: record.xb,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              hjd: record.hjd,
              hjdxz: record.hjdxz,
              xzd: record.xzd,
              xzdxz: record.xzdxz,
              sxpjjg: record.sxpjjg,
              pjwswh: record.pjwswh,
              pjzm: record.pjzm,
              pjqtzm: record.pjqtzm,
              zx: record.zx,
              fjx: record.fjx,
              jzlnr: record.jzlnr,
              jzlb: record.jzlb,
              jdjg: record.jdjg,
              sfs: record.sfs,
              jzqx: record.jzqx,
              sqjzzxd: record.sqjzzxd,
              jyswh: record.jyswh,
              tqly: record.tqly,
              tqyj: record.tqyj,
              jzdw: record.jzdw,
              jcy: record.jcy,
              jcymc: record.jcymc,
              fy: record.fy,
              fymc: record.fymc,
              zt: record.zt
            }
          )
        }, 100)
        // 时间单独处理
        if (record.csrq != null) {
            this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
        }
        this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.pjrq != null) {
            this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
        }
        this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jzqxqr != null) {
            this.form.getFieldDecorator('jzqxqr', { initialValue: moment(record.jzqxqr, 'YYYY-MM-DD') })
        }
        this.jzqxqrDateString = moment(record.jzqxqr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.jzqxzr != null) {
            this.form.getFieldDecorator('jzqxzr', { initialValue: moment(record.jzqxzr, 'YYYY-MM-DD') })
        }
        this.jzqxzrDateString = moment(record.jzqxzr).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.sqjzksrq != null) {
            this.form.getFieldDecorator('sqjzksrq', { initialValue: moment(record.sqjzksrq, 'YYYY-MM-DD') })
        }
        this.sqjzksrqDateString = moment(record.sqjzksrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.sqjzjsrq != null) {
            this.form.getFieldDecorator('sqjzjsrq', { initialValue: moment(record.sqjzjsrq, 'YYYY-MM-DD') })
        }
        this.sqjzjsrqDateString = moment(record.sqjzjsrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.zqyjrq != null) {
            this.form.getFieldDecorator('zqyjrq', { initialValue: moment(record.zqyjrq, 'YYYY-MM-DD') })
        }
        this.zqyjrqDateString = moment(record.zqyjrq).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.tqrq != null) {
            this.form.getFieldDecorator('tqrq', { initialValue: moment(record.tqrq, 'YYYY-MM-DD') })
        }
        this.tqrqDateString = moment(record.tqrq).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.csrq = this.csrqDateString
            values.pjrq = this.pjrqDateString
            values.jzqxqr = this.jzqxqrDateString
            values.jzqxzr = this.jzqxzrDateString
            values.sqjzksrq = this.sqjzksrqDateString
            values.sqjzjsrq = this.sqjzjsrqDateString
            values.zqyjrq = this.zqyjrqDateString
            values.tqrq = this.tqrqDateString
            revocationProbationEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangecsrq(date, dateString) {
        this.csrqDateString = dateString
      },
      onChangepjrq(date, dateString) {
        this.pjrqDateString = dateString
      },
      onChangejzqxqr(date, dateString) {
        this.jzqxqrDateString = dateString
      },
      onChangejzqxzr(date, dateString) {
        this.jzqxzrDateString = dateString
      },
      onChangesqjzksrq(date, dateString) {
        this.sqjzksrqDateString = dateString
      },
      onChangesqjzjsrq(date, dateString) {
        this.sqjzjsrqDateString = dateString
      },
      onChangezqyjrq(date, dateString) {
        this.zqyjrqDateString = dateString
      },
      onChangetqrq(date, dateString) {
        this.tqrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <a-col :span="24">
        <a-form-item label="审核结果" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-select
            dictType="cxtqspjg"
            style="width: 100%;"
            @onChange="handleChange"
            placeholder="请选择"
            :disabled="readOnly"
            v-decorator="['csjg', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="审批人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            v-decorator="['csry', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="审批时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled="readOnly"
            v-decorator="['cssj', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>
      </a-col>
      <template v-if="!flagType">
        <a-col :span="24">
          <a-form-item label="审批意见" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <a-textarea
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['csyj', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="撤销缓刑审核表" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <sh-file-upload
              :disabled="readOnly"
              v-decorator="['file2', { initialValue: [], rules: [{ required: false, message: '请输入' }] }]"
              uploadUrl="/api/sysFileInfo/uploadOss" /> </a-form-item>
        </a-col>
      </template>

      <a-col :span="24" v-if="flagType">
        <a-alert style="margin: 15px 0;" message="退回信息" type="info" show-icon />
        <a-form-item label="退回说明" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['bz', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>

    </a-row>
  </a-form>
</template>

<script>

import { mapGetters } from 'vuex'
import moment from 'moment'
function isEmpty(obj) {
    if (!obj) {
        return false
    } else {
        return Object.keys(obj).length === 0;
    }
}
export default {
    components: {},
    props: {
        readOnly: {
            default: false,
            type: Boolean
        },
        baseObj: {
            default: () => { },
            type: Object
        }
    },
    computed: {
        ...mapGetters(['userInfo'])
    },
    data() {
        return {
            flagType: false,
            labelCol: { xs: { span: 24 }, sm: { span: 6 } },
            wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
            labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
            wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
            form: this.$form.createForm(this)
        }
    },
    async mounted() {
        if (this.baseObj.csjg === 'return') {
            this.flagType = true
        } else {
            this.flagType = false
        }
        this.$emit('change', this.flagType)
        await this.$nextTick()
        if (!isEmpty(this.baseObj)) {
            this.setVals()
        } else {
            this.form.setFieldsValue({
                csry: this.userInfo.name,
                cssj: moment().format('YYYY-MM-DD HH:mm:ss')
            })
        }
    },
    watch: {
        baseObj: {
            handler() {
                if (this.readOnly) {
                    this.$nextTick(() => {
                        this.setVals()
                    })
                }
            },
            deep: true
        }
    },
    methods: {
        handleChange(e) {
            console.log(e)
            if (e === 'return') {
                this.flagType = true
            } else {
                this.flagType = false
            }
            this.$emit('change', this.flagType)
        },
        chooseSqjzry() {
            // 关联社区矫正对象
            this.$refs.chooseSqjzryRadio.choose('stay')
        },
        jzdx(e) {
            console.log(e)
        },
        handleSubmit() {
            return new Promise((resolve, reject) => {
                this.form.validateFieldsAndScroll((err, values) => {
                    if (!err) {
                        resolve(values)
                    } else {
                        this.$message.error('信息未填写完成,请检查')
                        reject(err)
                    }
                })
            })
        },
        setVals() {
            const allData = this.form.getFieldsValue()
            const values = {}
            Object.keys(allData).map(key => {
                if (this.baseObj[key]) {
                    values[key] = this.baseObj[key]
                } else {
                    values[key] = null
                }
            })
            this.form.setFieldsValue({
                ...values
            })
        }
    }

}
</script>

<style lang="less" scoped></style>

<template>
  <div>
    <a-alert style="margin: 15px 0;" message="区县司法局负责人审批信息" type="info" show-icon />
    <AduitForm
      :readOnly="parentData.readOnly || disabled"
      :baseObj="detail || {}"
      @change="handleChange"
      ref="AduitForm" />

  </div>
</template>

<script>

// 司法所合议信息
import AduitForm from '@/views/main/revocationprobation/ApprovalForm/components/Aduit.vue';

export default {
  components: { AduitForm },
  data() {
    return {
      flagType: false
    }
  },
  props: {
    detail: {
      type: Object,
      default: () => { }

    },
    disabled: {
      default: false
    }
  },
  inject: ['parentData'],
  mounted() {
    if (this.detail.csjg === 'return') {
      this.flagType = true
    } else {
      this.flagType = false
    }
  },
  methods: {
    handleChange(code) {
      this.flagType = code
    },
    async handleSubmit() {
      const postData1 = await this.$refs.AduitForm.handleSubmit();
      const postDAta2 = {}
      return Promise.resolve({
        ...postData1, ...postDAta2
      })
    }
  }

}
</script>

<style lang="scss" scoped></style>

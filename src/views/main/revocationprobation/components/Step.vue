<template>
  <div class="step-wrapper">
    <div class="step-title">流程记录</div>

    <div :class="{active: step.active}" class="step " v-for="step in steps " :key="step.icon">

      <div class="step-left ">
        <div class="step-left-item">
          <span class="icon " :class="{icon1:step.icon==='step1',icon2:step.icon==='step2',icon3:step.icon==='step3',icon4:step.icon==='step4'}"></span>
          <div>{{ step.taskName }}</div>
        </div>
      </div>
      <span class="arrow " :class="{error:step.stepType==='0'}"> </span>
      <div class="step-right">

        <div class="step-right-title">

          {{ step.stepName }}
          <a-tag style="margin-left: 10px;" color="blue" v-if="step.approve==='pass'">
            {{ 'cxtqspjg' | dictType(step.approve) }}

          </a-tag>
          <a-tag style="margin-left: 10px;" color="blue" v-if="step.approve==='fail'">
            {{ 'cxtqspjg' | dictType(step.approve) }}

          </a-tag>
          <a-tag style="margin-left: 10px;" color="orange" v-if="step.approve==='return'">
            {{ 'cxtqspjg' | dictType(step.approve) }}

          </a-tag>

        </div>
        <div class="subtext">处理人：<b>{{ step.assignee }}</b></div>
        <div class="subtext">处理时间：<b>{{ step.endTime }}</b></div>
        <!-- <div class="subtext">{{ step.timeTitle }}：<b>{{ step.opTime }}</b></div> -->
        <div v-if="step.comment" class="subtext">备注：
          <div class="reason">{{ step.comment }}</div>
        </div>

      </div>
    </div>
    <!-- <div class="step active">
        <div class="step-left ">
          <div class="step-left-item">
            <span class="icon icon2"></span>
            <div> 人员衔接</div>
          </div>
        </div>
        <span class="arrow "> </span>
        <div class="step-right">

          <div class="step-right-title">

            社区矫正决定机关决定
            <a-tag style="margin-left: 10px;" color="blue">
              待接收
            </a-tag>

          </div>
          <div class="subtext">办理人：<b>某某法院</b></div>
          <div class="subtext">办理人2：<b>某某法院</b></div>
          <div class="subtext">办理人3：<b>某某法院</b></div>

        </div>
      </div>
      <div class="step active">
        <div class="step-left ">
          <div class="step-left-item">
            <span class="icon icon3"></span>
            <div> 人员衔接</div>
          </div>
        </div>
        <span class="arrow error"> </span>
        <div class="step-right">

          <div class="step-right-title">

            社区矫正决定机关决定
            <a-tag style="margin-left: 10px;" color="orange">
              待接收
            </a-tag>

          </div>
          <div class="subtext">办理人：<b>某某法院</b></div>
          <div class="subtext">办理人2：<b>某某法院</b></div>
          <div class="subtext">办理人3：<b>某某法院</b></div>

        </div>

      </div>
      <div class="step active">

        <span class="arrow error"> </span>
        <div class="step-right">

          <div class="step-right-title">

            社区矫正决定机关决定
            <a-tag style="margin-left: 10px;" color="orange">
              待接收
            </a-tag>

          </div>
          <div class="subtext">办理人：<b>某某法院</b></div>
          <div class="subtext">办理人2：<b>某某法院</b></div>
          <div class="subtext">办理人3：<b>某某法院</b></div>

        </div>

      </div>
      <div class="step ">
        <div class="step-left ">
          <div class="step-left-item">
            <span class="icon icon4"></span>
            <div> 人员衔接</div>
          </div>
        </div>
        <span class="arrow "> </span>
        <div class="step-right">

          <div class="step-right-title">

            社区矫正决定机关决定
            <a-tag style="margin-left: 10px;" color="blue">
              待接收
            </a-tag>

          </div>
          <div class="subtext">办理人：<b>某某法院</b></div>
          <div class="subtext">办理人2：<b>某某法院</b></div>
          <div class="subtext">办理人3：<b>某某法院</b></div>

        </div>
      </div> -->
  </div>
</template>

  <script>
  export default {
    props: {
      steps: {
        default: () => [],
        type: Array
      }
    },
    watch: {
      steps: {
        handler() {
          const list = []
          this.steps.map(item => {
            item.steps.map((item2, index) => {
              list.push({
                groupName: !index ? item.groupName : null,
                ...item2
              })
            })
          })
        //   this.list = list
        this.list = this.steps
        },
        deep: true
      }
    },
    data() {
      return {
        list: []
      }
    }
  }
  </script>

  <style lang="less" scoped>
  .step-title {
    height: 44px;
    // border-bottom: 1px solid #e8e8e8;
    padding-left: 20px;
    margin-bottom: 20px;
    line-height: 44px;
    font-size: 16px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    color: #1890FF;
  }

  .step-wrapper {

    // .step-left{}
    .step {
      position: relative;
      padding-left: 116px;

      &:last-child {
        .step-right {
          border: none;
        }
      }

      &.active {
        .step-left {
          color: #1690FF;

          .icon {
            &.icon2 {
              background: url('~@/assets/step2-active.png') no-repeat center;
              background-size: 100%;
            }

            &.icon3 {
              background: url('~@/assets/step3-active.png') no-repeat center;
              background-size: 100%;
            }

            &.icon4 {
              background: url('~@/assets/step4-active.png') no-repeat center;
              background-size: 100%;
            }
          }

        }

        .arrow {
          background: url('~@/assets/arrow-active.png') no-repeat;
          background-size: 100%;

          &.error {
            background: url('~@/assets/arrow-error.png') no-repeat;
            background-size: 100%;
          }
        }
      }

      .arrow {
        background: url('~@/assets/arrow-default.png') no-repeat;
        background-size: 100%;
        display: inline-block;
        width: 16px;
        height: 16px;
        position: absolute;
        left: 116px;
        top: 0px;
        transform: translateX(-50%);

        &.active {
          background: url('~@/assets/arrow-active.png') no-repeat;
          background-size: 100%;

        }
      }

      .step-left {
        position: absolute;
        left: 0px;
        width: 116px;
        text-align: right;
        padding-right: 16px;
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #B9BCC1;

        .step-left-item {
          text-align: center;
          display: inline-block;
          width: 100%;
        }

        .icon {
          display: inline-flex;
          width: 32px;
          height: 32px;
          background: url('~@/assets/step1.png') no-repeat center;
          background-size: 100%;

          &.icon1 {
            background: url('~@/assets/step1.png') no-repeat center;
            background-size: 100%;
          }

          &.icon2 {
            background: url('~@/assets/step2.png') no-repeat center;
            background-size: 100%;
          }

          &.icon3 {
            background: url('~@/assets/step3.png') no-repeat center;
            background-size: 100%;
          }

          &.icon4 {
            background: url('~@/assets/step4.png') no-repeat center;
            background-size: 100%;
          }
        }
      }

      .step-right {
        border-left: 1px dashed #E3E3E3;
        padding-left: 16px;
        padding-bottom: 30px;

        .step-right-title {
          // padding-top: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;

          color: #000000;
          line-height: 1.2em;
          padding-bottom: 6px;
          display: flex;
          align-items: center;
        }

        .subtext {
          color: #919398;
          font-size: 14px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          line-height: 1.5em;
          padding: 3px 0;

          .reason {
            // width: 284px;
            margin-top: 6px;
            background: #F4F6FA;
            font-size: 14px;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            line-height: 1.3em;
            padding: 10px;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
          }

          b {
            color: #333;
            font-weight: normal;
          }
        }
      }
    }
  }
  </style>

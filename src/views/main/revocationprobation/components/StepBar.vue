<template>
  <div id="smartwizard" dir="" class="sw sw-justified sw-theme-arrows">
    <ul class="nav nav-progress">

      <li class="nav-item" @click="handleStepChange(item,index)" v-for="(item, index) in stepList" :key="index">
        <a class="nav-link " href="#step-1" :class="{active:item===value,default:currentNodeIndex<index,done:currentNodeIndex>index}">
          <div class="num">{{ index + 1 }}</div>
          {{ stepBarObj[item] }}
        </a>
      </li>
      <!-- <li class="nav-item" @click="handleStepChange(2)">
        <a class="nav-link default active" href="#step-2">
          <span class="num">2</span>
          初审/合议
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(3)">
        <a class="nav-link default" href="#step-3">
          <span class="num">3</span>
          初审/评议
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(4)">
        <a class="nav-link default" href="#step-4">
          <span class="num">4</span>
          区县局审批
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(5)">
        <a class="nav-link default" href="#step-4">
          <span class="num">5</span>
          征求检察建议
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(6)">
        <a class="nav-link default" href="#step-4">
          <span class="num">6</span>
          接收检察建议
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(7)">
        <a class="nav-link default" href="#step-4">
          <span class="num">7</span>
          提请撤销缓刑
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(8)">
        <a class="nav-link default" href="#step-4">
          <span class="num">8</span>
          处理结果
        </a>
      </li>
      <li class="nav-item" @click="handleStepChange(9)">
        <a class="nav-link default" href="#step-4">
          <span class="num">9</span>
          文书打印
        </a>
      </li> -->
    </ul>
  </div>
</template>

<script>
import { hasRole } from '@/utils/permissions'

// 新增  =  xz   = 新增
// 司法所合议审核  =  sh   = 初审/合议
// 区县局评议审核  =  sh1   = 初审/评议
// 区县局审批  =  sp1   = 区县局审批
// 市局评议审核  =  sh2   = 市局评议
// 市局审批  =  sp2   = 市局审批
// 省厅评议审核  =  sh3   = 省厅评议
// 省厅审批  =  sp3   = 省厅审批
// 征求检查建议  =  zqjcjy   = 征求检察建议
// 接收检查建议  =  jsjcjy   = 接收检察建议
// 提请撤销  =  tqcx   = 提请撤销缓刑
// 处理结果  =  cljg   = 处理结果
const stepBarObj = {
  xz: '新增',
  sh: '初审/合议',
  sh1: '初审/评议',
  sp1: '区县局审批',
  sh2: '市局评议',
  sp2: '市局审批',
  sh3: '省厅评议',
  sp3: '省厅审批',
  zqjcjy: '征求检察建议',
  jsjcjy: '接收检察建议',
  tqcx: '提请撤销缓刑',
  cljg: '处理结果'
}
export default {
  props: {
    processNodes: {
      default: '',
      type: String
    },
    currentNode: {
      default: '',
      type: String
    },
    value: {
      default: '',
      type: String
    }
  },

  data() {
    return {
      stepList: [],
      stepBarObj,
      activeIndex: ''
    }
  },
  mounted() {
    if (this.processNodes) {
      this.stepList = this.processNodes.split(',')
    }
  },
  computed: {
     currentNodeIndex () {
       return this.stepList.indexOf(this.currentNode)
     }

  },
  methods: {
    handleStepChange(code, index) {
      console.log(code, this.value, hasRole('SFSSZ'))
      if (code === 'sh' && this.value === 'xz' && !hasRole('SFSSZ')) {
        return
      }
      if (index <= this.currentNodeIndex) {
        this.$emit('input', code)
      }
    }

  }
}
</script>

<style lang="less" scoped>
@import url('./smart_wizard_all.min.css');

.sw {
  .nav-link {
    display: flex;
    align-items: center;
  }
}
</style>

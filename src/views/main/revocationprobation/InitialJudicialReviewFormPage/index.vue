<template>
  <div>
    <a-alert style="margin: 15px 0;" message="司法所负责人初审信息" type="info" show-icon />
    <InitialReviewInformation
      :readOnly="parentData.readOnly || disabled"
      :baseObj="parentData.detailObj.phase.sh"
      @change="handleChange"
      ref="InitialReviewInformation" />
    <template v-if="!flagType">
      <a-alert style="margin: 15px 0;" message="司法所合议信息" type="info" show-icon />
      <DeliberationInformation
        :readOnly="parentData.readOnly ||disabled"
        :baseObj="parentData.detailObj.phase.sh"
        ref="DeliberationInformation" />
    </template>

  </div>
</template>

<script>
// 司法所负责人初审信息
import InitialReviewInformation from '@/views/main/revocationprobation/InitialJudicialReviewFormPage/components/InitialReviewInformation.vue'

// 司法所合议信息
import DeliberationInformation from '@/views/main/revocationprobation/InitialJudicialReviewFormPage/components/DeliberationInformation.vue';

export default {
  components: { InitialReviewInformation, DeliberationInformation },
  inject: ['parentData'],
  data() {
    return {
      flagType: false
    }
  },
  props: {
    disabled: {
      default: false
    }
  },
  methods: {
    handleChange(code) {
      this.flagType = code
    },
    async handleSubmit() {
      const postData1 = await this.$refs.InitialReviewInformation.handleSubmit();
      let postDAta2 = {}
      if (!this.flagType) {
        postDAta2 = await this.$refs.DeliberationInformation.handleSubmit();
      }
      // try {
      //   postDAta2 = await this.$refs.DeliberationInformation.handleSubmit();
      // } catch (error) {

      // }
      return Promise.resolve({
        ...postData1, ...postDAta2
      })
    }
  }

}
</script>

<style lang="scss" scoped></style>

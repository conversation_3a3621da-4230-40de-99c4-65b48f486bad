<template>
  <div>
    <a-alert
      style="margin: 15px 0;"
      message="矫正对象基本信息"
      type="info"
      show-icon/>
    <BaseInfo :baseObj="parentData.detailObj" ref="BaseInfo"/>
    <a-alert
      style="margin: 15px 0;"
      message="提请信息"
      type="info"
      show-icon/>
    <ProsecutorialAdviceFrom
      :readOnly="parentData.readOnly||disabled"
      :baseObj="parentData.detailObj"
      @change="handleChange"
      ref="ProsecutorialAdviceFrom"/>

  </div>
</template>

<script>
// 基本信息
import BaseInfo from '@/views/main/revocationprobation/SolicitingProsecutorialAdvice/components/BaseInfo.vue'
// 检察建议
import ProsecutorialAdviceFrom from './components/ProsecutorialAdviceFrom.vue'

export default {
  components: { ProsecutorialAdviceFrom, BaseInfo },
  inject: ['parentData'],
  data() {
    return {
      flagType: false
    }
  },
  props: {
    disabled: {
      default: false
    }
  },
  methods: {
    handleChange(code) {
      this.flagType = code
    },
    async handleSubmit() {
      const postData1 = await this.$refs.ProsecutorialAdviceFrom.handleSubmit();
      const postDAta2 = {}
      return Promise.resolve({
        ...postData1, ...postDAta2
      })
    }
  }

}
</script>

<style lang="scss" scoped></style>

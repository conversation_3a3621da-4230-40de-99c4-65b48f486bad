<template>
  <div>
    <a-alert
      style="margin: 15px 0;"
      message="矫正对象基本信息（如有部分同步信息缺失，请手动补全）"
      type="info"
      show-icon />
    <BaseInfo :readOnly="parentData.readOnly || readOnly" :baseObj="parentData.detailObj" ref="BaseInfo"/>
    <a-alert
      style="margin: 15px 0;"
      message="提请撤销缓刑信息"
      type="info"
      show-icon />
    <RevokeProbationRequest :readOnly="parentData.readOnly ||readOnly" :baseObj="parentData.detailObj" ref="RevokeProbationRequest"/>
  </div>
</template>

<script>
// 矫正对象基本信
import BaseInfo from './components/BaseInfo.vue';
// 提请撤销缓刑信息
import RevokeProbationRequest from './components/RevokeProbationRequest.vue';
    export default {
        components: { BaseInfo, RevokeProbationRequest },
        inject: ['parentData'],
        props: {
          readOnly: {
            default: false,
            type: <PERSON><PERSON><PERSON>
          }
        },
        methods: {
        async  handleSubmit() {
          const postData1 = await this.$refs.BaseInfo.handleSubmit();
          const postData2 = await this.$refs.RevokeProbationRequest.handleSubmit();
          return Promise.resolve({
            ...postData1, ...postData2
          })
        }
        }

    }
</script>

<style lang="less" scoped>

</style>

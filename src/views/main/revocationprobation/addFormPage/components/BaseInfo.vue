<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
      <a-col :span="24" v-show="false">
        <a-form-item label=" 姓名 " :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <div style="display:flex">
            <a-input
              style="width:39%"
              disabled
              class="width"
              placeholder="请输入姓名 "
              v-decorator="['sqjzryId', { rules: [{ required: true, message: '请选择证件类型 ！' }] }]" />
            <a-button style="margin-left:10px" @click="chooseSqjzry">选择矫正对象</a-button>
          </div>

        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label=" 姓名 " :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <div style="display:flex">
            <a-input
              style="width:39%"
              disabled
              class="width"
              placeholder="请输入姓名 "
              v-decorator="['xm', { rules: [{ required: true, message: '请选择证件类型 ！' }] }]" />
            <a-button style="margin-left:10px" @click="chooseSqjzry">选择矫正对象</a-button>
          </div>

        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="身份证" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            disabled
            v-decorator="['zjhm', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            style="width: 100%;"
            placeholder="请选择"
            disabled
            v-decorator="['jzlb', { initialValue: '', rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="矫正单位" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-correction-org-tree
            disabled
            v-decorator="['sfs', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>

    </a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

<script>
import chooseSqjzryRadio from '../../../correctionobjectinformation/chooseSqjzryRadio.vue'

import { mapGetters } from 'vuex'
import moment from 'moment'
function isEmpty(obj) {
    if (!obj) {
        return true
    } else {
        return Object.keys(obj).length === 0;
    }
}
export default {
    components: { chooseSqjzryRadio },
    props: {
        readOnly: {
            default: false,
            type: Boolean
        },
        baseObj: {
            default: () => { },
            type: Object

        }
    },
    computed: {
        ...mapGetters(['userInfo'])
    },
    data() {
        return {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
            shjgTree: [
                {
                    children: [{
                        children: [],
                        id: '11',
                        parentId: '1',
                        title: '信息无误，确认接收',
                        value: '11'
                    }, {
                        children: [],
                        id: '12',
                        parentId: '1',
                        title: '存在部分信息错误，确认接收',
                        value: '12'
                    }],
                    id: '1',
                    parentId: '0',
                    title: '正常接收',
                    disabled: true,
                    value: '1'
                }, {
                    children: [{
                        children: [],
                        id: '21',
                        parentId: '2',
                        title: '信息错误，非我区管辖矫正对象',
                        value: '21'
                    }, {
                        children: [],
                        id: '22',
                        parentId: '2',
                        title: '数据滞后，已办理手续',
                        value: '22'
                    }, {
                        children: [],
                        id: '23',
                        parentId: '2',
                        title: '数据重复发送',
                        value: '23'
                    }],
                    id: '2',
                    parentId: '0',
                    title: '退回',
                    disabled: true,
                    value: '2'
                }, {
                    children: [],
                    id: '3',
                    parentId: '0',
                    title: '其他',
                    value: '3'

                }],
            form: this.$form.createForm(this)
        }
    },
   async mounted() {
        // this.$nextTick(() => {
            await this.$nextTick()
            if (!isEmpty(this.baseObj)) {
                this.setVals()
            } else {
                this.form.setFieldsValue({
                    shry: this.userInfo.name,
                    shsj: moment().format('YYYY-MM-DD HH:mm:ss')
                })
            }
        // })
    },
    watch: {
        baseObj: {
            handler() {
                if (this.readOnly) {
                    this.$nextTick(() => {
                        // alert(2)
                        this.setVals()
                    })
                }
            },
            deep: true
        }
    },
    methods: {
        chooseSqjzry() {
            // 关联社区矫正对象
            this.$refs.chooseSqjzryRadio.choose('stay')
        },
        jzdx(e) {
            console.log(e)
            const jzdxObj = e
            jzdxObj.sqjzryId = e.id
            this.setVals({
                ...jzdxObj,
                zjhm: jzdxObj.sfzh,
                jzlb: jzdxObj.jzlbName,
                sfs: jzdxObj.jzjg
            })
        },
        handleSubmit() {
            return new Promise((resolve, reject) => {
                this.form.validateFieldsAndScroll((err, values) => {
                    if (!err) {
                        resolve(values)
                    } else {
                        this.$message.error('信息未填写完成,请检查')
                        reject(err)
                    }
                })
            })
        },
        setVals(vals) {
            const allData = this.form.getFieldsValue()
            const values = {}
            let Odata = this.baseObj
            if (vals) {
                Odata = vals
            }
            Object.keys(allData).map(key => {
                if (Odata[key]) {
                    values[key] = Odata[key]
                } else {
                    values[key] = null
                }
            })
            // values.zsd = addres
            // values.hjszd = addres2
            this.form.setFieldsValue({
                ...values
            })
        }
    }

}
</script>

<style lang="less" scoped></style>

<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
      <a-col :span="12">
        <a-form-item label="拟呈送法院" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <div style="display:flex">
            <sh-select
              style="width: 100%;"
              api="/extOrgInfo/list?type=20"
              :disabled="readOnly"
              placeholder="请选择"
              labelKey="orgName"
              valueKey="orgCode"
              v-decorator="['fyP1', {initialValue:'', rules: [{ required: true, message: '请输入' }] }]" />
          </div>

        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="法院类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            style="width: 100%;"
            disabled
            :options="[{label:'默认法院',value:1}]"
            placeholder="请选择"
            labelKey="label"
            valueKey="value"
            v-decorator="['fylx', {initialValue:1, rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="提请依据" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-tree-select
            :disabled="readOnly"
            tree-checkable
            @onChange="handleChange"
            v-decorator="['tqyjP1', {initialValue:[], rules: [{ required: true, message: '请输入' }] }]"
            apiURL="/sysDictData/tree?dictTypeId=1531931283775598594"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="提请理由" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['tqlyP1', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-textarea
            :disabled="readOnly"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入"
            v-decorator="['bzP1', { rules: [{ required: false, message: '请输入' }] }]" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="征求检察院名称" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <div style="display:flex">
            <sh-select
              style="width: 100%;"
              api="/extOrgInfo/list?type=30"
              :disabled="readOnly"
              placeholder="请选择"
              labelKey="orgName"
              valueKey="orgCode"
              v-decorator="['jcyP1', {initialValue:'', rules: [{ required: true, message: '请输入' }] }]" />
          </div>

        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="提请撤销缓刑证据" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <sh-file-upload
            :disabled="readOnly"
            v-decorator="['files1', {initialValue:[], rules: [{ required: false, message: '请输入' }] }]"
            uploadUrl="/api/sysFileInfo/uploadOss" />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="司法所申请人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input disabled v-decorator="['addPsnName', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="司法所申请时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 100%"
            showTime
            placeholder="请选择"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled="readOnly"
            v-decorator="['addTime', { rules: [{ required: true, message: '请选择！' }] }]" />
        </a-form-item>

      </a-col></a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

  <script>
  import chooseSqjzryRadio from '../../../correctionobjectinformation/chooseSqjzryRadio.vue'

  import { mapGetters } from 'vuex'
  import moment from 'moment'
  function isEmpty(obj) {
      if (!obj) {
          return false
      } else {
          return Object.keys(obj).length === 0;
      }
  }
  export default {
      components: { chooseSqjzryRadio },
      props: {
          readOnly: {
              default: false,
              type: Boolean
          },
          baseObj: {
              default: () => { },
              type: Object

          }
      },
      computed: {
          ...mapGetters(['userInfo'])
      },
      data() {
          return {
              labelCol: {
                  xs: { span: 24 },
                  sm: { span: 6 }
              },
              wrapperCol: {
                  xs: { span: 24 },
                  sm: { span: 16 }
              },
              labelCol2: {
                  xs: { span: 24 },
                  sm: { span: 3 }
              },
              wrapperCol2: {
                  xs: { span: 24 },
                  sm: { span: 20 }
              },
              shjgTree: [
                  {
                      children: [{
                          children: [],
                          id: '11',
                          parentId: '1',
                          title: '信息无误，确认接收',
                          value: '11'
                      }, {
                          children: [],
                          id: '12',
                          parentId: '1',
                          title: '存在部分信息错误，确认接收',
                          value: '12'
                      }],
                      id: '1',
                      parentId: '0',
                      title: '正常接收',
                      disabled: true,
                      value: '1'
                  }, {
                      children: [{
                          children: [],
                          id: '21',
                          parentId: '2',
                          title: '信息错误，非我区管辖矫正对象',
                          value: '21'
                      }, {
                          children: [],
                          id: '22',
                          parentId: '2',
                          title: '数据滞后，已办理手续',
                          value: '22'
                      }, {
                          children: [],
                          id: '23',
                          parentId: '2',
                          title: '数据重复发送',
                          value: '23'
                      }],
                      id: '2',
                      parentId: '0',
                      title: '退回',
                      disabled: true,
                      value: '2'
                  }, {
                      children: [],
                      id: '3',
                      parentId: '0',
                      title: '其他',
                      value: '3'

                  }],
              form: this.$form.createForm(this)
          }
      },
      mounted() {
        this.$nextTick(() => {
          if (!isEmpty(this.baseObj)) {
              this.setVals()
          } else {
              this.form.setFieldsValue({
                  addPsnName: this.userInfo.name,
                  addTime: moment().format('YYYY-MM-DD HH:mm:ss')
              })
          }
        })
      },
      watch: {
          baseObj: {
              handler() {
                  if (this.readOnly) {
                      this.$nextTick(() => {
                          // alert(2)
                          this.setVals()
                      })
                  }
              },
              deep: true,
              immediate: true
          }
      },
      methods: {
        handleChange({ value, label, extra }) {
            console.log(value, label, extra)
            this.form.setFieldsValue({
                tqlyP1: label.toString()
            })
        },
          chooseSqjzry() {
              // 关联社区矫正对象
              this.$refs.chooseSqjzryRadio.choose('stay')
          },
          jzdx(e) {
              console.log(e)
          },
          handleSubmit() {
              return new Promise((resolve, reject) => {
                  this.form.validateFieldsAndScroll((err, values) => {
                      if (!err) {
                          resolve(values)
                      } else {
                          this.$message.error('信息未填写完成,请检查')
                          reject(err)
                      }
                  })
              })
          },
          setVals() {
              const allData = this.form.getFieldsValue()
              const values = {}

              Object.keys(allData).map(key => {
                  if (this.baseObj[key]) {
                      values[key] = this.baseObj[key]
                  } else {
                      values[key] = null
                  }
              })
              // values.zsd = addres
              // values.hjszd = addres2
              this.form.setFieldsValue({
                  ...values
              })
          }
      }

  }
  </script>

  <style lang="less" scoped></style>

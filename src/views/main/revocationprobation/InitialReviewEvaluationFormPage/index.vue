<template>
  <div>
    <a-alert style="margin: 15px 0;" :message="`${titlePre}司法局初审信息`" type="info" show-icon />
    <InitialReviewInformation
      :readOnly="parentData.readOnly || disabled"
      :baseObj="detail"
      @change="handleChange"
      ref="InitialReviewInformation" />
    <template v-if="!flagType">
      <!-- message="区县司法局评议审核信息" -->
      <a-alert style="margin: 15px 0;" :message="`${titlePre}司法局评议审核信息`" type="info" show-icon />
      <DeliberationInformation
        :readOnly="parentData.readOnly ||disabled"
        :baseObj="detail"
        ref="DeliberationInformation" />
    </template>

  </div>
</template>

<script>
// 司法所负责人初审信息
import InitialReviewInformation from './components/InitialReviewInformation.vue'

// 司法所合议信息
import DeliberationInformation from './components/DeliberationInformation.vue';

export default {
  components: { InitialReviewInformation, DeliberationInformation },
  inject: ['parentData'],
  data() {
    return {
      flagType: false
    }
  },
  props: {
    detail: {
      type: Object,
      default: () => { }

    },
    disabled: {
      default: false
    }
  },
  computed: {
    titlePre() {
      if (['sp2', 'sh2'].includes(this.parentData.detailObj.zt)) {
        return '市局'
      } else if (['sp3', 'sh3'].includes(this.parentData.detailObj.zt)) {
        return '省厅'
      } else {
        return '区县'
      }
    }
  },
  mounted() {
    if (this.detail.csjg === 'return') {
      this.flagType = true
    } else {
      this.flagType = false
    }
  },
  methods: {
    handleChange(code) {
      this.flagType = code
    },
    async handleSubmit() {
      const postData1 = await this.$refs.InitialReviewInformation.handleSubmit();
      let postDAta2 = {}
      if (!this.flagType) {
        postDAta2 = await this.$refs.DeliberationInformation.handleSubmit();
      }
      // try {
      //   postDAta2 = await this.$refs.DeliberationInformation.handleSubmit();
      // } catch (error) {

      // }
      return Promise.resolve({
        ...postData1, ...postDAta2
      })
    }
  }

}
</script>

<style lang="scss" scoped></style>

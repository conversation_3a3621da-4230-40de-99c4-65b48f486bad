<template>
  <a-form :form="form">
    <a-row :gutter="24">
      <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio" />
      <a-col :span="24">
        <a-col :span="12">
          <a-form-item label="评议审核事项" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input :disabled="readOnly" v-decorator="['shsx', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主持人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input :disabled="readOnly" v-decorator="['zcr', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="评议审核地点" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input :disabled="readOnly" v-decorator="['shdd', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              showTime
              placeholder="请选择"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              :disabled="readOnly"
              v-decorator="['shsj', { rules: [{ required: true, message: '请选择！' }] }]" />          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="评议审核人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-input :disabled="readOnly" v-decorator="['shry', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="记录人" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-input :disabled="readOnly" v-decorator="['jlr', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="评议审核情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['pyshqk', { rules: [{ required: true, message: '请输入' }] }]" />          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="评议审核意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['pyshyj', { rules: [{ required: true, message: '请输入' }] }]" />          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="负责人" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-input :disabled="readOnly" v-decorator="['fzr', { rules: [{ required: true, message: '请选择！' }] }]"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="readOnly"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入"
              v-decorator="['bz', { rules: [{ required: true, message: '请输入' }] }]" />          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="评议审核意见表" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <sh-file-upload
              :disabled="readOnly"
              v-decorator="['file1', {initialValue:[], rules: [{ required: false, message: '请输入' }] }]"
              uploadUrl="/api/sysFileInfo/uploadOss" />     </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="撤销缓刑审核表" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <sh-file-upload
              :disabled="readOnly"
              v-decorator="['file2', {initialValue:[], rules: [{ required: false, message: '请输入' }] }]"
              uploadUrl="/api/sysFileInfo/uploadOss" />     </a-form-item>
        </a-col>

      </a-col>
    </a-row>
    <!-- {{ userInfo }} -->
  </a-form>
</template>

    <script>
    import chooseSqjzryRadio from '../../../correctionobjectinformation/chooseSqjzryRadio.vue'

    import { mapGetters } from 'vuex'
    import moment from 'moment'
    function isEmpty(obj) {
        if (!obj) {
            return true
        } else {
            return Object.keys(obj).length === 0;
        }
    }
    export default {
        components: { chooseSqjzryRadio },
        props: {
            readOnly: {
                default: false,
                type: Boolean
            },
            baseObj: {
                default: () => { },
                type: Object

            }
        },
        computed: {
            ...mapGetters(['userInfo'])
        },
        data() {
            return {
                labelCol: {
                    xs: { span: 24 },
                    sm: { span: 6 }
                },
                wrapperCol: {
                    xs: { span: 24 },
                    sm: { span: 16 }
                },
                labelCol2: {
                    xs: { span: 24 },
                    sm: { span: 3 }
                },
                wrapperCol2: {
                    xs: { span: 24 },
                    sm: { span: 20 }
                },
                shjgTree: [
                    {
                        children: [{
                            children: [],
                            id: '11',
                            parentId: '1',
                            title: '信息无误，确认接收',
                            value: '11'
                        }, {
                            children: [],
                            id: '12',
                            parentId: '1',
                            title: '存在部分信息错误，确认接收',
                            value: '12'
                        }],
                        id: '1',
                        parentId: '0',
                        title: '正常接收',
                        disabled: true,
                        value: '1'
                    }, {
                        children: [{
                            children: [],
                            id: '21',
                            parentId: '2',
                            title: '信息错误，非我区管辖矫正对象',
                            value: '21'
                        }, {
                            children: [],
                            id: '22',
                            parentId: '2',
                            title: '数据滞后，已办理手续',
                            value: '22'
                        }, {
                            children: [],
                            id: '23',
                            parentId: '2',
                            title: '数据重复发送',
                            value: '23'
                        }],
                        id: '2',
                        parentId: '0',
                        title: '退回',
                        disabled: true,
                        value: '2'
                    }, {
                        children: [],
                        id: '3',
                        parentId: '0',
                        title: '其他',
                        value: '3'

                    }],
                form: this.$form.createForm(this)
            }
        },
         async mounted() {
          if (!isEmpty(this.baseObj)) {
              this.setVals()
            } else {
                this.form.setFieldsValue({
                    shry: this.userInfo.name,
                    shsj: moment().format('YYYY-MM-DD HH:mm:ss')
                })
            }
        },
        watch: {
            baseObj: {
                handler() {
                    if (this.readOnly) {
                        this.$nextTick(() => {
                            // alert(2)
                            this.setVals()
                        })
                    }
                },
                deep: true
            }
        },
        methods: {
            chooseSqjzry() {
                // 关联社区矫正对象
                this.$refs.chooseSqjzryRadio.choose('stay')
            },
            jzdx(e) {
                console.log(e)
            },
            handleSubmit() {
                return new Promise((resolve, reject) => {
                    this.form.validateFieldsAndScroll((err, values) => {
                        if (!err) {
                            resolve(values)
                        } else {
                            this.$message.error('信息未填写完成,请检查')
                            reject(err)
                        }
                    })
                })
            },
            setVals() {
                const allData = this.form.getFieldsValue()
                const values = {}
                Object.keys(allData).map(key => {
                    if (this.baseObj[key]) {
                        values[key] = this.baseObj[key]
                    } else {
                        values[key] = null
                    }
                })
                // values.zsd = addres
                // values.hjszd = addres2
                this.form.setFieldsValue({
                    ...values
                })
            }
        }

    }
    </script>

    <style lang="less" scoped></style>

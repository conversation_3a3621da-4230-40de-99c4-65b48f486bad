<template>
  <a-modal
    title="新增收监执行接收表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label=""
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入" v-decorator="['taskId', {rules: [{required: true, message: '请输入！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据状态" v-decorator="['zt', {rules: [{required: true, message: '请输入数据状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据来源(机构类型)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据来源(机构类型)" v-decorator="['sjlylx', {rules: [{required: true, message: '请输入数据来源(机构类型)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="接收单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入接收单位" v-decorator="['jsdw', {rules: [{required: true, message: '请输入接收单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="接收单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入接收单位名称" v-decorator="['jsdwmc', {rules: [{required: true, message: '请输入接收单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="推送单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入推送单位" v-decorator="['tsdw', {rules: [{required: true, message: '请输入推送单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="推送单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入推送单位名称" v-decorator="['tsdwmc', {rules: [{required: true, message: '请输入推送单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="送达时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择送达时间" v-decorator="['sdsj',{rules: [{ required: true, message: '请选择送达时间！' }]}]" @change="onChangesdsj"/>
        </a-form-item>
        <a-form-item
          label="统一赋号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入统一赋号" v-decorator="['tyfh', {rules: [{required: true, message: '请输入统一赋号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社区矫正案件编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社区矫正案件编号" v-decorator="['sqjzajbh', {rules: [{required: true, message: '请输入社区矫正案件编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="（法院｜监狱）案件标识"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入（法院｜监狱）案件标识" v-decorator="['ajbs', {rules: [{required: true, message: '请输入（法院｜监狱）案件标识！'}]}]" />
        </a-form-item>
        <a-form-item
          label="罪犯编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入罪犯编号" v-decorator="['zfbh', {rules: [{required: true, message: '请输入罪犯编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['xm', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="曾用名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入曾用名" v-decorator="['cym', {rules: [{required: true, message: '请输入曾用名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['xb', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类型" v-decorator="['zjlx', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件号码" v-decorator="['zjhm', {rules: [{required: true, message: '请输入证件号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="决定书文号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入决定书文号" v-decorator="['jdswh', {rules: [{required: true, message: '请输入决定书文号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否决定收监"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否决定收监" v-decorator="['sfjdsj', {rules: [{required: true, message: '请输入是否决定收监！'}]}]" />
        </a-form-item>
        <a-form-item
          label="（不）收监决定日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择（不）收监决定日期" v-decorator="['sjjdrq',{rules: [{ required: true, message: '请选择（不）收监决定日期！' }]}]" @change="onChangesjjdrq"/>
        </a-form-item>
        <a-form-item
          label="（不）收监原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入（不）收监原因" v-decorator="['sjyy', {rules: [{required: true, message: '请输入（不）收监原因！'}]}]" />
        </a-form-item>
        <a-form-item
          label="（不）收监决定机关"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入（不）收监决定机关" v-decorator="['sjjdjg', {rules: [{required: true, message: '请输入（不）收监决定机关！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { acceptPrisonExecuteAdd } from '@/api/modular/main/acceptprisonexecute/acceptPrisonExecuteManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        sdsjDateString: '',
        sjjdrqDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.sdsj = this.sdsjDateString
            values.sjjdrq = this.sjjdrqDateString
            acceptPrisonExecuteAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangesdsj(date, dateString) {
        this.sdsjDateString = dateString
      },
      onChangesjjdrq(date, dateString) {
        this.sjjdrqDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

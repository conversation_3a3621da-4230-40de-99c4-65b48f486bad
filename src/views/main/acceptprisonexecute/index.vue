<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('acceptPrisonExecute:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="数据来源">
                <a-select v-model="queryParam.sjlylx" placeholder="数据来源" >
                  <a-select-option v-for="(item,index) in sjlylxDropDown" :key="index" :value="item.code" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="送达时间">
                <a-range-picker
                  v-model="queryParam.dates"
                  format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="接收单位">
                <a-tree-select
                  v-model="queryParam.jsdw"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择接收单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="数据来源(机构类型)">
                  <a-input v-model="queryParam.sjlylx" allow-clear placeholder="请输入数据来源(机构类型)"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="接收单位">
                  <a-input v-model="queryParam.jsdw" allow-clear placeholder="请输入接收单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="接收单位名称">
                  <a-input v-model="queryParam.jsdwmc" allow-clear placeholder="请输入接收单位名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="推送单位">
                  <a-input v-model="queryParam.tsdw" allow-clear placeholder="请输入推送单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="推送单位名称">
                  <a-input v-model="queryParam.tsdwmc" allow-clear placeholder="请输入推送单位名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="送达时间">
                  <a-date-picker style="width: 100%" placeholder="请选择送达时间" v-model="queryParam.sdsjDate" @change="onChangesdsj"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="统一赋号">
                  <a-input v-model="queryParam.tyfh" allow-clear placeholder="请输入统一赋号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="社区矫正案件编号">
                  <a-input v-model="queryParam.sqjzajbh" allow-clear placeholder="请输入社区矫正案件编号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="（法院｜监狱）案件标识">
                  <a-input v-model="queryParam.ajbs" allow-clear placeholder="请输入（法院｜监狱）案件标识"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="罪犯编号">
                  <a-input v-model="queryParam.zfbh" allow-clear placeholder="请输入罪犯编号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="曾用名">
                  <a-input v-model="queryParam.cym" allow-clear placeholder="请输入曾用名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="性别">
                  <a-input v-model="queryParam.xb" allow-clear placeholder="请输入性别"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件类型">
                  <a-input v-model="queryParam.zjlx" allow-clear placeholder="请输入证件类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件号码">
                  <a-input v-model="queryParam.zjhm" allow-clear placeholder="请输入证件号码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="决定书文号">
                  <a-input v-model="queryParam.jdswh" allow-clear placeholder="请输入决定书文号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="是否决定收监">
                  <a-input v-model="queryParam.sfjdsj" allow-clear placeholder="请输入是否决定收监"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="（不）收监决定日期">
                  <a-date-picker style="width: 100%" placeholder="请选择（不）收监决定日期" v-model="queryParam.sjjdrqDate" @change="onChangesjjdrq"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="（不）收监原因">
                  <a-input v-model="queryParam.sjyy" allow-clear placeholder="请输入（不）收监原因"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="（不）收监决定机关">
                  <a-input v-model="queryParam.sjjdjg" allow-clear placeholder="请输入（不）收监决定机关"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('acceptPrisonExecute:add')" >
          <!--          <a-button type="primary" v-if="hasPerm('acceptPrisonExecute:add')" icon="plus" @click="$refs.addForm.add()">新增收监执行接收表</a-button>-->
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('acceptPrisonExecute:edit')" @click="$refs.editForm.edit(record)">详情</a>
        </span>
        <span slot="sjlylx" slot-scope="text">
          {{ 'xtjgdm' | dictType(text) }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { acceptPrisonExecutePage, acceptPrisonExecuteDelete } from '@/api/modular/main/acceptprisonexecute/acceptPrisonExecuteManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
          ellipsis: true,
          title: '数据来源',
            align: 'center',
            dataIndex: 'sjlylx',
            scopedSlots: { customRender: 'sjlylx' }
          },
          {
          ellipsis: true,
          title: '姓名',
            align: 'center',
            dataIndex: 'xm'
          },
          {
          ellipsis: true,
          title: '接收单位',
            align: 'center',
            dataIndex: 'jsdwmc'
          },
          {
          ellipsis: true,
          title: '推送单位',
            align: 'center',
            dataIndex: 'tsdwmc'
          },
          {
          ellipsis: true,
          title: '送达时间',
            align: 'center',
            dataIndex: 'sdsj',
            customRender: (val, record) => {
              if (val == null) {
                return ''
              }
              return moment(val).format('YYYY-MM-DD HH:mm')
            }
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return acceptPrisonExecutePage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        orgTree: [],
        ztDropDown: [],
        sjlylxDropDown: [],
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
      this.sysDictTypeDropDown()
      if (this.hasPerm('acceptPrisonExecute:edit') || this.hasPerm('acceptPrisonExecute:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const dates = this.queryParam.dates
        if (dates != null) {
          this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
          if (dates.length < 1) {
            delete this.queryParam.searchBeginTime
            delete this.queryParam.searchEndTime
          }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        delete obj.dates
        return obj
      },
      acceptPrisonExecuteDelete (record) {
        acceptPrisonExecuteDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangesdsj(date, dateString) {
        this.sdsjDateString = dateString
      },
      onChangesjjdrq(date, dateString) {
        this.sjjdrqDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      /**
       * 获取字典数据
       */
      sysDictTypeDropDown () {
        this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
        this.sjlylxDropDown = this.$options.filters['dictData']('sjlylx')
      },
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

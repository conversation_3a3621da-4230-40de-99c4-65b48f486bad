<template>
  <a-modal
    title="编辑收监执行接收表"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <!-- 步骤条 -->
    <div style="margin-bottom:20px">
      <a-steps v-model:current="current" type="navigation" :style="stepStyle" >
        <a-step title="个人基本信息" />
        <a-step title="案件信息" />
      </a-steps>
    </div>
    <!--基本信息-->
    <div v-show="current===0">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <!--个人信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >个人信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
                <a-form-item
                  label="罪犯编号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zfbh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xm']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="曾用名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['cym']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['xb']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="证件类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zjlx']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="证件号码"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['zjhm']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-spin>
    </div>
    <!--案件信息-->
    <div v-show="current===1">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <!--案件信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >案件信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="统一赋号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['tyfh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="案件标识"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['ajbs']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="决定书文号"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['jdswh']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="是否决定收监"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sfjdsj']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="（不）收监决定日期"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-date-picker style="width: 100%" :disabled="true" v-decorator="['sjjdrq']"/>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="（不）收监原因"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sjyy']" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24">
                <a-form-item
                  label="（不）收监决定机关"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input :disabled="true" v-decorator="['sjjdjg']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!--文书信息-->
          <div
            class="border"
            style="
                font-family: '微软雅黑', sans-serif;font-weight: 400;
                font-style: normal;color: #1078C9;
                background-color: rgba(228, 243, 255, 1);
              "
          >文书信息</div>
          <div class="border" style="">
            <a-row :gutter="24">
              <a-col :md="24" :sm="24">
                <s-table
                  ref="table"
                  :columns="wsColumns"
                  :data="wsLoadData"
                  :alert="false"
                  :rowKey="(record) => record.code"
                >
                </s-table>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-spin>
    </div>
    <a-spin :spinning="confirmLoading">
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { acceptCorrectionDocPage } from '@/api/modular/main/subList/subListManage';
  import { STable } from '@/components';
  export default {
    components: {
      STable
    },
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        current: 0,
        stepStyle: {
          marginBottom: '10px',
          boxShadow: '0px -1px 0 0 #e8e8e8 inset'
        },
        yesOrNoData: [
          { code: '1', name: '是' },
          { code: '0', name: '否' }
        ],
        // 表头 文书
        wsColumns: [
          {
          ellipsis: true,
          title: '法律文书名称',
            dataIndex: 'ws'
          },
          {
          ellipsis: true,
          title: '附件',
            dataIndex: 'wsdm'
          }
        ],
        wsLoadData: parameter => {
          parameter.contactId = this.recordId
          return acceptCorrectionDocPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        this.recordId = record.id
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              taskId: record.taskId,
              zt: record.zt,
              sjlylx: record.sjlylx,
              jsdw: record.jsdw,
              jsdwmc: record.jsdwmc,
              tsdw: record.tsdw,
              tsdwmc: record.tsdwmc,
              tyfh: record.tyfh,
              sqjzajbh: record.sqjzajbh,
              ajbs: record.ajbs,
              zfbh: record.zfbh,
              xm: record.xm,
              cym: record.cym,
              xb: record.xb,
              zjlx: record.zjlx,
              zjhm: record.zjhm,
              jdswh: record.jdswh,
              sfjdsj: record.sfjdsj,
              sjyy: record.sjyy,
              sjjdjg: record.sjjdjg
            }
          )
        }, 100)
        if (record.sjjdrq != null) {
            this.form.getFieldDecorator('sjjdrq', { initialValue: moment(record.sjjdrq, 'YYYY-MM-DD') })
        }
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <div>
    <a-form :form="form">
      <div class="cus-title-d">外省迁入审核详情信息</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="接收结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['opinionResult']" :options="resultOption"> </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="24">
          <a-form-item label="情况说明" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-input disabled v-decorator="['opinionRemark']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['extProcessPsn']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['extProcessDeptTel']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['extProcessDept']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="意见反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              format="YYYY-MM-DD"
              disabled
              v-decorator="['opinionDate', { initialValue: moment() }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="执行地变更复函" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-file-upload :fileList="form.getFieldValue('fileList')" disabled v-decorator="['fileList']" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import { getOrgTree } from '@/api/modular/system/orgManage'
import moment from 'moment'
import { sysDictTypeDropDown } from '@/api/modular/system/dictManage'
import { correctionPlacechangeImmigrationAdd } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
export default {
  data() {
    return {
      fjxDropDown: [],
      zxzlDropDown: [],
      sfjDropDown: [],
      mzDropDown: [],
      nsysqjzrylxDropDown: [],
      xbDropDown: [],
      orgTree: [],
      fileList: [],
      resultOption: [
        { label: '同意接收', value: '1' },
        { label: '不同意接收', value: '2' }
      ],
      jzlbSelect: [
        { name: '管制', value: '1' },
        { name: '缓刑', value: '2' },
        { name: '假释', value: '3' },
        { name: '暂予监外执行', value: '4' },
        { name: '其他', value: '5' }
      ],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      sdwtsjDateString: '',
      csrqDateString: '',
      jyfhqxDateString: '',
      ypxqksrqDateString: '',
      ypxqjsrqDateString: '',
      pjrqDateString: '',
      zpsjDateString: '',
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    this.getdata()
    this.initOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    initFormData(data) {
      this.$nextTick(() => {
        this.form.setFieldsValue(data)
        const fileList =
          data.files46002 && data.files46002.length > 0
            ? data.files46002.map(item => {
              return { uid: item.id, name: item.fileOriginName, status: 'done', url: item.filePath }
            })
            : []
        this.form.setFieldsValue({
          fileList
        })
      })
    },
    fileChange(fileList) {
      console.log(fileList)
      this.form.setFieldsValue({ fileList })
    },
    initOrgTree() {
      getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    sysDictTypeDropDown() {
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.crblxDropDown = this.$options.filters['dictData']('crblx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.hyzkDropDown = this.$options.filters['dictData']('hyzk')
      this.jyjxqkDropDown = this.$options.filters['dictData']('jyjxqk')
      this.zzmmDropDown = this.$options.filters['dictData']('zzmm')
      this.gjDropDown = this.$options.filters['dictData']('guoji')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
      this.wcnDropDown = this.$options.filters['dictData']('wcn')
    },
    moment,
    onChangecsrq(date, dateString) {
      this.csrqDateString = dateString
    },
    onChangejyfhqx(date, dateString) {
      this.jyfhqxDateString = dateString
    },
    onChangepjrq(date, dateString) {
      this.pjrqDateString = dateString
    },
    onChangeypxqksrq(date, dateString) {
      this.ypxqksrqDateString = dateString
    },
    onChangeypxqjsrq(date, dateString) {
      this.ypxqjsrqDateString = dateString
    },
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    getdata() {
      sysDictTypeDropDown({ code: 'sfj' }).then(res => {
        res.data.forEach(p => {
          this.sfjDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'fjx' }).then(res => {
        res.data.forEach(p => {
          this.fjxDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'zxzl' }).then(res => {
        res.data.forEach(p => {
          this.zxzlDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'xb' }).then(res => {
        res.data.forEach(p => {
          this.xbDropDown.push({ value: p.code, label: p.value })
        })
      })
      sysDictTypeDropDown({ code: 'nsysqjzrylx' }).then(res => {
        res.data.forEach(p => {
          this.nsysqjzrylxDropDown.push({ value: p.code, label: p.value })
        })
      })
    },
    open(record, disabled = false) {
      this.disabled = disabled
      this.visible = true
    },
    // 初始化方法
    edit(record, disabled = false) {
      this.disabled = disabled
      this.visible = true
      this.id = record.id
      setTimeout(() => {
        this.form.setFieldsValue({
          id: record.id,
          wtdwmc: record.wtdwmc,
          wtdwId: record.wtdwId,
          wtbh: record.wtbh,
          wtdcs: record.wtdcs,
          wtwsfj: record.wtwsfj,
          xm: record.xm,
          nsysqjzrylx: record.nsysqjzrylx,
          xb: record.xb,
          sfzh: record.sfzh,
          jzddzP: record.jzddzP,
          jzddzC: record.jzddzC,
          jzddz: record.jzddz,
          sfyjzddzmx: record.sfyjzddzmx,
          jzddzmx: record.jzddzmx,
          hjsfyjzdxt: record.hjsfyjzdxt,
          hjdzP: record.hjdzP,
          hjdzC: record.hjdzC,
          hjdz: record.hjdz,
          sfyhjdzmx: record.sfyhjdzmx,
          hjdzmx: record.hjdzmx,
          hzdw: record.hzdw,
          nsyjzlb: record.nsyjzlb,
          sfyypxq: record.sfyypxq,
          ypxq: record.ypxq,
          ypxf: record.ypxf,
          fjx: record.fjx,
          pjjg: record.pjjg,
          zpwdmc: record.zpwdmc,
          zpdwId: record.zpdwId,
          zpr: record.zpr,
          zpbz: record.zpbz
        })
      }, 100)
      // 时间单独处理
      if (record.sdwtsj != null) {
        this.form.getFieldDecorator('sdwtsj', { initialValue: moment(record.sdwtsj, 'YYYY-MM-DD') })
      }
      this.sdwtsjDateString = moment(record.sdwtsj).format('YYYY-MM-DD')
      // 时间单独处理
      if (record.csrq != null) {
        this.form.getFieldDecorator('csrq', { initialValue: moment(record.csrq, 'YYYY-MM-DD') })
      }
      this.csrqDateString = moment(record.csrq).format('YYYY-MM-DD')
      // 时间单独处理
      if (record.ypxqksrq != null) {
        this.form.getFieldDecorator('ypxqksrq', { initialValue: moment(record.ypxqksrq, 'YYYY-MM-DD') })
      }
      this.ypxqksrqDateString = moment(record.ypxqksrq).format('YYYY-MM-DD')
      // 时间单独处理
      if (record.ypxqjsrq != null) {
        this.form.getFieldDecorator('ypxqjsrq', { initialValue: moment(record.ypxqjsrq, 'YYYY-MM-DD') })
      }
      this.ypxqjsrqDateString = moment(record.ypxqjsrq).format('YYYY-MM-DD')
      // 时间单独处理
      if (record.pjrq != null) {
        this.form.getFieldDecorator('pjrq', { initialValue: moment(record.pjrq, 'YYYY-MM-DD') })
      }
      this.pjrqDateString = moment(record.pjrq).format('YYYY-MM-DD')
      // 时间单独处理
      if (record.zpsj != null) {
        this.form.getFieldDecorator('zpsj', { initialValue: moment(record.zpsj, 'YYYY-MM-DD') })
      }
      this.zpsjDateString = moment(record.zpsj).format('YYYY-MM-DD')
    },
    handleSubmit() {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof values[key] === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.id = this.id
          values.jyfhqx = this.jyfhqxDateString
          values.sdwtsj = this.sdwtsjDateString
          values.csrq = this.csrqDateString
          values.ypxqksrq = this.ypxqksrqDateString
          values.ypxqjsrq = this.ypxqjsrqDateString
          values.pjrq = this.pjrqDateString
          // values.zpsj = this.zpsjDateString
          correctionPlacechangeImmigrationAdd(values)
            .then(res => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败') //  + res.message
              }
            })
            .finally(res => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    onChangesdwtsj(date, dateString) {
      this.sdwtsjDateString = dateString
    },

    onChangezpsj(date, dateString) {
      this.zpsjDateString = dateString
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 68vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.title {
  border-width: 0;
  width: 100%;
  margin-bottom: 12px;
  padding-left: 1.5%;
  background: inherit;
  background-color: rgba(242, 242, 242, 1);
  border: none;
  border-radius: 0;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
  font-weight: 700;
  font-style: normal;
  text-align: left;
  line-height: 35px;
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

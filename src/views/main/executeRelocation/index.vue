<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.deptId"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select style="width: 100%" v-model="queryParam.processStatus" placeholder="请选择状态">
                  <a-select-option v-for="(item, index) in dcpgztData" :key="index" :value="item.code">{{
                    item.name
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker v-model="queryParam.dates" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.correctionObjName" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="handleSearch">查询</a-button>
                <a-button
                  style="margin-left: 8px"
                  @click="
                    queryParam = {}
                    $refs.tableEmigration.refresh(true)
                  "
                  >重置</a-button
                >
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <!--跨省迁出-->
      <s-table
        ref="tableEmigration"
        :columns="columnsEmigration"
        :data="loadEmigrationData"
        :alert="false"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.moveOutForm.open()">跨省迁出</a-button>
          <x-down ref="batchExport" @batchExport="batchExport" />
        </template>
        <span slot="action" slot-scope="text, record">
          <!-- v-if="record.processStatus === '2'" -->
          <a v-if="record.processStatus === '2'" @click="$refs.fileTransfer.open(record)">移送档案</a>
          <a-divider v-if="record.processStatus === '2'" type="vertical" />
          <a @click="$refs.detailsForm.open(record, true)">详情</a>
        </span>
        <span slot="processStatus" slot-scope="text">
          {{ 'place_change_status' | dictType(text) }}
        </span>
        <span slot="receiveStatus" slot-scope="text">
          {{ 'place_change_result' | dictType(text) }}
        </span>
        <span slot="changeReason" slot-scope="text">
          <ellipsis :length="30" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="extDeptName" slot-scope="text">
          <ellipsis :length="10" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="deptName" slot-scope="text">
          <ellipsis :length="10" tooltip>{{ text }}</ellipsis>
        </span>
      </s-table>
      <moveOutForm ref="moveOutForm" @ok="handleOk" />
      <fileTransfer ref="fileTransfer" @ok="handleOk" />
      <detailsForm ref="detailsForm" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown, Ellipsis } from '@/components'
import moment from 'moment'
import {
  placechangeTransProvExport,
  placechangeTransProvList
} from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
import moveOutForm from './moveOutForm.vue'
import fileTransfer from './fileTransfer.vue'
import detailsForm from './detailsForm.vue'
import {} from 'vuex'
export default {
  components: {
    moveOutForm,
    fileTransfer,
    detailsForm,
    STable,
    Ellipsis,
    XDown
  },
  data() {
    return {
      current: 0,
      // 查询参数
      queryParam: {},
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      selectedRowKeys: [],
      selectedRows: [],
      // 表头 跨省迁出
      columnsEmigration: [
        {
          title: '状态',
          align: 'center',
          dataIndex: 'processStatus',
          scopedSlots: { customRender: 'processStatus' }
        },
        {
          title: '迁出结果',
          align: 'center',
          dataIndex: 'receiveStatus',
          scopedSlots: { customRender: 'receiveStatus' }
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'correctionObjName'
        },
        {
          title: '矫正单位',
          align: 'center',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'deptName' }
        },
        {
          title: '迁出原因',
          align: 'center',
          dataIndex: 'changeReason',
          scopedSlots: { customRender: 'changeReason' }
        },
        {
          title: '迁入省(市)',
          align: 'center',
          dataIndex: 'destinationProvName'
        },
        {
          title: '迁入单位',
          align: 'center',
          dataIndex: 'extDeptName',
          scopedSlots: { customRender: 'extDeptName' }
        },
        {
          title: '报到时间',
          align: 'center',
          dataIndex: 'correctionStartAt',
          customRender: val => {
            if (val == null) {
              return ''
            }
            return moment(val).format('YYYY-MM-DD')
          }
        },
        {
          title: '操作',
          // width: '150px',
          align: 'center',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 跨省迁出 加载数据方法 必须为 Promise 对象
      loadEmigrationData: parameter => {
        return placechangeTransProvList(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      orgTree: [],
      dcpgztData: [],
      bdztData: []
    }
  },
  watch: {
    current: {
      handler() {
        this.handleOk()
      }
    }
  },
  created() {
    this.getOrgTree()
    this.dataTypeItem()
  },
  methods: {
    moment,
    /**
     * 批量导出
     */
    batchExport() {
      placechangeTransProvExport(this.switchingDate()).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    /**
     * 查询参数组装
     */
    switchingDate() {
      const dates = this.queryParam.dates
      this.queryParam.transType = '0'
      if (dates != null) {
        this.queryParam.searchBeginTime = moment(dates[0]).format('YYYY-MM-DD ')
        this.queryParam.searchEndTime = moment(dates[1]).format('YYYY-MM-DD ')
        if (dates.length < 1) {
          delete this.queryParam.searchBeginTime
          delete this.queryParam.searchEndTime
        }
      }
      delete this.queryParam.dates
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      return obj
    },
    handleSearch() {
      this.$refs.tableEmigration.refresh(true)
    },
    handleOk() {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.tableEmigration.clearSelected()
      this.$refs.tableEmigration.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /**
     * 获取字典数据
     */
    dataTypeItem() {
      this.dcpgztData = this.$options.filters['dictData']('place_change_status')
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

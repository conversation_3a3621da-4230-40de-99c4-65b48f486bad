<template>
  <div>
    <a-drawer title="详情" width="85%" :visible="visible" @close="onClose" :footer="null">
      <a-tabs v-model="activeTab" @change="changeTab">
        <a-tab-pane key="1" tab="迁出申请">
          <detail ref="detail" />
        </a-tab-pane>
        <a-tab-pane key="2" tab="外省审核">
          <otherProvincesToexamine ref="otherProvincesToexamine" />
        </a-tab-pane>
        <a-tab-pane key="3" tab="移送档案">
          <file ref="file" />
        </a-tab-pane>
        <a-tab-pane key="4" tab="外省入矫反馈">
          <otherProvincesFeedBack ref="otherProvincesFeedBack" />
        </a-tab-pane>
      </a-tabs>
    </a-drawer>
  </div>
</template>
<script>
import detail from './components/detail.vue'
import file from './components/file.vue'
import otherProvincesToexamine from './otherProvincesToexamine.vue'
import otherProvincesFeedBack from './otherProvincesFeedBack.vue'
import { placechangeTransProvDetail } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
export default {
  components: {
    detail,
    file,
    otherProvincesToexamine,
    otherProvincesFeedBack
  },
  data() {
    return {
      visible: false,
      activeTab: '1',
      detailData: {}
    }
  },
  methods: {
    changeTab(val) {
      console.log(val)
      if (val === '1') {
        this.$nextTick(() => {
          this.$refs.detail.initFormData(this.detailData)
        })
      } else if (val === '2') {
        this.$nextTick(() => {
          this.$refs.otherProvincesToexamine.initFormData(this.detailData)
        })
      } else if (val === '3') {
        this.$nextTick(() => {
          this.$refs.file.initFormData(this.detailData)
        })
      } else if (val === '4') {
        this.$nextTick(() => {
          this.$refs.otherProvincesFeedBack.initFormData(this.detailData)
        })
      }
    },
    open(record) {
      this.visible = true
      placechangeTransProvDetail({ id: record.id }).then(res => {
        this.detailData = res.data
        this.$nextTick(() => {
          this.$refs.detail.initFormData(this.detailData)
        })
      })
    },
    onClose() {
      this.visible = false
      this.detailData = {}
      this.activeTab = '1'
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-input-disabled,
/deep/.ant-cascader-picker-disabled {
  color: #222;
}
</style>

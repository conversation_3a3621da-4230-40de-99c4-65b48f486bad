<template>
  <div>
    <a-form :form="form">
      <div class="cus-title-d">
        社矫对象信息
<!--        <span class="title-right">-->
<!--          目前该社区矫正跨省迁出只支持贵州省-->
<!--        </span>-->
      </div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['correctionObjName']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['nation']">
              <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['certType']">
              <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['certNum']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select v-decorator="['nationality']" disabled style="width: 100%">
              <a-select-option v-for="(p, index) in gjDropDown" :key="index" :value="p.code">{{
                p.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['correctionType']">
              <a-select-option v-for="(p, index) in jzlbSelect" :key="index" :value="p.code">{{
                p.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="户籍地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-cascader-distpicker disabled v-decorator="['registeredAddressCode']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="户籍地详情" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-input disabled v-decorator="['registeredAddress']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="现住地详情" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-input disabled v-decorator="['residence']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['correctionStart']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['correctionEnd']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['correctionDuration']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['decisionDept']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="迁出单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptContactPsn']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="迁出单位联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactTel']" />
          </a-form-item>
        </a-col>
      </a-row>
      <div class="cus-title-d">执行地变更信息</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="申请日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['applicationDate']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="拟接收矫正（迁入）单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select v-decorator="['extDeptCode']" style="width: 100%" disabled>
              <a-select-option
                v-for="(p, index) in orgJsTree"
                :key="index"
                :value="p.orgCode"
              >{{ p.orgName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="迁入地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-cascader-distpicker disabled v-decorator="['destinationCode']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="迁入地明细" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-input disabled v-decorator="['destination']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="变更理由" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <a-textarea :autosize="{ minRows: 2, maxRows: 6 }" disabled v-decorator="['changeReason']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-file-upload disabled v-decorator="['fileList']" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';
export default {
  data() {
    return {
      selectedArea: [],
      mzDropDown: [],
      zjlxDropDown: [],
      crblxDropDown: [],
      whcdDropDown: [],
      hyzkDropDown: [],
      jyjxqkDropDown: [],
      zzmmDropDown: [],
      gjDropDown: [],
      xbDropDown: [],
      orgJsTree: [],
      fileList: [],
      jzlbSelect: [],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      disabled: false,
      form: this.$form.createForm(this),
      jzdxData: {},
      infoData: {}
    }
  },
  created() {
    this.initOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    initFormData(data) {
      this.$nextTick(() => {
        this.form.setFieldsValue(data)
        const fileList =
          data.files46001 && data.files46001.length > 0
            ? data.files46001.map(item => {
                return { uid: item.id, name: item.fileOriginName, status: 'done', url: item.filePath }
              })
            : []
        this.form.setFieldsValue({
          fileList
        })
      })
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleChangeJzdw(value) {
      const arr = this.orgJsTree.filter(item => item.orgCode === value)
      this.infoData.extDeptName = arr[0].orgName
    },
    initOrgTree() {
      extOrgInfoList({ type: '60', dist: '52' }).then(res => {
        if (res.success) {
          console.log(res.data, 22222)
          this.orgJsTree = res.data
        }
      })
    },
    sysDictTypeDropDown() {
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.crblxDropDown = this.$options.filters['dictData']('crblx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.hyzkDropDown = this.$options.filters['dictData']('hyzk')
      this.jyjxqkDropDown = this.$options.filters['dictData']('jyjxqk')
      this.zzmmDropDown = this.$options.filters['dictData']('zzmm')
      this.gjDropDown = this.$options.filters['dictData']('nationality')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
      this.wcnDropDown = this.$options.filters['dictData']('wcn')
      this.jzlbSelect = this.$options.filters['dictData']('SQJZ_JZLB_NEW')
    },
    moment
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 68vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.cus-title-d {
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

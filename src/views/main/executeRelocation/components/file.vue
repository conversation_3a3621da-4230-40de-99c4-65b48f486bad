<template>
  <div>
    <a-form :form="form">
      <div class="cus-title-d">
        社矫对象信息
<!--        <span class="title-right">-->
<!--          目前该社区矫正跨省迁出只支持贵州省-->
<!--        </span>-->
      </div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input allow-clear disabled v-decorator="['uniformCode']" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input allow-clear disabled v-decorator="['correctionObjName']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['nation']">
              <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['certType']">
              <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['certNum']" />
          </a-form-item>
        </a-col>
      </a-row>
      <div class="cus-title-d">移送档案信息</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="移送单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['extDeptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactPsn']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptContactTel']" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deptName']" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="移送日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker style="width: 100%" disabled v-decorator="['transDate']" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
            <sh-file-upload disabled v-decorator="['fileList']" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  props: {
    detailData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moment,
      fjxDropDown: [],
      zxzlDropDown: [],
      sfjDropDown: [],
      mzDropDown: [],
      nsysqjzrylxDropDown: [],
      xbDropDown: [],
      fileList: [],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  watch: {
    detailData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.$nextTick(() => {
            console.log(newVal)
            this.form.setFieldsValue(newVal)
          })
        }
      },
      deep: true
    }
  },
  created() {
    this.sysDictTypeDropDown()
  },
  methods: {
    initFormData(data) {
      this.$nextTick(() => {
        this.form.setFieldsValue(data)
        const fileList =
          data.files46003 && data.files46003.length > 0
            ? data.files46003.map(item => {
                return { uid: item.id, name: item.fileOriginName, status: 'done', url: item.filePath }
              })
            : []
        this.form.setFieldsValue({
          fileList
        })
      })
    },
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.mzDropDown = this.$options.filters['dictData']('mz')
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 68vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.cus-title-d {
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

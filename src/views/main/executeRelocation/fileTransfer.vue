<template>
  <a-drawer title="跨省迁出" width="80%" :visible="visible" :confirmLoading="confirmLoading" @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">
          社矫对象信息
<!--          <span class="title-right">-->
<!--            目前该社区矫正跨省迁出只支持贵州省-->
<!--          </span>-->
        </div>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input allow-clear disabled v-decorator="['uniformCode']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input allow-clear disabled v-decorator="['correctionObjName']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select disabled v-decorator="['nation']">
                <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{
                  item.name
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select disabled v-decorator="['certType']">
                <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{
                  item.name
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['certNum']" />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="cus-title-d">移送档案信息</div>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="移送单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['deptName']" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['extDeptName']" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['deptContactPsn', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['deptContactTel', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['deptName']" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="移送日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                style="width: 100%"
                placeholder="请选择移送日期"
                format="YYYY-MM-DD"
                v-decorator="['transDate', { rules: [{ required: true, message: '请选择移送日期' }] }]"
                @change="onChangeypxqjsrq"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <sh-file-upload
                :fileList="form.getFieldValue('fileList')"
                accept=".pdf"
                :multiple="true"
                v-decorator="['fileList', { initialValue: [], rules: [{ required: true, message: '请上传' }] }]"
                uploadUrl="/api/sysFileInfo/uploadOss"
                @change="fileChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <div
      v-if="!disabled"
      :style="{
        position: 'absolute',
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff'
      }"
    >
      <a-button style="margin-right: 10px;" @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
import moment from 'moment'
import { placechangeTransProvSubmit } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
export default {
  data() {
    return {
      moment,
      fjxDropDown: [],
      zxzlDropDown: [],
      sfjDropDown: [],
      mzDropDown: [],
      nsysqjzrylxDropDown: [],
      xbDropDown: [],
      fileList: [],
      jzlbSelect: [
        { name: '管制', value: '1' },
        { name: '缓刑', value: '2' },
        { name: '假释', value: '3' },
        { name: '暂予监外执行', value: '4' },
        { name: '其他', value: '5' }
      ],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      sdwtsjDateString: '',
      csrqDateString: '',
      jyfhqxDateString: '',
      ypxqksrqDateString: '',
      ypxqjsrqDateString: '',
      pjrqDateString: '',
      zpsjDateString: '',
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    this.sysDictTypeDropDown()
  },
  methods: {
    fileChange(fileList) {
      console.log(fileList)
      this.form.setFieldsValue({ fileList })
    },
    sysDictTypeDropDown() {
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.mzDropDown = this.$options.filters['dictData']('mz')
    },

    onChangeypxqjsrq(date, dateString) {
      console.log(date, dateString)
      this.form.setFieldsValue({ transDate: dateString })
    },
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    open(record, disabled = false) {
      this.disabled = disabled
      this.visible = true
      this.id = record.id
      this.$nextTick(() => {
        this.form.setFieldsValue({
          ...record,
          transDate: record.transDate ? moment(record.transDate).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD')
        })
      })
    },
    handleSubmit() {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof values[key] === 'object' && key !== 'transDate') {
              values[key] = JSON.stringify(values[key])
            }
          }
          values.id = this.id
          const fileList = this.form.getFieldValue('fileList')
          // Array.from获取附件id
          values.files = fileList && fileList.length > 0 ? Array.from(fileList, item => item.id).toString() : ''
          placechangeTransProvSubmit(values)
            .then(res => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败') //  + res.message
              }
            })
            .finally(res => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-drawer-body {
  height: calc(100% - 100px);
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.title {
  border-width: 0;
  width: 100%;
  margin-bottom: 12px;
  padding-left: 1.5%;
  background: inherit;
  background-color: rgba(242, 242, 242, 1);
  border: none;
  border-radius: 0;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
  font-weight: 700;
  font-style: normal;
  text-align: left;
  line-height: 35px;
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

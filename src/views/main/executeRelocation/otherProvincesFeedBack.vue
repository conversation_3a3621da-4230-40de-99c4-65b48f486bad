<template>
  <div>
    <a-form :form="form">
      <div class="cus-title-d">外省迁入报到情况反馈</div>
      <a-row :gutter="48">
        <a-col :span="12">
          <a-form-item label="入矫报到情况" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select disabled v-decorator="['registrationResult']" :options="registrationResultDropDown"> </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="入矫日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              disabled
              format="YYYY-MM-DD"
              v-decorator="['correctionStartAt', { initialValue: moment() }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="现矫正机构" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select
              v-decorator="['extDeptCode', { rules: [{ required: true, message: '请选择拟接收单位 ！' }] }]"
              style="width: 100%"
              disabled
              showSearch
              :filterOption="filterOption"
            >
              <a-select-option
                v-for="(p, index) in orgJsTree"
                :key="index"
                :value="p.orgCode"
              >{{ p.orgName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="反馈日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              style="width: 100%"
              disabled
              format="YYYY-MM-DD"
              v-decorator="['opinionDate', { initialValue: moment() }]"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';
export default {
  data() {
    return {
      registrationResultDropDown: [
        { label: '在规定时间内报到', value: '1' },
        { label: '超出规定时限报到', value: '2' },
        { label: '未报到且下落不明', value: '3' },
        { label: '其他', value: '4' }
      ],
      xbDropDown: [],
      orgJsTree: [],
      fileList: [],
      resultOption: [
        { label: '是', value: '1' },
        { label: '否', value: '2' }
      ],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    this.initOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    initOrgTree() {
      extOrgInfoList({ type: '60', dist: '52' }).then(res => {
        if (res.success) {
          console.log(res.data, 22222)
          this.orgJsTree = res.data
        }
      })
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    initFormData(data) {
      this.$nextTick(() => {
        this.form.setFieldsValue(data)
      })
    },
    fileChange(fileList) {
      console.log(fileList)
      this.form.setFieldsValue({ fileList })
    },
    sysDictTypeDropDown() {
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.crblxDropDown = this.$options.filters['dictData']('crblx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.hyzkDropDown = this.$options.filters['dictData']('hyzk')
      this.jyjxqkDropDown = this.$options.filters['dictData']('jyjxqk')
      this.zzmmDropDown = this.$options.filters['dictData']('zzmm')
      this.gjDropDown = this.$options.filters['dictData']('guoji')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
      this.wcnDropDown = this.$options.filters['dictData']('wcn')
    },
    moment,
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('非法格式')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    open(record, disabled = false) {
      this.disabled = disabled
      this.visible = true
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 68vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.title {
  border-width: 0;
  width: 100%;
  margin-bottom: 12px;
  padding-left: 1.5%;
  background: inherit;
  background-color: rgba(242, 242, 242, 1);
  border: none;
  border-radius: 0;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
  font-weight: 700;
  font-style: normal;
  text-align: left;
  line-height: 35px;
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

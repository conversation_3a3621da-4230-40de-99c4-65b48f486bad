<template>
  <a-drawer title="跨省迁出" width="80%" :visible="visible" :confirmLoading="confirmLoading" @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="cus-title-d">
          社矫对象信息
          <!--          <span class="title-right">-->
          <!--            目前该社区矫正跨省迁出只支持贵州省-->
          <!--          </span>-->
        </div>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-search
                allow-clear
                placeholder="请选择"
                :readonly="true"
                v-decorator="['correctionObjName', { rules: [{ required: true, message: '请选择 ！' }] }]"
                @search="handleSearch"
              >
                <a-button slot="enterButton" style="margin-right: 0;">
                  选择
                </a-button>
              </a-input-search>
              <a-input v-show="false" v-decorator="['correctionObjId']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                placeholder="请选择民族"
                v-decorator="['nation', { rules: [{ required: true, message: '请选择民族 ！' }] }]"
              >
                <a-select-option v-for="(item, index) in mzDropDown" :key="index" :value="item.code">{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                placeholder="请选择"
                v-decorator="['certType', { rules: [{ required: true, message: '请选择证件类型 ！' }] }]"
              >
                <a-select-option v-for="(item, index) in zjlxDropDown" :key="index" :value="item.code">{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                allow-clear
                placeholder="请输入"
                v-decorator="[
                  'certNum',
                  { rules: [{ required: true, message: '请输入证件号码 ！', validator: IDValidator }] }
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                v-decorator="['nationality', { rules: [{ required: true, message: '请选择国籍！' }] }]"
                show-search
                placeholder="国籍"
                style="width: 100%"
              >
                <a-select-option v-for="(p, index) in gjDropDown" :key="index" :value="p.code">{{ p.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                placeholder="请选择"
                v-decorator="['correctionType', { rules: [{ required: true, message: '请选择矫正类别' }] }]"
              >
                <a-select-option v-for="(p, index) in jzlbSelect" :key="index" :value="p.code">{{ p.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="户籍地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <sh-cascader-distpicker
                placeholder="请选择"
                v-decorator="[
                  'registeredAddressCode',
                  { initialValue: '', rules: [{ required: true, message: '请选择所在区域' }] }
                ]"
                @change="handleChangeHjdArea"
              />
              <a-input v-show="false" v-decorator="['registeredAddressCode']"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="户籍地详情" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <a-input
                placeholder="请输入"
                v-decorator="['registeredAddress', { rules: [{ required: true, message: '请输入矫正类别' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="现住地详情" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <a-input
                placeholder="请输入"
                v-decorator="['residence', { rules: [{ required: true, message: '请输入矫正类别' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                style="width: 100%"
                format="YYYY-MM-DD"
                placeholder="请选择矫正开始日期"
                v-decorator="['correctionStart', { rules: [{ required: true, message: '请选择矫正开始日期' }] }]"
                @change="onChangeypxqksrq"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                style="width: 100%"
                format="YYYY-MM-DD"
                placeholder="请选择矫正结束日期"
                v-decorator="['correctionEnd', { rules: [{ required: true, message: '请选择矫正结束日期' }] }]"
                @change="onChangeypxqjsrq"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="矫正期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['correctionDuration', { rules: [{ required: true, message: '请输入矫正期限' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                v-decorator="['decisionDept', { rules: [{ required: true, message: '请输入决定机关' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="迁出单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['deptContactPsn', { rules: [{ required: true, message: '请输入迁出单位联系人 ！' }] }]"
                placeholder="请输入迁出单位联系人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="迁出单位联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入迁出单位联系电话"
                v-decorator="[
                  'deptContactTel',
                  { rules: [{ required: true, message: '请输入联系电话！', validator: phoneValidator }] }
                ]"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="cus-title-d">执行地变更信息</div>
        <a-row :gutter="48">
          <a-col :span="12">
            <a-form-item label="申请日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                style="width: 100%"
                format="YYYY-MM-DD"
                placeholder="请选择申请日期"
                v-decorator="['applicationDate', { rules: [{ required: true, message: '请选择申请日期' }] }]"
                @change="onChangesqrq"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="拟接收矫正（迁入）单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                v-decorator="['extDeptCode', { rules: [{ required: true, message: '请选择拟接收单位 ！' }] }]"
                style="width: 100%"
                placeholder="请选择"
                showSearch
                :filterOption="filterOption"
                @change="handleChangeJzdw"
              >
                <a-select-option
                  v-for="(p, index) in orgJsTree"
                  :key="index"
                  :value="p.orgCode"
                >{{ p.orgName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="迁入地" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <sh-cascader-distpicker
                placeholder="请选择"
                v-decorator="[
                  'destinationCode',
                  { initialValue: '', rules: [{ required: true, message: '请选择所在区域' }] }
                ]"
                @change="handleChangeArea"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="迁入地明细" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <a-input
                placeholder="请输入"
                v-decorator="['destination', { rules: [{ required: true, message: '请输入迁入地明细' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="变更理由" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <a-textarea
                :autosize="{ minRows: 2, maxRows: 6 }"
                placeholder="请输入"
                v-decorator="[
                  'changeReason',
                  {
                    rules: [{ required: true, message: '请输入变更理由' }]
                  }
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
              <sh-file-upload
                :fileList="form.getFieldValue('fileList')"
                accept=".pdf"
                v-decorator="['fileList', { initialValue: [], rules: [{ required: true, message: '请上传' }] }]"
                uploadUrl="/api/sysFileInfo/uploadOss"
                @change="fileChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <div
      v-if="!disabled"
      :style="{
        position: 'absolute',
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff'
      }"
    >
      <a-button style="margin-right: 10px;" @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </div>
    <chooseSqjzryRadio @jzdx="jzdx" ref="chooseSqjzryRadio"/>
  </a-drawer>
</template>

<script>
import chooseSqjzryRadio from './selectJzdx.vue'
import moment from 'moment'
import {
  placechangeTransProvAdd,
  placechangeTransProvInfo
} from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage';

export default {
  components: {
    chooseSqjzryRadio
  },
  data() {
    return {
      selectedArea: [],
      mzDropDown: [],
      zjlxDropDown: [],
      whcdDropDown: [],
      hyzkDropDown: [],
      jyjxqkDropDown: [],
      zzmmDropDown: [],
      gjDropDown: [],
      xbDropDown: [],
      orgJsTree: [],
      fileList: [],
      jzlbSelect: [],
      id: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      sdwtsjDateString: '',
      csrqDateString: '',
      jyfhqxDateString: '',
      ypxqksrqDateString: '',
      ypxqjsrqDateString: '',
      pjrqDateString: '',
      zpsjDateString: '',
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      jzdxData: null,
      infoData: null
    }
  },
  created() {
    this.initOrgTree()
    this.sysDictTypeDropDown()
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleChangeJzdw(value) {
      const arr = this.orgJsTree.filter(item => item.orgCode === value)
      this.infoData.extDeptName = arr[0].orgName
    },
    handleChangeHjdArea(value) {
      console.log('选中的户籍地地区编码：', value)
    },
    handleChangeArea(value) {
      console.log('选中的地区编码：', value)
    },
    handleSearch() {
      // 关联社区矫正对象
      this.$refs.chooseSqjzryRadio.choose('stay', this.jzdxData)
    },
    jzdx(jzdx) {
      this.jzdxData = jzdx
      placechangeTransProvInfo({ id: jzdx.id }).then(res => {
        if (res.success) {
          this.infoData = res.data
          this.$nextTick(() => {
            this.form.setFieldsValue({
              ...res.data
            })
          })
        }
      })
    },
    fileChange(fileList) {
      console.log(fileList)
      this.form.setFieldsValue({ fileList })
    },
    initOrgTree() {
      extOrgInfoList({ type: '60', dist: '52' }).then(res => {
        if (res.success) {
          console.log(res.data, 22222)
          this.orgJsTree = res.data
        }
      })
    },
    sysDictTypeDropDown() {
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.zjlxDropDown = this.$options.filters['dictData']('zjlx')
      this.whcdDropDown = this.$options.filters['dictData']('whcd')
      this.hyzkDropDown = this.$options.filters['dictData']('hyzk')
      this.jyjxqkDropDown = this.$options.filters['dictData']('jyjxqk')
      this.zzmmDropDown = this.$options.filters['dictData']('zzmm')
      this.gjDropDown = this.$options.filters['dictData']('guoji')
      this.xbDropDown = this.$options.filters['dictData']('xb')
      this.mzDropDown = this.$options.filters['dictData']('mz')
      this.jzlbSelect = this.$options.filters['dictData']('SQJZ_JZLB_NEW')
    },
    moment,
    onChangeypxqksrq(date, dateString) {
      this.form.setFieldsValue({
        correctionStart: dateString
      })
    },
    onChangeypxqjsrq(date, dateString) {
      this.form.setFieldsValue({
        correctionEnd: dateString
      })
    },
    onChangesqrq(date, dateString) {
      this.form.setFieldsValue({
        applicationDate: dateString
      })
    },
    // 验证手机号
    phoneValidator(rule, value, callback) {
      const phoneReg = /^1[3456789]\d{9}$/
      if (!phoneReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('请输入正确的手机号')
      }
      callback()
    },
    IDValidator(rule, value, callback) {
      const idcardReg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      if (!idcardReg.test(value)) {
        // eslint-disable-next-line standard/no-callback-literal
        callback('请输入正确的身份证号')
      }
      // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
      callback()
    },
    open(record, disabled = false) {
      this.disabled = disabled
      this.visible = true
    },
    handleSubmit() {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        if (!errors) {
          for (const key in values) {
            if (typeof values[key] === 'object') {
              values[key] = JSON.stringify(values[key])
            }
          }
          const fileList = this.form.getFieldValue('fileList')
          // Array.from获取附件id
          values.files = fileList && fileList.length > 0 ? Array.from(fileList, item => item.id).toString() : ''
          values.transType = '0'
          console.log(values, 22222)
          const formData = Object.assign(this.infoData, values)
          // values.zpsj = this.zpsjDateString
          placechangeTransProvAdd(formData)
            .then(res => {
              if (res.success) {
                this.$message.success('提交成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('提交失败') //  + res.message
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        } else {
          this.confirmLoading = false
        }
      })
    },
    onChangesdwtsj(date, dateString) {
      this.sdwtsjDateString = dateString
    },

    onChangezpsj(date, dateString) {
      this.zpsjDateString = dateString
    },
    handleCancel() {
      this.form.resetFields()
      this.jzdxData = {}
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .ant-drawer-body {
  height: calc(100% - 100px);
  overflow-y: auto;

  .ant-form-item {
    margin-bottom: 12px;
  }
}

.cus-title-d {
  .title-right {
    font-size: 14px;
    color: red;
    font-weight: normal;
    margin-left: 20px;
  }
}
</style>

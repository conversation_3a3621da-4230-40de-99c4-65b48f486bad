<template>
  <a-modal
    title="选择矫正对象"
    :width="950"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <div>
      <!-- {{ userInfo.loginEmpInfo.orgId }} -->
      <a-card :bordered="false" :bodyStyle="tstyle">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-tree-select
                    v-model="queryParam.jiedaoId"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择矫正单位"
                  >
                    <span slot="title" slot-scope="{ id }">{{ id }}</span>
                    <a-input v-show="false" v-model="queryParam.jzjg" />
                  </a-tree-select>
                </a-form-item>
              </a-col>

              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.pname" allow-clear placeholder="请输入姓名" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="$refs.table2.refresh(true)">查询</a-button>
                  <a-button
                    style="margin-left: 8px"
                    @click="
                      queryParam = {}
                      $refs.table2.refresh(true)
                    "
                    >重置</a-button
                  >
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" class="table-card">
        <a-table
          ref="table2"
          :columns="columns"
          :data-source="loadData"
          :alert="true"
          :pagination="pagination"
          :rowKey="record => record.id"
          @change="handleTableChange"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
        >
          <span slot="rowIndex" slot-scope="t, r, index">
            {{ parseInt(index) + 1 }}
          </span>
          <span slot="timeFormat" slot-scope="text">
            {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
          </span>
        </a-table>
        <!-- <view-main ref="viewMain" @ok="handleOk" /> -->
      </a-card>
    </div>
  </a-modal>
</template>
<script>
import moment from 'moment'
import { placechangeTransProvPage } from '@/api/modular/main/coordinateinvestigate/coordinateInvestigateManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters(['nickname', 'userInfo'])
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        pageSize: 10,
        pageNo: 1
      },
      confirmLoading: false,
      loading: false,
      orgTree: [],
      total: 0,
      pagination: {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`,
        pageSizeOptions: ['10', '20', '30', '40', '50']
      },
      // 表头
      columns: [
        {
          ellipsis: true,
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          align: 'center',
          width: 70,
          scopedSlots: { customRender: 'rowIndex' }
        },
        { ellipsis: true, title: '姓名', align: 'center', width: 65, dataIndex: 'pname' },
        { ellipsis: true, title: '迁出单位', align: 'center', width: 125, dataIndex: 'jiedaoName' },
        { ellipsis: true, title: '迁入地', align: 'center', width: 90, dataIndex: 'njsjzdwName' },
        {
          ellipsis: true,
          title: '申请时间',
          align: 'center',
          dataIndex: 'sqsj',
          width: 85,
          scopedSlots: { customRender: 'timeFormat' }
        }
      ],
      tstyle: { 'padding-bottom': '0px' },
      // 加载数据方法 必须为 Promise 对象
      // loadData: parameter => {
      //   return placechangeTransProvPage(Object.assign(parameter, JSON.parse(JSON.stringify(this.queryParam)))).then(
      //     res => {
      //       if (this.selectedRowKeys.length > 0) {
      //         this.selectedRows = res.data.rows.filter(item => item.id === this.selectedRowKeys[0])
      //       }
      //       console.log(this.selectedRows, 9999)
      //       return res.data
      //     }
      //   )
      // },
      loadData: [],
      selectedRowKeys: [],
      selectedRows: [],
      visible: false,
      tag: ''
    }
  },
  created() {
    this.getOrgTree()
    this.queryParam.jzjg = this.userInfo.loginEmpInfo.orgId
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.pagination = pagination
      this.queryParam.pageNo = pagination.current
      this.queryParam.pageSize = pagination.pageSize
      this.initData()
    },
    initData() {
      placechangeTransProvPage(JSON.parse(JSON.stringify(this.queryParam))).then(res => {
        if (this.selectedRowKeys.length > 0) {
          this.selectedRows = res.data.rows.filter(item => item.id === this.selectedRowKeys[0])
        }
        console.log(this.selectedRows, 9999)
        this.loadData = res.data.rows
        this.total = res.data.totalRows
        this.pagination = {
          ...this.pagination,
          total: res.data.totalRows,
          current: res.data.pageNo
        }
      })
    },
    moment,
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRowKeys, this.selectedRows)
    },
    handleOk() {
      this.initData()
      // this.$refs.table2.refresh()
    },
    choose(tag, data) {
      this.visible = true
      this.tag = tag
      this.initData()
      if (data) {
        this.selectedRowKeys = [data.id]
      }
    },
    handleCancel() {
      this.selectedRowKeys = []
      this.selectedRows = []
      this.initData()
      // this.$refs.table2.refresh()
      this.visible = false
    },
    handleSubmit() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warn('请选择一个矫正对象')
        return
      }
      this.$emit('jzdx', this.selectedRows[0])
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  padding: 0;
  height: 68vh;
  overflow-y: auto;
}
.table-card {
  /deep/.ant-card-body {
    padding-top: 0;
  }
}
</style>

<template>
  <a-modal
    title="新增特困对象基本信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="申请人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请人" v-decorator="['ahcp0006', {rules: [{required: true, message: '请输入申请人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别 (1男, 2女)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别 (1男, 2女)" v-decorator="['ahcp0007', {rules: [{required: true, message: '请输入性别 (1男, 2女)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生日期" v-decorator="['ahcp0008', {rules: [{required: true, message: '请输入出生日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民族"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民族" v-decorator="['ahcp0009', {rules: [{required: true, message: '请输入民族！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文化程度 (10研究生教育, 50普通高级中学教育, ...)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文化程度 (10研究生教育, 50普通高级中学教育, ...)" v-decorator="['ahcp0010', {rules: [{required: true, message: '请输入文化程度 (10研究生教育, 50普通高级中学教育, ...)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="健康状况 (1健康, 4重度残疾, 2一般（长病）, 9其他, 3一般残疾)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入健康状况 (1健康, 4重度残疾, 2一般（长病）, 9其他, 3一般残疾)" v-decorator="['ahcp0011', {rules: [{required: true, message: '请输入健康状况 (1健康, 4重度残疾, 2一般（长病）, 9其他, 3一般残疾)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="婚姻状况 (10未婚, 23复婚, 20已婚, 30丧偶, 21初婚, 40离婚, 22再婚, 90未说明的婚姻状况)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入婚姻状况 (10未婚, 23复婚, 20已婚, 30丧偶, 21初婚, 40离婚, 22再婚, 90未说明的婚姻状况)" v-decorator="['ahcp0012', {rules: [{required: true, message: '请输入婚姻状况 (10未婚, 23复婚, 20已婚, 30丧偶, 21初婚, 40离婚, 22再婚, 90未说明的婚姻状况)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍地址" v-decorator="['ahcp0014', {rules: [{required: true, message: '请输入户籍地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住地址"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住地址" v-decorator="['ahcp0015', {rules: [{required: true, message: '请输入居住地址！'}]}]" />
        </a-form-item>
        <a-form-item
          label="居住邮编"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入居住邮编" v-decorator="['ahcp0016', {rules: [{required: true, message: '请输入居住邮编！'}]}]" />
        </a-form-item>
        <a-form-item
          label="联系电话"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入联系电话" v-decorator="['ahcp0017', {rules: [{required: true, message: '请输入联系电话！'}]}]" />
        </a-form-item>
        <a-form-item
          label="救助证编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入救助证编号" v-decorator="['ahcx0003', {rules: [{required: true, message: '请输入救助证编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申请救助原因"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请救助原因" v-decorator="['ahcx0004', {rules: [{required: true, message: '请输入申请救助原因！'}]}]" />
        </a-form-item>
        <a-form-item
          label="劳动能力 (1有劳动能力, 3完全丧失劳动能力, 2部分丧失劳动能力, 4无劳动能力)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入劳动能力 (1有劳动能力, 3完全丧失劳动能力, 2部分丧失劳动能力, 4无劳动能力)" v-decorator="['ahcx0013', {rules: [{required: true, message: '请输入劳动能力 (1有劳动能力, 3完全丧失劳动能力, 2部分丧失劳动能力, 4无劳动能力)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户银行"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户银行" v-decorator="['ahcx0019', {rules: [{required: true, message: '请输入开户银行！'}]}]" />
        </a-form-item>
        <a-form-item
          label="银行账号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入银行账号" v-decorator="['ahcx0020', {rules: [{required: true, message: '请输入银行账号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="供养方式"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入供养方式" v-decorator="['ahcx0028', {rules: [{required: true, message: '请输入供养方式！'}]}]" />
        </a-form-item>
        <a-form-item
          label="保障标准"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入保障标准" v-decorator="['ahcx0029', {rules: [{required: true, message: '请输入保障标准！'}]}]" />
        </a-form-item>
        <a-form-item
          label="总发放金额"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入总发放金额" v-decorator="['ahcx0033', {rules: [{required: true, message: '请输入总发放金额！'}]}]" />
        </a-form-item>
        <a-form-item
          label="行政区划"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行政区划" v-decorator="['azaa0002', {rules: [{required: true, message: '请输入行政区划！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职业状况 (1老年人（60周岁及以上）, 5未登记失业, ...)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职业状况 (1老年人（60周岁及以上）, 5未登记失业, ...)" v-decorator="['azcp0011', {rules: [{required: true, message: '请输入职业状况 (1老年人（60周岁及以上）, 5未登记失业, ...)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="政治面貌"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入政治面貌" v-decorator="['azcp0012', {rules: [{required: true, message: '请输入政治面貌！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户口性质 (1农业, 2非农业)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户口性质 (1农业, 2非农业)" v-decorator="['azcp0013', {rules: [{required: true, message: '请输入户口性质 (1农业, 2非农业)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="家庭总人口"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="手机号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入手机号码" v-decorator="['azdf0011', {rules: [{required: true, message: '请输入手机号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年份" v-decorator="['bhax0114', {rules: [{required: true, message: '请输入年份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="月份"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入月份" v-decorator="['bhax0115', {rules: [{required: true, message: '请输入月份！'}]}]" />
        </a-form-item>
        <a-form-item
          label="户籍邮编"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入户籍邮编" v-decorator="['bhcf0010', {rules: [{required: true, message: '请输入户籍邮编！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申请类别 (1农村低保, 2城镇低保)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请类别 (1农村低保, 2城镇低保)" v-decorator="['bhcx0002', {rules: [{required: true, message: '请输入申请类别 (1农村低保, 2城镇低保)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="致贫原因 (1疾病, 5失业, 2灾害, 6失地, 3残疾, 7因学致贫, 4缺乏劳动力, 8火灾, 9交通事故, 10突发意外事件, 99其他)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入致贫原因 (1疾病, 5失业, 2灾害, 6失地, 3残疾, 7因学致贫, 4缺乏劳动力, 8火灾, 9交通事故, 10突发意外事件, 99其他)" v-decorator="['bhcx0008', {rules: [{required: true, message: '请输入致贫原因 (1疾病, 5失业, 2灾害, 6失地, 3残疾, 7因学致贫, 4缺乏劳动力, 8火灾, 9交通事故, 10突发意外事件, 99其他)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户人" v-decorator="['bhcx0015', {rules: [{required: true, message: '请输入开户人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开户人身份证"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入开户人身份证" v-decorator="['bhcx0016', {rules: [{required: true, message: '请输入开户人身份证！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否集中供养 (0否, 1是)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否集中供养 (0否, 1是)" v-decorator="['bhcx0018', {rules: [{required: true, message: '请输入是否集中供养 (0否, 1是)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="供养地"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入供养地" v-decorator="['bhcx0020', {rules: [{required: true, message: '请输入供养地！'}]}]" />
        </a-form-item>
        <a-form-item
          label="保障总人口"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="护理等级 (1生活完全不能自理, 3部分不能自理, 2基本不能自理, 4全自理)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入护理等级 (1生活完全不能自理, 3部分不能自理, 2基本不能自理, 4全自理)" v-decorator="['bhcx0024', {rules: [{required: true, message: '请输入护理等级 (1生活完全不能自理, 3部分不能自理, 2基本不能自理, 4全自理)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="护理标准"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入护理标准" v-decorator="['bhcx0025', {rules: [{required: true, message: '请输入护理标准！'}]}]" />
        </a-form-item>
        <a-form-item
          label="护理费"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入护理费" v-decorator="['bhcx0026', {rules: [{required: true, message: '请输入护理费！'}]}]" />
        </a-form-item>
        <a-form-item
          label="救助日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入救助日期" v-decorator="['bhcx0027', {rules: [{required: true, message: '请输入救助日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查人" v-decorator="['bhcx0030', {rules: [{required: true, message: '请输入调查人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入调查时间" v-decorator="['bhcx0031', {rules: [{required: true, message: '请输入调查时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="调查结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="家庭关系 (1本人, 50父母, 10配偶, 60祖父母或外祖父母, 20子/婿, 70兄弟姐妹, 30女/媳, 99其他, 40孙子、孙女或外孙子、外孙女)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入家庭关系 (1本人, 50父母, 10配偶, 60祖父母或外祖父母, 20子/婿, 70兄弟姐妹, 30女/媳, 99其他, 40孙子、孙女或外孙子、外孙女)" v-decorator="['bhcx0033', {rules: [{required: true, message: '请输入家庭关系 (1本人, 50父母, 10配偶, 60祖父母或外祖父母, 20子/婿, 70兄弟姐妹, 30女/媳, 99其他, 40孙子、孙女或外孙子、外孙女)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="年龄"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入年龄" v-decorator="['bhcx0038', {rules: [{required: true, message: '请输入年龄！'}]}]" />
        </a-form-item>
        <a-form-item
          label="是否保障对象 (1家庭对象, 2个人对象)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入是否保障对象 (1家庭对象, 2个人对象)" v-decorator="['bhcx0039', {rules: [{required: true, message: '请输入是否保障对象 (1家庭对象, 2个人对象)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员类别 (1归正人员, 3五保对象, 2三无对象, 4大学生, 5退伍军人, 9其他人员)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员类别 (1归正人员, 3五保对象, 2三无对象, 4大学生, 5退伍军人, 9其他人员)" v-decorator="['bhcx0041', {rules: [{required: true, message: '请输入人员类别 (1归正人员, 3五保对象, 2三无对象, 4大学生, 5退伍军人, 9其他人员)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="特定对象"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入特定对象" v-decorator="['bhcx0046', {rules: [{required: true, message: '请输入特定对象！'}]}]" />
        </a-form-item>
        <a-form-item
          label="残疾类别 (61视力残疾, 65智力残疾, 62听力残疾, 66精神残疾, 63言语残疾, 67多重残疾, 64肢体残疾, 69其他残疾)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入残疾类别 (61视力残疾, 65智力残疾, 62听力残疾, 66精神残疾, 63言语残疾, 67多重残疾, 64肢体残疾, 69其他残疾)" v-decorator="['bhcx0048', {rules: [{required: true, message: '请输入残疾类别 (61视力残疾, 65智力残疾, 62听力残疾, 66精神残疾, 63言语残疾, 67多重残疾, 64肢体残疾, 69其他残疾)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="残疾等级 (1一级残疾, 3三级残疾, 2二级残疾, 4四级残疾)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入残疾等级 (1一级残疾, 3三级残疾, 2二级残疾, 4四级残疾)" v-decorator="['bhcx0049', {rules: [{required: true, message: '请输入残疾等级 (1一级残疾, 3三级残疾, 2二级残疾, 4四级残疾)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="残疾证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入残疾证号" v-decorator="['bhcx0050', {rules: [{required: true, message: '请输入残疾证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单据类型 默认：wb_jz"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单据类型 默认：wb_jz" v-decorator="['bhcx0071', {rules: [{required: true, message: '请输入单据类型 默认：wb_jz！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员信息类别 默认：jt"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员信息类别 默认：jt" v-decorator="['bhcx0074', {rules: [{required: true, message: '请输入人员信息类别 默认：jt！'}]}]" />
        </a-form-item>
        <a-form-item
          label="省厅区划编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入省厅区划编码" v-decorator="['bhcx0080', {rules: [{required: true, message: '请输入省厅区划编码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="民政部区划编码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入民政部区划编码" v-decorator="['bhcx0081', {rules: [{required: true, message: '请输入民政部区划编码！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsExtremePoorBasicInfoAdd } from '@/api/modular/main/irsextremepoorbasicinfo/irsExtremePoorBasicInfoManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsExtremePoorBasicInfoAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

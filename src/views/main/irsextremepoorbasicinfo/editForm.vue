<template>
  <a-modal
    title="详情"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-card>
        <a-form :form="form">
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">基本信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="姓名（申请人）"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled placeholder="请输入申请人" v-decorator="['ahcp0006', {rules: [{ message: '请输入申请人！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="矫正单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['jzjgName', {rules: [{ message: '请输入申请人！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="性别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['ahcp0007', {rules: [{ message: '请输入性别 (1男, 2女)！'}]}]">
                <a-select-option value="1">
                  男
                </a-select-option>
                <a-select-option value="2">
                  女
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="身份证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['bhcx0016']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="出生日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahcp0008', {rules: [{ message: '请输入出生日期！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="联系电话"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahcp0017']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="婚姻状况"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['ahcp0012']">
                <a-select-option value="10">
                  未婚
                </a-select-option>
                <a-select-option value="23">
                  复婚
                </a-select-option>
                <a-select-option value="20">
                  已婚
                </a-select-option>
                <a-select-option value="30">
                  丧偶
                </a-select-option>
                <a-select-option value="21">
                  初婚
                </a-select-option>
                <a-select-option value="10">
                  离婚
                </a-select-option>
                <a-select-option value="22">
                  再婚
                </a-select-option>
                <a-select-option value="90">
                  未说明的婚姻状况
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="家庭人口数"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['azdf0005']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="是否保障对象"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0039']">
                <a-select-option value="01">
                  家庭对象
                </a-select-option>
                <a-select-option value="02">
                  个人对象
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="人员类别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0041']">
                <a-select-option value="1">
                  归正人员
                </a-select-option>
                <a-select-option value="2">
                  三无对象
                </a-select-option>
                <a-select-option value="3">
                  五保对象
                </a-select-option>
                <a-select-option value="4">
                  大学生
                </a-select-option>
                <a-select-option value="5">
                  退伍军人
                </a-select-option>
                <a-select-option value="9">
                  其他人员
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="健康状况"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['ahcp0011']">
                <a-select-option value="1">
                  健康
                </a-select-option>
                <a-select-option value="2">
                  一般（长病）
                </a-select-option>
                <a-select-option value="3">
                  一般残疾
                </a-select-option>
                <a-select-option value="9">
                  其他
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="残疾证号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['bhcx0050']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="残疾类别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0048']">
                <a-select-option value="61">
                  视力残疾
                </a-select-option>
                <a-select-option value="62">
                  听力残疾
                </a-select-option>
                <a-select-option value="63">
                  言语残疾
                </a-select-option>
                <a-select-option value="64">
                  肢体残疾
                </a-select-option>
                <a-select-option value="65">
                  智力残疾
                </a-select-option>
                <a-select-option value="66">
                  精神残疾
                </a-select-option>
                <a-select-option value="67">
                  多重残疾
                </a-select-option>
                <a-select-option value="69">
                  其他残疾
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="残疾等级"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0049']">
                <a-select-option value="1">
                  一级残疾
                </a-select-option>
                <a-select-option value="2">
                  二级残疾
                </a-select-option>
                <a-select-option value="3">
                  三级残疾
                </a-select-option>
                <a-select-option value="4">
                  四级残疾
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="户籍地址"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahcp0014']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="居住地址"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahcp0015']" />
            </a-form-item>
          </a-col>
          <a-row :gutter="24">
            <a-col :span="24">
              <div class="cus-title-d">证件信息</div>
            </a-col>
          </a-row>
          <a-col :span="12">
            <a-form-item
              label="申请类别"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0002']">
                <a-select-option value="1">
                  农村低保
                </a-select-option>
                <a-select-option value="2">
                  城镇低保
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="致贫原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select disabled v-decorator="['bhcx0008']">
                <a-select-option value="01">
                  疾病
                </a-select-option>
                <a-select-option value="05">
                  失业
                </a-select-option>
                <a-select-option value="02">
                  灾害
                </a-select-option>
                <a-select-option value="06">
                  失地
                </a-select-option>
                <a-select-option value="03">
                  残疾
                </a-select-option>
                <a-select-option value="07">
                  因学致贫
                </a-select-option>
                <a-select-option value="04">
                  缺乏劳动力
                </a-select-option>
                <a-select-option value="08">
                  火灾
                </a-select-option>
                <a-select-option value="09">
                  交通事故
                </a-select-option>
                <a-select-option value="10">
                  突发意外事件
                </a-select-option>
                <a-select-option value="99">
                  其他
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="救助证编号"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input disabled v-decorator="['ahcx0003']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="申请救助原因"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea disabled v-decorator="['ahcx0004']" />
            </a-form-item>
          </a-col>
        </a-form>
      </a-card>
    </a-spin>
  </a-modal>
</template>

<script>
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              jzjgName: record.jzjgName,
              ahcp0005: record.ahcp0005,
              ahcp0006: record.ahcp0006,
              ahcp0007: record.ahcp0007,
              ahcp0008: record.ahcp0008,
              ahcp0009: record.ahcp0009,
              ahcp0010: record.ahcp0010,
              ahcp0011: record.ahcp0011,
              ahcp0012: record.ahcp0012,
              ahcp0014: record.ahcp0014,
              ahcp0015: record.ahcp0015,
              ahcp0016: record.ahcp0016,
              ahcp0017: record.ahcp0017,
              ahcx0003: record.ahcx0003,
              ahcx0004: record.ahcx0004,
              ahcx0013: record.ahcx0013,
              ahcx0019: record.ahcx0019,
              ahcx0020: record.ahcx0020,
              ahcx0028: record.ahcx0028,
              ahcx0029: record.ahcx0029,
              ahcx0033: record.ahcx0033,
              azaa0002: record.azaa0002,
              azcp0011: record.azcp0011,
              azcp0012: record.azcp0012,
              azcp0013: record.azcp0013,
              azdf0005: record.azdf0005,
              azdf0011: record.azdf0011,
              bhax0114: record.bhax0114,
              bhax0115: record.bhax0115,
              bhcf0010: record.bhcf0010,
              bhcx0002: record.bhcx0002,
              bhcx0008: record.bhcx0008,
              bhcx0015: record.bhcx0015,
              bhcx0016: record.bhcx0016,
              bhcx0018: record.bhcx0018,
              bhcx0020: record.bhcx0020,
              bhcx0022: record.bhcx0022,
              bhcx0024: record.bhcx0024,
              bhcx0025: record.bhcx0025,
              bhcx0026: record.bhcx0026,
              bhcx0027: record.bhcx0027,
              bhcx0030: record.bhcx0030,
              bhcx0031: record.bhcx0031,
              bhcx0032: record.bhcx0032,
              bhcx0033: record.bhcx0033,
              bhcx0038: record.bhcx0038,
              bhcx0039: record.bhcx0039,
              bhcx0041: record.bhcx0041,
              bhcx0046: record.bhcx0046,
              bhcx0048: record.bhcx0048,
              bhcx0049: record.bhcx0049,
              bhcx0050: record.bhcx0050,
              bhcx0071: record.bhcx0071,
              bhcx0074: record.bhcx0074,
              bhcx0080: record.bhcx0080,
              bhcx0081: record.bhcx0081
            }
          )
        }, 100)
      },
      handleSubmit () {
        this.handleCancel()
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
<style>
.cus-title-d {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

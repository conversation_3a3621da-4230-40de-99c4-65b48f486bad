<template>
  <sh-drawer
    title="协同反馈消息链路"
    :visible="visible"
    :footer="null"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <Flow :messageLogList="messageLog" />
  </sh-drawer>
</template>

<script>
import Flow from './Flow.vue'
import { Message } from 'ant-design-vue';
export default {
  components: {
    Flow
  },
  setup() {
    const visible = ref(false)
    const confirmLoading = ref(false)
    const messageLog = ref([{
      linkType: '1',
      msgList: []
    }])
    const getMessageLog = async (id, exclude) => {
      try {
        const res = await $http.get(`/ywxtMessageLog/detailByDataId?dataId=${id}`)
        if (res.success) {
          messageLog.value = res.data
          if (exclude && exclude.length > 0) {
            messageLog.value = messageLog.value.filter(item => exclude.indexOf(item.type) === -1);
            console.log(messageLog.value);
          }
        }
      } catch (error) {
        Message.error('获取消息日志失败')
      }
    }

    const view = (record, exclude) => {
      if (record.zt === '2' && record.returnMsgTime) {
        Message.warning(`退回消息发送成功，发送时间: ${record.returnMsgTime}`)
        return
      }
      visible.value = true
      getMessageLog(record.id, exclude)
    }

    const handleCancel = () => {
      visible.value = false
      messageLog.value = []
    }

    return {
      visible,
      confirmLoading,
      messageLog,
      getMessageLog,
      view,
      handleCancel

    }
  }
}
</script>

<style lang="less" scoped>
</style>

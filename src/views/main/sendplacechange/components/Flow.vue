<template>
  <div class="flow-container">
    <!-- <div class="flow-wrapper"> -->
    <div>
      <a-row :gutter="24">
        <a-col :span="messageLogList.length === 1 ? 24 : messageLogList.length === 2 ? 12 : 8" v-for="(log, logIndex) in messageLogList" :key="logIndex">
          <div class="flow-item">
            <div class="flow-title">
              {{ getFlowTitle(log.type) }}
            </div>

            <div class="timeline">
              <div v-for="(item, index) in log.msgList" :key="index" class="status-item">
                <div class="left-section">
                  <template v-if="shouldShowPlatform(item)">
                    <div class="platform-icon">
                      <span>{{ getPlatformIcon(item) }}</span>
                    </div>
                    <div class="platform-text">
                      {{ getPlatformText(item) }}
                    </div>
                  </template>
                </div>

                <div class="status-dot" :class="getStatusClass(item)"></div>

                <div class="status-content">
                  <div class="status-title">
                    {{ 'xxlx' | dictType(item.xxlx) }}
                  </div>
                  <div class="status-result">
                    接收结果：<span class="result-text" :class="getStatusClass(item)">
                      {{ item.success ? '成功' : '失败' }}
                    </span>
                  </div>
                  <div class="status-description" v-if="item.xxnr && ['3', '4'].includes(item.xxlx)">
                    接收说明：<span class="description-text">{{ item.xxnr }}</span>
                  </div>
                  <div class="status-description" v-if="item.opTime">
                    接收时间：<span class="description-text">{{ item.opTime }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row></div>
  </div></template>

<script>

export default ({
  props: {
    messageLogList: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance()
    const getFlowTitle = (linkType) => {
      return proxy.$options.filters.dictType('server_num', linkType)
    }

    const shouldShowPlatform = (item) => {
      // return !item.sendOrgType
      return true
    }

    const getPlatformIcon = (item) => {
      return item.sendOrgName ? item.sendOrgName.charAt(0) : '一'
    }

    const getPlatformText = (item) => {
      return item.sendOrgName || '一体化办案平台'
    }

    const getStatusClass = (item) => {
      return {
        success: item.success,
        error: !item.success
      }
    }

    return {
      getFlowTitle,
      shouldShowPlatform,
      getPlatformIcon,
      getPlatformText,
      getStatusClass
    }
  }
})
</script>

<style lang="less" scoped>
.flow-container {
  padding: 24px;
  background: #fafafa;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.05);
}

.flow-wrapper {
  display: flex;
  gap: 48px;
}

.flow-item {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.06);
  transition: all 0.3s ease;
  min-width: 300px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.08);
  }
}

.flow-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1a73e8;
  position: relative;
  padding-left: 12px;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background: #1a73e8;
    border-radius: 2px;
  }
}

.timeline {
  position: relative;
}

.status-item {
  position: relative;
  padding-bottom: 32px;
  display: flex;
  min-height: 70px;
}

.status-item::after {
  content: '';
  position: absolute;
  left: 110px;
  top: 25px;
  bottom: -25px;
  width: 2px;
  background: linear-gradient(to bottom, #e8e8e8 50%, transparent 50%);
  background-size: 4px 4px;
}

.status-item:last-child::after {
  display: none;
}

.left-section {
  width: 90px;
  margin-right: 60px;
  text-align: center;
}

.platform-icon {
  width: 52px;
  height: 52px;
  background: linear-gradient(135deg, #1a73e8, #34a5ff);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin: 0 auto 10px;
  box-shadow: 0 4px 12px rgba(26,115,232,0.2);
}

.platform-text {
  font-size: 14px;
  color: #555;
  text-align: center;
}

.status-content {
  flex: 1;
  padding-top: 6px;
  background: #fafbfc;
  padding: 16px;
  border-radius: 8px;
}

.status-dot {
  position: absolute;
  left: 110px;
  top: 15px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #1a73e8;
  transform: translateX(-12px);
  z-index: 1;
  transition: all 0.3s ease;
}

.status-dot::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #00c853;
  font-size: 14px;
}

.status-dot.error::after {
  content: '✕';
  color: #f44336;
}

.status-dot.success {
  border-color: #00c853;
  box-shadow: 0 0 0 4px rgba(0,200,83,0.1);
}

.status-dot.error {
  border-color: #f44336;
  box-shadow: 0 0 0 4px rgba(244,67,54,0.1);
}

.status-title {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 12px;
}

.status-result {
  font-size: 14px;
  color: #666;
}

.result-text.success {
  color: #00c853;
  font-weight: 500;
}

.result-text.error {
  color: #f44336;
  font-weight: 500;
}

.status-description {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  line-height: 1.5;
}

.status-description .description-text {
  color: #444;
}
</style>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zt" default-value="1" style="width: 100%" @change="$forceUpdate()">
                  <a-select-option value="0">
                    待发送
                  </a-select-option>
                  <a-select-option value="1">
                    已发送
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="发送日期">
                <a-range-picker v-model="queryParam.rq" style="width: 100%">
                  <template slot="dateRender" slot-scope="current">
                    <div class="ant-calendar-date" :style="getCurrentStyle(current)">
                      {{ current.date() }}
                    </div>
                  </template>
                </a-range-picker>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <sh-correction-org-tree v-model="queryParam.jzjgId" style="width: 100%"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => { queryParam = {};$refs.table.refresh() }">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <div style="margin:10px">
      <a-button @click="handleOk('0')">迁出</a-button>
      <a-button @click="handleOk('1')">迁入</a-button>
    </div>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-popconfirm
            :disabled="!(selectedRowKeys.length === 1&&(selectedRows.length&&selectedRows[0].zt === '0'))"
            placement="topRight"
            title="确认删除？"
            @confirm="() => sendPlaceChangeDelete(selectedRows[0])">
            <a-button :disabled="!(selectedRowKeys.length === 1&&(selectedRows.length&&selectedRows[0].zt === '0'))" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.addForm.add({ ...record, disabled: true })">查看详情</a>
          <a-divider v-if="record.zt === '1' || record.zt === '2'" type="vertical" />
          <a v-if="record.zt === '1' || record.zt === '2'" @click="$refs.linkFlow.view({ ...record})">消息链路</a>
          <a-divider v-if="record.zt === '0'" type="vertical" />
          <a v-if="record.zt === '0'" @click="$refs.addForm.add({ ...record, disabled: false })">发送</a>
        </span>
        <span slot="timeFormat" slot-scope="text">
          {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
        </span>
        <span slot="sqjzjdjg" slot-scope="text">
          {{ sqjzjdjg(text) }}
        </span>
        <span slot="csdw" slot-scope="text,record">
          {{ csdw(record) }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <LinkFlow ref="linkFlow" />
    </a-card>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
import { sendPlaceChangePage, sendPlaceChangeDelete } from '@/api/modular/main/sendplacechange/sendPlaceChangeManage'
import addForm from './addForm.vue'
import LinkFlow from '@/views/main/sendplacechange/components/LinkFlow.vue'
export default {
  components: {
    STable,
    addForm,
    LinkFlow
  },
  data() {
    return {
      type: '0',
      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        { title: '状态', align: 'center', dataIndex: 'zt', customRender: v => v === '0' ? '待发送' : '已发送' },
        { title: '类别', align: 'center', dataIndex: 'type', customRender: v => v === '1' ? '迁入' : '迁出' },
        { ellipsis: true, title: '姓名', align: 'center', dataIndex: 'xm' },
        { ellipsis: true, title: '原迁出单位', align: 'center', dataIndex: 'jzjgName' },
        { ellipsis: true, title: '现迁入单位', align: 'center', dataIndex: 'qrdwName' },
        { ellipsis: true, title: '申请日期', align: 'center', dataIndex: 'sqrq', scopedSlots: { customRender: 'timeFormat' } },
        { title: '决定机关', align: 'center', dataIndex: 'sqjzjdjg', scopedSlots: { customRender: 'sqjzjdjg' } },
        { title: '抄送单位', align: 'center', dataIndex: 'csga', scopedSlots: { customRender: 'csdw' } },
        { ellipsis: true, title: '发送时间', align: 'center', dataIndex: 'fssj', scopedSlots: { customRender: 'timeFormat' } },
        { ellipsis: true, title: '操作', width: '250px', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        parameter.type = this.type
        return sendPlaceChangePage(Object.assign(parameter, this.switchingDate())).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      extOrgInfoData: []
    }
  },
  created() {
    // console.log(this.$route.query.zt)
    if (this.$route.query.zt) {
      this.queryParam.zt = this.$route.query.zt
    }
    this.getExtOrgInfoList()
  },
  watch: {
    $route(to, from) {
      // 对路由变化作出响应...
      this.ztDropDown = this.$options.filters['dictData']('accept_data_status')
      this.queryParam.zt = to.query.zt
      this.$refs.table.refresh()
    }
  },
  methods: {
    moment,
    /**
     * 查询参数组装
     */
    switchingDate() {
      const obj = JSON.parse(JSON.stringify(this.queryParam))
      if (obj.rq) {
        const searchBeginTime = moment(obj[0]).format('YYYY-MM-DD HH:mm:ss')
        const searchEndTime = moment(obj[1]).format('YYYY-MM-DD HH:mm:ss')
        const newObj = this.queryParam.rq ? { ...obj, searchBeginTime, searchEndTime } : obj
        delete newObj.rq
        return newObj
      } else {
        return obj
      }
    },
    sqjzjdjg(record) {
      if (this.extOrgInfoData.sqjz) {
        const jdjg = this.extOrgInfoData.sqjz.filter(item => item.orgCode === record)[0];
        if (jdjg) {
          return jdjg.orgName
        } else {
          return '-'
        }
      }
    },
    csdw(record) {
      if (this.extOrgInfoData.ga) {
        const ga = this.extOrgInfoData.ga.filter(item => item.orgCode === record.csga)[0]
        const jcy = this.extOrgInfoData.jcy.filter(item => item.orgCode === record.csjcy)[0]
        if (ga && jcy) {
          return ga.orgName + '、' + jcy.orgName
        } else {
          return '-'
        }
      }
    },
    sendPlaceChangeDelete(record) {
      sendPlaceChangeDelete(record).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    handleOk(type) {
      if (type) {
        this.type = type
      }
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    getCurrentStyle(current, today) {
      const style = {}
      if (current.date() === 1) {
        style.border = '1px solid #1890ff'
        style.borderRadius = '50%'
      }
      return style
    },
    // 外部单位信息列表
    getExtOrgInfoList() {
      extOrgInfoList().then(res => {
        const sqjz = []
        const ga = []
        const jcy = []
        res.data.forEach((v, i) => {
          if (v.type === 20) {
            sqjz.push(v)
          } else if (v.type === 40) {
            ga.push(v)
          } else if (v.type === 30) {
            jcy.push(v)
          }
        })
        this.extOrgInfoData = { ga, jcy, sqjz }
      })
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

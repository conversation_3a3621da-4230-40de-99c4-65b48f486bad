<template>
  <a-drawer
    title="协同信息"
    :width="drawerWidth"
    :visible="visible"
    placement="right"
    class="has-footer"
    @close="handleCancel"
  >
    <div v-if="visible" style="padding-bottom: 220px;">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <div class="cus-title">案件信息</div>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['tyfh']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正案件编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['sqjzajbh']" />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="cus-title">罪犯信息</div>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="社区矫正对象姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input disabled v-decorator="['xm']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select disabled dictType="xb" style="width: 100%;" v-decorator="['xb']" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  disabled
                  style="width: 100%;"
                  v-decorator="['csrq']"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-select disabled dictType="jzlb" style="width: 100%;" v-decorator="['jzlb']" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['lxdh', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <sh-correction-org-tree
                  :disabled="linkDisabled"
                  v-decorator="['sfs', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="户籍地" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <sh-cascader-distpicker
                  :disabled="linkDisabled"
                  v-decorator="['hjd', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="户籍地详细地址" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-input
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['hjdxz', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="现住地" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <sh-cascader-distpicker
                  :disabled="linkDisabled"
                  v-decorator="['xzd', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="现住地详址" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-input
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['xzdxz', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="社区矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="linkDisabled"
                  style="width: 100%;"
                  v-decorator="['sqjzksrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="社区矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="linkDisabled"
                  style="width: 100%;"
                  v-decorator="['sqjzjsrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="cus-title">执行地变更信息</div>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="迁入地" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['qrd', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="迁入单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['qrdwName', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="申请时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  :disabled="linkDisabled"
                  style="width: 100%;"
                  v-decorator="['sqrq', { rules: [{ required: true, message: '请输入' }] }]"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="现住地详址" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <a-textarea
                  :disabled="linkDisabled"
                  placeholder="请输入"
                  v-decorator="['syjyj', { rules: [{ required: false, message: '请输入事由及依据！' }] }]"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="变更执行地协同附件" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                <sh-file-upload
                  :disabled="linkDisabled"
                  accept=".pdf"
                  v-decorator="[
                    'ws',
                    { initialValue: [], rules: [{ required: true, message: '请上传执行地变更通知书' }] }
                  ]"
                  uploadUrl="/api/sysFileInfo/uploadOss"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="cus-title">协同信息</div>
          <a-col :span="12" v-if="type !== '1'" >
            <a-form-item label="是否外省决定" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group name="radioGroup" v-model="otherProv">
                <a-radio value="0">否</a-radio>
                <a-radio value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type !== '1'&&otherProv === '0'">
            <a-form-item label="社区矫正决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                :disabled="linkDisabled"
                v-decorator="['sqjzjdjg', { rules: [{ required: true, message: '请选择决定机关！' }] }]"
                style="width: 100%;"
                :show-search="true"
                :options="[...extOrgInfoData.fy, ...extOrgInfoData.jy, ...extOrgInfoData.kss]"
                valueKey="orgCode"
                labelKey="orgName"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type !== '1'&&otherProv === '1'">
            <a-form-item label="社区矫正决定机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :disabled="linkDisabled"
                placeholder="请输入"
                v-decorator="['sqjzjdjg', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type === '0' || type === '00'">
            <a-form-item label="抄送单位（原执行地公安）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                :disabled="linkDisabled"
                v-decorator="['csga', { rules: [{ required: true, message: '请选择决定机关！' }] }]"
                style="width: 100%;"
                :show-search="true"
                :options="extOrgInfoData.ga"
                valueKey="orgCode"
                labelKey="orgName"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type === '1'">
            <a-form-item label="抄送单位（新执行地公安机关）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                :disabled="linkDisabled"
                v-decorator="['csga', { rules: [{ required: true, message: '请选择决定机关！' }] }]"
                style="width: 100%;"
                :show-search="true"
                :options="extOrgInfoData.ga"
                valueKey="orgCode"
                labelKey="orgName"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type === '0' || type === '00'">
            <a-form-item label="抄送单位（原执行地检察院）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                :disabled="linkDisabled"
                v-decorator="['csjcy', { rules: [{ required: true, message: '请选择决定机关！' }] }]"
                style="width: 100%;"
                :show-search="true"
                :options="extOrgInfoData.jcy"
                valueKey="orgCode"
                labelKey="orgName"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="type === '1'">
            <a-form-item label="抄送单位（新执行地检察院）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                :disabled="linkDisabled"
                v-decorator="['csjcy', { rules: [{ required: true, message: '请选择决定机关！' }] }]"
                style="width: 100%;"
                :show-search="true"
                :options="extOrgInfoData.jcy"
                valueKey="orgCode"
                labelKey="orgName"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发送时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                disabled
                style="width: 100%;"
                v-decorator="['fssj', { rules: [{ required: true, message: '请输入' }] }]"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :disabled="linkDisabled"
                placeholder="请输入"
                v-decorator="['cbr', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :disabled="linkDisabled"
                placeholder="请输入"
                v-decorator="['cbrdh', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承办人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['cbrbm']" />
            </a-form-item>
          </a-col>
        </a-form>
      </a-spin>
    </div>
    <div
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px'
      }"
    >
      <a-button style="margin-Right: 8px" @click="handleCancel">取消</a-button>
      <a-button v-if="!linkDisabled" :loading="confirmLoading" type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
import { sendPlaceChangeEdit } from '@/api/modular/main/sendplacechange/sendPlaceChangeManage'
import { extOrgInfoList } from '@/api/modular/main/extorginfo/extOrgInfoManage'
import { fileList } from '@/api/modular/system/fileManage'
import moment from 'moment'

export default {
  data() {
    return {
      drawerWidth: 1000,
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      linkDisabled: true,
      extOrgInfoData: {}, // 矫正决定机关
      record: null,
      type: '0',
      otherProv: '0'
    }
  },
  created() {
    this.drawerWidth = window.innerWidth * 0.8
    this.getExtOrgInfoList()
  },
  methods: {
    async getFileList() {
      const { data } = await fileList({ ids: this.record.ws })
      this.form.setFieldsValue({
        ws: data.map(item => {
          return { ...item, id: item.uid }
        })
      })
    },
    // 初始化方法
    add(record) {
      this.visible = true
      this.record = record
      this.linkDisabled = record.disabled
      this.type = record.type
      this.getFileList()
      this.$nextTick(() => {
        this.form.setFieldsValue({
          ...this.record,
          fssj: moment()
        })
      })
    },
    /**
     * 提交表单
     */
    handleSubmit() {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields(async (errors, values) => {
        if (!errors) {
          const postData = {
            ...values
          }
          postData.ws = postData.ws.map(item => item.id).toString()
          postData.zt = 1

          console.log(postData, 'values')
          if (this.record) {
            postData.id = this.record.id
            sendPlaceChangeEdit(postData)
              .then(res => {
                if (res.success) {
                  this.$message.success('发送成功')
                  this.confirmLoading = false
                  this.$emit('ok', this.type)
                  this.handleCancel()
                } else {
                  this.$message.error('发送失败') // + res.message
                }
              })
              .finally(res => {
                this.confirmLoading = false
              })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.record = null
      this.visible = false
    },
    // 外部单位信息列表
    getExtOrgInfoList() {
      extOrgInfoList().then(res => {
        const fy = []
        const ga = []
        const jcy = []
        const jy = []
        const kss = []
        res.data.forEach(v => {
          if (v.type === 20) {
            fy.push(v)
          } else if (v.type === 40) {
            ga.push(v)
          } else if (v.type === 30) {
            jcy.push(v)
          } else if (v.type === 41) {
            kss.push(v)
          } else if (v.type === 61) {
            jy.push(v)
          }
        })
        this.extOrgInfoData = { fy, ga, jcy, jy, kss }
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-drawer-body {
  padding-bottom: 78px;
}

.cus-title {
  font-size: 18px;
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 10px;
  &::after {
    display: block;
    content: '';
    width: 6px;
    height: 60%;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #1990ff;
  }
}
</style>

<template>
  <a-modal
    title="编辑量卷维护"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="量卷类型，字典值：LJLX"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入量卷类型，字典值：LJLX" v-decorator="['paperType', {rules: [{required: true, message: '请输入量卷类型，字典值：LJLX！'}]}]" />
        </a-form-item>
        <a-form-item
          label="笔录类型，字典值：BLLX"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入笔录类型，字典值：BLLX" v-decorator="['blType', {rules: [{required: true, message: '请输入笔录类型，字典值：BLLX！'}]}]" />
        </a-form-item>
        <a-form-item
          label="笔录类型名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入笔录类型名称" v-decorator="['blTypeName', {rules: [{required: true, message: '请输入笔录类型名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态，0：启用 1：禁用"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入状态，0：启用 1：禁用" v-decorator="['status', {rules: [{required: true, message: '请输入状态，0：启用 1：禁用！'}]}]" />
        </a-form-item>
        <a-form-item
          label="使用单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入使用单位" v-decorator="['jzjg', {rules: [{required: true, message: '请输入使用单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="使用单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入使用单位名称" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入使用单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正" v-decorator="['score', {rules: [{required: true, message: '请输入分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正！'}]}]" />
        </a-form-item>
        <a-form-item
          label="删除状态， 0：未删除  1：已删除"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-input placeholder="请输入删除状态， 0：未删除  1：已删除" v-decorator="['delFlag', {rules: [{required: true, message: '请输入删除状态， 0：未删除  1：已删除！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { paperMaintenanceEdit } from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              paperType: record.paperType,
              blType: record.blType,
              blTypeName: record.blTypeName,
              status: record.status,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName,
              score: record.score,
              delFlag: record.delFlag
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            paperMaintenanceEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="量卷类型">
                <sh-select dictType="LJLX" style="width: 100%;" placeholder="请选择" v-model="queryParam.paperType" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="笔录类型">
                <sh-select dictType="BLLX" style="width: 100%;" placeholder="请选择" v-model="queryParam.blType" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.status" placeholder="状态" :options="statusOption"></a-select>
                <!-- <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态，0：启用 1：禁用" /> -->
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="使用单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择使用单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button
                  style="margin-left: 8px"
                  @click="
                    queryParam = {}
                    $refs.table.refresh(true)
                  "
                  >重置</a-button
                >
                <!-- <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a> -->
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator">
          <a-button type="primary" icon="plus" @click="$refs.addForm.add()">新增量卷</a-button>
          <a-popconfirm
            :disabled="!selectedRowKeys.length"
            placement="topRight"
            title="确认删除？"
            @confirm="() => paperQuestionBankBatchDelete()"
          >
            <a-button :disabled="!selectedRowKeys.length" type="danger" icon="delete">删除</a-button>
          </a-popconfirm>
          <x-down ref="batchExport" @batchExport="batchExport" />
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="record.status === 1" @click="clickDisabled(record)">禁用</a>
          <a v-if="record.status === 2" @click="clickDisabled(record)">启用</a>
          <a-divider type="vertical" v-if="record.status === 1 || record.status === 2" />
          <a @click="$refs.addForm.edit(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="$refs.addForm.detail(record)">查看详情</a>
          <!-- <a-divider type="vertical" />
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => paperMaintenanceDelete(record)">
            <a>删除</a>
          </a-popconfirm> -->
        </span>
        <span slot="paperType" slot-scope="text, record">
          {{ 'LJLX' | dictType(record.paperType) }}
        </span>
        <span slot="blType" slot-scope="text, record">
          {{ 'BLLX' | dictType(record.blType) }}
        </span>
        <span slot="bltitle" slot-scope="text, record">
          <span v-if="record.title">
            <ellipsis :length="25" tooltip>{{ record.title }}</ellipsis></span
          >
          <span v-else> </span>
        </span>
        <span slot="status" slot-scope="text, record">
          {{ record.status === 0 ? '暂存' : record.status === 1 ? '启用' : '禁用' }}
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" :orgTree="orgTree" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown, Ellipsis } from '@/components'
import { getOrgTree } from '@/api/modular/system/orgManage'
import {
  paperMaintenancePage,
  paperMaintenanceDelete,
  paperMaintenanceSetStatus,
  paperMaintenanceExport
} from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
import addForm from './addForm.vue'
import editForm from './editForm.vue'
export default {
  components: {
    STable,
    XDown,
    Ellipsis,
    addForm,
    editForm
  },
  data() {
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      orgTree: [],
      // 表头
      columns: [
        {
          title: '序号',
          align: 'center',
          dataIndex: '',
          width: '70px',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '量卷类型',
          align: 'center',
          dataIndex: 'paperType',
          scopedSlots: { customRender: 'paperType' }
        },
        {
          title: '笔录类型',
          align: 'center',
          dataIndex: 'blType',
          scopedSlots: { customRender: 'blType' }
        },
        {
          title: '笔录名称',
          align: 'center',
          dataIndex: 'title',
          width: '20%',
          scopedSlots: { customRender: 'bltitle' }
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '使用单位',
          align: 'center',
          dataIndex: 'jzjgName'
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return paperMaintenancePage(Object.assign(parameter, this.queryParam)).then(res => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      statusOption: [
        {
          value: 0,
          label: '暂存'
        },
        {
          value: 1,
          label: '启用'
        },
        {
          value: 2,
          label: '禁用'
        }
      ]
    }
  },
  created() {
    this.getOrgTree()
    this.columns.push({
      title: '操作',
      width: '170px',
      dataIndex: 'action',
      scopedSlots: { customRender: 'action' }
    })
  },
  methods: {
    batchExport() {
      paperMaintenanceExport(this.queryParam).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    clickDisabled(record) {
      const params = {
        id: record.id,
        status: record.status === 1 ? 2 : 1
      }
      paperMaintenanceSetStatus(params).then(res => {
        if (res.success) {
          this.$message.success('操作成功')
          this.$refs.table.refresh()
        }
      })
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    paperQuestionBankBatchDelete() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择需要删除的量卷')
      } else {
        paperMaintenanceDelete({ idList: this.selectedRowKeys }).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      }
    },
    paperMaintenanceDelete(record) {
      paperMaintenanceDelete({ idList: [record.id] }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.$refs.table.refresh()
        } else {
          this.$message.error('删除失败') // + res.message
        }
      })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

<template>
  <a-drawer title="预览" width="80%" :visible="visible" :footer="null" @close="handleCancel">
    <iframe :src="pdfUrl" frameborder="0" style="width: 100%;height: 100%;"></iframe>
  </a-drawer>
</template>

<script>
import { paperMaintenancePreview } from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
export default {
  data() {
    return {
      visible: false,
      pdfUrl: ''
    }
  },
  methods: {
    // 初始化方法
    open(record) {
      this.visible = true
      paperMaintenancePreview(record).then(res => {
        this.$nextTick(() => {
          const blob = new Blob([res.data], { type: 'application/pdf' })
          this.pdfUrl = window.URL.createObjectURL(blob)
          console.log(this.pdfUrl)
        })
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-drawer-body {
  height: calc(100% - 60px);
}
</style>

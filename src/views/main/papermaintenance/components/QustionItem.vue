<template>
  <div>
    <div v-for="(item, index) in qustionData" :key="index" class="qustion-box">
      <img v-if="!disabled" src="~@/assets/icons/delete.png" class="delete-img" @click="deleteQuestion(index)" />
      <a-col :span="24">
        <a-form-item :label="'问题' + (index + 1)" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入问题"
            :disabled="disabled"
            v-decorator="[`topicName${index}`, { rules: [{ required: true, message: '请输入问题' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item :label="'指标' + (index + 1)" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入指标"
            :disabled="disabled"
            v-decorator="[`indexName${index}`, { rules: [{ required: true, message: '请输入指标' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item :label="'类型' + (index + 1)" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-tree-select
            v-decorator="[`topicType${index}`, { rules: [{ required: false, message: '请选择' }] }]"
            :dataSource="dataSource"
            :valueKey="'value'"
            :labelKey="'title'"
            placeholder="请选择"
            :disabled="disabled"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24" v-for="(x, y) in item.itemList" :key="y">
        <a-form-item
          :label="'指标选项' + (y + 1)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          class="indicator-options"
        >
          <a-input
            placeholder="请输入指标选项"
            :disabled="disabled"
            v-decorator="[`content${index}-${y}`, { rules: [{ required: false, message: '请输入指标选项！' }] }]"
          />
          <a-input
            class="score-input"
            placeholder="请输入分数"
            :disabled="disabled"
            @change="validateScore"
            v-decorator="[`itemScore${index}-${y}`, { rules: [{ required: false, message: '请输入分数！' }] }]"
          />
          <a-icon
            type="plus-circle"
            v-if="y === item.itemList.length - 1 && !disabled"
            class="add-icon"
            @click="addItems(index)"
          />
          <a-icon
            type="minus-circle"
            v-if="!disabled"
            :style="{ right: y === item.itemList.length - 1 ? '-60px' : '-30px' }"
            @click="deleteItems(index, y)"
            class="delete-icon"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item :label="'备注' + (index + 1)" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            placeholder="请输入备注"
            :disabled="disabled"
            v-decorator="[`remark${index}`, { rules: [{ required: false, message: '请输入备注' }] }]"
          />
        </a-form-item>
      </a-col>
    </div>
  </div>
</template>
<script>
import { paperMaintenanceGetTypeTree } from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
export default {
  props: {
    qustionData: {
      type: Array,
      default: () => []
    },
    labelCol: {
      type: Object,
      default: () => ({})
    },
    wrapperCol: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {
    this.initTree()
  },
  methods: {
    initTree() {
      paperMaintenanceGetTypeTree({ dictTypeId: '1909436718755471362' }).then(res => {
        this.dataSource = res.data
      })
    },
    validateScore(e) {
      console.log(e.target.value)
      // 可以输入0，但是不能输入负数和小数
      const reg = /^(0|[1-9]\d*)$/
      if (!reg.test(e.target.value)) {
        this.$message.warning('请输入正确的正整数')
        e.target.value = ''
      }
    },
    addItems(index) {
      this.qustionData[index].itemList.push({
        itemScore: '',
        content: ''
      })
    },
    deleteItems(index, y) {
      this.qustionData[index].itemList.splice(y, 1)
    },
    deleteQuestion(index) {
      this.qustionData.splice(index, 1)
    }
  }
}
</script>
<style lang="less" scoped>
.qustion-box {
  width: 100%;
  display: table;
  background: #f7f7f7;
  border-radius: 4px 4px 4px 4px;
  padding-top: 12px;
  margin-bottom: 12px;
  position: relative;
  .delete-img {
    width: 18px;
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 100;
    cursor: pointer;
  }
}
.indicator-options {
  display: flex;
  align-items: center;
  position: relative;
  /deep/.ant-form-item-children {
    display: flex;
    align-items: center;
    .score-input {
      width: 20%;
      margin-left: 10px;
    }
    .add-icon {
      position: absolute;
      right: -40px;
      color: #1690ff;
      font-size: 20px;
      cursor: pointer;
      margin-right: 10px;
    }
    .delete-icon {
      position: absolute;
      right: -40px;
      color: #b1b1b1;
      font-size: 20px;
      cursor: pointer;
    }
  }
}
</style>

<template>
  <a-modal
    title="题库"
    width="80%"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="问题描述">
              <a-input v-model="queryParam.topicName" allow-clear placeholder="请输入问题" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="指标">
              <a-input v-model="queryParam.indexName" allow-clear placeholder="请输入指标" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
              <a-button
                style="margin-left: 8px"
                @click="
                  queryParam = {}
                  $refs.table.refresh(true)
                "
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-table
      ref="table"
      :columns="columns"
      :data-source="loadData"
      :pagination="pagination"
      :rowKey="record => record.id"
      @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <span slot="items" slot-scope="text, record">
        <ellipsis :length="28" tooltip>{{ record.items }}</ellipsis>
      </span>
      <span slot="topicName" slot-scope="text, record">
        <ellipsis :length="20" tooltip>{{ record.topicName }}</ellipsis>
      </span>
      <span slot="indexName" slot-scope="text, record">
        <ellipsis :length="20" tooltip>{{ record.indexName }}</ellipsis>
      </span>
      <span slot="remark" slot-scope="text, record">
        <ellipsis :length="20" tooltip>{{ record.remark }}</ellipsis>
      </span>
    </a-table>
  </a-modal>
</template>

<script>
import { Ellipsis } from '@/components'
import { paperQuestionBankPageDetail } from '@/api/modular/main/paperquestionbank/paperQuestionBankManage'
export default {
  components: {
    Ellipsis
  },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 }
      },
      visible: false,
      confirmLoading: false,
      // 查询参数
      queryParam: {
        pageSize: 10,
        pageNo: 1
      },
      // 表头
      columns: [
        {
          title: '序号',
          align: 'center',
          dataIndex: '',
          width: '70px',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '问题',
          align: 'center',
          dataIndex: 'topicName',
          width: '18%',
          scopedSlots: { customRender: 'topicName' }
        },
        {
          title: '指标',
          align: 'center',
          dataIndex: 'indexName',
          width: '18%',
          scopedSlots: { customRender: 'indexName' }
        },
        {
          title: '指标选项',
          align: 'center',
          dataIndex: 'items',
          width: '23%',
          scopedSlots: { customRender: 'items' }
        },
        {
          title: '总分',
          align: 'center',
          dataIndex: 'topicScore'
        },
        {
          title: '备注',
          align: 'center',
          dataIndex: 'remark',
          width: '18%',
          scopedSlots: { customRender: 'remark' }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: [],
      selectedRowKeys: [],
      selectedRows: [],
      pagination: {
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条`,
        pageSizeOptions: ['10', '20', '30', '40', '50']
      }
    }
  },
  mounted() {},
  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.pagination = pagination
      this.queryParam.pageNo = pagination.current
      this.queryParam.pageSize = pagination.pageSize
      this.initData()
    },
    initData() {
      paperQuestionBankPageDetail(this.queryParam).then(res => {
        if (res.success) {
          this.loadData = res.data.rows
          this.total = res.data.totalRows
          this.pagination = {
            ...this.pagination,
            total: res.data.totalRows,
            current: res.data.pageNo
          }
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = Array.from(new Set([...this.selectedRowKeys, ...selectedRowKeys]))
      this.selectedRows = [...this.selectedRows, ...selectedRows].filter((item, index, array) => {
        return array.findIndex(i => i.id === item.id) === index // 使用findIndex来确保是第一个具有该id的项
      })
      console.log(this.selectedRowKeys, this.selectedRows)
    },
    // 初始化方法
    open(record) {
      this.visible = true
      this.initData()
      if (record && record.length > 0) {
        this.selectedRows = record
        this.selectedRowKeys = record.map(item => item.id)
      }
    },
    handleSubmit() {
      this.confirmLoading = true
      this.$emit('setQuestion', this.selectedRows)
      this.confirmLoading = false
      this.handleCancel()
    },
    handleCancel() {
      this.visible = false
      this.selectedRows = []
      this.selectedRowKeys = []
      this.queryParam = {
        pageSize: 10,
        pageNo: 1
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 68vh;
  overflow: auto;
  overflow-x: hidden;
}
</style>

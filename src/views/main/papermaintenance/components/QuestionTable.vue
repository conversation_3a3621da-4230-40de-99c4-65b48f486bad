<template>
  <div>
    <a-table :columns="columns" :dataSource="qustionData" :rowKey="record => record.id" :customRow="customRow">
      <template slot="action" slot-scope="text, record, index">
        <a-button type="link" @click="deleteQuestion(index)">删除</a-button>
      </template>
      <template slot="topicName" slot-scope="text, record">
        <ellipsis :length="35" tooltip>{{ record.topicName }}</ellipsis>
      </template>
      <template slot="topicName1" slot-scope="text, record">
        <ellipsis :length="50" tooltip>{{ record.topicName }}</ellipsis>
      </template>
    </a-table>
  </div>
</template>
<script>
import { Ellipsis } from '@/components'
export default {
  components: {
    Ellipsis
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      qustionData: [],
      columns: [
        {
          title: '序号',
          align: 'center',
          dataIndex: '',
          width: '70px',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '问题',
          align: 'center',
          dataIndex: 'topicName'
        },
        {
          title: '分数',
          align: 'center',
          dataIndex: 'topicScore'
        },
        {
          title: '操作',
          align: 'center',
          dataIndex: 'action',
          width: '70px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      sourceObj: null,
      targetObj: null,
      dragStartIndex: 0
    }
  },
  watch: {
    disabled: {
      handler(val) {
        console.log(val, 'table val')
        if (val) {
          this.columns = [
            {
              title: '序号',
              align: 'center',
              dataIndex: '',
              width: '70px',
              customRender: (text, record, index) => {
                return index + 1
              }
            },
            {
              title: '问题',
              align: 'center',
              dataIndex: 'topicName',
              scopedSlots: { customRender: 'topicName1' }
            },
            {
              title: '分数',
              align: 'center',
              width: '90px',
              dataIndex: 'topicScore'
            }
          ]
        } else {
          this.columns = [
            {
              title: '序号',
              align: 'center',
              dataIndex: '',
              width: '70px',
              customRender: (text, record, index) => {
                return index + 1
              }
            },
            {
              title: '问题',
              align: 'center',
              dataIndex: 'topicName',
              scopedSlots: { customRender: 'topicName' }
            },
            {
              title: '分数',
              align: 'center',
              width: '90px',
              dataIndex: 'topicScore'
            },
            {
              title: '操作',
              align: 'center',
              dataIndex: 'action',
              width: '70px',
              scopedSlots: { customRender: 'action' }
            }
          ]
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    deleteQuestion(index) {
      this.qustionData.splice(index, 1)
      this.$emit('deleteData', index)
    },
    customRow(record, index) {
      return {
        style: {
          cursor: 'pointer'
        },
        on: {
          // 鼠标移入
          mouseenter: event => {
            // 兼容IE
            var ev = event || window.event
            ev.target.draggable = true
          },
          // 开始拖拽
          dragstart: event => {
            // 兼容IE
            var ev = event || window.event
            // 阻止冒泡
            ev.stopPropagation()
            // 得到源目标数据
            this.dragStartIndex = index
            this.sourceObj = record
            console.log(this.dragStartIndex, this.sourceObj)
          },
          // 拖动元素经过的元素
          dragover: event => {
            // 兼容 IE
            var ev = event || window.event
            // 阻止默认行为
            ev.preventDefault()
          },
          // 鼠标松开
          drop: event => {
            // 兼容IE
            var ev = event || window.event
            // 阻止冒泡
            ev.stopPropagation()
            // 得到目标数据
            this.targetObj = record
            const _data = JSON.parse(JSON.stringify(this.qustionData))
            // _data[index] = this.sourceObj
            _data.splice(index, 1, this.sourceObj)
            _data.splice(this.dragStartIndex, 1, this.targetObj)
            // _data[this.dragStartIndex] = this.targetObj
            this.qustionData = _data
            console.log(this.qustionData, '拖拽后的数据', this.dragStartIndex, index)
            console.log(this.sourceObj, '源数据')
            console.log(this.targetObj, '目标数据')
            this.$emit('dragData', this.qustionData)
          }
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.qustion-box {
  width: 100%;
  display: table;
  background: #f7f7f7;
  border-radius: 4px 4px 4px 4px;
  padding-top: 12px;
  margin-bottom: 12px;
  position: relative;
  .delete-img {
    width: 20px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
}
.indicator-options {
  display: flex;
  align-items: center;
  position: relative;
  /deep/.ant-form-item-children {
    display: flex;
    align-items: center;
    .score-input {
      width: 20%;
      margin-left: 10px;
    }
    .add-icon {
      position: absolute;
      right: -40px;
      color: #1690ff;
      font-size: 20px;
      cursor: pointer;
      margin-right: 10px;
    }
    .delete-icon {
      position: absolute;
      right: -40px;
      color: #b1b1b1;
      font-size: 20px;
      cursor: pointer;
    }
  }
}
</style>

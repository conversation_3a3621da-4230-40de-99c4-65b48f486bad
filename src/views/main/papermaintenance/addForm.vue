<template>
  <a-drawer :title="title" width="90%" :visible="visible" @close="handleCancel">
    <a-spin :spinning="confirmLoading">
      <div class="accept-wrapper">
        <a-form :form="form" class="accept-left">
          <div class="info-title"><span class="line"></span>笔录信息</div>
          <a-row>
            <a-col :span="12">
              <a-form-item label="量卷类型" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <sh-select
                  dictType="LJLX"
                  style="width: 100%;"
                  :disabled="disabled"
                  placeholder="请选择"
                  v-decorator="['paperType', { rules: [{ required: true, message: '请选择量卷类型！' }] }]"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="使用单位" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <a-tree-select
                  v-decorator="['jzjg', { rules: [{ required: true, message: '请选择使用单位！' }] }]"
                  style="width: 100%"
                  :disabled="disabled"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择使用单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="笔录类型" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <sh-select
                  dictType="BLLX"
                  :disabled="disabled"
                  style="width: 100%;"
                  placeholder="请选择"
                  v-decorator="['blType', { rules: [{ required: true, message: '请选择笔录类型！' }] }]"
                  @change="changeBlType"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="blType === 'BLLX99'">
              <a-form-item label="笔录类型名称" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <a-input
                  v-decorator="['blTypeName', { rules: [{ required: true, message: '请输入笔录类型名称！' }] }]"
                  :disabled="disabled"
                  placeholder="请输入笔录类型名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="笔录名称" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <a-input
                  v-decorator="['title', { rules: [{ required: true, message: '请输入笔录名称！' }] }]"
                  :disabled="disabled"
                  placeholder="请输入笔录名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="分数" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <a-input
                  v-decorator="['score', { rules: [{ required: true, message: '请输入分数！' }] }]"
                  :disabled="disabled"
                  placeholder="请输入分数"
                  @change="validateScore"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="info-title"><span class="line"></span>笔录问题</div>

          <a-col :span="24" v-if="!disabled">
            <a-form-item label="" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-button type="primary" @click="changeQuestionBank">
                题库选择
              </a-button>
              <a-button type="primary" @click="handleImport">
                文件导入
              </a-button>
              <a-button type="primary" @click="addQustion">
                手动添加
              </a-button>
            </a-form-item>
          </a-col>
          <QustionItem
            ref="QustionItem"
            :qustionData="qustionData"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            :disabled="disabled"
          />
          <a-col :span="24" v-if="qustionData.length > 1 && !disabled">
            <a-form-item label="" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-button type="primary" @click="addQustion">
                添加问题
              </a-button>
            </a-form-item>
          </a-col>
        </a-form>
        <div class="accept-right">
          <div class="info-title"><span class="line"></span>已添加的问题</div>
          <a-row>
            <a-col :span="12">
              <a-form-item label="指标总分" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="scoreTotal" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="!disabled">
              <a-button type="link" @click="loadQuestion">生成问题列表</a-button>
            </a-col>
          </a-row>
          <QuestionTable ref="QuestionTable" :disabled="disabled" @deleteData="deleteData" @dragData="dragData" />
          <div class="info-title" style="margin-top:16px"><span class="line"></span>笔录预览</div>
          <a-form-item label="笔录预览" :labelCol="labelCol" :wrapperCol="{ span: 8 }">
            <a-button type="link" @click="seePdf">查看</a-button>
          </a-form-item>
        </div>
      </div>
    </a-spin>
    <div
      v-if="!disabled"
      :style="{
        position: 'absolute',
        bottom: 0,
        zIndex: 111,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px'
      }"
    >
      <a-button style="marginRight: 8px" @click="handleCancel">
        取消
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit(0)">
        暂存
      </a-button>
      <a-button :loading="confirmLoading" type="primary" @click="handleSubmit(1)">
        提交
      </a-button>
    </div>
    <QuestionBank ref="QuestionBank" @setQuestion="setQuestion" />
    <PdfModel ref="PdfModel" />
  </a-drawer>
</template>

<script>
// 题库
import QuestionBank from './components/QuestionBank.vue'
// 问题指标项
import QustionItem from './components/QustionItem.vue'
// 预览pdf
import PdfModel from './components/PdfModel.vue'
// 已添加问题
import QuestionTable from './components/QuestionTable.vue'
import {
  paperMaintenanceAdd,
  paperMaintenanceDetail,
  paperMaintenanceEdit
} from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
export default {
  props: {
    orgTree: {
      type: Array,
      default: () => []
    }
  },
  components: {
    QustionItem,
    QuestionBank,
    PdfModel,
    QuestionTable
  },
  data() {
    return {
      title: '新增量卷维护',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 }
      },
      visible: false,
      disabled: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      qustionData: [],
      scoreTotal: 0,
      id: '',
      blType: ''
    }
  },
  created() {},
  methods: {
    changeBlType(e) {
      console.log(e, 'changeBlType')
      this.blType = e
    },
    validateScore(e) {
      console.log(e.target.value)
      const reg = /^(0|[1-9]\d*)$/
      if (!reg.test(e.target.value)) {
        this.$message.warning('请输入正确的正整数')
        e.target.value = ''
      }
    },
    loadQuestion() {
      console.log(this.form.getFieldsValue(), 1111)
      const formData = this.form.getFieldsValue()
      this.scoreTotal = 0
      this.qustionData.map((item, index) => {
        item.topicName = formData['topicName' + index]
        item.indexName = formData['indexName' + index]
        item.topicType = formData['topicType' + index]
        item.remark = formData['remark' + index]
        item.topicScore = 0
        let num = 0
        if (item.itemList && item.itemList.length > 0) {
          item.itemList = item.itemList.map((x, y) => {
            x.content = formData['content' + index + '-' + y]
            x.itemScore = Number(formData['itemScore' + index + '-' + y])

            if (formData['itemScore' + index + '-' + y] && Number(formData['itemScore' + index + '-' + y] > num)) {
              num = Number(formData['itemScore' + index + '-' + y])
            }
            return x
            // item.topicScore += formData['itemScore' + index + '-' + y]
            //   ? Number(formData['itemScore' + index + '-' + y])
            //   : 0
          })
          console.log(num, 'num为')
          item.topicScore = num
          this.scoreTotal += item.topicScore
        }
        return item
      })
      console.log(this.qustionData, 2222)
      this.$refs.QuestionTable.qustionData = this.qustionData
    },
    setQuestion(data) {
      this.qustionData = data
      data.forEach((item, index) => {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            [`topicName${index}`]: item.topicName,
            [`indexName${index}`]: item.indexName,
            [`topicType${index}`]: item.topicType,
            [`remark${index}`]: item.remark
          })
          if (item.itemList && item.itemList.length > 0) {
            item.itemList.forEach((x, y) => {
              this.$nextTick(() => {
                this.form.setFieldsValue({
                  [`content${index}-${y}`]: x.content,
                  [`itemScore${index}-${y}`]: x.itemScore
                })
              })
            })
          }
        })
      })
    },
    changeQuestionBank() {
      this.$refs.QuestionBank.open(this.qustionData)
    },
    deleteData(data) {
      this.qustionData.splice(data, 1)
    },
    dragData(data) {
      // this.qustionData = []
      // this.qustionData = JSON.parse(JSON.stringify(data))
      // const sourceObj = this.qustionData[data.index]
      // const targetObj = this.qustionData[data.dragStartIndex]
      // const _data = JSON.parse(JSON.stringify(this.qustionData))
      // _data[index] = this.sourceObj
      // _data[data.index] = targetObj
      // _data[data.dragStartIndex] = sourceObj
      // _data[this.dragStartIndex] = this.targetObj
      this.$nextTick(() => {
        this.qustionData = JSON.parse(JSON.stringify(data))
      })
    },
    addQustion() {
      this.qustionData.push({
        topicName: '',
        indexName: '',
        topicType: '',
        remark: '',
        topicScore: 0,
        itemList: [
          {
            itemScore: '',
            content: ''
          }
        ]
      })
    },
    // 初始化方法
    add(record) {
      this.visible = true
      this.id = ''
      this.disabled = false
      this.title = '新增量卷维护'
      this.$nextTick(() => {
        this.form.setFieldsValue({
          score: '80'
        })
      })
    },
    edit(record) {
      this.visible = true
      this.disabled = false
      this.id = record.id
      this.title = '量卷维护编辑'
      this.getDetail(record.id)
    },
    detail(record) {
      this.visible = true
      this.disabled = true
      this.title = '量卷维护详情'
      this.getDetail(record.id, 1)
    },
    getDetail(id, type) {
      paperMaintenanceDetail({ id: id }).then(res => {
        if (res.success) {
          this.scoreTotal = 0
          this.blType = res.data.blType
          this.$nextTick(() => {
            this.form.setFieldsValue({
              paperType: res.data.paperType,
              blType: res.data.blType,
              jzjg: res.data.jzjg,
              title: res.data.title,
              score: res.data.score,
              blTypeName: res.data.blTypeName
            })
            res.data.topicList.forEach((item, index) => {
              this.$nextTick(() => {
                this.form.setFieldsValue({
                  [`topicName${index}`]: item.topicName,
                  [`indexName${index}`]: item.indexName,
                  [`topicType${index}`]: item.topicType,
                  [`remark${index}`]: item.remark
                })
                if (item.itemList && item.itemList.length > 0) {
                  let num = 0
                  item.itemList.forEach((x, y) => {
                    this.$nextTick(() => {
                      this.form.setFieldsValue({
                        [`content${index}-${y}`]: x.content,
                        [`itemScore${index}-${y}`]: x.itemScore
                      })
                    })
                    if (x.itemScore && Number(x.itemScore) > num) {
                      num = Number(x.itemScore)
                    }
                  })
                  this.scoreTotal += num
                }
              })
            })
            this.qustionData = res.data.topicList
            this.$refs.QuestionTable.qustionData = res.data.topicList
          })
        }
      })
    },
    handleImport() {
      this.$message.info('功能暂未开放')
    },
    /** 0:暂存 1:启用 2:禁用
     * 提交表单
     */
    handleSubmit(type) {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      this.loadQuestion()
      if (type === 0) {
        const values = this.form.getFieldsValue()
        values.status = type
        values.topicList = this.qustionData
        console.log(values, this.qustionData)
        if (this.id) {
          values.id = this.id
          paperMaintenanceEdit(values).then(res => {
            if (res.success) {
              this.$message.success('编辑成功')
              this.confirmLoading = false
              this.$emit('ok', values)
              this.handleCancel()
            }
          })
        } else {
          paperMaintenanceAdd(values)
            .then(res => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败') // + res.message
              }
            })
            .finally(res => {
              this.confirmLoading = false
            })
        }
      } else {
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof values[key] === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.topicList = this.qustionData
            values.status = type
            if (this.id) {
              values.id = this.id
              paperMaintenanceEdit(values).then(res => {
                if (res.success) {
                  this.$message.success('编辑成功')
                  this.confirmLoading = false
                  this.$emit('ok', values)
                  this.handleCancel()
                }
              })
            } else {
              paperMaintenanceAdd(values)
                .then(res => {
                  if (res.success) {
                    this.$message.success('新增成功')
                    this.confirmLoading = false
                    this.$emit('ok', values)
                    this.handleCancel()
                  } else {
                    this.$message.error('新增失败') // + res.message
                  }
                })
                .finally(res => {
                  this.confirmLoading = false
                })
            }
          } else {
            this.confirmLoading = false
          }
        })
      }
    },
    handleCancel() {
      this.form.resetFields()
      this.qustionData = []
      this.scoreTotal = 0
      this.visible = false
      this.$refs.QuestionTable.qustionData = []
    },
    seePdf() {
      const values = this.form.getFieldsValue()
      if (this.qustionData.length > 0 && values.paperType && values.blType) {
        values.topicList = this.qustionData
        this.$refs.PdfModel.open(values)
      } else {
        this.$message.warning('请先填写表单')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.accept-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  .accept-left {
    width: 50%;
    overflow: hidden;
    overflow-y: auto;
    height: 100%;
    padding: 20px 20px 20px 0;
    padding: 20px 20px 20px 0;
  }
  .accept-right {
    border-left: 1px solid #e8e8e8;
    width: 50%;
    overflow: hidden;
    overflow-y: auto;
    height: 100%;
    padding: 20px 20px;
  }
  /deep/.ant-form-item {
    margin-bottom: 10px;
  }
}
/deep/ .ant-drawer-body {
  padding-top: 0;
  height: calc(100% - 78px);
}
/deep/.ant-spin-nested-loading {
  height: 100%;
  .ant-spin-container {
    height: 100%;
  }
}
.info-title {
  font-size: 16px;
  display: flex;
  align-items: center;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
  .line {
    width: 3px;
    height: 20px;
    border-radius: 5px;
    background: #1990ff;
    margin-right: 10px;
  }
}
/deep/.ant-input-disabled {
  color: #222;
}
</style>

<template>
  <a-modal
    title="选择矫正对象"
    :width="950"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <div>
      <!-- {{ userInfo.loginEmpInfo.orgId }} -->
      <a-card :bordered="false" :bodyStyle="tstyle">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="8" :sm="24">
                <a-form-item label="矫正单位">
                  <a-tree-select
                    v-model="queryParam.jzjg"
                    style="width: 100%"
                    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                    :treeData="orgTree"
                    placeholder="请选择矫正单位"
                  >
                    <span slot="title" slot-scope="{ id }">{{ id }}</span>
                    <a-input v-show="false" v-model="queryParam.jzjg"/>
                  </a-tree-select>
                </a-form-item>
              </a-col>

              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24" >
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="$refs.table2.refresh(true)" >查询</a-button>
                  <a-button style="margin-left: 8px" @click="queryParam = {}">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false">
        <s-table
          ref="table2"
          :columns="columns"
          :data="loadData"
          :alert="true"
          :rowKey="(record) => record.id"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type:'radio' }"
        >
          <span slot="rowIndex" slot-scope="t, r, index">
            {{ parseInt(index) + 1 }}
          </span>
          <span slot="timeFormat" slot-scope="text">
            {{ text === null ? '' : moment(text).format('YYYY-MM-DD') }}
          </span>
        </s-table>
        <!-- <view-main ref="viewMain" @ok="handleOk" /> -->
      </a-card>
    </div>
  </a-modal>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import { correctionObjectInformationPage } from '@/api/modular/main/correctionobjectinformation/correctionObjectInformationManage'
import { getOrgTree } from '@/api/modular/system/orgManage'
import { mapGetters } from 'vuex'

export default {
  // inject: ['getSqjzry'],
  components: { STable },
  computed: {
    ...mapGetters(['nickname', 'userInfo'])
  },
  data () {
    return {
      // 查询参数
      queryParam: {},
      confirmLoading: false,
      loading: false,
      orgTree: [],
      // 表头
      columns: [
       { ellipsis: true, title: '序号', dataIndex: '', key: 'rowIndex', align: 'center', width: 10, scopedSlots: { customRender: 'rowIndex' } },
       { ellipsis: true, title: '矫正单位', align: 'center', width: 125, dataIndex: 'jzjgName' },
       { ellipsis: true, title: '姓名', align: 'center', width: 65, dataIndex: 'xm' },
       { ellipsis: true, title: '身份证号', align: 'center', width: 90, dataIndex: 'sfzh' },
       { ellipsis: true, title: '矫正类别', align: 'center', width: 75, dataIndex: 'jzlbName' },
       { ellipsis: true, title: '入矫时间', align: 'center', dataIndex: 'rujiaoriqi', width: 85, scopedSlots: { customRender: 'timeFormat' } }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return correctionObjectInformationPage(Object.assign(parameter, JSON.parse(JSON.stringify(this.queryParam)))).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      visible: false,
      tag: ''
    }
  },
  created () {
    this.getOrgTree()
    this.queryParam.jzjg = this.userInfo.loginEmpInfo.orgId
  },
  methods: {
    moment,
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleOk () {
      this.$refs.table2.refresh()
    },
    choose (tag) {
      this.visible = true
      this.tag = tag
    },
    handleCancel () {
      this.selectedRowKeys = []
      this.$refs.table2.refresh()
      this.visible = false
    },
    handleSubmit () {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warn('请选择一个矫正对象')
        return
      }
      this.$emit('jzdx', this.selectedRows[0])
      this.visible = false
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
</style>

<template>
  <a-modal
    title="编辑边缘救助对象信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['azdf0004']" /></a-form-item>
        <a-form-item
          label="申请人"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申请人" v-decorator="['azdf0002', {rules: [{required: true, message: '请输入申请人！'}]}]" />
        </a-form-item>
        <a-form-item
          label="低保类别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入低保类别" v-decorator="['bhgx0010', {rules: [{required: true, message: '请输入低保类别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="总发放金额"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入总发放金额" v-decorator="['bhgx0028', {rules: [{required: true, message: '请输入总发放金额！'}]}]" />
        </a-form-item>
        <a-form-item
          label="救助日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入救助日期" v-decorator="['bhgx0029', {rules: [{required: true, message: '请输入救助日期！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsEdgeAssistanceRecipientsEdit } from '@/api/modular/main/irsedgeassistancerecipients/irsEdgeAssistanceRecipientsManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              azdf0004: record.azdf0004,
              azdf0002: record.azdf0002,
              bhgx0010: record.bhgx0010,
              bhgx0028: record.bhgx0028,
              bhgx0029: record.bhgx0029
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            irsEdgeAssistanceRecipientsEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

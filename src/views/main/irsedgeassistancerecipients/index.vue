<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="矫正单位">
                <a-tree-select
                  v-model="queryParam.jzjg"
                  style="width: 100%"
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择矫正单位"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="姓名">
                <a-input v-model="queryParam.azdf0002" allow-clear placeholder="请输入姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.azdf0004" allow-clear placeholder="请输入身份证号码"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="低保类别">
                <a-input v-model="queryParam.bhgx0010" allow-clear placeholder="请输入低保类别"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.zhuangtai" allow-clear placeholder="请选择状态">
                  <a-select-option value="0">
                    在矫
                  </a-select-option>
                  <a-select-option value="1">
                    解矫
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.azdf0004"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="$refs.editForm.edit(record)">详情</a>
        </span>
        <span slot="dblb" slot-scope="text, record">
          <span v-if="record.bhgx0010==='1'">低保边缘户</span>
          <span v-if="record.bhgx0010==='2'">因病致贫户</span>
          <span v-if="record.bhgx0010==='3'">就学困难户</span>
          <span v-if="record.bhgx0010==='4'">残疾人保障</span>
          <span v-if="record.bhgx0010==='5'">大病医疗救助</span>
          <span v-if="record.bhgx0010==='6'">扶助证家庭</span>
          <span v-if="record.bhgx0010==='7'">市级困难家庭</span>
          <span v-if="record.bhgx0010==='8'">支出型贫困</span>
          <span v-if="record.bhgx0010==='99'">其他低边户</span>
        </span>
        <span slot="zhuangtai" slot-scope="text, record">
          <span v-if="record.zhuangtai==='200'">在矫</span>
          <span v-else>解矫</span>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { irsEdgeAssistanceRecipientsPage, irsEdgeAssistanceRecipientsDelete } from '@/api/modular/main/irsedgeassistancerecipients/irsEdgeAssistanceRecipientsManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  import { getOrgTree } from '@/api/modular/system/orgManage';
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        orgTree: [],
        // 表头
        columns: [
          {
            ellipsis: true,
            title: '姓名（申请人）',
            align: 'center',
            dataIndex: 'azdf0002'
          },
          {
            ellipsis: true,
            title: '矫正单位',
            align: 'center',
            dataIndex: 'jzjgName'
          },
          {
            ellipsis: true,
            title: '状态',
            align: 'center',
            dataIndex: 'zhuangtai',
            scopedSlots: { customRender: 'zhuangtai' }
          },
          {
            ellipsis: true,
            title: '身份证号',
            align: 'center',
            dataIndex: 'azdf0004'
          },
          {
            ellipsis: true,
            title: '低保类别',
            align: 'center',
            dataIndex: 'bhgx0010',
            scopedSlots: { customRender: 'dblb' }
          },
          {
            ellipsis: true,
            title: '更新时间',
            align: 'center',
            dataIndex: 'updateTime'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return irsEdgeAssistanceRecipientsPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      this.getOrgTree()
    },
    methods: {
      getOrgTree() {
        return getOrgTree().then((res) => {
          if (res.success) {
            this.orgTree = res.data
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      irsEdgeAssistanceRecipientsDelete (record) {
        irsEdgeAssistanceRecipientsDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>

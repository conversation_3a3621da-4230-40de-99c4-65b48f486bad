<template>
  <a-modal
    title="新增职业技能证书"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="评定成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评定成绩" v-decorator="['assessmentachievement', {rules: [{required: true, message: '请输入评定成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="出生日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入出生日期" v-decorator="['birthDate', {rules: [{required: true, message: '请输入出生日期！'}]}]" />
        </a-form-item>
        <a-form-item
          label="身份证号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入身份证号" v-decorator="['cardid', {rules: [{required: true, message: '请输入身份证号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书ID" v-decorator="['certificateId', {rules: [{required: true, message: '请输入证书ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书类型" v-decorator="['certificateType', {rules: [{required: true, message: '请输入证书类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="文化程度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入文化程度" v-decorator="['culturallevel', {rules: [{required: true, message: '请输入文化程度！'}]}]" />
        </a-form-item>
        <a-form-item
          label="性别"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入性别" v-decorator="['genderName', {rules: [{required: true, message: '请输入性别！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类型" v-decorator="['idType', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="发证单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入发证单位" v-decorator="['issuedOrgan', {rules: [{required: true, message: '请输入发证单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="等级"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入等级" v-decorator="['jdrank', {rules: [{required: true, message: '请输入等级！'}]}]" />
        </a-form-item>
        <a-form-item
          label="职业方向"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入职业方向" v-decorator="['jobfx', {rules: [{required: true, message: '请输入职业方向！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['name', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="技能成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入技能成绩" v-decorator="['skillachievement', {rules: [{required: true, message: '请输入技能成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="理论成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入理论成绩" v-decorator="['theoreticalachievement', {rules: [{required: true, message: '请输入理论成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据归集日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择数据归集日期" v-decorator="['tongTime',{rules: [{ required: true, message: '请选择数据归集日期！' }]}]" @change="onChangetongTime"/>
        </a-form-item>
        <a-form-item
          label="单位统一社会信用代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位统一社会信用代码" v-decorator="['uscc', {rules: [{required: true, message: '请输入单位统一社会信用代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工种"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工种" v-decorator="['workingtype', {rules: [{required: true, message: '请输入工种！'}]}]" />
        </a-form-item>
        <a-form-item
          label="综合成绩"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入综合成绩" v-decorator="['zhScore', {rules: [{required: true, message: '请输入综合成绩！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证书颁发日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证书颁发日期" v-decorator="['zsbftime', {rules: [{required: true, message: '请输入证书颁发日期！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { irsVocationalSkillsCertificateAdd } from '@/api/modular/main/irsvocationalskillscertificate/irsVocationalSkillsCertificateManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        tongTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.tongTime = this.tongTimeDateString
            irsVocationalSkillsCertificateAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangetongTime(date, dateString) {
        this.tongTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

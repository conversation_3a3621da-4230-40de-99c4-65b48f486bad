<template>
  <a-modal
    title="编辑矫正对象个人公共信用评价信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['uscc']" /></a-form-item>
        <a-form-item
          label="主体名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入主体名称" v-decorator="['qymc', {rules: [{required: true, message: '请输入主体名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="信用分"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入信用分" v-decorator="['zhxyf', {rules: [{required: true, message: '请输入信用分！'}]}]" />
        </a-form-item>
        <a-form-item
          label="评价时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入评价时间" v-decorator="['pjsj', {rules: [{required: true, message: '请输入评价时间！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正对象id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正对象id" v-decorator="['jzdxId', {rules: [{required: true, message: '请输入矫正对象id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构id"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构id" v-decorator="['jzjg', {rules: [{required: true, message: '请输入矫正机构id！'}]}]" />
        </a-form-item>
        <a-form-item
          label="矫正机构"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入矫正机构" v-decorator="['jzjgName', {rules: [{required: true, message: '请输入矫正机构！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { sjzxDm019GrScoreDeltaGtOldEdit } from '@/api/modular/main/sjzxdm019grscoredeltagtold/sjzxDm019GrScoreDeltaGtOldManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              uscc: record.uscc,
              qymc: record.qymc,
              zhxyf: record.zhxyf,
              pjsj: record.pjsj,
              jzdxId: record.jzdxId,
              jzjg: record.jzjg,
              jzjgName: record.jzjgName
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            sjzxDm019GrScoreDeltaGtOldEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

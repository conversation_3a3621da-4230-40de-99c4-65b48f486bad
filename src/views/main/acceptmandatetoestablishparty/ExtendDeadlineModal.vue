<template>
  <div>
    <a-modal
      title="延长调查期限"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      :width="1000"
      :maskClosable="false"
    >
      <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" v-if="visible">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="收到委托时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['entrustmentReceiveTime', { rules: [{ required: true, message: '请选择收到委托时间' }] }]"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查截止时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['inveTimeLimit', { rules: [{ required: true, message: '请选择调查截止时间' }] }]"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="延长期限至" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['inveTimeLimitAfter', { rules: [{ required: true, message: '请选择延长期限时间' }] }]"
                :disabled-date="disabledDate"
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="延长期限原因" required>
          <a-textarea
            v-decorator="['reason', { rules: [{ required: true, message: '请输入延长期限原因' }] }]"
            placeholder="请输入"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="附件">
          <sh-file-uploader
            v-decorator="['files', { valuePropName: 'fileList' }]"
            :url="'/sysFileInfo/uploadOss'"
            :maxFileSize="50 * 1024 * 1024"
            :acceptedFormats="'.jpg,.png,.gif,.pdf'"
            :multiple="true"
            :listType="'text'"
          />
        </a-form-item>
      </a-form>

      <template slot="footer">
        <a-button key="back" @click="handleCancel">取消</a-button>
        <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleSubmit">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
import { mapGetters } from 'vuex'
export default {
  name: 'StopProcessModal',
  props: {},
  data() {
    return {
      visible: false,
      form: this.$form.createForm(this),
      confirmLoading: false,
      fileList: [],
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      selectedDate: null,
      record: {}
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    visible(val) {
      // if (val) {
      // 每次打开重置表单
      this.$nextTick(() => {
        if (val) {
          // this.form.resetFields()
        }
        this.fileList = []
      })
      // }
    }
    // record(val) {
    //   console.log('record', val)
    //   this.$nextTick(() => {
    //     this.form.setFieldsValue({
    //       entrustmentReceiveTime: val.entrustmentReceiveTime,
    //       inveTimeLimit: val.inveTimeLimit
    //     })
    //   })
    // }
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    async open(record) {
      console.log('record', record)
      this.visible = true
      this.record = record
      this.$nextTick(() => {
        this.form.setFieldsValue({
          entrustmentReceiveTime: record.entrustmentReceiveTime,
          inveTimeLimit: record.inveTimeLimit
        })
      })
    },
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    },
    // 禁用日期的判断函数
    disabledDate(date) {
      console.log(this.record.inveTimeLimit, '--------------')
      // 禁止选择 targetDate 之前的日期（包括当天？根据需求调整，这里不包括当天）
      return date && date.isBefore(this.record.inveTimeLimit, 'day')
      // 如果需要禁止选择包括 targetDate 当天之前的日期，改为：date <= this.targetDate
    },
    handleFileChange(info) {
      //   this.fileList = info.fileList
      //   // 处理文件上传状态
      //   if (info.file.status === 'done') {
      //     this.$message.success(`${info.file.name} 上传成功`);
      //   } else if (info.file.status === 'error') {
      //     this.$message.error(`${info.file.name} 上传失败`);
      //   }
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (err) {
          return
        }

        this.confirmLoading = true
        const date = new Date()
        // 处理文件IDs
        const files = values.files
        console.log(this.userInfo, values, '----------------')
        // 构建请求参数
        const params = {
          operatorId: this.userInfo.id,
          operator: this.userInfo.nickName,
          pid: this.record.id,
          inveTimeLimitBefore: moment(values.inveTimeLimit).format('YYYY-MM-DD'),
          inveTimeLimitAfter: moment(values.inveTimeLimitAfter).format('YYYY-MM-DD'),
          files: files && files.map(item => item.id).toString(),
          reason: values.reason
        }
        console.log('params', params)
        // return
        // 发送请求
        this.$http
          .post('/investigation/delayAdd', params)
          .then(res => {
            this.confirmLoading = false
            if (res.success) {
              this.$message.success('延期成功')
              this.$emit('success')
              this.handleCancel()
            } else {
              this.$message.error(res.message || '延期失败')
            }
          })
          .catch(error => {
            this.confirmLoading = false
            this.$message.error('延期失败：' + (error.message || '未知错误'))
          })
      })
    }
  }
}
</script>

<style lang="less" scoped></style>

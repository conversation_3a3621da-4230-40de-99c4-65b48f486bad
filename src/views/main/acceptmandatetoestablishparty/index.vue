<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="调查单位">
                <a-tree-select
                  v-model="queryParam.inveDept"
                  style="width: 100%"
                  allow-clear
                  :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                  :treeData="orgTree"
                  placeholder="请选择"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="调查评估对象">
                <a-input v-model="queryParam.correctionObjName" allow-clear placeholder="请输入" />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <sh-select
                  dictType="PGZT"
                  allow-clear
                  style="width: 100%;"
                  placeholder="请选择"
                  v-model="queryParam.status"
                />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="委托单位">
                <a-input v-model="queryParam.entrustmentDeptName" allow-clear placeholder="请输入" />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="收到委托时间">
                <a-range-picker
                  v-model="queryParam.investigationTime"
                  format="YYYY-MM-DD"
                  :placeholder="['开始时间', '结束时间']"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :md="8" :sm="24">
              <a-form-item label="调查截止时间">
                <a-range-picker
                  v-model="queryParam.investigationDeadline"
                  format="YYYY-MM-DD"
                  :placeholder="['开始时间', '结束时间']"
                  allow-clear
                />
              </a-form-item>
            </a-col>

            <a-col :md="24" :sm="24" style="text-align: right">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="record => record.id"
        :rowSelection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :customRow="customRowStyle"
      >
        <template slot="operator">
          <div style="display: flex; align-items: center;justify-content: space-between">
            <a-radio-group
              v-model="queryParam.nodeType"
              button-style="solid"
              style="margin-right: 16px"
              @change="handleStatusChange"
            >
              <a-radio-button value="0">全部</a-radio-button>
              <a-radio-button value="1">接收/建档</a-radio-button>
              <a-radio-button value="2">调查中</a-radio-button>
              <a-radio-button value="3">审批中</a-radio-button>
              <a-radio-button value="4">已结束</a-radio-button>
            </a-radio-group>
            <div style="margin-right: 8px">
              <a-button
                v-if="idDev"
                type="primary"
                icon="layout"
                style="margin-right: 8px"
                :disabled="selectedRows.length === 0"
                @click="handleNavigateToElectronic"
                >组卷</a-button
              >
              <a-button
                v-if="hasAddPermission()"
                type="primary"
                icon="plus"
                style="margin-right: 8px"
                @click="handleAdd"
                >新增</a-button
              >
              <a-button
                type="primary"
                icon="clock-circle"
                style="margin-right: 8px"
                @click="handleExtend"
                :disabled="!hasExtendDeadlinePermission()"
                >延长调查期限</a-button
              >
              <a-button
                type="danger"
                icon="flow"
                style="margin-right: 8px"
                @click="handleStopProcess"
                :disabled="!hasStopPermission()"
                >流程终止</a-button
              >
              <a-button v-if="false" type="primary" icon="file-text" style="margin-right: 8px">全量文书</a-button>
              <x-down
                ref="batchExport"
                @batchExport="batchExport"
                type="primary"
                icon="export"
                style="margin-right: 8px"
                >导出</x-down
              >
            </div>
          </div>
        </template>
        <template slot="completionProgress" slot-scope="text, record">
          <div>
            <div>
              <template v-if="record.status !== 'PGZT99' && record.status !== 'PGZT98'">
                {{ record.percent }}
                <!-- {{ calculateProgress(record) }}% -->
              </template>
              <template v-if="record.status === 'PGZT08'">
                <a-tag
                  style="margin:0;background-color: rgba(18, 191, 92, 0.08);border-radius: 20px; color: rgba(17, 175, 84, 1); border-color:  rgba(18,191,92,0.13)"
                  >已完成</a-tag
                >
              </template>
              <template v-else-if="record.status === 'PGZT98'">
                <a-tag
                  style="margin:0;background-color: rgba(255, 22, 22, 0.08);border-radius: 20px; color: rgba(255, 22, 22, 1); border-color: rgba(255, 22, 22, 0.13)"
                  >退回</a-tag
                >
              </template>
              <template v-else-if="record.status === 'PGZT99'">
                <a-tag
                  style="margin:0;background-color: rgba(255, 22, 22, 0.08);border-radius: 20px; color: rgba(255, 22, 22, 1); border-color: rgba(255, 22, 22, 0.13)"
                  >已终止</a-tag
                >
              </template>
              <template v-else-if="record.status === 'PGZT98'">
                <a-tag
                  style="margin:0;background-color: rgba(255, 22, 22, 0.08);border-radius: 20px; color: rgba(255, 22, 22, 1); border-color: rgba(255, 22, 22, 0.13)"
                  >已退回</a-tag
                >
              </template>
              <template v-else-if="record.dayNum < 0">
                <a-tag
                  style="margin:0;background-color: rgba(255, 22, 22, 0.13);border-radius: 20px; color: rgba(255, 22, 22, 1); border-color: rgba(255, 22, 22, 0.13)"
                  >逾期{{ Math.abs(record.dayNum) }}天</a-tag
                >
              </template>
              <template v-else-if="record.dayNum <= 3">
                <a-tag
                  style="margin:0;background-color: rgba(255, 136, 2, 0.08);border-radius: 20px; color: rgba(255, 136, 2, 1); border-color: rgba(255, 136, 2, 0.13)"
                  >剩余{{ record.dayNum }}天</a-tag
                >
              </template>
              <template v-else>
                <a-tag
                  style="margin:0;background-color: rgba(22, 144, 255, 0.08);border-radius: 20px; color: rgba(22, 144, 255, 1); border-color: rgba(22, 144, 255, 0.13)"
                  >剩余{{ record.dayNum }}天</a-tag
                >
              </template>
            </div>
          </div>
        </template>
        <span slot="action" slot-scope="text, record">
          <template v-if="record.status === 'PGZT03' && hasPermission('PGZT03')">
            <a @click="handleOpenFlowNode(record, 'PGZT03_1')">
              {{ record.tag > 0 ? '继续评估' : '开始评估' }}
            </a>
            <a-divider type="vertical" />
          </template>
          <template v-else-if="getNextAction(record) && hasPermission(record.status)">
            <a @click="handleOpenFlowNode(record, getNextAction(record).key)">
              {{ getNextAction(record).name }}
            </a>
            <a-divider type="vertical" />
          </template>
          <a @click="handleViewFlowNode(record, record.status)">查看详情</a>
        </span>
      </s-table>
      <edit-form ref="editForm" @ok="handleOk" />
      <flow-process-drawer ref="flowProcessDrawer" @ok="handleOk" />
      <stop-process-modal
        ref="stopProcessModal"
        :visible="stopModalVisible"
        :record="currentRecord"
        @cancel="stopModalVisible = false"
        @success="handleStopSuccess"
      />
      <extend-deadline-modal
        ref="extendDeadlineModal"
        :record="currentRecord"
        @cancel="extendDeadlineModalVisible = false"
        @success="handleStopSuccess"
      />
    </a-card>
  </div>
</template>
<script>
import { STable, XDown } from '@/components'
import { investigationInfoExport } from '@/api/modular/main/petitionletterinfo/petitionLetterInfoManage'
import editForm from './editForm.vue'
import flowProcessDrawer from './flowProcess/index.vue'
import StopProcessModal from './StopProcessModal.vue'
import ExtendDeadlineModal from './ExtendDeadlineModal.vue'
import { getOrgTree } from '@/api/modular/system/orgManage'
import moment from 'moment'
import { hasRole } from '@/utils/permissions'
export default {
  components: {
    STable,
    XDown,
    editForm,
    flowProcessDrawer,
    StopProcessModal,
    ExtendDeadlineModal
  },
  computed: {
    idDev() {
      return process.env.NODE_ENV === 'development'
    }
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        // zhuangtai: '',
        // status: '',
        nodeType: '0' // 添加默认值
      },
      // 流程节点权限映射
      nodePermissions: {
        PGZT01: ['QXSFJJLD', 'QXSFJKZ', 'QXSFJYWRY'], // 接收
        PGZT02: ['QXSFJJLD', 'QXSFJKZ', 'QXSFJYWRY'], // 小组公告
        PGZT03: ['SFSSZ', 'SFSYWRY'], // 调查评估
        PGZT03_1: ['SFSSZ', 'SFSYWRY'], // 调查单位接收
        PGZT03_2: ['SFSSZ', 'SFSYWRY'], // 管理评估清单
        PGZT03_3: ['SFSSZ', 'SFSYWRY'], // 查看和提交评估结果
        PGZT04: ['SFSSZ', 'SFSYWRY'], // 小组合议
        PGZT05: ['QXSFJKZ'], // 集体评议
        PGZT06: ['QXSFJJLD'], // 审批
        PGZT07: ['QXSFJJLD', 'QXSFJKZ', 'QXSFJYWRY'] // 反馈
      },
      // 表头
      baseColumns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          align: 'center',
          width: 50,
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '收到委托时间',
          align: 'center',
          width: 110,
          dataIndex: 'entrustmentReceiveTime',
          customRender: text => {
            return text ? moment(text).format('YYYY-MM-DD') : ''
          }
        },
        {
          title: '调查截止时间',
          align: 'center',
          width: 100,
          dataIndex: 'inveTimeLimit',
          customRender: text => {
            return text ? moment(text).format('YYYY-MM-DD') : ''
          }
        },

        {
          title: '调查评估对象',
          align: 'center',
          width: 120,
          dataIndex: 'correctionObjName'
        },
        {
          title: '接收单位',
          align: 'center',
          width: 120,
          dataIndex: 'receiveDeptName'
        },
        {
          title: '调查单位',
          align: 'center',
          dataIndex: 'inveDeptName',
          customRender: text => {
            return <sh-ellipsis content={text} tooltip />
          }
        },
        {
          title: '委托单位',
          align: 'center',
          dataIndex: 'entrustmentDeptName',
          customRender: text => {
            return <sh-ellipsis content={text} tooltip />
          }
        },
        {
          title: '委托方式',
          align: 'center',
          width: 80,
          dataIndex: 'entrustmentType',
          customRender: text => {
            return text ? this.$options.filters['dictType']('entrustment_type', text) : ''
          }
        },
        {
          title: '状态',
          align: 'center',
          width: 100,
          dataIndex: 'status',
          customRender: text => {
            return text ? this.$options.filters['dictType']('PGZT', text) : ''
          }
        },
        {
          title: '进度',
          align: 'right',
          width: 120,
          dataIndex: 'completionProgress',
          scopedSlots: { customRender: 'completionProgress' }
        }
      ],
      // 结论列定义
      conclusionColumn: {
        title: '结论',
        align: 'center',
        width: 120,
        dataIndex: 'conclusion',
        customRender: text => {
          return text ? this.$options.filters['dictType']('dcpgyj', text) : ''
        }
      },
      // 操作列定义
      actionColumn: {
        title: '操作',
        width: '200px',
        dataIndex: 'action',
        scopedSlots: { customRender: 'action' }
      },
      columns: [], // 实际使用的columns数组，会动态生成
      orgTree: [],
      letterTypeDropDown: [],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return this.$http
          .get('/investigationInfo/page', { params: Object.assign(parameter, this.switchingDate()) })
          .then(res => {
            return res.data
          })
      },
      selectedRowKeys: [],
      selectedRows: [],
      stopModalVisible: false,
      currentRecord: {},
      customRowStyle(record) {
        if (record.id && record.id.length >= 32) {
          return {
            style: {
              background: 'linear-gradient(to right, rgba(84, 157, 247, 0.08), rgba(84, 157, 247, 0.02))',
              fontWeight: '500',
              position: 'relative'
            },
            class: 'manual-add-row'
          }
        }
        return {}
      },
      extendDeadlineModalVisible: false
    }
  },
  created() {
    if (this.$route.query.date) {
      this.queryParam.dates = [moment(this.$route.query.date), moment(this.$route.query.date)]
    }
    if (this.$route.query.name) {
      this.queryParam.name = this.$route.query.name
    }
    this.getOrgTree()
    this.letterTypeDropDown = this.$options.filters['dictData']('letter_type')
    this.updateColumns() // 初始化columns
  },
  methods: {
    // 更新columns方法，根据nodeType决定是否显示结论列
    updateColumns() {
      // 复制基础列
      const newColumns = [...this.baseColumns]

      // 如果是已结束状态，替换调查截止时间列为调查结束时间列
      if (this.queryParam.nodeType === '4') {
        // 找到调查截止时间列的索引（第三列）
        const timeColumnIndex = 2
        // 替换为调查结束时间列
        newColumns[timeColumnIndex] = {
          title: '调查结束时间',
          align: 'center',
          width: 110,
          dataIndex: 'endTime',
          customRender: text => {
            return text ? moment(text).format('YYYY-MM-DD') : ''
          }
        }
        // 添加结论列
        newColumns.push(this.conclusionColumn)
        // 已结束状态下的操作列宽度为100px
        const actionCol = { ...this.actionColumn, width: '100px' }
        newColumns.push(actionCol)
      } else {
        // 添加结论列
        newColumns.push(this.conclusionColumn)
        // 其他状态保持原宽度
        newColumns.push(this.actionColumn)
      }

      // 更新columns
      this.columns = newColumns
    },

    // 检查当前用户是否有权限处理指定节点
    hasPermission(nodeType) {
      if (!nodeType) return false
      const permittedRoles = this.nodePermissions[nodeType]
      if (!permittedRoles || permittedRoles.length === 0) return false

      // 使用hasRole方法检查用户是否拥有任一所需角色
      return hasRole(permittedRoles)
    },

    // 检查是否有流程终止权限
    hasStopPermission() {
      // 仅区县司法局局领导、科长和业务人员可以终止流程
      return hasRole(['QXSFJJLD', 'QXSFJKZ', 'QXSFJYWRY'])
    },

    // 检查是否有新增权限
    hasAddPermission() {
      // 仅区县司法局局领导、科长和业务人员可以新增
      return hasRole(['QXSFJJLD', 'QXSFJKZ', 'QXSFJYWRY'])
    },
    // 检查是否有延长调查期限权限
    hasExtendDeadlinePermission() {
      // 仅区县司法局局领导、科长和业务人员可以新增
      return hasRole(['QXSFJJLD', 'QXSFJKZ'])
    },
    switchingDate() {
      // 处理调查委托时间
      const investigationTime = this.queryParam.investigationTime
      if (investigationTime != null) {
        this.queryParam.inveTimeLimitStart = moment(investigationTime[0]).format('YYYY-MM-DD')
        this.queryParam.inveTimeLimitEnd = moment(investigationTime[1]).format('YYYY-MM-DD')
        if (investigationTime.length < 1) {
          delete this.queryParam.inveTimeLimitStart
          delete this.queryParam.inveTimeLimitEnd
        }
      }

      // 处理调查截止时间
      const deadlineTime = this.queryParam.investigationDeadline
      if (deadlineTime != null) {
        this.queryParam.entrustmentTimeStar = moment(deadlineTime[0]).format('YYYY-MM-DD')
        this.queryParam.entrustmentTimeEnd = moment(deadlineTime[1]).format('YYYY-MM-DD')
        if (deadlineTime.length < 1) {
          delete this.queryParam.entrustmentTimeStar
          delete this.queryParam.entrustmentTimeEnd
        }
      }

      const obj = JSON.parse(JSON.stringify(this.queryParam))
      // 删除原始的日期范围数据
      delete obj.investigationTime
      delete obj.investigationDeadline
      return obj
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    batchExport() {
      investigationInfoExport({ ...this.switchingDate() }).then(res => {
        this.$refs.batchExport.downloadfile(res)
      })
    },
    getOrgTree() {
      return getOrgTree().then(res => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleOpenFlowNode(record, nodeType) {
      if (record.status === 'PGZT01') {
        this.$http
          .post('/investigationInfo/checkInfo', {
            id: record.id,
            correctionObjName: record.correctionObjName,
            certNum: record.certNum,
            receiveDeptId: record.receiveDeptId
          })
          .then(res => {
            if (res.data.num == 0) {
              this.$refs.flowProcessDrawer.open(record, nodeType)
            } else if (res.data.num > 0) {
              this.$warning({
                title: '温馨提示',
                content: `在${res.data.obj.entrustmentReceiveTime}已经接收过${record.correctionObjName}的调查委托，请检查是否为同一个人重复委托`,
                okText: '确定',
                onOk: () => {
                  this.$refs.flowProcessDrawer.open(record, nodeType)
                }
              })
            }
          })
        return
      }
      this.$refs.flowProcessDrawer.open(record, nodeType)
    },
    handleViewFlowNode(record, nodeType) {
      this.$refs.flowProcessDrawer.open(record, nodeType, true)
    },
    handleStatusChange(e) {
      // this.queryParam.status = e.target.value
      this.updateColumns() // 状态变化时更新columns
      this.$refs.table.refresh(true)
    },
    resetQuery() {
      this.queryParam = {
        zhuangtai: '',
        nodeType: '0',
        status: ''
      }
      this.$refs.table.refresh(true)
    },
    getNextAction(record) {
      // 修改映射关系，确保匹配所有9个步骤
      const actionMap = {
        PGZT01: { key: 'receive', name: '接收' },
        PGZT02: { key: 'announcement', name: '小组公告' },
        PGZT03_1: { key: 'unitReceive', name: '调查单位接收' },
        PGZT03_2: { key: 'assessmentList', name: '管理评估清单' },
        PGZT03_3: { key: 'assessmentResult', name: '查看和提交评估结果' },
        PGZT04: { key: 'preliminary', name: '小组合议' },
        PGZT05: { key: 'collective', name: '集体评议' },
        PGZT06: { key: 'approval', name: '审批' },
        PGZT07: { key: 'feedback', name: '反馈' }
      }

      // 使用record.status而不是record.stepCode
      if (record.id.length < 32 && record.status === 'PGZT01') {
        const action = actionMap[record.status]
        action.name = '继续新增'
        return action
      }
      return actionMap[record.status]
    },
    calculateProgress(record) {
      // 如果状态为PGZT08，表示已完成，直接返回100%
      if (record.status === 'PGZT08') {
        return 100
      }

      // 根据expeditedProcedure确定总天数
      const totalDays = record.expeditedProcedure === '1' ? 5 : 10

      // 计算已用天数
      let usedDays = totalDays - record.dayNum

      // 如果已逾期，则已用天数为总天数
      if (record.dayNum < 0) {
        usedDays = totalDays
      }

      // 计算进度百分比
      const progress = Math.round((usedDays / totalDays) * 100)

      // 确保进度不超过100%
      return Math.min(progress, 100)
    },
    handleStopProcess() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择需要终止的记录')
        return
      }

      if (this.selectedRowKeys.length > 1) {
        this.$message.warning('一次只能终止一条记录')
        return
      }

      // 检查用户是否有权限终止流程
      if (!this.hasStopPermission()) {
        this.$message.warning('您没有权限终止流程')
        return
      }

      // 检查选中记录的状态是否允许终止
      const record = this.selectedRows[0]
      if (record.status === 'PGZT01') {
        this.$message.warning('待接收状态无法终止流程')
        return
      }
      if (record.status === 'PGZT08' || record.status === 'PGZT09') {
        this.$message.warning('已结束的流程不能再次终止')
        return
      }

      this.$confirm({
        title: '确认操作',
        content: `您确定要终止"${record.correctionObjName}"的流程吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.currentRecord = record
          this.stopModalVisible = true
        }
      })
    },
    handleStopSuccess() {
      this.$refs.table.refresh()
      this.selectedRowKeys = []
      this.selectedRows = []
    },
    handleAdd() {
      // 先调用API获取初始数据
      const id = this.$store.getters.userInfo.loginEmpInfo.orgId
      console.log(id)
      this.$http
        .get('/investigationInfo/initFlow')
        .then(res => {
          if (res.success) {
            const record = {
              id: res.data,
              addType: 'new',
              receiveDeptId: this.$store.getters.userInfo.loginEmpInfo.orgId,
              status: 'PGZT01' // 添加状态字段，确保流程控制器能正确识别当前节点
            }
            // 使用获取到的数据作为record打开流程处理抽屉
            this.$refs.flowProcessDrawer.open(record, 'PGZT01')
          } else {
            this.$message.error(res.message || '获取初始数据失败')
          }
        })
        .catch(err => {
          this.$message.error('获取初始数据失败：' + (err.message || err))
        })
    },
    async handleExtend() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择需要延长调查期限的记录')
        return
      }
      const record = this.selectedRows[0]
      if (record.status === 'PGZT08' || record.status === 'PGZT09') {
        this.$message.warning('已结束的流程不能延长')
        return
      }

      this.$refs.extendDeadlineModal.open(record)
    },
    handleNavigateToElectronic() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择需要组卷的记录')
        return
      }

      const selectedRecord = this.selectedRows[0]
      // 跳转到电子卷宗页面，传递选中记录的id
      this.$router.push({
        path: '/electronic/home',
        query: {
          correctionObjId: selectedRecord.id
        }
      })
    }
  }
}
</script>
<style lang="less">
/deep/.ant-tag {
  margin: 0;
}
.table-operator {
  margin-bottom: 18px;
}
button {
  margin-right: 8px;
}
.manual-add-row::after {
  content: '跨';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 16px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  border-radius: 10px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(79, 172, 254, 0.3);
  animation: pulse 2s infinite;
  backdrop-filter: blur(4px);
  opacity: 0.9;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(79, 172, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
  }
}
</style>

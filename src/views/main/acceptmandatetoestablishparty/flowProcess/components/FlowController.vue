<template>
  <div class="flow-controller">
    <div class="flow-container">
      <div class="flow-title">
        <!-- <a-icon type="apartment" class="title-icon" /> -->
        <div class="title-container">
          <span class="title-text">流程记录</span>
          <span class="title-tip">(点击流程节点可切换页面)</span>
        </div>
        <a-button v-if="record.delay == 1" class="a_button" @click="openExtensionRecordList">延期记录</a-button>
      </div>
      <section class="flow-process-nodes-container">
        <div class="flow-process-nodes">
          <!-- 垂直贯穿整个流程的主干连接线 -->
          <div class="flow-main-connector"></div>

          <div
            v-for="(node, index) in flowNodes"
            :key="index"
            class="flow-node-group"
            :class="{ 'has-completed-node': hasCompletedNode(node), 'has-active-node': hasActiveNode(node) }"
          >
            <div
              class="flow-main-node"
              :class="{
                'active-main': currentStep === node.id || actualActiveStep === node.id,
                'completed-main': isParentCompleted(node)
              }"
              :style="{ borderLeftColor: node.color }"
              @click="toggleNodeExpand(index)"
            >
              <div class="flow-main-node-left">
                <!--receive_mandate  -->
                <!--investigation -->
                <!--approval -->
                <!--feedback  -->

                <img
                  :src="
                    require('@/assets/step/step' +
                      (!isParentCompleted(node) ? node.nodeLevel : node.nodeLevel + '-active') +
                      '.png')
                  "
                  alt=""
                />
              </div>
              <div class="flow-main-node-content">
                <div class="flow-node-title">
                  <a-icon :type="node.icon" class="node-icon" :style="{ color: node.color }" />
                  {{ node.name }}
                </div>
                <div class="flow-node-subtitle">{{ node.subName }}</div>
              </div>
              <a-icon
                class="flow-main-node-icon"
                :class="{ 'is-expanded': node.expanded }"
                :type="node.expanded ? 'up' : 'down'"
              />
            </div>
            <div class="flow-sub-nodes-wrapper" :class="{ 'has-children': node.children.length > 0 }">
              <transition-group name="expand-height" @before-enter="beforeEnter" @enter="enter" @leave="leave">
                <div v-if="node.expanded" :key="'container-' + index" class="flow-sub-nodes">
                  <div
                    v-for="(subNode, subIndex) in node.children"
                    :key="subIndex"
                    class="flow-sub-node"
                    :class="{
                      active: currentStep === subNode.id,
                      'last-child': subIndex === node.children.length - 1,
                      completed: autoCompletedSteps.includes(subNode.id) || subNode.rawData.type === 0,
                      'current-active': actualActiveStep === subNode.id,
                      'future-node':
                        !isNodeClickable(subNode.id) && actualActiveStep !== subNode.id && subNode.rawData.type !== 0
                    }"
                    :style="{
                      borderLeft:
                        currentStep !== subNode.id ? `3px solid ${subNode.color}` : `3px solid ${subNode.color}`
                    }"
                    @click="selectNode(subNode)"
                  >
                    <div class="flow-sub-node-main">
                      <a-badge :status="getNodeStatus(subNode)" />
                      <a-icon :type="subNode.icon" :style="{ color: subNode.color, marginRight: '8px' }" />
                      {{ subNode.name }}

                      <!-- 添加状态提示 -->
                      <template v-if="false">
                        <div v-if="actualActiveStep === subNode.id" class="node-status-indicator current">当前</div>
                        <div
                          v-else-if="autoCompletedSteps.includes(subNode.id)"
                          class="node-status-indicator completed"
                        >
                          已完成
                        </div>
                        <div v-else-if="!isNodeClickable(subNode.id)" class="node-status-indicator future">未开始</div>
                      </template>
                    </div>

                    <!-- 添加详细信息部分 -->
                    <div class="flow-sub-node-details" v-if="[0, 1, 2].includes(subNode.rawData.type)">
                      <!-- <div class="flow-sub-node-details" v-if="(autoCompletedSteps.includes(subNode.id) || actualActiveStep === subNode.id) "> -->
                      <!-- 接收调查评估委托节点的特定格式 -->
                      <template v-if="subNode.stepCode === 'PGZT01'">
                        <div class="detail-item">
                          <span class="detail-label">委托单位：</span>
                          <span class="detail-value">{{ subNode.rawData.gwDeptName }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">委托时间：</span>
                          <span class="detail-value">{{ subNode.rawData.wtTime }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理意见：</span>
                          <a-tag :style="getTagStyle(subNode.rawData.type)">
                            {{ subNode.reviewResult }}
                          </a-tag>
                        </div>
                      </template>

                      <!-- 调查小组公告节点的特定格式 -->
                      <template v-else-if="subNode.stepCode === 'PGZT02'">
                        <div class="detail-item">
                          <span class="detail-label">办理人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">公告时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">公告单位：</span>
                          <span class="detail-value">{{ subNode.rawData.gwDeptName }}</span>
                        </div>
                      </template>

                      <!-- 调查单位接收节点的特定格式 -->
                      <template v-else-if="subNode.stepCode === 'PGZT03_1'">
                        <div class="detail-item">
                          <span class="detail-label">办理人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理意见：</span>
                          <a-tag :style="getTagStyle(subNode.rawData.type)">
                            {{ subNode.reviewResult }}
                          </a-tag>
                        </div>
                      </template>

                      <!-- 管理评估清单节点的特定格式 -->
                      <template v-else-if="subNode.stepCode === 'PGZT03_2'">
                        <div class="detail-item">
                          <span class="detail-label">办理人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">办理时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                      </template>

                      <!-- 查看和提交评估结果节点的特定格式 -->
                      <template v-else-if="subNode.stepCode === 'PGZT03_3'">
                        <div class="detail-item">
                          <span class="detail-label">评估初步意见：</span>
                          <span class="detail-value">{{ subNode.rawData.msg }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">评估时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                      </template>

                      <!-- 审批/小组合议、审批/集体评议和审批节点的特定格式 -->
                      <template v-else-if="['PGZT04', 'PGZT05', 'PGZT06'].includes(subNode.stepCode)">
                        <div class="detail-item">
                          <span class="detail-label">审批结果：</span>
                          <a-tag :style="getTagStyle(subNode.rawData.type)">
                            {{ subNode.reviewResult }}
                          </a-tag>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">意见：</span>
                          <span class="detail-value">{{ subNode.rawData.msg }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">审批人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">审批时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                      </template>

                      <!-- 反馈节点的特定格式 -->
                      <template v-else-if="subNode.stepCode === 'PGZT07'">
                        <div class="detail-item">
                          <span class="detail-label">反馈人：</span>
                          <span class="detail-value"
                            >{{ subNode.rawData.deptName }}/{{ subNode.rawData.userName }}</span
                          >
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">反馈时间：</span>
                          <span class="detail-value">{{ subNode.rawData.spTime }}</span>
                        </div>
                      </template>

                      <!-- 流程终止时显示终止原因和附件（适用于所有节点类型） -->
                      <template v-if="subNode.rawData.type === 2">
                        <div class="detail-item">
                          <span class="detail-label">终止原因：</span>
                          <span class="detail-value" style="color: #f5222d; font-weight: bold;">{{
                            subNode.rawData.msg
                          }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">相关附件：</span>
                          <span class="detail-value">
                            <a
                              @click.stop="handleViewAttachment(subNode.rawData.fileList)"
                              v-if="subNode.rawData.fileList && subNode.rawData.fileList.length > 0"
                            >
                              <a-icon type="paper-clip" /> 查看附件
                            </a>
                            <span v-else style="color: #bfbfbf;">无附件</span>
                          </span>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </transition-group>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 流程图视图 (选项卡切换) -->
    <div class="flow-chart-container" v-if="showFlowChart">
      <div class="flow-chart-controls">
        <a-button-group class="view-toggle">
          <a-button type="primary" :ghost="viewMode !== 'list'" @click="viewMode = 'list'">
            <a-icon type="ordered-list" />列表视图
          </a-button>
          <a-button type="primary" :ghost="viewMode !== 'chart'" @click="viewMode = 'chart'">
            <a-icon type="deployment-unit" />流程图视图
          </a-button>
        </a-button-group>
      </div>

      <transition name="fade" mode="out-in">
        <div class="flow-chart" v-if="viewMode === 'chart'" key="chart">
          <!-- 流程图模拟展示 -->
          <div class="flow-chart-wrapper">
            <div v-for="(node, index) in flowNodes" :key="node.id" class="flow-chart-node-group">
              <!-- 主节点 -->
              <div class="flow-chart-main-node" :style="{ backgroundColor: node.color }">
                <a-icon :type="node.icon" />
                <span>{{ node.name }}</span>
              </div>

              <!-- 连接线和子节点 -->
              <div class="flow-chart-children">
                <div v-for="subNode in node.children" :key="subNode.id" class="flow-chart-sub-node-container">
                  <div class="flow-chart-connector" :style="{ backgroundColor: node.color }"></div>
                  <div
                    class="flow-chart-sub-node"
                    :class="{
                      'flow-node-completed': autoCompletedSteps.includes(subNode.id) || subNode.rawData.type === 0,
                      'flow-node-active': currentStep === subNode.id,
                      'flow-node-future':
                        !isNodeClickable(subNode.id) && actualActiveStep !== subNode.id && subNode.rawData.type !== 0
                    }"
                    :style="{ borderColor: subNode.color }"
                    @click="selectNode(subNode, true)"
                  >
                    <div class="node-header" :style="{ backgroundColor: subNode.color }">
                      <a-icon :type="subNode.icon" />
                      <span>{{ subNode.name }}</span>
                    </div>
                    <div class="node-status" v-if="false">
                      <a-badge :status="getNodeStatus(subNode)" :text="getStatusText(subNode)" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 流程阶段连接线 -->
              <div class="flow-chart-stage-connector" v-if="index < flowNodes.length - 1">
                <a-icon type="arrow-right" />
              </div>
            </div>
          </div>
        </div>
        <div v-else key="empty" class="flow-chart-empty">
          <div class="flow-chart-empty-text">
            <a-icon type="ordered-list" style="font-size: 24px; margin-right: 8px;" />
            <span>当前为列表视图模式</span>
          </div>
        </div>
      </transition>
    </div>
    <ExtensionRecordList ref="extensionRecordList" />
  </div>
</template>

<script>
import ExtensionRecordList from './ExtensionRecordList.vue'
export default {
  name: 'FlowController',
  components: { ExtensionRecordList },
  props: {
    // 使用v-model绑定的当前流程步骤ID
    value: {
      type: String,
      default: ''
    },
    // 已完成的步骤ID数组
    completedSteps: {
      type: Array,
      default: () => []
    },
    // 当前进行中的步骤ID (已不再使用，保留是为了兼容)
    activeStep: {
      type: String,
      default: ''
    },
    // 是否显示流程图
    showFlowChart: {
      type: Boolean,
      default: false
    },
    // 添加record prop用于获取流程数据
    record: {
      type: Object,
      required: true
    },
    // 添加是否为查看模式的prop
    isViewMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentStep: '', // 不再使用传入的value作为初始值
      actualActiveStep: '', // 不再使用传入的activeStep作为初始值
      viewMode: 'list',
      // 随机数据库
      departments: [
        '北京市海淀区司法局',
        '上海市徐汇区司法局',
        '广州市天河区司法局',
        '深圳市南山区司法局',
        '杭州市西湖区司法局'
      ],
      handlers: [
        '张三/海淀区司法局',
        '李四/徐汇区司法局',
        '王五/天河区司法局',
        '赵六/南山区司法局',
        '钱七/西湖区司法局'
      ],
      reviewResults: ['审核通过', '退回修改', '退回重审', '已完成', '暂缓通过', '需补充材料'],
      flowNodes: [], // 初始化为空数组，将通过API获取
      loading: false,
      allFlowData: [] // 存储原始流程数据，用于自行确定节点
    }
  },
  created() {
    this.fetchFlowNodes()
  },
  watch: {
    value(newVal) {
      if (newVal && newVal !== this.currentStep) {
        this.currentStep = newVal
        this.expandParentNode(newVal)
      }
    },
    // 监听isViewMode变化，重新确定当前节点
    isViewMode(newVal) {
      this.determineCurrenStepFromData()
    }
  },
  computed: {
    // 获取当前节点的索引
    currentStepIndex() {
      // 扁平化节点列表
      const allSteps = this.flowNodes.reduce((acc, node) => {
        return acc.concat(node.children.map(child => child.id))
      }, [])

      return allSteps.indexOf(this.actualActiveStep)
    },

    // 自动计算已完成的步骤（当前活跃节点之前的所有节点）
    autoCompletedSteps() {
      // 如果没有活跃节点，返回传入的completedSteps
      if (!this.actualActiveStep) {
        return this.completedSteps
      }

      // 扁平化所有子节点
      const allSteps = this.flowNodes.reduce((acc, node) => {
        return acc.concat(node.children.map(child => child.id))
      }, [])

      // 获取当前活跃节点的索引
      const activeIndex = allSteps.indexOf(this.actualActiveStep)

      // 如果找不到活跃节点，返回传入的completedSteps
      if (activeIndex === -1) {
        return this.completedSteps
      }

      // 返回活跃节点之前的所有节点ID和传入的completedSteps的合并数组
      const previousSteps = allSteps.slice(0, activeIndex)

      // 合并并去重
      return [...new Set([...previousSteps, ...this.completedSteps])]
    },

    // 是否节点可点击（节点是当前或之前的节点）
    isNodeClickable() {
      return nodeId => {
        if (!this.actualActiveStep) return true // 如果没有设置活跃节点，都可点击

        // 扁平化节点列表
        const allSteps = this.flowNodes.reduce((acc, node) => {
          return acc.concat(node.children.map(child => child.id))
        }, [])

        const activeIndex = allSteps.indexOf(this.actualActiveStep)
        const nodeIndex = allSteps.indexOf(nodeId)

        // 节点在当前进行中节点之前或者就是当前节点，则可点击
        return nodeIndex <= activeIndex
      }
    }
  },
  methods: {
    beforeEnter(el) {
      el.style.height = '0'
      el.style.opacity = '0'
    },
    enter(el, done) {
      const height = el.scrollHeight
      el.style.height = height + 'px'
      el.style.opacity = '1'

      setTimeout(() => {
        el.style.height = 'auto'
        done()
      }, 300)
    },
    leave(el, done) {
      const height = el.scrollHeight
      el.style.height = height + 'px'

      // 触发强制重绘后设置高度为0
      void el.offsetHeight
      el.style.height = '0'
      el.style.opacity = '0'

      setTimeout(() => {
        done()
      }, 300)
    },
    expandParentNode(id) {
      this.flowNodes.forEach(parent => {
        parent.children.forEach(child => {
          if (child.id === id) {
            parent.expanded = true
          }
        })
      })
    },

    toggleNodeExpand(index) {
      this.flowNodes[index].expanded = !this.flowNodes[index].expanded
    },

    selectNode(node, forceView = false) {
      console.log(node, 'node')
      const id = node.id
      console.log(node.rawData.type)
      // 检查节点是否可点击，但如果是强制选择则跳过检查
      if (!forceView && !this.isNodeClickable(id) && node.rawData.type !== 0) {
        // 不可点击时提示用户
        this.$message.info('此步骤尚未开始，请先完成当前步骤')
        return
      }

      this.currentStep = id
      this.$emit('input', id)

      let component = null
      let currentNode = null
      let found = false
      // 如果是查看模式或强制查看，则使用查看模式打开表单
      let viewMode = forceView || this.isViewMode ? 'view' : 'edit'

      // 查找对应的组件名称
      for (let i = 0; i < this.flowNodes.length && !found; i++) {
        const parent = this.flowNodes[i]
        for (let j = 0; j < parent.children.length && !found; j++) {
          const child = parent.children[j]
          if (child.id === id) {
            component = child.component
            currentNode = child
            found = true

            // 如果不是当前活跃节点，则始终以查看模式打开
            if (id !== this.actualActiveStep && !this.isViewMode) {
              viewMode = 'view'
            }
          }
        }
      }

      if (component) {
        // 传递组件名称、查看模式和当前节点
        this.$emit('component-change', component, viewMode, currentNode)
      }
    },

    // 获取节点状态: completed-已完成, processing-进行中, default-未开始, disabled-禁用
    getNodeStatus(node) {
      if (this.autoCompletedSteps.includes(node.id)) {
        return 'processing'
      } else if (this.actualActiveStep === node.id) {
        return 'processing'
      } else if (!this.isNodeClickable(node.id)) {
        return 'warning' // 添加禁用状态
      } else {
        return 'default'
      }
    },

    // 获取下一个步骤ID
    getNextStepId() {
      // 扁平化步骤数组
      const allSteps = this.flowNodes.reduce((acc, node) => {
        return acc.concat(node.children.map(child => child.id))
      }, [])

      // 查找当前步骤索引
      const currentIndex = allSteps.indexOf(this.currentStep)

      // 如果是最后一个步骤或找不到当前步骤，则返回null
      if (currentIndex === -1 || currentIndex === allSteps.length - 1) {
        return null
      }

      // 返回下一个步骤ID
      return allSteps[currentIndex + 1]
    },

    // 前往下一步
    goToNextStep() {
      // 查找当前节点的位置
      let currentParentIndex = -1
      let currentChildIndex = -1

      // 查找当前节点的索引
      for (let i = 0; i < this.flowNodes.length; i++) {
        const parent = this.flowNodes[i]
        for (let j = 0; j < parent.children.length; j++) {
          if (parent.children[j].id === this.currentStep) {
            currentParentIndex = i
            currentChildIndex = j
            break
          }
        }
        if (currentParentIndex !== -1) break
      }

      // 如果找到了当前节点
      if (currentParentIndex !== -1 && currentChildIndex !== -1) {
        const currentParent = this.flowNodes[currentParentIndex]

        // 尝试获取同一父节点下的下一个子节点
        if (currentChildIndex < currentParent.children.length - 1) {
          // 选择同一父节点下的下一个子节点
          const nextNodeId = currentParent.children[currentChildIndex + 1].id
          this.currentStep = nextNodeId
          this.actualActiveStep = nextNodeId // 同时更新活跃节点
          this.$emit('input', nextNodeId)
          this.selectNode(nextNodeId)
          return true
        }

        // 如果是当前父节点的最后一个子节点，则尝试跳转到下一个父节点的第一个子节点
        if (currentParentIndex < this.flowNodes.length - 1) {
          const nextParent = this.flowNodes[currentParentIndex + 1]
          if (nextParent.children.length > 0) {
            // 收起当前父节点
            this.flowNodes[currentParentIndex].expanded = false
            // 展开下一个父节点
            this.flowNodes[currentParentIndex + 1].expanded = true
            // 选择下一个父节点的第一个子节点
            const nextNodeId = nextParent.children[0].id
            this.currentStep = nextNodeId
            this.actualActiveStep = nextNodeId // 同时更新活跃节点
            this.$emit('input', nextNodeId)
            this.selectNode(nextNodeId)
            return true
          }
        }
      }

      // 如果无法找到下一步，返回false
      return false
    },

    // 前往上一步
    goToPreviousStep() {
      // 查找当前节点的位置
      let currentParentIndex = -1
      let currentChildIndex = -1

      // 查找当前节点的索引
      for (let i = 0; i < this.flowNodes.length; i++) {
        const parent = this.flowNodes[i]
        for (let j = 0; j < parent.children.length; j++) {
          if (parent.children[j].id === this.currentStep) {
            currentParentIndex = i
            currentChildIndex = j
            break
          }
        }
        if (currentParentIndex !== -1) break
      }

      // 如果找到了当前节点
      if (currentParentIndex !== -1 && currentChildIndex !== -1) {
        const currentParent = this.flowNodes[currentParentIndex]

        // 尝试获取同一父节点下的上一个子节点
        if (currentChildIndex > 0) {
          // 选择同一父节点下的上一个子节点
          const previousNodeId = currentParent.children[currentChildIndex - 1].id
          this.currentStep = previousNodeId
          this.actualActiveStep = previousNodeId // 同时更新活跃节点
          this.$emit('input', previousNodeId)
          this.selectNode(previousNodeId)
          return true
        }

        // 如果是当前父节点的第一个子节点，则尝试跳转到上一个父节点的最后一个子节点
        if (currentParentIndex > 0) {
          const previousParent = this.flowNodes[currentParentIndex - 1]
          if (previousParent.children.length > 0) {
            // 收起当前父节点
            this.flowNodes[currentParentIndex].expanded = false
            // 展开上一个父节点
            this.flowNodes[currentParentIndex - 1].expanded = true
            // 选择上一个父节点的最后一个子节点
            const previousNodeId = previousParent.children[previousParent.children.length - 1].id
            this.currentStep = previousNodeId
            this.actualActiveStep = previousNodeId // 同时更新活跃节点
            this.$emit('input', previousNodeId)
            this.selectNode(previousNodeId)
            return true
          }
        }
      }

      // 如果无法找到上一步，返回false
      return false
    },

    // 获取节点状态文本
    getStatusText(node) {
      if (this.autoCompletedSteps.includes(node.id)) {
        return '已完成'
      } else if (this.activeStep === node.id) {
        return '进行中'
      } else {
        return '未开始'
      }
    },

    // 添加新方法，判断父节点是否所有子节点都已完成
    isParentCompleted(node) {
      if (!node.children || node.children.length === 0) return false
      // 修改逻辑：只要有任何子节点已完成或者有子节点是当前活跃节点，父节点就标记为completed
      return node.children.some(
        child => this.autoCompletedSteps.includes(child.id) || this.actualActiveStep === child.id
      )
    },

    // 检查节点组是否包含已完成的子节点
    hasCompletedNode(node) {
      return node.children.some(child => this.autoCompletedSteps.includes(child.id))
    },

    // 检查节点组是否包含当前活跃节点
    hasActiveNode(node) {
      return node.children.some(child => this.actualActiveStep === child.id)
    },

    // 获取随机办理人
    getRandomHandler() {
      return this.handlers[Math.floor(Math.random() * this.handlers.length)]
    },

    // 获取随机时间
    getRandomTime() {
      const year = 2023 + Math.floor(Math.random() * 2)
      const month = 1 + Math.floor(Math.random() * 12)
      const day = 1 + Math.floor(Math.random() * 28)
      const hour = Math.floor(Math.random() * 24)
      const minute = Math.floor(Math.random() * 60)
      const second = Math.floor(Math.random() * 60)

      return `${year}-${month.toString().padStart(2, '0')}-${day
        .toString()
        .padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute
        .toString()
        .padStart(2, '0')}:${second.toString().padStart(2, '0')}`
    },

    // 获取随机办理单位
    getRandomDepartment() {
      return this.departments[Math.floor(Math.random() * this.departments.length)]
    },

    // 添加获取审核结果颜色的方法
    getReviewResultColor(result) {
      if (result === 1) {
        return '#52c41a' // 审核通过-绿色
      } else if (result === 0) {
        return '#faad14' // 退回修改-黄色
      } else if (result === 2) {
        return '#f5222d' // 流程终止-红色
      } else if (result === 9) {
        return '#1890ff' // 未开始-蓝色
      } else {
        return '#8c8c8c' // 其他状态-灰色
      }
    },

    // 添加获取标签样式的方法
    getTagStyle(result) {
      if (result === 1) {
        // 审核通过-绿色
        return {
          backgroundColor: 'rgba(18, 191, 92, 0.08)',
          borderRadius: '20px',
          color: 'rgba(17, 175, 84, 1)',
          borderColor: 'rgba(18, 191, 92, 0.13)'
        }
      } else if (result === 0) {
        // 退回修改-黄色
        return {
          backgroundColor: 'rgba(250, 173, 20, 0.08)',
          borderRadius: '20px',
          color: 'rgba(250, 173, 20, 1)',
          borderColor: 'rgba(250, 173, 20, 0.13)'
        }
      } else if (result === 2) {
        // 流程终止-红色
        return {
          backgroundColor: 'rgba(245, 34, 45, 0.08)',
          borderRadius: '20px',
          color: 'rgba(245, 34, 45, 1)',
          borderColor: 'rgba(245, 34, 45, 0.13)'
        }
      } else if (result === 9) {
        // 未开始-蓝色
        return {
          backgroundColor: 'rgba(24, 144, 255, 0.08)',
          borderRadius: '20px',
          color: 'rgba(24, 144, 255, 1)',
          borderColor: 'rgba(24, 144, 255, 0.13)'
        }
      } else {
        // 其他状态-灰色
        return {
          backgroundColor: 'rgba(140, 140, 140, 0.08)',
          borderRadius: '20px',
          color: 'rgba(140, 140, 140, 1)',
          borderColor: 'rgba(140, 140, 140, 0.13)'
        }
      }
    },

    // 获取随机审批结果
    getRandomReviewResult() {
      return this.reviewResults[Math.floor(Math.random() * this.reviewResults.length)]
    },

    // 根据当前流程数据确定当前节点
    determineCurrenStepFromData() {
      if (!this.allFlowData || this.allFlowData.length === 0) {
        return
      }

      let targetNodeId = ''

      if (this.isViewMode) {
        // 查看模式：找到type===1或type===2的最后一个节点作为当前节点
        const completedNodes = this.allFlowData.filter(item => item.type === 1 || item.type === 2 || item.type === 0)
        if (completedNodes.length > 0) {
          const lastCompletedNode = completedNodes[completedNodes.length - 1]
          targetNodeId = `${lastCompletedNode.id}` // 使用原始数据的id
        } else {
          // 如果没有已完成节点，选择第一个节点
          targetNodeId = `${this.allFlowData[0].id}` // 使用原始数据的id
        }
      } else {
        // 非查看模式：找到type===9的第一个节点作为当前节点
        const unStartedNodes = this.allFlowData.filter(item => item.type === 9)
        if (unStartedNodes.length > 0) {
          targetNodeId = `${unStartedNodes[0].id}` // 使用原始数据的id
        } else {
          // 如果没有未开始的节点，选择最后一个已完成节点的下一个节点
          const completedNodes = this.allFlowData.filter(item => item.type === 1 || item.type === 2)
          if (completedNodes.length > 0 && completedNodes.length < this.allFlowData.length) {
            const lastCompletedIndex = this.allFlowData.findIndex(
              item => item.id === completedNodes[completedNodes.length - 1].id
            )
            if (lastCompletedIndex < this.allFlowData.length - 1) {
              targetNodeId = `${this.allFlowData[lastCompletedIndex + 1].id}` // 使用原始数据的id
            }
          } else {
            // 如果所有节点都完成了或没有完成的节点，选择第一个节点
            targetNodeId = `${this.allFlowData[0].id}` // 使用原始数据的id
          }
        }
      }

      if (targetNodeId) {
        this.currentStep = targetNodeId
        this.actualActiveStep = targetNodeId
        this.$emit('input', targetNodeId)
        this.expandParentNode(targetNodeId)

        // 找到对应组件
        let component = null
        let currentNode = null
        const viewMode = this.isViewMode ? 'view' : 'edit'

        // 查找组件
        for (const group of this.flowNodes) {
          for (const child of group.children) {
            if (child.id === targetNodeId) {
              component = child.component
              currentNode = child
              break
            }
          }
          if (component) break
        }

        if (component) {
          this.$emit('component-change', component, viewMode, currentNode)
        }
      }
    },

    // 添加获取流程节点数据的方法
    async fetchFlowNodes() {
      try {
        this.loading = true
        // 修改接口路径
        const res = await this.$http.get('/investigationInfo/flowData', {
          params: { id: this.record.id }
        })

        if (res.success) {
          // 提取所有子节点数据，保存到 allFlowData 中
          this.allFlowData = res.data.reduce((acc, group) => {
            if (group.childNode && Array.isArray(group.childNode)) {
              return acc.concat(group.childNode)
            }
            return acc
          }, [])

          // 将后端数据转换为组件所需的格式
          this.flowNodes = this.transformFlowNodes(res.data)

          // 根据数据自动确定当前节点
          this.determineCurrenStepFromData()
        } else {
          this.$message.error(res.message || '获取流程数据失败')
        }
      } catch (error) {
        console.error('获取流程数据失败:', error)
        this.$message.error('获取流程数据失败')
      } finally {
        this.loading = false
      }
    },

    // 转换后端数据为组件所需格式
    transformFlowNodes(data) {
      // 定义节点映射关系 - 保留以便获取基本样式和图标
      const nodeLevelConfig = {
        1: {
          id: 'receive_mandate',
          name: '接收委托建档',
          nodeLevel: 1,
          subName: '委托矫正机构和指派调查',
          icon: 'file-add',
          color: '#1890ff'
        },
        2: {
          id: 'investigation',
          name: '调查评估',
          nodeLevel: 2,
          subName: '组织调查队伍、确定评估清单、生成初步结论',
          icon: 'search',
          color: '#722ed1'
        },
        3: {
          id: 'approval',
          name: '评估审批阶段',
          nodeLevel: 3,
          subName: '司法所合议、区县司法局评议及审批',
          icon: 'audit',
          color: '#2f54eb'
        },
        4: {
          id: 'feedback',
          name: '反馈结果阶段',
          nodeLevel: 4,
          subName: '评估意见反馈委托机关及检察机关',
          icon: 'message',
          color: '#fa541c'
        }
      }

      // stepCode到组件的映射
      const componentMap = {
        PGZT01: { id: 'receive', component: 'ReceiveForm', icon: 'solution', color: '#52c41a' },
        PGZT02: { id: 'announcement', component: 'AnnouncementForm', icon: 'notification', color: '#faad14' },
        PGZT03_1: { id: 'unitReceive', component: 'UnitReceiveForm', icon: 'team', color: '#eb2f96' },
        PGZT03_2: { id: 'assessmentList', component: 'AssessmentListForm', icon: 'ordered-list', color: '#fa8c16' },
        PGZT03_3: { id: 'assessmentResult', component: 'AssessmentResultForm', icon: 'file-text', color: '#13c2c2' },
        PGZT04: { id: 'preliminary', component: 'PreliminaryForm', icon: 'audit', color: '#a0d911' },
        PGZT05: { id: 'collective', component: 'CollectiveForm', icon: 'team', color: '#1890ff' },
        PGZT06: { id: 'approval', component: 'ApprovalForm', icon: 'check-square', color: '#52c41a' },
        PGZT07: { id: 'feedback', component: 'FeedbackForm', icon: 'mail', color: '#faad14' }
      }

      // 直接将每个数据项作为一个主节点处理，不再合并
      return data.map((item, index) => {
        // 获取nodeLevel对应的基本配置
        const levelConfig = nodeLevelConfig[item.nodeLevel] || {
          id: `level_${item.nodeLevel}`,
          name: `阶段${item.nodeLevel}`,
          nodeLevel: item.nodeLevel,
          subName: `流程阶段${item.nodeLevel}`,
          icon: 'cluster',
          color: ['#1890ff', '#722ed1', '#2f54eb', '#fa541c', '#eb2f96', '#a0d911'][index % 6]
        }

        // 使用数据项的nodeLevel和索引创建唯一ID
        const parentId = `node_${item.nodeLevel}_${index}`

        return {
          id: parentId,
          name: levelConfig.name,
          subName: levelConfig.subName,
          nodeLevel: levelConfig.nodeLevel,
          icon: levelConfig.icon,
          color: levelConfig.color,
          expanded: true, // 修改这里，默认全部展开
          children: (item.childNode || []).map(childItem => {
            if (childItem.stepCode === 'PGZT98') {
              childItem.stepCode = 'PGZT01'
            }
            const componentConfig = componentMap[childItem.stepCode] || {}
            console.log('childItem:', childItem.stepName, JSON.parse(childItem.formVal))
            return {
              id: `${childItem.id}`, // 使用原始数据的id作为节点id
              stepCode: childItem.stepCode, // 保存stepCode用于组件映射
              name: childItem.stepName,
              component: componentConfig.component,
              icon: componentConfig.icon,
              color: componentConfig.color,
              handler: childItem.userName,
              department: childItem.deptName,
              processTime: childItem.spTime || childItem.wtTime,
              reviewResult: this.getReviewResultByType(childItem.type),
              // 添加原始数据，以备后续使用
              rawData: {
                ...childItem,
                // 确保formVal存在，如果后端返回了表单值则使用，否则初始化为空对象
                formVal: childItem.formVal ? JSON.parse(childItem.formVal) : {}
              }
            }
          })
        }
      })
    },

    // 根据type获取审核结果
    getReviewResultByType(type) {
      const typeMap = {
        0: '退回',
        1: '审核通过',
        2: '流程终止',
        9: '未开始'
      }
      return typeMap[type] || '未知状态'
    },
    openExtensionRecordList() {
      this.$refs.extensionRecordList.open(this.record)
    },
    // 查看附件
    handleViewAttachment(fileList) {
      if (!fileList) return

      this.$imageViewer.view(
        fileList.map(item => item.filePath),
        0,
        '/pdfJsLib/web/viewer.html'
      )

      // try {
      //   // 如果附件是字符串，尝试解析JSON
      //   const files = typeof fileList === 'string'
      //     ? JSON.parse(fileList)
      //     : fileList;

      //   if (Array.isArray(files) && files.length > 0) {
      //     // 如果有文件，打开第一个附件
      //     const firstFile = files[0];
      //     // 处理文件预览，可以调用系统预览接口或打开下载链接
      //     this.$message.info('正在打开附件: ' + (firstFile.name || firstFile.fileName || '未命名文件'));

      //     // 如果有url属性，打开链接
      //     if (firstFile.filePath) {
      //       window.open(firstFile.filePath, '_blank');
      //     } else if (firstFile.fileId) {
      //       // 如果有fileId，可以调用系统API获取文件
      //       // 示例：调用下载文件接口
      //       // window.location.href = `/api/file/download?fileId=${firstFile.fileId}`;
      //       this.$message.info('正在准备下载文件，请稍候...');
      //     }
      //   } else {
      //     this.$message.warning('没有找到有效的附件');
      //   }
      // } catch (error) {
      //   console.error('查看附件出错:', error);
      //   this.$message.error('查看附件时出现错误');
      // }
    }
  }
}
</script>

<style lang="less" scoped>
@import '../styles/flowProcessStylesFake.less';

.flow-controller {
  width: 100%;
  height: 100%;
  user-select: none;
}

.flow-node-group {
  margin-bottom: 24px;
  position: relative;
  transition: all 0.3s ease;

  &.has-completed-node::before {
    // content: '';
    // position: absolute;
    // left: -12px;
    // top: 0;
    // bottom: 0;
    // width: 0px;
    // border-left: 2px solid #40a9ff;
    // z-index: 2;
  }

  &.has-active-node::before {
    // content: '';
    // position: absolute;
    // left: -12px;
    // top: 0;
    // bottom: 0;
    // width: 0px;
    // border-left: 2px solid #096dd9;
    // z-index: 2;
  }
}

.flow-main-node {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  flex-direction: row;
  position: relative;
  border-left: 3px solid transparent;
  transition: all 0.3s;
  display: flex;
  align-items: center;

  &:hover {
    background-color: #e6f7ff;
  }
  .flow-main-node-left {
    width: 32px;
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: -42px; /* 调整图标位置，使其与主干线对齐 */
    img {
      width: 32px;
      height: 32px;

      transition: all 0.3s ease;
    }
  }
  .flow-main-node-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  &.completed-main {
    background-color: rgba(64, 169, 255, 0.08);
    border-left: 3px solid #40a9ff;

    .flow-main-node-left img {
    }
  }

  &.active-main {
    background-color: #e6f7ff;
    border-left: 3px solid #096dd9;

    .flow-main-node-left img {
      transform: scale(1.1);
      box-shadow: 0 0 0 3px rgba(9, 109, 217, 0.2);
    }
  }

  .flow-main-node-icon {
    transition: transform 0.3s ease, color 0.3s ease;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);

    &.is-expanded {
      transform: translateY(-50%) rotate(180deg);
    }
  }

  &:hover .flow-main-node-icon {
    color: #40a9ff;
  }

  // 箭头旋转效果
  .anticon-up {
    transform: rotate(180deg) !important;
  }

  .anticon-down {
    transform: rotate(0deg) !important;
  }
}

.flow-node-title {
  font-weight: bold;

  padding-left: 8px;
  font-size: 16px;
}

.flow-node-subtitle {
  font-size: 15px;
  color: rgba(0, 0, 0, 1);
  padding-left: 8px;
}

.flow-sub-nodes-wrapper {
  position: relative;
}

.flow-sub-nodes {
  margin-top: 8px;
  position: relative;
  padding-left: 0; /* 重置内边距 */
}

.flow-sub-node {
  padding: 12px;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.25s ease;
  transform-origin: left center;
  border-left: 3px solid transparent;
  position: relative;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 3;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background-color: rgba(64, 169, 255, 0.08);
    z-index: -1;
    transition: width 0.3s ease;
  }

  .flow-sub-node-main {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 15px;
    position: relative;
  }

  .flow-sub-node-details {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed rgba(0, 0, 0, 0.09);
    width: 100%;
    font-size: 14px;
    color: rgba(93, 93, 93, 1);
  }

  .detail-item {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }

  .detail-label {
    color: rgba(0, 0, 0, 0.45);
    margin-right: 4px;
    flex-shrink: 0;
  }

  .detail-value {
    color: rgba(0, 0, 0, 0.65);
    word-break: break-all;
  }

  /deep/ .ant-badge-status {
    position: relative;
    z-index: 11;
    margin-left: -31px;
  }

  /deep/ .ant-badge-status-dot {
    position: relative;
    z-index: 2;
    width: 10px;
    height: 10px;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px #e8e8e8;
    transition: all 0.3s ease;
  }

  &:hover {
    background-color: #e6f7ff;
    transform: translateX(5px);

    &::after {
      // width: 100%;
    }

    &::before {
      background-color: #40a9ff;
    }

    /deep/ .ant-badge-status-dot {
      box-shadow: 0 0 0 1px #40a9ff;
      transform: scale(1.2);
    }
  }

  &.active {
    background-color: rgba(9, 109, 217, 0.08);
    color: #096dd9;
    transform: translateX(0);
    border-left: 3px solid #096dd9;
    // font-weight: bold;
    // box-shadow: 0 2px 8px rgba(9, 109, 217, 0.15);

    .anticon {
      color: #096dd9 !important;
    }

    .detail-value {
      color: #096dd9;
    }

    &::after {
      width: 0;
    }

    &::before {
      background-color: #096dd9;
    }

    /deep/ .ant-badge-status-dot {
      box-shadow: 0 0 0 1px #096dd9;
    }
  }

  // 已完成节点样式
  &.completed {
    background-color: rgba(64, 169, 255, 0.08);
    border-left: 3px solid #40a9ff;
    box-shadow: 0 1px 2px rgba(64, 169, 255, 0.15);

    &::before {
      background-color: #40a9ff;
    }

    .detail-value {
      color: #40a9ff;
    }

    .node-status-indicator.completed {
      color: #40a9ff;
      background-color: rgba(64, 169, 255, 0.1);
      border: 1px solid rgba(64, 169, 255, 0.3);
      font-weight: bold;
    }

    &:hover {
      background-color: rgba(64, 169, 255, 0.15);
      border-left: 3px solid #40a9ff;
    }

    // 已完成且被选中的节点样式
    &.active {
      background: linear-gradient(to right, rgba(24, 144, 255, 0.12), rgba(64, 169, 255, 0.2));

      &::after {
        background-color: rgba(24, 144, 255, 0.05);
        // width: 100%;
      }

      .anticon {
        color: #096dd9 !important;
        transform: scale(1.1);
      }

      .flow-sub-node-main {
        padding-bottom: 4px;
      }

      .node-status-indicator.completed {
        background-color: rgba(9, 109, 217, 0.15);
        border: 1px solid #096dd9;
        color: #096dd9;
      }

      /deep/ .ant-badge-status-dot {
        background-color: #096dd9 !important;
        box-shadow: 0 0 0 2px rgba(9, 109, 217, 0.3);
      }
    }
  }

  // 当前活跃节点样式
  &.current-active {
    background-color: #e6f7ff;
    border-left: 3px solid #096dd9;
    transform: translateY(-2px);

    &::before {
      background-color: #096dd9;
    }

    .detail-value {
      color: #096dd9;
      font-weight: 500;
    }

    /deep/ .ant-badge-status-dot {
      background-color: #096dd9 !important;
      box-shadow: 0 0 0 2px #096dd9;
    }

    .node-status-indicator.current {
      color: #096dd9;
      background-color: rgba(9, 109, 217, 0.08);
      border: 1px solid #096dd9;
      font-weight: bold;
    }

    &:hover {
      background-color: rgba(9, 109, 217, 0.12);
      transform: translateY(-2px) translateX(3px);
    }
  }

  // 未来节点样式（不可点击）
  &.future-node {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: #fafafa;

    &:hover {
      transform: none;
      background-color: #fafafa;

      &::after {
        width: 0;
      }

      &::before {
        background-color: #e8e8e8;
      }

      /deep/ .ant-badge-status-dot {
        box-shadow: 0 0 0 1px #e8e8e8;
        transform: none;
      }
    }

    .node-status-indicator.future {
      color: #8c8c8c;
      background-color: rgba(0, 0, 0, 0.03);
      border: 1px solid #d9d9d9;
    }
  }

  // 节点状态指示器通用样式
  .node-status-indicator {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    z-index: 3;
  }

  .ant-badge {
    margin-right: 8px;
  }
}

/* 修改主干连接线的位置 */
.flow-main-connector {
  position: absolute;
  left: 28px; /* 调整主干线位置 */
  top: 18px;
  bottom: 50px;
  width: 0px;
  border-left: 2px dotted #d9d9d9;
  z-index: 1;
}
.flow-process-nodes-container {
  height: calc(100% - 60px);
  overflow-y: auto;
  padding: 12px 24px;
}
/* 调整整体布局 */
.flow-process-nodes {
  overflow-y: auto;

  position: relative;
  padding-left: 40px; /* 调整左边距给主干线和连接线留出空间 */
}

// 添加子节点列表的交错动画
.flow-sub-nodes {
  .flow-sub-node {
    animation: slideInFromLeft 0.3s ease forwards;
    opacity: 0;
    transform: translateX(-10px);
  }

  .flow-sub-node:nth-child(1) {
    animation-delay: 0.05s;
  }
  .flow-sub-node:nth-child(2) {
    animation-delay: 0.1s;
  }
  .flow-sub-node:nth-child(3) {
    animation-delay: 0.15s;
  }
  .flow-sub-node:nth-child(4) {
    animation-delay: 0.2s;
  }
  .flow-sub-node:nth-child(5) {
    animation-delay: 0.25s;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 修改展开收起动画
.expand-height-enter-active,
.expand-height-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.completion-mark {
  display: none; // 隐藏所有completion-mark元素
}

.flow-chart-container {
  margin-top: 16px;
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.flow-chart-controls {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.view-toggle {
  margin-right: 8px;
}

.flow-chart {
  position: relative;
  padding: 20px;
  overflow-x: auto;
}

.flow-chart-wrapper {
  display: flex;
  min-width: 900px;
}

.flow-chart-node-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-right: 40px;
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.flow-chart-node-group:nth-child(1) {
  animation-delay: 0.1s;
}
.flow-chart-node-group:nth-child(2) {
  animation-delay: 0.2s;
}
.flow-chart-node-group:nth-child(3) {
  animation-delay: 0.3s;
}
.flow-chart-node-group:nth-child(4) {
  animation-delay: 0.4s;
}

.flow-chart-main-node {
  background-color: #096dd9;
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  text-align: center;
  min-width: 120px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(9, 109, 217, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(9, 109, 217, 0.3);
  }

  .anticon {
    margin-right: 8px;
  }

  span {
    font-weight: bold;
  }
}

.flow-chart-children {
  display: flex;
  flex-direction: column;
}

.flow-chart-sub-node-container {
  position: relative;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  animation: fadeInRight 0.5s ease forwards;
  opacity: 0;
  transform: translateX(-10px);
}

.flow-chart-sub-node-container:nth-child(1) {
  animation-delay: 0.15s;
}
.flow-chart-sub-node-container:nth-child(2) {
  animation-delay: 0.25s;
}
.flow-chart-sub-node-container:nth-child(3) {
  animation-delay: 0.35s;
}

.flow-chart-connector {
  width: 2px;
  height: 30px;
  background-color: #e8e8e8;
  margin: 0 10px;
  transition: background-color 0.3s ease;
}

.flow-chart-sub-node {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  width: 160px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateX(5px);

    & + .flow-chart-connector {
      background-color: #40a9ff;
    }
  }

  &.flow-node-completed {
    border: 2px solid #40a9ff;
    background-color: rgba(64, 169, 255, 0.05);
    position: relative;

    .node-header {
      background-color: #40a9ff;
    }

    .node-status {
      color: #40a9ff;
      font-weight: bold;
    }
  }

  &.flow-node-active {
    border-color: #096dd9;
    box-shadow: 0 4px 12px rgba(9, 109, 217, 0.15);
    transform: scale(1.05);
    background-color: rgba(9, 109, 217, 0.05);

    .node-header {
      background-color: #096dd9;
    }

    .node-status {
      color: #096dd9;
      font-weight: bold;
    }
  }

  &.flow-node-future {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      & + .flow-chart-connector {
        background-color: #e8e8e8;
      }
    }
  }

  .node-header {
    background-color: #f5f5f5;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    color: white;
    transition: background-color 0.3s ease;

    .anticon {
      margin-right: 8px;
    }
  }

  .node-status {
    padding: 8px 12px;
  }
}

.flow-chart-stage-connector {
  position: absolute;
  right: -30px;
  top: 60px; /* 主节点高度的中心位置 */
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.5s ease forwards 0.5s;
  opacity: 0;

  .anticon {
    font-size: 20px;
    color: #bfbfbf;
    transition: all 0.3s ease;
  }

  &:hover .anticon {
    color: #40a9ff;
    transform: scale(1.2);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.flow-chart-empty {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #bfbfbf;

  &-text {
    display: flex;
    align-items: center;
    font-size: 16px;
  }
}

.flow-main-node-icon {
  &.is-expanded {
    transform: translateY(-50%) rotate(180deg);
  }
}

.flow-main-node:hover .flow-main-node-icon {
  color: #40a9ff;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s ease, transform 0.4s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
.flow-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title-container {
    display: flex;
  }
}
::v-deep .a_button {
  border: 1px solid #f59a23;
  color: #f59a23;
  background-color: #fef1e0;
}
</style>

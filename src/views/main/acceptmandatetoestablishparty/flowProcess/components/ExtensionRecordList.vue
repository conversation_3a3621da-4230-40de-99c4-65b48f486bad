<template>
  <div>
    <a-modal title="延期记录" :visible="visible" @cancel="handleCancel" :width="1100" :maskClosable="false" footer>
      <a-table
        :columns="columns"
        :dataSource="data"
        :rowKey="record => record.id"
        :pagination="{ pageSize: 10 }"
        :scroll="{ y: 240 }"
      >
        <template slot="fileName" slot-scope="text, record">
          <div v-for="(item, index) in record.fileInfoList" :key="index">
            <a :href="item.filePath" target="_blank">{{ item.fileOriginName }}</a>
          </div>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import { sysFileInfoPage } from '@/api/modular/system/fileManage'
import moment from 'moment'
export default {
  data() {
    return {
      visible: false,
      confirmLoading: false,
      columns: [
        {
          title: '序号',
          dataIndex: 'id',
          key: 'id',
          width: 80,
          align: 'left',
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '操作人',
          dataIndex: 'operator',
          key: 'operator',
          align: 'left',
          width: 80
        },
        {
          title: '操作时间',
          dataIndex: 'opTime',
          width: 130,
          customRender: (text, record, index) => {
            return moment(text).format('YYYY-MM-DD')
          }
        },
        {
          title: '原截止时间',
          dataIndex: 'inveTimeLimitBefore',
          key: 'inveTimeLimitBefore',
          align: 'left',
          width: 130,
          customRender: (text, record, index) => {
            return moment(text).format('YYYY-MM-DD')
          }
        },
        {
          title: '延长时间至',
          dataIndex: 'inveTimeLimitAfter',
          width: 130,
          customRender: (text, record, index) => {
            return moment(text).format('YYYY-MM-DD')
          }
        },
        {
          title: '延长期限原因',
          dataIndex: 'reason',
          key: 'reason',
          align: 'left'
        },
        {
          title: '相关附件',
          dataIndex: 'files',
          scopedSlots: { customRender: 'fileName' }
        }
      ],
      data: []
    }
  },
  methods: {
    handleCancel() {
      this.visible = false
    },
    open(record) {
      this.visible = true
      this.confirmLoading = false
      this.getList(record.id)
    },
    getList(id) {
      this.$http.get(`/investigation/delayList?id=${id}`).then(res => {
        this.data = res.data
      })
    }
  }
}
</script>

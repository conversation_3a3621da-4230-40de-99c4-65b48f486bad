<template>
  <base-form-template
    title="接收调查评估委托"
    title-icon="solution"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack"
  >
    <!-- 表单头部信息 -->
    <!-- <div class="flow-form-info" v-if="formData">
      <span>委托编号：{{ formData.requestNo }}</span>
      <span>接收时间：{{ formData.receiveTime }}</span>
      <span>处理人员：{{ formData.handler }}</span>
      <span>处理时间：{{ formData.handleTime }}</span>
    </div> -->
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>

      <!-- 案件信息 -->
      <div class="form-section" v-if="!isNewRecord && detailData?.id?.length >= 32">
        <div class="section-title">
          <div class="section-line"></div>
          <span>案件信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['uniformCode', { rules: [{ required: true, message: '请输入统一赋号' }] }]"
                placeholder="请输入统一赋号"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['caseCode', { rules: [{ required: true, message: '请输入案件标识' }] }]"
                placeholder="请输入案件标识"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['caseName', { rules: [{ required: true, message: '请输入案件名称' }] }]"
                placeholder="请输入案件名称"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 委托调查信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>委托调查信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="委托编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['entrustmentCode', { rules: [{ required: true, message: '请输入委托编号' }] }]"
                placeholder="请输入委托编号"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="委托单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['entrustmentDeptName', { rules: [{ required: true, message: '请输入委托单位' }] }]"
                placeholder="请输入委托单位"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="委托单位地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-input
                v-decorator="['entrustmentDeptAddress']"
                placeholder="请输入委托单位地址"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="委托时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['entrustmentTime', { rules: [{ required: true, message: '请选择委托时间' }] }]"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="mode === 'view'"
                :disabledDate="disabledFutureDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="委托方联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['entrustmentContactPsn', { rules: [{ required: false, message: '请输入联系人' }] }]"
                placeholder="请输入联系人"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="委托方联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['entrustmentContactTel', { rules: [{ required: false, message: '请输入联系电话' }] }]"
                placeholder="请输入联系电话"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="接收单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-tree-select
                v-decorator="['receiveDeptId', { rules: [{ required: true, message: '请选择接收单位' }] }]"
                apiURL="/sysOrg/tree"
                :params="{ level: 4 }"
                placeholder="选择本区县管辖的下级司法所"
                :disabled="mode === 'view' || isNewRecord || detailData?.id?.length <= 32"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 文书信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>文书信息</span>
        </div>
        <document-info-component
          v-model="docList"
          :disabled="mode === 'view'"
          :labelCol="labelCol2"
          :wrapperCol="wrapperCol2"
          :recordId="detailData?.id"
          bizType="inve_receive"
          :xm="detailData?.correctionObjName"
        />
      </div>

      <!-- 调查对象信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查对象信息</span>
        </div>
        <investigation-subject-info
          ref="subjectInfo"
          :mode="mode"
          :isNewRecord="isNewRecord"
          :initialValues="detailData"
          @subjectInfoChange="handleSubjectInfoChange"
        />
      </div>

      <!-- 区县司法局反馈信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>区县司法局审核信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12" v-if="!isNewRecord && detailData?.id?.length >= 32">
            <a-form-item label="审核结果" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <sh-tree-select
                v-decorator="[
                  'approvalResult',
                  {
                    rules: [{ required: true, message: '请选择审核结果' }]
                  }
                ]"
                @change="handleApprovalResultChange"
                apiURL="/sysDictData/tree"
                :disabled="mode === 'view'"
                :params="{ dictTypeId: '1732298638446571522' }"
                placeholder="请选择审核结果"
                showSearch
              />
            </a-form-item>
          </a-col>

          <!-- 当审核结果为1时显示的内容 -->
          <template v-if="showInvestigationFields">
            <a-col :span="12">
              <a-form-item label="调查机构" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <sh-tree-select
                  v-decorator="[
                    'inveDept',
                    { rules: [{ required: showInvestigationFields, message: '请选择调查机构' }] }
                  ]"
                  @change="handleInveDeptChange"
                  apiURL="/sysOrg/tree"
                  :params="{ level: 4 }"
                  placeholder="选择本区县管辖的下级司法所"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>

            <a-col :span="24" v-show="false">
              <a-form-item label="调查小组名称" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-input
                  v-decorator="[
                    'inveGroupName',
                    { rules: [{ required: showInvestigationFields, message: '请输入调查小组名称' }] }
                  ]"
                  placeholder="关于成立（类型）（姓名）实施社区矫正调查评估小组"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="调查人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <sh-person-selector
                  :disabled="mode === 'view'"
                  :treeUrl="'/sysOrg/tree?remark=all'"
                  treeLabelKey="title"
                  treeValueKey="id"
                  :list-url="'/sysUser/page'"
                  :name-field="'nickName'"
                  searchNameField="searchValue"
                  searchOrgField="sysEmpParam.orgId"
                  :org-field="'orgNames'"
                  :selectedOrgId="selectedOrgId"
                  v-decorator="[
                    'invePsnList',
                    {
                      rules: [{ type: 'array', required: showInvestigationFields, message: '请选择!' }]
                    }
                  ]"
                />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="收到委托时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-date-picker
                  v-decorator="[
                    'entrustmentReceiveTime',
                    { rules: [{ required: showInvestigationFields, message: '请选择收到委托时间' }] }
                  ]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  :disabled="mode === 'view'"
                  placeholder="自动关联，可修改"
                  :disabledDate="disabledFutureDate"
                  @change="calculateDeadline"
                />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="调查时限" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-input
                  v-decorator="[
                    'investigationDuration',
                    { rules: [{ required: showInvestigationFields, message: '请输入调查时限' }] }
                  ]"
                  placeholder="根据是否速裁自动关联"
                  :disabled="true"
                />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="调查截止时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-date-picker
                  v-decorator="[
                    'inveTimeLimit',
                    { rules: [{ required: showInvestigationFields, message: '请选择调查截止时间' }] }
                  ]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  :disabled="true"
                  placeholder="自动关联，可修改"
                />
              </a-form-item>
            </a-col>
          </template>

          <!-- 当审核结果不为1时显示的内容 -->
          <template v-if="!showInvestigationFields">
            <a-col :span="24">
              <a-form-item label="退回意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="[
                    'approvalRemark',
                    { rules: [{ required: !showInvestigationFields, message: '请输入退回意见' }] }
                  ]"
                  placeholder="请输入退回意见"
                  :disabled="mode === 'view'"
                  :rows="4"
                  @change="handleApprovalRemarkChange"
                />
                <div style="width: 100%; text-align: right;">{{ approvalRemarkCount }} / 1000</div>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="退回材料" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <sh-file-uploader
                  v-decorator="[
                    'returnFiles',
                    { rules: [{ type: 'array', required: false, message: '请上传退回材料' }] }
                  ]"
                  :url="'/sysFileInfo/uploadOss'"
                  :maxFileSize="20 * 1024 * 1024"
                  :acceptedFormats="'.jpg,.png,.pdf'"
                  :readonly="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </template>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审核人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalPsn']" placeholder="默认当前登录人" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalTime']" placeholder="格式：YYYY-MM-DD HH:MM:SS" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <!-- <a-button style="margin-left: 8px" @click="handleReset" :disabled="submitLoading">
          <a-icon type="reload" />重置
        </a-button> -->
        <!-- <a-button style="margin-left: 8px" @click="handleBack" :disabled="submitLoading">
          <a-icon type="left" />取消
        </a-button> -->
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />保存并下一步
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import InvestigationSubjectInfo from './components/InvestigationSubjectInfo.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import DocumentInfoComponent from './components/DocumentInfoComponent.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  components: {
    BaseFormTemplate,
    InvestigationSubjectInfo,
    DocumentInfoComponent
  },
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    isNewRecord() {
      return this.record?.addType === 'new'
    }
  },
  provide() {
    return {
      isNewRecord: this.isNewRecord,
      record: this.record
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      formData: {
        requestNo: 'CT' + new Date().getTime(),
        receiveTime: moment().format('YYYY-MM-DD'),
        handler: '张科长',
        handleTime: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      docList: [],
      showInvestigationFields: true, // 默认显示调查机构相关字段
      submitLoading: false,
      loading: false,
      currentExpediteStatus: '0', // 默认非速裁
      tempLoading: false,
      selectedOrgId: '',
      personSelectorParams: {
        id: ''
      }, // 调查人员选择器的额外参数
      approvalRemarkCount: 0
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData()
  },
  methods: {
    disabledFutureDate,
    handleInveDeptChange(value) {
      // 当调查机构变化时更新调查人员选择器的额外参数
      this.personSelectorParams = {
        // status: 'active',
        id: value
        // permission: 'admin'
      }
      this.selectedOrgId = value
    },

    handleApprovalResultChange(value) {
      // 当审核结果为"1"时显示调查机构相关字段，否则隐藏
      this.showInvestigationFields = value === '1'
    },

    initFormData() {
      // 使用父组件传入的detailData作为表单数据源
      const formValues = this.detailData || {}

      if ((this.currentNode?.rawData?.type === 9 && this.currentNode?.rawData?.draft === 1) || this.mode === 'view') {
        this.showInvestigationFields = formValues.approvalResult === '1'
      } else {
        formValues.approvalResult = '1'
      }
      // if (this.isNewRecord && !this.detailData.receiveDeptId) {
      //   this.detailData.receiveDeptId = this.userInfo.loginEmpInfo.orgId
      // }

      // 初始化人员选择器的额外参数
      if (formValues.inveDept) {
        this.personSelectorParams = {
          // status: 'active',
          id: formValues.inveDept
          // permission: 'admin'
        }
      }

      // 设置表单的初始值
      this.$nextTick(() => {
        this.selectedOrgId = formValues.inveDept
        const currentTime = moment().format('YYYY-MM-DD')
        // 在非查看模式下，设置当前用户为反馈人，当前时间为反馈时间
        const auditor =
          this.mode !== 'view' ? this.userInfo?.name || '当前用户' : formValues.approvalPsn || '默认当前登录人'
        const auditTime =
          this.mode !== 'view'
            ? currentTime
            : formValues.approvalTime
            ? moment(formValues.approvalTime).format('YYYY-MM-DD')
            : currentTime
        // 设置登录人机构id和机构名称
        const approvalDeptId = this.mode !== 'view' ? this.userInfo?.orgId || '' : formValues.approvalDeptId || ''
        const approvalDeptName = this.mode !== 'view' ? this.userInfo?.orgName || '' : formValues.approvalDeptName || ''

        // 初始化文档列表
        this.docList = formValues.docList || []

        // 初始化显示调查机构字段状态
        if (formValues.approvalResult) {
          this.showInvestigationFields = formValues.approvalResult === '1'
        }

        // 设置当前速裁状态，默认为'0'（非速裁）
        this.currentExpediteStatus = formValues.expeditedProcedure || '0'
        console.log('初始化速裁状态:', this.currentExpediteStatus)

        // 设置调查时限
        let investigationDuration = formValues.investigationDuration
        if (!investigationDuration) {
          investigationDuration = this.currentExpediteStatus === '1' ? '5个工作日' : '10个工作日'
          console.log('初始化调查时限:', investigationDuration)
        }

        // 初始收到委托时间，默认当天
        const entrustmentReceiveTime = formValues.entrustmentReceiveTime
          ? moment(formValues.entrustmentReceiveTime).format('YYYY-MM-DD')
          : moment().format('YYYY-MM-DD')
        console.log('初始化收到委托时间:', entrustmentReceiveTime)
        this.form.setFieldsValue({
          // 案件信息
          id: formValues.id || null,
          uniformCode: formValues.uniformCode || null, // 统一赋号
          caseCode: formValues.caseCode || null, // 案件标识
          caseName: formValues.caseName || null, // 案件名称

          // 委托调查信息
          entrustmentCode: formValues.entrustmentCode || null, // 委托编号
          entrustmentDeptName: formValues.entrustmentDeptName || null, // 委托单位
          entrustmentDeptAddress: formValues.entrustmentDeptAddress || null, // 委托单位地址
          entrustmentTime: formValues.entrustmentTime && moment(formValues.entrustmentTime).format('YYYY-MM-DD'), // 委托时间
          entrustmentContactPsn: formValues.entrustmentContactPsn || null, // 委托方联系人
          entrustmentContactTel: formValues.entrustmentContactTel || null, // 委托方联系电话
          receiveDeptId: formValues.receiveDeptId || null, // 接收单位

          // 调查对象信息字段初始化
          adultStatus: formValues.adultStatus || null, // 是否成年
          correctionObjName: formValues.correctionObjName || null, // 调查对象姓名
          gender: formValues.gender || null, // 性别
          whcd: formValues.whcd || null, // 文化程度
          birthday: formValues.birthday, // 出生日期
          contactTel: formValues.contactTel || null, // 联系电话
          certType: formValues.certType || null, // 证件类型
          certNum: formValues.certNum || null, // 证件号码
          usedName: formValues.usedName || null, // 曾用名
          nickName: formValues.nickName || null, // 别名
          nationality: formValues.nationality || null, // 国籍/民族
          guardianName: formValues.guardianName || null, // 监护人姓名
          guardianContactTel: formValues.guardianContactTel || null, // 监护人联系电话
          residenceCode: formValues.residenceCode || null, // 住所地
          residence: formValues.residence || null, // 住所地详细地址
          registeredAddressCode: formValues.registeredAddressCode || null, // 户籍所在地
          registeredAddress: formValues.registeredAddress || null, // 户籍所在地明细
          companyName: formValues.companyName || null, // 工作单位
          criminalDetail: formValues.criminalDetail || null, // 主要犯罪事实
          psnType: formValues.psnType || null, // 拟适宜社区矫正人员类型
          correctionType: formValues.correctionType || null, // 拟适宜社区矫正类别
          expeditedProcedure: formValues.expeditedProcedure || '', // 是否速裁，默认为否
          hasInitialSentence: formValues.hasInitialSentence || null, // 是否有原判刑期
          criminalCharge: formValues.criminalCharge || [], // 罪名信息

          // 区县司法局审核信息字段初始化
          approvalResult: formValues.approvalResult || null, // 审核结果
          inveDept: formValues.inveDept || null, // 调查机构
          inveDeptName: formValues.inveDeptName || null, // 调查机构名称
          inveGroupName:
            formValues.inveGroupName ||
            `关于成立${
              formValues.psnType ? this.$options.filters['dictType']('nsysqjzrylx', formValues.psnType) : ''
            }${formValues.correctionObjName || ''}实施社区矫正调查评估小组`, // 调查小组名称
          invePsnList: formValues?.groups?.invePsnList || [], // 调查人员
          entrustmentReceiveTime: entrustmentReceiveTime, // 收到委托时间，默认当天
          investigationDuration: investigationDuration, // 调查时限
          inveTimeLimit: formValues.inveTimeLimit, // 调查截止时间
          approvalRemark: formValues.approvalRemark || null, // 退回意见
          approvalPsn: auditor, // 审核人
          approvalTime: auditTime, // 审核时间
          currentExpediteStatus: this.currentExpediteStatus, // 当前速裁状态
          approvalDeptId: approvalDeptId,
          approvalDeptName: approvalDeptName,
          returnFiles: formValues.returnFiles || []
        })

        // 强制在初始化完成后计算截止时间
        if (this.mode !== 'view' && this.showInvestigationFields) {
          this.$nextTick(() => {
            console.log('初始化后计算截止时间')
            this.calculateDeadline()
          })
        }
      })
    },

    handleOk() {
      this.handleSubmit(true)
    },

    // 通用提交方法，参数showConfirm控制是否显示确认对话框
    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return // 如果正在提交且是直接下一步模式，阻止操作

      // 设置提交加载状态
      this.submitLoading = true

      // 首先获取子组件的完整表单数据
      if (this.$refs.subjectInfo && typeof this.$refs.subjectInfo.getFullFormData === 'function') {
        // 先获取调查对象信息组件的完整数据
        this.$refs.subjectInfo.getFullFormData().then(subjectFormData => {
          // 获取当前审核结果
          const currentApprovalResult = this.form.getFieldValue('approvalResult')

          // 如果审核结果明确不是1且不为空，跳过表单验证
          if (currentApprovalResult && currentApprovalResult !== '1') {
            // 直接获取表单值，不进行验证
            const mainFormValues = this.form.getFieldsValue()

            // 合并主表单和子表单的数据
            const combinedValues = { ...mainFormValues, ...subjectFormData }

            // 准备表单数据
            const preparedData = this.prepareFormData(combinedValues)

            // 提交数据
            this.submitData(preparedData, showConfirm)
          } else {
            // 审核结果为1时或为空时，需要验证表单
            this.form.validateFields((err, mainFormValues) => {
              if (err) {
                this.submitLoading = false
                this.$message.error('请完善必填信息')
                console.error('表单验证失败：', err)
                return
              }

              // 合并主表单和子表单的数据
              const combinedValues = { ...mainFormValues, ...subjectFormData }

              // 准备表单数据
              const preparedData = this.prepareFormData(combinedValues)

              // 提交数据
              this.submitData(preparedData, showConfirm)
            })
          }
        })
      }
    },

    // 提取的提交数据方法
    submitData(preparedData, showConfirm) {
      // 使用$http进行请求
      this.$http
        .post('/investigationInfo/accept', { ...preparedData, otherInfo: this.record })
        .then(response => {
          console.log('保存成功：', response.data)

          // 请求成功，关闭加载状态
          this.submitLoading = false

          // 判断接口返回是否成功
          if (response && response.success) {
            // 处理成功响应
            if (showConfirm) {
              // 显示确认对话框，询问用户是否继续下一步流程
              this.$message.success('提交成功')
              if (this.showInvestigationFields) {
                this.$confirm({
                  title: '操作提示',
                  content: '接收调查评估委托提交成功，是否立即进行下一步流程？',
                  okText: '继续下一步',
                  cancelText: '留在当前页面',
                  onOk: () => {
                    // 通知父组件进入下一步流程
                    this.$emit('nextStep', preparedData)
                  },
                  onCancel: () => {
                    // 留在当前页面
                    this.$emit('close')
                  }
                })
              } else {
                // 审核结果不是1时，直接关闭页面
                this.$emit('close')
              }
            } else {
              // 直接进入下一步
              if (this.showInvestigationFields) {
                this.$emit('nextStep', preparedData)
              } else {
                this.$emit('close')
              }
            }
          } else {
            // 接口返回失败
            const errorMsg = (response && response.message) || '提交失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('保存失败：', error)
          this.$message.error('提交失败，请稍后重试')
          this.submitLoading = false
        })
    },

    // 处理表单数据的通用方法
    prepareFormData(values, isDraft = false) {
      // 拷贝表单数据，避免直接修改原对象
      const preparedData = { ...values }
      if (preparedData.supplementaryPunishment) {
        // preparedData.supplementaryPunishment = preparedData.supplementaryPunishment.join(',')
      }

      // 处理日期格式
      if (preparedData.entrustmentReceiveTime) {
        preparedData.entrustmentReceiveTime = moment(preparedData.entrustmentReceiveTime).format('YYYY-MM-DD')
      }
      if (preparedData.inveTimeLimit) {
        preparedData.inveTimeLimit = moment(preparedData.inveTimeLimit).format('YYYY-MM-DD')
      }

      // 添加当前用户和时间
      if (this.mode !== 'view') {
        preparedData.approvalPsn = this.userInfo?.name || '当前用户'
        preparedData.approvalTime = moment().format('YYYY-MM-DD HH:mm:ss')
      }

      // 添加文档列表到提交数据
      preparedData.docList = this.docList

      // 添加record的id参数
      if (this.record && this.record.id) {
        preparedData.id = this.record.id
      }

      // 添加登录人机构id和机构名称
      preparedData.approvalDeptId = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgId
        : this.userInfo?.orgId || ''
      preparedData.approvalDeptName = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgName
        : this.userInfo?.orgName || ''

      // 处理invePsnList，只保留id、nickName和orgNames字段
      if (preparedData.invePsnList && preparedData.invePsnList.length > 0) {
        preparedData.invePsnList = preparedData.invePsnList.map(person => ({
          id: person.id,
          nickName: person.nickName,
          sjName: person.sjName,
          orgNames: person.orgNames,
          name: person.orgFullName
        }))
      }

      // 如果是暂存，添加标记
      if (isDraft) {
        preparedData.isDraft = 1
      }

      return preparedData
    },

    handleTemporarily() {
      // 暂存功能不需要验证表单，直接获取当前表单值
      this.tempLoading = true

      // 从子组件获取完整数据
      if (this.$refs.subjectInfo && typeof this.$refs.subjectInfo.getFullFormData === 'function') {
        this.$refs.subjectInfo.getFullFormData().then(subjectFormData => {
          // 获取主表单值
          const mainFormValues = this.form.getFieldsValue()

          // 合并主表单和子表单的数据
          const combinedValues = { ...mainFormValues, ...subjectFormData }

          // 准备表单数据，传入isDraft=true参数
          const preparedData = this.prepareFormData(combinedValues, true)

          // 使用$http发送请求，使用与提交相同的接口
          this.$http
            .post('/investigationInfo/accept', preparedData)
            .then(response => {
              console.log('暂存成功：', response.data)
              this.tempLoading = false

              // 判断接口返回是否成功
              if (response && response.success) {
                this.$message.success('暂存成功')
                this.$emit('close')
              } else {
                // 接口返回失败
                const errorMsg = (response && response.message) || '暂存失败，请稍后重试'
                this.$message.error(errorMsg)
              }
            })
            .catch(error => {
              console.error('暂存失败：', error)
              this.$message.error('暂存失败，请稍后重试')
              this.tempLoading = false
            })
        })
      }
    },

    handleNextStep() {
      this.handleSubmit(false)
    },
    handleReset() {
      if (this.submitLoading) return // 如果正在提交，阻止重置操作

      this.form.resetFields()
      this.docList = []
      this.showInvestigationFields = true
      this.initFormData()
    },
    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return // 如果正在提交且不是查看模式，阻止返回操作

      this.$emit('back')
    },
    handleSubjectInfoChange(subjectInfo) {
      // 将调查对象信息合并到表单数据中
      this.formData = {
        ...this.formData,
        ...subjectInfo
      }

      // 当调查对象姓名或类型变化时更新调查小组名称
      if (subjectInfo.correctionObjName || subjectInfo.psnType) {
        const currentValues = this.form.getFieldsValue()
        const psnTypeValue = subjectInfo.psnType || currentValues.psnType || ''
        const psnType = psnTypeValue ? this.$options.filters['dictType']('nsysqjzrylx', psnTypeValue) : ''
        const name = subjectInfo.correctionObjName || currentValues.correctionObjName || ''

        // 只有在审核结果为1时才需要更新调查小组名称
        if (this.showInvestigationFields) {
          this.form.setFieldsValue({
            inveGroupName: `关于成立${psnType}${name}实施社区矫正调查评估小组`
          })
        }
      }

      // 如果是否速裁发生变化，更新调查时限和截止日期
      if (subjectInfo.expeditedProcedure) {
        console.log('速裁状态变化:', subjectInfo.expeditedProcedure)

        // 更新当前速裁状态
        this.currentExpediteStatus = subjectInfo.expeditedProcedure

        const duration = subjectInfo.expeditedProcedure === '1' ? '5个工作日' : '10个工作日'
        this.form.setFieldsValue({
          investigationDuration: duration
        })

        // 重新计算截止时间
        this.$nextTick(() => {
          this.calculateDeadline()
        })
      }
    },
    handleApprovalRemarkChange(e) {
      const value = e.target.value
      this.approvalRemarkCount = value.length
      if (value && value.length > 1000) {
        this.$message.warning('退回意见字数已超过1000字，请注意控制字数长度')
        // 截取前1000个字符并更新输入框的值
        const trimmedValue = value.substring(0, 1000)
        this.$nextTick(() => {
          this.form.setFieldsValue({
            approvalRemark: trimmedValue
          })
          this.approvalRemarkCount = 1000
        })
      }
    },
    async calculateDeadline() {
      await this.$nextTick()
      const values = this.form.getFieldsValue()
      let receiveDate = values.entrustmentReceiveTime
      console.log('当前收到委托时间:', receiveDate ? moment(receiveDate).format('YYYY-MM-DD') : 'undefined')

      // 如果收到委托日期为空，则使用当前日期
      if (!receiveDate) {
        receiveDate = moment()
        console.log('使用当前日期作为收到委托时间:', moment(receiveDate).format('YYYY-MM-DD'))
        this.form.setFieldsValue({
          entrustmentReceiveTime: moment(receiveDate).format('YYYY-MM-DD')
        })
      }

      // 使用当前存储的速裁状态
      console.log('计算截止日期使用的速裁状态:', this.currentExpediteStatus)
      const totalNum = this.currentExpediteStatus === '1' ? 5 : 10
      console.log('工作日数量:', totalNum)

      try {
        // 调用后端接口计算截止日期
        this.$http
          .get('/investigationInfo/initJzsj', {
            params: {
              entrustmentReceiveTime: moment(receiveDate).format('YYYY-MM-DD'),
              totalNum: totalNum
            }
          })
          .then(response => {
            if (response && response.success && response.data) {
              // 将接口返回的截止时间设置到表单
              const deadline = moment(response.data.date)
              console.log('接口返回的截止日期:', deadline.format('YYYY-MM-DD'))

              // 设置调查截止时间
              this.form.setFieldsValue({
                inveTimeLimit: deadline
              })
            } else {
              console.error('获取截止日期失败:', response?.message || '接口调用失败')
              this.$message.error('获取调查截止时间失败')
            }
          })
          .catch(error => {
            console.error('调用截止日期接口出错:', error)
            this.$message.error('获取调查截止时间失败')
          })
      } catch (error) {
        console.error('计算截止日期出错:', error)
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 可以添加特定于本表单的样式（如果有必要）
</style>

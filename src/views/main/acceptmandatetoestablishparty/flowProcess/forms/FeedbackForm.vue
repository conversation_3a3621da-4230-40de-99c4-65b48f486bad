<template>
  <base-form-template
    title="反馈信息"
    title-icon="solution"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack">
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
      <a-form-item v-show="false"><a-input v-decorator="['entrustmentType']" /></a-form-item>

      <!-- 案件信息 -->
      <div class="form-section" v-if="detailData.id.length >=32">
        <div class="section-title">
          <div class="section-line"></div>
          <span>案件信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="统一赋号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['uniformCode']" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="案件标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['caseCode']" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="案件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['caseName']" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 反馈信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>反馈信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-tree-select
                v-decorator="['inveDept', { rules: [{ required: true, message: '请选择调查机构' }] }]"
                apiURL="/sysOrg/tree"
                :params="{ level: 4 }"
                placeholder="选择本区县管辖的下级司法所"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查单位联系人" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input
                placeholder="请输入调查单位联系人"
                v-decorator="['inveDeptContactPsn', { rules: [{ required: true, message: '请输入调查单位联系人' }] }]"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查单位联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入调查单位联系电话" v-decorator="['inveDeptTel']" :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查评估文书号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input placeholder="请输入调查评估文书号" v-decorator="['docNum', { rules: [{ required: true, message: '请输入调查评估文书号' }] }]" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <sh-select
                v-decorator="['conclusion', { initialValue: null, rules: [{ required: true, message: '请选择调查评估结论' }] }]"
                dictType="dcpgyj"
                style="width: 100%;"
                allowClear
                placeholder="请选择调查评估结论"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查评估开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker
                v-decorator="['entrustmentReceiveTime', { rules: [{ required: true, message: '请选择调查评估开始日期' }] }]"
                style="width: 100%"
                :disabled="mode === 'view'"
                format="YYYY-MM-DD"
                :disabledDate="disabledFutureDate" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查评估结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker
                v-decorator="['endTime', { rules: [{ required: true, message: '请选择调查评估结束日期' }] }]"
                style="width: 100%"
                :disabled="mode === 'view'"
                format="YYYY-MM-DD"
                :disabledDate="disabledFutureDate" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查评估意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
              <a-textarea
                v-decorator="['particular', { rules: [{ required: true, message: '请输入调查评估意见' }] }]"
                :rows="4"
                placeholder="请输入调查评估意见"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="住所地" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>

              <sh-cascader-distpicker
                :disabled="mode === 'view'"
                v-decorator="['residenceCode', { rules: [{ required: true, message: '请选择住所地' }] }]" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="住所地详细地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
              <a-input
                v-decorator="['residence', { rules: [{ required: true, message: '请输入住所地详细地址' }] }]"
                placeholder="请输入详细地址"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="家庭监护人或保证人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input
                v-decorator="['guardianName', { rules: [{ required: true, message: '请输入家庭监护人或保证人姓名' }] }]"
                placeholder="请输入保证人姓名"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="监护人与罪犯关系" :labelCol="labelCol" :wrapperCol="wrapperCol" >
              <a-input
                v-decorator="['guardianRelationship', { rules: [{ required: true, message: '请输入监护人与罪犯关系' }] }]"
                placeholder="请输入"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="反馈委托单位" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input placeholder="请输入反馈委托单位" v-decorator="['feedbackTo', { rules: [{ required: true, message: '请输入反馈委托单位' }] }]" :disabled="true" />
              <!-- <sh-tree-select
                v-decorator="['feedbackTo', { rules: [{ required: true, message: '请选择调查机构' }] }]"
                apiURL="/sysOrg/tree"
                :params="{ level: 4 }"
                placeholder="选择本区县管辖的下级司法所"
                :disabled="mode === 'view'" /> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="抄送单位" :labelCol="labelCol" :wrapperCol="wrapperCol" required>

              <sh-tree-select
                v-decorator="['ccTo', { rules: [{ required: true, message: '请选择抄送单位' }] }]"
                apiURL="/extOrgInfo/list"
                :params="{ type: 30 }"
                :labelKey="'orgName'"
                valueKey="orgCode"
                placeholder="选择抄送单位"
                :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="反馈说明" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-textarea v-decorator="['remark']" :rows="3" placeholder="请输入反馈说明" :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 文书信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>文书信息</span>
        </div>
        <document-info-component
          v-model="docList"
          :disabled="mode === 'view'"
          :labelCol="labelCol2"
          :wrapperCol="wrapperCol2"
          :recordId="detailData?.id"
          :showDownload="false"
          bizType="feedback"
          :xm="detailData?.correctionObjName"
        >
          <template #headerActions>
            <a-button type="primary" @click="handleChooseDocument">
              <a-icon type="file" />选择文书
            </a-button>
          </template>
        </document-info-component>
      </div>

      <!-- 调查过程信息 -->
      <div class="form-section" v-if="detailData.id.length >= 32">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查过程信息</span>
        </div>
        <process-info-component
          v-model="processInfoList"
          :noticeTitle="detailData?.groups?.noticeTitle"
          :disabled="mode === 'view'"
          :detailData="detailData"
        />
      </div>

      <!-- 区县司法局反馈信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>区县司法局反馈信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="反馈人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['feedbackPsn', { initialValue: '默认当前登录人' }]" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="反馈时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['feedbackTime', { initialValue: '2025-03-03 11:35:00' }]" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-alert v-if="record.id.length < 32" style="" message="说明：手动新增的线上委托，无法反馈给对应委托单位和检察单位，只做信息登记，请线下邮寄。后续具备反馈条件后再进行统一发送。" type="info" show-icon />

      </div>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />提交
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack">
          <a-icon type="left" />返回
        </a-button>
      </div>
    </template>

    <!-- 选择文书模态框 -->
    <document-select-modal
      :visible.sync="docModalVisible"
      :business-id="detailData?.id || record?.id"
      :choosed-file-ids="docList.map(doc => doc.id)"
      @add-documents="handleAddDocuments"
    />
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import DocumentInfoComponent from './components/DocumentInfoComponent.vue'
import DocumentSelectModal from './components/DocumentSelectModal.vue'
import ProcessInfoComponent from './components/ProcessInfoComponent.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  components: {
    BaseFormTemplate,
    DocumentInfoComponent,
    DocumentSelectModal,
    ProcessInfoComponent
  },
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      docList: [],
      docModalVisible: false,
      processInfoList: []
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData();
  },
  methods: {
    disabledFutureDate,
    initFormData() {
      // 使用父组件传入的detailData.feedback作为表单数据源
      const formValues = this.detailData || {};
      const feedback = formValues.feedback || {};
      const groups = formValues.groups || {};

      // 设置表单的初始值
      this.$nextTick(() => {
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
        // 在非查看模式下，设置当前用户为反馈人，当前时间为反馈时间
        const feedbackPsn = this.mode !== 'view' ? (this.userInfo?.name || '当前用户') : (feedback.feedbackPsn || '默认当前登录人');
        const feedbackTime = this.mode !== 'view' ? currentTime : (feedback.feedbackTime || '2025-03-03 11:35:00');

        // 直接使用后端字段名，不做映射
        this.form.setFieldsValue({
          id: formValues.id || null,
          entrustmentType: feedback.entrustmentType || null,

          // 案件信息
          uniformCode: formValues.uniformCode || null, // 统一赋号
          caseCode: formValues.caseCode || null, // 案件标识
          caseName: formValues.caseName || null, // 案件名称

          // 反馈信息
          inveDept: groups.inveDept || feedback.inveDept,
          inveDeptContactPsn: feedback?.inveDeptContactPsn || '',
          inveDeptTel: feedback?.inveDeptTel || null,
          docNum: feedback?.docNum || '',
          conclusion: feedback?.conclusion || null,
          entrustmentReceiveTime: feedback?.entrustmentReceiveTime,
          endTime: feedback?.endTime,
          particular: feedback?.particular || null,
          residenceCode: feedback?.residenceCode || null,
          residence: feedback?.residence || null,
          guardianName: feedback?.guardianName || null,
          guardianRelationship: feedback?.guardianRelationship || null,
          feedbackTo: feedback?.feedbackTo || null,
          ccTo: feedback?.ccTo || null,
          remark: feedback?.remark || null,

          // 反馈人信息
          feedbackPsn: feedbackPsn,
          feedbackTime: feedbackTime
        });

        // 初始化文档列表
        this.docList = feedback.existsFiles ? feedback.existsFiles.map(file => ({
          id: file.id,
          ws: file.fileOriginName,
          wsdm: file.filePath,
          ossUrl: file.filePath,
          url: file.filePath
        })) : [];

        // 初始化调查过程信息
        if (feedback && feedback.processInfo) {
          try {
            const processData = JSON.parse(feedback.processInfo);
            if (Array.isArray(processData)) {
              this.processInfoList = processData;
            }
          } catch (e) {
            console.error('解析调查过程信息失败:', e);
            this.processInfoList = [];
          }
        } else {
          this.processInfoList = [];
        }
      });
    },

    handleOk() {
      this.handleSubmit(true);
    },

    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return;

      this.submitLoading = true;

      this.form.validateFields((err, values) => {
        if (!err) {
          const preparedData = this.prepareFormData(values);

          this.$http.post('/investigationInfo/feedback', preparedData)
            .then(response => {
              console.log('保存成功：', response.data);
              this.submitLoading = false;

              if (response && response.success) {
                this.$message.success('提交成功');
                this.$emit('close');
              } else {
                const errorMsg = (response && response.message) || '提交失败，请稍后重试';
                this.$message.error(errorMsg);
              }
            })
            .catch(error => {
              console.error('保存失败：', error);
              this.$message.error('提交失败，请稍后重试');
              this.submitLoading = false;
            });
        } else {
          this.submitLoading = false;
          console.error('表单验证失败：', err);
          this.$message.error('请完善必填信息');
        }
      });
    },

    prepareFormData(values, isDraft = false) {
      const preparedData = { ...values };

      // 处理日期时间格式
      if (preparedData.entrustmentReceiveTime) {
        preparedData.entrustmentReceiveTime = moment(preparedData.entrustmentReceiveTime).format('YYYY-MM-DD');
      }

      if (preparedData.endTime) {
        preparedData.endTime = moment(preparedData.endTime).format('YYYY-MM-DD');
      }

      // 添加文档列表
      preparedData.feedbackFiles = this.docList.map(doc => doc.id).join(',');

      // 添加额外字段
      preparedData.feedbackPsnId = this.userInfo?.id || null;
      // 添加登录人机构id和机构名称
      preparedData.approvalDeptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : (this.userInfo?.orgId || '');
      preparedData.approvalDeptName = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgName : (this.userInfo?.orgName || '');
      preparedData.deleted = 0; // 默认未删除

      // 添加调查过程信息处理
      // 处理调查过程信息
      preparedData.processInfo = this.processInfoList.length > 0 ? JSON.stringify(this.processInfoList) : null;

      const submitData = {
        // otherInfo: this.detailData,
        id: this.record && this.record.id ? this.record.id : null,
        ...preparedData
      };

      if (isDraft) {
        submitData.isDraft = 1;
      }

      return submitData;
    },

    handleTemporarily() {
      this.tempLoading = true;

      const values = this.form.getFieldsValue();
      const preparedData = this.prepareFormData(values, true);

      this.$http.post('/investigationInfo/feedback', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data);
          this.tempLoading = false;

          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试';
            this.$message.error(errorMsg);
          }
        })
        .catch(error => {
          console.error('暂存失败：', error);
          this.$message.error('暂存失败，请稍后重试');
          this.tempLoading = false;
        });
    },

    handleNextStep() {
      this.handleSubmit(false);
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return;
      this.$emit('back');
    },

    handleChooseDocument() {
      this.docModalVisible = true;
    },

    handleAddDocuments(newDocs) {
      // 添加到docList
      this.docList = [...this.docList, ...newDocs];
    },

    handleDocModalCancel() {
      this.docModalVisible = false;
    }
  }
}
</script>

<style lang="less" scoped>
.file-table-container {
  margin-bottom: 20px;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-help-text {
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
}
</style>

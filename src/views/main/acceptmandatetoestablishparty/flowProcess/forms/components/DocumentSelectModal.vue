<template>
  <a-modal
    title="选择文书"
    :visible="visible"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div style="margin-bottom: 16px;">
      <a-row type="flex" justify="space-between" align="middle">
        <a-col>
          <span>选择要添加的文书材料</span>
        </a-col>
        <a-col>
          <a-button type="primary" @click="handleAddSelectedDocs">确认添加</a-button>
        </a-col>
      </a-row>
    </div>
    <a-table
      :columns="docColumns"
      :dataSource="documentList"
      :pagination="{ pageSize: 5 }"
      :rowKey="record => record.id"
      :rowSelection="{ selectedRowKeys: selectedDocKeys, onChange: onSelectChange }"
    >
      <template slot="fileOriginName" slot-scope="text, record">
        <span>{{ record.fileOriginName }}</span>
      </template>
      <template slot="createTime" slot-scope="text, record">
        <span>{{ record.createTime }}</span>
      </template>
    </a-table>
  </a-modal>
</template>

<script>
export default {
  name: 'DocumentSelectModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    businessId: {
      type: String,
      default: ''
    },
    choosedFileIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      documentList: [],
      selectedDocKeys: [],
      docColumns: [
        { title: '文书名称', dataIndex: 'fileOriginName', key: 'fileOriginName', scopedSlots: { customRender: 'fileOriginName' } },
        { title: '上传时间', dataIndex: 'createTime', key: 'createTime', scopedSlots: { customRender: 'createdTime' } }
      ]
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.fetchDocumentList();
      }
    }
  },
  methods: {
    fetchDocumentList() {
      if (!this.businessId) {
        this.$message.warning('未找到业务ID，无法获取文书列表');
        return;
      }

      // 重置数据
      this.documentList = [];
      this.selectedDocKeys = [];

      // 调用接口获取文书列表
      this.$http.get('/investigationInfo/chooseDocument', {
        params: {
          id: this.businessId,
          choosedFileIds: this.choosedFileIds.join(',')
        }
      }).then(response => {
        if (response && response.success && response.data) {
          this.documentList = response.data;
        } else {
          this.$message.error(response?.message || '获取文书列表失败');
        }
      }).catch(error => {
        console.error('获取文书列表失败:', error);
        this.$message.error('获取文书列表失败，请稍后重试');
      });
    },

    onSelectChange(selectedRowKeys) {
      this.selectedDocKeys = selectedRowKeys;
    },

    handleAddSelectedDocs() {
      if (this.selectedDocKeys.length === 0) {
        this.$message.warning('请选择要添加的文书');
        return;
      }

      // 获取选中的文档
      const selectedDocs = this.documentList.filter(doc => this.selectedDocKeys.includes(doc.id));

      // 转换为所需格式并通过事件发送出去
      const newDocs = selectedDocs.map(doc => ({
        id: doc.id,
        ws: doc.fileOriginName,
        wsdm: doc.filePath,
        ossUrl: doc.filePath,
        url: doc.filePath
      }));

      // 发送添加事件
      this.$emit('add-documents', newDocs);

      // 关闭模态框
      this.$emit('update:visible', false);
      this.$message.success(`成功添加${newDocs.length}个文书`);
    },

    handleCancel() {
      this.$emit('update:visible', false);
    }
  }
}
</script>

<style scoped>
/* 可以根据需要添加样式 */
</style>

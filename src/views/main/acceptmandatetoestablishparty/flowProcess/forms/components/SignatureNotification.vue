<template>
  <a-modal
    title="发送签名通知"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
    width="800px"
  >
    <a-form :form="form">
      <a-form-item label="通知内容：" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <div class="modern-notification-box">
          <div class="notification-icon">
            <a-icon type="notification" />
          </div>
          <div class="notification-content">
            <p>
              您有一份待签名的文书，请前往矫务通或浙里社区矫正专区进行文件签名。
              <!-- 您有一份待签名的文书，请点击链接
              <a href="http://www.zjssqjzdcog.com" target="_blank" class="modern-link">
                <span>www.zjssqjzdcog.com</span>
                <a-icon type="link" class="link-icon" />
              </a>
              去办理。可在矫务通或浙里社区矫正专区查看文件详情。 -->
            </p>
          </div>
        </div>
      </a-form-item>

      <a-form-item label="选择人员：" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <div class="select-users-container" @click="openUserSelector">
          <a-input :value="selectedUsersText" placeholder="请点击选择人员" readonly />
          <a-button class="select-btn" type="primary" icon="user" size="small">选择</a-button>
        </div>
      </a-form-item>

      <a-form-item label="签名状态：" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-table :columns="columns" :dataSource="signatureStatusList" :pagination="false" bordered size="small">
          <template slot="signStatus" slot-scope="text">
            <span :style="{ color: getStatusColor(text) }">{{ text }}</span>
          </template>
        </a-table>
      </a-form-item>
    </a-form>

    <div slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk" :loading="confirmLoading">确定</a-button>
    </div>

    <!-- 用户选择器组件 -->
    <user-selector ref="userSelector" v-model="selectedUsers" @change="handleUserSelectionChange" />
  </a-modal>
</template>

<script>
import UserSelector from './UserSelector.vue'

export default {
  name: 'SignatureNotification',
  components: {
    UserSelector
  },
  props: {
    detailData: {
      type: Object,
      default: () => ({})
    },
    // 文档ID
    docId: {
      type: [String, Object, Array],
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          key: 'name',
          width: '25%',
          align: 'center'
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          key: 'phone',
          width: '25%',
          align: 'center'
        },
        {
          title: '类型',
          dataIndex: 'userType',
          key: 'userType',
          width: '25%',
          align: 'center'
        },
        {
          title: '签名状态',
          dataIndex: 'signStatus',
          key: 'signStatus',
          width: '25%',
          align: 'center',
          scopedSlots: { customRender: 'signStatus' }
        }
      ],
      signatureStatusList: [],
      userOptions: [],
      // 修改为对象结构，分别存储两种类型的用户
      selectedUsers: {
        zhezhengdingUsers: [], // 浙政钉用户
        zhelibaoUsers: [] // 浙里办用户
      }
    }
  },
  computed: {
    // 更新已选择用户文本显示
    selectedUsersText() {
      const zhezhengdingText = (this.selectedUsers.zhezhengdingUsers || [])
        .map(user => `${user.name || user.receiveUserName}(浙政钉)`)
        .join('、')

      const zhelibaoText = (this.selectedUsers.zhelibaoUsers || [])
        .map(user => `${user.receiveUserName || user.name}(浙里办)`)
        .join('、')

      if (zhezhengdingText && zhelibaoText) {
        return `${zhezhengdingText}、${zhelibaoText}`
      }

      return zhezhengdingText || zhelibaoText || ''
    },

    // 计算总的选择用户数
    totalSelectedUsers() {
      const zhezhengdingCount = (this.selectedUsers.zhezhengdingUsers || []).length
      const zhelibaoCount = (this.selectedUsers.zhelibaoUsers || []).length
      return zhezhengdingCount + zhelibaoCount
    }
  },
  methods: {
    // 打开模态框
    show(paperType) {
      this.visible = true
      if (paperType) {
        this.paperType = paperType
      } else {
        this.paperType = ''
      }
      // 初始化数据
      this.initData()
    },

    // 初始化数据
    initData() {
      // 初始化选择用户为空
      this.selectedUsers = {
        zhezhengdingUsers: [],
        zhelibaoUsers: []
      }

      // 清空签名状态表
      this.signatureStatusList = []

      // 添加加载状态
      this.confirmLoading = true

      // 如果存在文档ID，则获取签名数据
      if (this.docId) {
        console.log('docId', this.docId)
        this.$http
          .get(`/investigationSign/getByPaperId?paperId=${this.docId[0].id}`)
          .then(res => {
            if (res.success && res.code === 200 && res.data) {
              // 处理获取到的数据
              console.log('获取签名数据成功:', res.data)

              // 处理接口返回的浙政钉用户和浙里办用户
              const zhezhengdingUsers =
                res.data.zzdUsers.map(u => {
                  console.log('处理浙政钉用户数据:', u)
                  return {
                    ...u,
                    receiveUserName: u.receiveUserName || u.name,
                    name: u.receiveUserName || u.name,
                    status: u.status || u.signStatus || '0' // 尝试多个可能的状态字段名
                  }
                }) || []
              const zhelibaoUsers =
                res.data.zlbUsers.map(u => {
                  console.log('处理浙里办用户数据:', u)
                  return {
                    ...u,
                    name: u.receiveUserName || u.name,
                    receiveUserName: u.receiveUserName || u.name,
                    id: u.receiveUserId || u.id,
                    receiveUserId: u.receiveUserId || u.id,
                    status: u.status || u.signStatus || '0' // 尝试多个可能的状态字段名
                  }
                }) || []

              console.log('处理后的浙政钉用户:', zhezhengdingUsers)
              console.log('处理后的浙里办用户:', zhelibaoUsers)

              this.selectedUsers = {
                zhezhengdingUsers,
                zhelibaoUsers
              }

              // 如果有用户数据，更新签名状态
              if (zhezhengdingUsers.length > 0 || zhelibaoUsers.length > 0) {
                this.updateSignatureStatus()
              }
            } else {
              this.$message.warning(res.message || '获取签名数据失败')
            }
          })
          .catch(err => {
            console.error('获取签名数据出错:', err)
            this.$message.error('获取签名数据出错')
          })
          .finally(() => {
            this.confirmLoading = false
          })
      } else {
        this.confirmLoading = false
      }
    },

    // 打开用户选择器
    openUserSelector() {
      this.$refs.userSelector.show()
    },

    // 处理用户选择变更
    handleUserSelectionChange(users) {
      console.log('用户选择变更:', users)
      console.log('当前已有用户(浙政钉):', this.selectedUsers.zhezhengdingUsers)
      console.log('当前已有用户(浙里办):', this.selectedUsers.zhelibaoUsers)

      // 保留原有用户的状态信息
      const preserveUserStatus = (oldUsers, newUsers) => {
        console.log('原有用户:', oldUsers)
        console.log('新用户:', newUsers)

        return newUsers.map(newUser => {
          // 更灵活的匹配逻辑，尝试多种匹配方式
          const oldUser = oldUsers.find(old => {
            // 严格比较ID
            if (old.id && newUser.id && old.id === newUser.id) {
              return true
            }

            // 比较receiveUserId (如果存在)
            if (
              (old.receiveUserId && newUser.id && old.receiveUserId === newUser.id) ||
              (old.id && newUser.receiveUserId && old.id === newUser.receiveUserId)
            ) {
              return true
            }

            // 如果用户名和手机都匹配
            if (
              old.name &&
              newUser.name &&
              old.name === newUser.name &&
              old.phone &&
              newUser.phone &&
              old.phone === newUser.phone
            ) {
              return true
            }

            return false
          })

          if (oldUser) {
            console.log('找到匹配用户:', oldUser.name, '状态:', oldUser.status)
            return { ...newUser, status: oldUser.status }
          } else {
            console.log('未找到匹配用户:', newUser.name)
            return newUser
          }
        })
      }

      // 更新用户选择，同时保留原有状态
      this.selectedUsers = {
        zhezhengdingUsers: preserveUserStatus(
          this.selectedUsers.zhezhengdingUsers || [],
          users.zhezhengdingUsers || []
        ),
        zhelibaoUsers: preserveUserStatus(this.selectedUsers.zhelibaoUsers || [], users.zhelibaoUsers || [])
      }

      this.updateSignatureStatus()
    },

    // 更新签名状态表
    updateSignatureStatus() {
      // 获取签名状态对应的文本
      const getSignStatusText = status => {
        console.log('处理状态值:', status, typeof status)
        if (status === '1' || status === 1) return '已签名'
        if (status === '2' || status === 2) return '已退回'
        if (status === '3' || status === 2) return '已过期'

        return '待签名' // 默认为待签名（status为0或空）
      }

      // 合并浙政钉和浙里办用户，生成状态表格数据
      this.signatureStatusList = [
        ...(this.selectedUsers.zhezhengdingUsers || []).map(user => {
          console.log('浙政钉用户状态:', user.name, user.status)
          return {
            key: `zhezhengding_${user.id}`,
            name: user.name || user.receiveUserName,
            phone: user.phone,
            userType: '浙政钉',
            signStatus: getSignStatusText(user.status) // 根据status字段确定签名状态
          }
        }),
        ...(this.selectedUsers.zhelibaoUsers || []).map(user => {
          console.log('浙里办用户状态:', user.receiveUserName || user.name, user.status)
          return {
            key: `zhelibao_${user.id}`,
            name: user.receiveUserName || user.name,
            phone: user.phone || '-',
            userType: '浙里办',
            signStatus: getSignStatusText(user.status) // 根据status字段确定签名状态
          }
        })
      ]

      console.log('更新后的签名状态表:', this.signatureStatusList)
    },

    // 根据状态获取颜色
    getStatusColor(status) {
      switch (status) {
        case '已签名':
          return '#52c41a' // 绿色
        case '已退回':
        case '退回':
          return '#f5222d' // 红色
        case '已过期':
          return '#faad14' // 黄色
        case '待签名':
          return '#faad14' // 黄色
        default:
          return '#faad14' // 默认为待签名颜色
      }
    },

    // 确认
    handleOk() {
      if (this.totalSelectedUsers === 0) {
        this.$message.warning('请选择至少一名用户')
        return
      }

      this.confirmLoading = true
      console.log('this.docId', this.docId[0].id)
      // 准备请求参数
      const params = {
        id: this.detailData.id,
        paperId: this.docId[0].id, // 文档ID
        type: this.docId[0].type, // 文档类型
        zlbUsers:
          this.selectedUsers.zhelibaoUsers.map(user => {
            return {
              ...user,
              paperId: this.docId[0].id, // 文档ID
              // type: this.docId[0].type, // 文档类型
              receiveUserName: user.name || user.receiveUserName,
              // 保持ID一致性
              receiveUserId: user.receiveUserId || user.id,
              title: this.docId[0].fileOriginName,
              pid: this.detailData.id,
              paperType: this.paperType,
              blId: this.docId[0].blId,
              status: user.status || '0' // 保留状态信息
            }
          }) || [], // 浙里办用户
        zzdUsers:
          this.selectedUsers.zhezhengdingUsers.map(user => {
            return {
              ...user,
              // 保持ID一致性
              receiveUserId: user.receiveUserId || user.id,
              receiveUserName: user.name || user.receiveUserName,
              title: this.docId[0].fileOriginName,
              paperId: this.docId[0].id, // 文档ID
              pid: this.detailData.id,
              paperType: this.paperType,
              blId: this.docId[0].blId,
              status: user.status || '0' // 保留状态信息
            }
          }) || [] // 浙政钉用户
      }

      console.log('发送签名通知请求参数:', params)

      // 发送真实API请求
      this.$http
        .post('/investigationSign/addSignUsers', params)
        .then(res => {
          if (res.success && res.code === 200) {
            console.log('发送签名通知成功', res.data)
            this.$message.success('发送签名通知成功')

            // 触发成功事件
            this.$emit('success', this.selectedUsers)

            // 关闭弹窗
            this.handleCancel()
          } else {
            this.$message.error(res.message || '发送签名通知失败')
          }
        })
        .catch(err => {
          console.error('发送签名通知出错:', err)
          this.$message.error('发送签名通知出错，请稍后重试')
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },

    // 取消
    handleCancel() {
      this.selectedUsers = {
        zhezhengdingUsers: [],
        zhelibaoUsers: []
      }
      this.signatureStatusList = []
      this.visible = false
    }
  },
  watch: {
    // 监听用户选择变化
    selectedUsers: {
      handler() {
        this.updateSignatureStatus()
      },
      deep: true
    }
  }
}
</script>

<style lang="less" scoped>
.ant-form-item-label {
  font-weight: bold;
}

.ant-table-small {
  border: 1px solid #e8e8e8;
}

.modern-notification-box {
  display: flex;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f7ff 100%);
  border-radius: 12px;
  padding: 16px;
  //box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #91d5ff;
  transition: all 0.3s ease;

  &:hover {
    // box-shadow: 0 8px 20px -10px rgba(0, 0, 0, 0.12), 0 12px 32px 0 rgba(0, 0, 0, 0.08);
    // transform: translateY(-2px);
  }

  .notification-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    margin-right: 16px;
    flex-shrink: 0;

    .anticon {
      font-size: 20px;
      color: white;
    }
  }

  .notification-content {
    flex: 1;
    display: flex;
    align-items: center;

    p {
      margin: 0;
      line-height: 1.6;
      color: #262626;
      font-size: 14px;
    }
  }
}

.modern-link {
  display: inline-flex;
  align-items: center;
  color: #1890ff;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  background: rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;

  &:hover {
    background: rgba(24, 144, 255, 0.2);
    color: #096dd9;
    text-decoration: none;

    .link-icon {
      transform: translateX(2px);
    }
  }

  span {
    margin-right: 4px;
  }

  .link-icon {
    font-size: 12px;
    transition: all 0.3s ease;
  }
}

.select-users-container {
  display: flex;
  align-items: center;

  .ant-input {
    flex: 1;
    margin-right: 10px;
    cursor: pointer;
    background-color: #f9f9f9;

    &:hover {
      border-color: #40a9ff;
    }
  }

  .select-btn {
    flex-shrink: 0;
  }
}
</style>

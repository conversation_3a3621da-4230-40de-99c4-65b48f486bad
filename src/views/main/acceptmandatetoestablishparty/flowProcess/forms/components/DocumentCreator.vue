<template>
  <div class="document-creator">
    <div v-if="!disabled && (!value || value.length === 0)" class="document-create-card">
      <div class="create-card-content" @click="handleCreateDocument">
        <div class="create-icon-wrapper">
          <a-icon type="file-add" class="create-icon" />
        </div>
        <div class="create-text">
          <div class="create-title">{{ value.length > 0 ? '编辑文书' : '制作文书' }}</div>
          <div class="create-desc">点击创建新的文书文件</div>
        </div>
      </div>
    </div>

    <!-- 文档列表展示 -->
    <div v-if="value && value.length > 0" class="document-list-container">
      <div v-for="(doc, index) in value" :key="index" class="document-list-item">
        <div class="document-item-content">
          <div class="document-icon">
            <a-icon type="file-pdf" theme="filled" />
          </div>
          <div class="document-info">
            <div class="document-name" :title="doc.fileOriginName">{{ doc.fileOriginName }}</div>
            <!-- <div class="document-size">{{ doc.fileSizeInfo || doc.fileSizeKb + ' KB' }}</div> -->
            <!-- 添加文档状态标志 -->
            <div class="document-status" v-if="showStatusTag">
              <span v-if="doc.type === '1'" class="status-tag manual-upload"> <a-icon type="upload" />手动上传 </span>
              <span v-else-if="doc.signType === '1'" class="status-tag signed">
                <a-icon type="safety-certificate" />已签章
              </span>
              <span v-else class="status-tag unsigned"> <a-icon type="file-text" />未签章 </span>
            </div>
          </div>
          <div class="document-actions">
            <a-tooltip title="查看文书">
              <a-button type="link" class="action-button" @click="viewPdf">
                <a-icon type="eye" />
              </a-button>
            </a-tooltip>
            <a-tooltip title="编辑文书" v-if="!disabled">
              <a-button type="link" class="action-button" @click="handleCreateDocument">
                <a-icon type="edit" />
              </a-button>
            </a-tooltip>

            <a-tooltip title="下载文书">
              <a-button type="link" class="action-button" @click="downloadDocument(doc)">
                <a-icon type="download" />
              </a-button>
            </a-tooltip>
            <a-tooltip title="导出Word">
              <a-button type="link" class="action-button" @click="exportToWord(doc)">
                <a-icon type="file-word" />
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>

    <sh-drawer
      title="文书制作"
      :centered="true"
      :visible="visible"
      :footer="null"
      @cancel="handleCancel"
      destroyOnClose
      :bodyStyle="{ padding: '0px' }"
      wrapClassName="document-creator-modal"
    >
      <sh-cloud-sign
        style="height: 100%;"
        v-model="docId"
        :showUpload="showUpload"
        :showCloudSign="showCloudSign"
        :signParams="signParams"
        :key="docRefreshKey"
        :showSmartSign="showSmartSign"
        :customStampImage="customStampImage"
        :customStampSize="customStampSize"
        @change="handleDocIdChange"
        @upload-success="handleUploadSuccess"
        ref="cloudSign"
      >
        <p style="color:#fff;opacity:.6;padding: 6px 20px;font-size: 12px;">
          注意：【手动上传】后会替换文书区的文书，且无法进行"智能盖章"，需使用拖动签章，盖章前请确认盖章类型；【重新制作】文书会系统根据录入信息再次生成未盖章的空白文书，请谨慎操作。如您单位尚未授权和维护云签章，无法使用签章等功能，只能进行手动上传，如需开通请联系技术人员。
        </p>
        <div
          class="signCard"
          v-if="
            (alwaysShowSignatureBtn ||
              (['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status) && isShowAuthSign2) ||
              ((alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status)) &&
                isShowAuthSign)) &&
              isManualUpload
          "
        >
          <div class="area">选择区域</div>
          <div class="suggestion">
            <div>
              <a-icon
                type="bulb"
                style="color: #ffc107;"
              />&nbsp;操作说明： 请点击所需签名或审核意见后，鼠标滑动至左侧文书对应位置后再次点击，即可完成签批操作。
            </div>
            <div>请注意： 务必确保签批内容完全位于文书表格线框内，否则可能导致无法完成签批。</div>
          </div>
          <div class="imgList">
            <div class="imgList_title">
              意见
            </div>
            <div class="imgList_list">
              <div style="background: #fff;padding: 10px;">
                <img style="width: 100%;" @click="handleManualSignature(opinion1, 1)" :src="opinion1" alt="连笔签名" />
              </div>
              <div style="background: #fff;padding: 10px;">
                <img style="width: 100%;" @click="handleManualSignature(opinion2, 1)" :src="opinion2" alt="连笔签名" />
              </div>
            </div>
          </div>
          <div class="imgList">
            <div class="imgList_title">
              签名
            </div>
            <div class="imgList_list" v-if="signList.length > 0">
              <div v-for="(item, index) in signList" :key="index" style="background: #fff; width: 100%;">
                <img
                  v-if="item.signFileBase64"
                  :src="item.signFileBase64"
                  style="height:60px;"
                  @click="handleManualSignature(item.signFileBase64, 2)"
                  alt="连笔签名"
                />
                <div v-else style="color:#000;width:100%;text-align: center;cursor: pointer;">{{ item.sealName }}</div>
              </div>
            </div>
            <div v-else>
              <a-empty :image="simpleImage" description="暂无签名,点击【签名(APP签)】或【设备签名捺印】获取签名。" />
            </div>
          </div>
        </div>
        <template slot="toolbar">
          <!-- <a-button
            type="primary"
            icon="reload"
            size="small"
            class="seal-selector-btn"
            @click="handleRegenerateDocument"
            :loading="regenLoading"
            style="margin-right: 8px;height: 30px;"
          >
            重新生成
          </a-button> -->
          <a-button
            type="primary"
            icon="reload"
            size="small"
            class="seal-selector-btn"
            @click="handleRegenerateDocument"
            :loading="regenLoading"
            style="margin-right: 8px;height: 30px;"
          >
            重新制作
          </a-button>

          <!-- 添加WPS编辑按钮 -->
          <a-button
            v-if="false"
            type="primary"
            icon="edit"
            size="small"
            class="seal-selector-btn"
            @click="openWpsEditor(docId[0])"
            :disabled="!docId || docId.length === 0 || (docId[0] && docId[0].signType === '1')"
            style="margin-right: 8px;height: 30px;"
          >
            WPS编辑
          </a-button>

          <!-- <a-button
            type="primary"
            size="small"
            class="seal-selector-btn"
            @click="showAuthSignatureSelector"
            :loading="regenLoading"
            :disabled="isManualUpload"
            style="margin-right: 8px;height: 30px;"
            v-if="
              (alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status)) && isShowAuthSign
            "
          >
            签名（授权签）
            <a-tooltip v-if="isManualUpload" placement="top">
              <template slot="title">手动上传的文档不支持此功能</template>
              <a-icon type="question-circle" style="margin-left: 4px;" />
            </a-tooltip>
          </a-button> -->
          <!-- <a-button
                  type="primary"
                  size="small"
                  class="seal-selector-btn"
                  @click="showSignatureNotification"
                  :loading="regenLoading"
                  :disabled="isManualUpload"
                  style="margin-right: 8px;height: 30px;"
                >
                签名（APP签）
                <a-tooltip v-if="isManualUpload" placement="top">
                  <template slot="title">手动上传的文档不支持此功能</template>
                  <a-icon type="question-circle" style="margin-left: 4px;" />
                </a-tooltip>
                </a-button> -->
          <!-- 添加发送签名通知按钮 -->
          <a-dropdown
            v-if="
              alwaysShowSignatureBtn ||
                (['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status) && isShowAuthSign2) ||
                ((alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status)) &&
                  isShowAuthSign)
            "
            :trigger="['click']"
            overlayClassName="modern-dropdown-menu"
          >
            <a-menu slot="overlay">
              <a-menu-item disabled class="seal-menu-title">
                <div class="seal-menu-title-text">选择签名类型</div>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item
                key="1"
                :loading="regenLoading"
                @click="showSignatureNotification"
                v-if="
                  alwaysShowSignatureBtn ||
                    (['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status) && isShowAuthSign2)
                "
              >
                签名（APP签）
              </a-menu-item>
              <a-menu-item
                key="2"
                @click="showAuthSignatureSelector"
                :loading="regenLoading"
                v-if="
                  (alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(detailData.status)) &&
                    isShowAuthSign
                "
              >
                签名（授权签）
              </a-menu-item>
              <a-menu-item key="3" @click="showSignatureWithDevice()">
                设备签名捺印
              </a-menu-item>
            </a-menu>
            <a-button class="seal-selector-btn" style="height: 30px;"> 签名方式 <a-icon type="down" /> </a-button>
          </a-dropdown>

          <a-button
            type="primary"
            icon="file-word"
            size="small"
            class="seal-selector-btn"
            @click="exportToWord(docId[0])"
            style="margin-right: 8px;height: 30px;"
          >
            导出Word
          </a-button>
          <!-- 添加签章类型标签 -->
          <!-- <span class="seal-type-label" v-if="signatureList.length > 0 && showCloudSign">签章类型：</span> -->

          <!-- 使用Dropdown替代Select实现签章选择 -->

          <a-dropdown
            v-if="signatureList.length > 0 && showCloudSign"
            :trigger="['click']"
            overlayClassName="modern-dropdown-menu"
          >
            <a-button class="seal-selector-btn" size="small" style="height: 30px;">
              <a-icon type="stamp" style="margin-right: 4px; font-size: 12px;" />
              <span style="margin-right: 4px">{{ currentSealName }}</span>
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <!-- 添加菜单标题 -->
              <a-menu-item disabled class="seal-menu-title">
                <div class="seal-menu-title-text">选择签章类型</div>
              </a-menu-item>
              <a-menu-divider />

              <a-menu-item
                v-for="(item, index) in signatureList"
                :key="item.id"
                @click="handleSealChange(item.sealNo, item.sealTypeName || '签章' + (index + 1))"
                :class="{ 'seal-menu-item-selected': signParams.sealSn === item.sealNo }"
              >
                <div class="seal-item">
                  <a-icon type="stamp" style="margin-right: 8px; color: #1890ff;" />
                  <span>{{ item.sealTypeName || '签章' + (index + 1) }}</span>
                  <a-icon
                    v-if="signParams.sealSn === item.sealNo"
                    type="check"
                    style="margin-left: auto; color: #52c41a;"
                  />
                </div>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </sh-cloud-sign>
    </sh-drawer>

    <!-- 添加签名通知组件 -->
    <signature-notification
      :detailData="detailData"
      ref="signatureNotification"
      :docId="docId"
      @success="handleSignatureNotificationSuccess"
    />

    <!-- 添加签名选择弹窗 -->
    <a-modal
      title="选择签名"
      :visible="signatureSelectorVisible"
      :footer="null"
      @cancel="handleSignatureSelectorCancel"
      width="600px"
      destroyOnClose
    >
      <div class="signature-list-container">
        <div v-if="authSignatureList.length === 0" class="signature-empty">
          尚未找到可用的签名，请联系管理员添加签名
        </div>
        <div v-else class="signature-list">
          <div
            v-for="(item, index) in authSignatureList"
            :key="item.id"
            class="signature-item"
            :class="{ 'signature-item-selected': selectedSignatureIdForUI === item.id }"
            @click="selectSignature(item)"
          >
            <div class="signature-image">
              <img :src="defaultSignImage" :alt="item.signName" />
            </div>
            <div class="signature-name">{{ item.sealName || '签名' + (index + 1) }}</div>
          </div>
        </div>
        <div class="signature-actions">
          <a-button @click="handleSignatureSelectorCancel">取消</a-button>
          <a-button type="primary" @click="handleStartCustomSign" :disabled="!selectedSignatureId"
            >确定并开始签名</a-button
          >
        </div>
      </div>
    </a-modal>
    <!-- 签名捺印组件 -->
    <SignatureAndSeal ref="signatureAndSeal" @change="handleSignatureAndSeal" />

    <!-- 使用WPS编辑器组件 -->
    <wps-document-editor
      :visible="wpsEditorVisible"
      :document="currentEditingDoc"
      @cancel="closeWpsEditor"
      @save-success="handleWpsSaveSuccess"
    />
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'
import { mapGetters } from 'vuex'
import { downloadFile } from '@/utils/fileUtils'
import SignatureNotification from './SignatureNotification.vue'
import SignatureAndSeal from './SignatureAndSeal.vue'
// 引入WPS编辑器组件
import WpsDocumentEditor from './WpsDocumentEditor.vue'
export default {
  name: 'DocumentCreator',
  components: {
    SignatureNotification,
    SignatureAndSeal,
    WpsDocumentEditor
  },
  props: {
    // 支持v-model绑定文书ID
    value: {
      type: Array,
      default: () => []
    },
    isShowAuthSign: {
      type: Boolean,
      default: true
    },
    isShowAuthSign2: {
      type: Boolean,
      default: true
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },
    status: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否启用云签章功能
    enableCloudSign: {
      type: Boolean,
      default: true
    },
    // 是否显示文档状态标志
    showStatusTag: {
      type: Boolean,
      default: true
    },
    // 自定义创建文书接口
    createDocumentApi: {
      type: String,
      default: '/investigationInfo/createNotice'
    },
    // 表单校验函数
    validateForm: {
      type: Function,
      default: null
    },
    // 自定义文档列表路径
    docListPath: {
      type: String,
      default: 'groups.noticeDocList'
    },
    // 是否显智能签章按钮
    showSmartSign: {
      type: Boolean,
      default: true
    },
    paperType: {
      type: String,
      default: ''
    },
    // 是否始终显示签名按钮
    alwaysShowSignatureBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      docId: this.value || [],
      loading: false,
      regenLoading: false,
      docRefreshKey: 0,
      signParams: {
        key: '公章',
        sealSn: null
      },
      signatureList: [],
      showCloudSign: this.enableCloudSign, // 初始值基于props
      pollTimer: null, // 轮询定时器
      pollTimeoutId: null, // 用于存储延迟开始轮询的timeout ID
      newSignTimeoutId: null, // 用于存储新签名后延迟轮询的timeout ID
      isSigned: false, // 添加签名状态标识
      // 新增自定义签章相关数据
      authSignatureList: [], // 授权签名列表
      signatureSelectorVisible: false, // 签名选择器可见性
      selectedSignatureId: null, // 当前选择的签名sealNo，用于API调用
      selectedSignatureIdForUI: null, // 当前选择的签名id，用于UI显示
      customStampImage: '', // 自定义签章图片URL
      customStampSize: { width: 100, height: 50 }, // 自定义签章图片尺寸
      isCustomSignMode: false, // 是否处于自定义签章模式
      defaultSignImage: require('@/assets/sign.png'), // 默认签名图片

      // WPS编辑器相关数据
      wpsEditorVisible: false, // WPS编辑器弹框可见性
      currentEditingDoc: null, // 当前编辑的文档对象
      signList: [],
      simpleImage: null,
      opinion1:
        'data:image/png;base64,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',
      opinion2:
        'data:image/png;base64,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'
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    // 生成缩略图URL
    thumbnailUrl() {
      if (!this.value || this.value.length === 0) return ''
      // 使用文件路径
      return this.value[0].filePath
    },

    // 判断是否为手动上传的文档
    isManualUpload() {
      console.log('this.value', this.value)
      if (!this.value || this.value.length === 0) return false
      return this.value[0].type === '1'
    },
    // 当前选中的签章名称
    currentSealName() {
      // 找到当前选中的签章
      const currentSeal = this.signatureList.find(item => item.sealNo === this.signParams.sealSn)
      if (currentSeal) {
        return currentSeal.sealTypeName || '签章'
      }
      // 默认显示第一个签章名称
      return this.signatureList.length > 0 ? this.signatureList[0].sealTypeName || '签章' : '选择签章'
    }
  },
  watch: {
    value(newVal) {
      this.docId = newVal || []
    },
    // 监听启用云签章属性变化
    enableCloudSign(newVal) {
      this.showCloudSign = newVal
    },
    // 监听visible、paperType和docId变化，控制轮询
    visible(newVal) {
      if (newVal && this.paperType && this.docId && this.docId.length > 0) {
        this.startPolling()
      } else {
        this.stopPolling()
      }
    },
    paperType(newVal) {
      if (this.visible && newVal && this.docId && this.docId.length > 0) {
        this.startPolling()
      } else {
        this.stopPolling()
      }
    },
    docId: {
      handler(newVal) {
        if (this.visible && this.paperType && newVal && newVal.length > 0) {
          this.startPolling()
        } else {
          this.stopPolling()
        }
      },
      deep: true
    }
  },
  mounted() {
    console.log('mounted', this.value)
    this.signParams.bizId = this.detailData.id
    this.signParams.bizType = this.paperType
  },
  methods: {
    // 开始轮询查询签名状态
    startPolling() {
      this.stopPolling() // 先停止之前的轮询
      // 延迟5秒后开始轮询
      this.pollTimeoutId = setTimeout(() => {
        this.pollTimer = setInterval(() => {
          this.pollSignStatus()
        }, 5000)
      }, 5000)
    },

    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
      if (this.pollTimeoutId) {
        clearTimeout(this.pollTimeoutId)
        this.pollTimeoutId = null
      }
      if (this.newSignTimeoutId) {
        clearTimeout(this.newSignTimeoutId)
        this.newSignTimeoutId = null
      }
    },

    // 处理上传成功事件
    handleUploadSuccess() {
      // 手动上传成功后停止轮询查询签名状态
      this.stopPolling()
      this.isTop = true
      this.$message.success('文档上传成功，已停止签名状态检查')
    },

    // 轮询查询签名状态
    async pollSignStatus() {
      const currentDoc = this.docId[0]
      const params = {
        id: this.detailData.id, // 使用detailData中的id
        fileId: currentDoc.id, // 使用当前文档的id
        fileBucket: currentDoc.fileBucket, // 使用当前文档的fileBucket  bizType是 BLLX 时 该字段存笔录类型的字典 如 BLLX01、BLLX02
        blId: currentDoc.blId, // 笔录id
        bizType: this.paperType // 使用paperType作为bizType
      }
      if (this.isManualUpload) return this.getSignatureImage(params)
      if (this.isTop) {
        this.stopPolling()
        return
      }
      if (!this.docId || this.docId.length === 0 || !this.paperType || !this.visible) {
        this.stopPolling()
        return
      }

      try {
        const response = await this.$http.get('/investigationSign/latestSigned', {
          params
        })

        console.log('response', response)

        if (response && response.success && response.data) {
          // 如果返回有新签名状态
          if (this.isManualUpload) this.getSignatureImage(response.data.fileInfo.id)
          if (response.data.hasNewSign) {
            // 标记签名状态
            this.isSigned = true

            const docId = [{ ...response.data.fileInfo }]
            this.emitValueChange(docId)
            this.$message.success('文档已收到新签名')
            // 刷新组件
            this.docRefreshKey += 1

            // 收到新签名后停止当前轮询，并延迟5秒后重新开始轮询
            this.stopPolling()
            this.newSignTimeoutId = setTimeout(() => {
              this.pollTimer = setInterval(() => {
                this.pollSignStatus()
              }, 5000)
            }, 5000)
          }
        }
      } catch (error) {
        console.error('查询签名状态失败：', error)
      }
    },

    // 显示签名通知弹窗
    showSignatureNotification() {
      // 检查状态是否允许显示签名通知
      if (!(this.alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(this.detailData.status))) {
        return
      }
      // 如果是手动上传的文档，则不允许使用APP签名
      // if (this.isManualUpload) {
      //   this.$message.warning('手动上传的文档不支持APP签名功能')
      //   return
      // }

      // 使用$refs引用SignatureNotification组件
      this.$refs.signatureNotification.show(this.paperType)
    },

    // 处理文档ID变化（来自sh-cloud-sign组件）
    handleDocIdChange(newVal) {
      console.log('handleDocIdChange', newVal)
      if (!this.value || this.value.length === 0 || JSON.stringify(newVal) !== JSON.stringify(this.value)) {
        this.emitValueChange(newVal)
      }
    },

    // 导出当前正在编辑的文档为Word
    exportCurrentDocToWord() {
      if (!this.docId || this.docId.length === 0) {
        this.$message.warning('当前没有可导出的文档')
        return
      }
      const currentDoc = this.docId[0]
      this.exportToWord(currentDoc)
    },

    // 统一处理值变化的emit事件
    emitValueChange(newVal) {
      // 将文件对象作为数组emit出去
      const fileArray = Array.isArray(newVal) ? newVal : [newVal]

      this.$emit('input', fileArray)
      this.$emit('change', fileArray)
      this.$emit('onChange', fileArray)
      this.$emit('onchange', fileArray)
      this.$emit('update:docId', fileArray)
    },

    // 处理签章变更
    handleSealChange(sealId, sealName) {
      this.signParams = {
        ...this.signParams,
        sealSn: sealId
      }
      // this.docRefreshKey += 1; // 强制刷新组件
    },

    // 获取签章信息
    async getSignatureInfo() {
      // 如果未启用云签章，则不请求签章信息
      if (!this.enableCloudSign) {
        return
      }

      try {
        const deptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : this.userInfo?.orgId || ''

        // 调用获取签章接口
        const response = await this.$http.get('/investigationInfo/getSignature', {
          params: {
            deptId,
            status: this.status || '' // 使用表单数据中的状态或默认值
          }
        })

        if (response && response.success) {
          this.signatureList = response.data || []

          if (this.signatureList.length === 0) {
            this.$message.warning('尚未授权云签章，请联系管理员或使用手动上传')
            // 即使没有签章，也不修改showCloudSign的计算逻辑
          } else {
            // 默认使用第一个签章
            this.signParams.sealSn = this.signatureList[0].sealNo
          }
        } else {
          throw new Error((response && response.message) || '获取签章信息失败')
        }

        // 同时获取授权签名列表
        await this.getAuthSignatureList()
      } catch (error) {
        console.error('获取签章信息失败：', error)
        this.$message.error(error.message || '获取签章信息失败')
        // 出错时也不修改showCloudSign的计算逻辑
      }
    },

    // 获取授权签名列表
    async getAuthSignatureList() {
      // 检查状态是否允许获取签名列表
      if (!(this.alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(this.detailData.status))) {
        return
      }

      try {
        const deptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : this.userInfo?.orgId || ''

        // 调用获取签名列表接口
        const response = await this.$http.get('/signatureMaintenance/page', {
          params: {
            enabled: '0',
            jzjg: deptId || 'DEPT0000000000000000000000900023',
            sealType: 3, // 签名类型
            pageSize: 100, // 一次获取足够多的签名
            pageNum: 1
          }
        })

        if (response && response.success && response.data) {
          this.authSignatureList = response.data.rows || []
          console.log('授权签名列表：', this.authSignatureList)

          // 如果有签名列表，默认选择第一个
          if (this.authSignatureList.length > 0) {
            this.selectedSignatureId = this.authSignatureList[0].sealNo
            this.selectedSignatureIdForUI = this.authSignatureList[0].id
            // 统一使用默认签名图片
            // this.customStampImage = this.defaultSignImage;
            // 设置签章尺寸
            this.customStampSize = {
              width: 120,
              height: 60
            }
          }
          this.selectSignature(this.authSignatureList[0])
          this.customStampImage = null
        } else {
          console.warn('获取授权签名列表失败：', response && response.message)
        }
      } catch (error) {
        console.error('获取授权签名列表失败：', error)
      }
    },

    // 显示签名选择器
    showAuthSignatureSelector() {
      // 检查状态是否允许显示签名选择器
      if (!(this.alwaysShowSignatureBtn || ['PGZT03', 'PGZT05', 'PGZT04'].includes(this.detailData.status))) {
        return
      }
      // 如果是手动上传的文档，则不允许使用授权签名
      // if (this.isManualUpload) {
      //   this.$message.warning('手动上传的文档不支持授权签名功能')
      //   return
      // }

      if (this.authSignatureList.length === 0) {
        // 如果还没有获取授权签名列表，先获取
        this.getAuthSignatureList().then(() => {
          if (this.authSignatureList.length === 0) {
            this.$message.warning('未找到可用的签名，请联系管理员添加签名')
            return
          }
          this.signatureSelectorVisible = true
        })
      } else {
        this.signatureSelectorVisible = true
        this.selectedSignatureId = this.authSignatureList[0].sealNo
        this.selectedSignatureIdForUI = this.authSignatureList[0].id
        this.customStampImage = this.defaultSignImage
        this.customStampSize = {
          width: 120,
          height: 60
        }
      }
    },

    // 关闭签名选择器
    handleSignatureSelectorCancel() {
      this.signatureSelectorVisible = false
    },

    // 选择签名
    selectSignature(signature) {
      this.selectedSignatureId = signature.sealNo
      this.selectedSignatureIdForUI = signature.id
      // 始终使用默认签名图片，不使用接口返回的图片
      this.customStampImage = this.defaultSignImage

      // 根据签名图片尺寸调整大小，这里简单处理为固定尺寸
      this.customStampSize = {
        width: 120,
        height: 60
      }
    },

    // 开始自定义签章
    async handleStartCustomSign() {
      if (!this.selectedSignatureId || !this.customStampImage) {
        this.$message.warning('请先选择一个签名')
        return
      }

      if (!this.docId || this.docId.length === 0) {
        this.$message.warning('当前没有可用的文档')
        return
      }

      try {
        // 关闭签名选择弹窗
        this.signatureSelectorVisible = false

        // 显示加载提示
        // const loadingKey = 'startCustomSign';
        // this.$message.loading({ content: '正在准备签名工具，请稍候...', key: loadingKey, duration: 0 });

        this.isCustomSignMode = true

        // 调用组件的startCustomSign方法
        const position = await this.$refs.cloudSign.startCustomSign()

        // 成功获取位置信息后，调用签名接口
        if (position) {
          await this.applyCustomSignature(position)
        }
      } catch (error) {
        console.error('启动自定义签章失败：', error)
        this.$message.error('启动自定义签章失败，请稍后重试')
      } finally {
        this.isCustomSignMode = false
        this.$message.destroy('startCustomSign')
      }
    },
    //获取签名列表
    getSignatureImage(params) {
      this.$http({
        url: `/investigationSign/lastSign`,
        params
      }).then(msg => {
        // 创建当前签名ID集合（只包含有ID的项）
        const existingIds = new Set(this.signList.filter(item => item.id).map(item => item.id))
        if (!msg.data) return
        // 有ID且不存在时才添加
        msg.data.forEach(item => {
          // 有ID且不存在时才添加
          if (item.id && !existingIds.has(item.id)) {
            this.signList.push(item)
          }
        })
        // if (msg.data.id && !existingIds.has(msg.data.id)) {
        //   this.signList.push(msg.data)
        // }
      })
    },
    //手动上传——拖动签名
    /**
     * base64Data: base64格式的图片数据
     * type: 签名类型 1 意见 2 签名
     */
    async handleManualSignature(base64Data, type) {
      console.log('手动上传——拖动签名:', this.docId)
      if (!this.docId || (this.docId && this.docId.length === 0)) {
        this.$message.warning('当前没有可用的文档')
        return
      }
      this.customStampImage = base64Data
      this.customStampSize = {
        width: 100,
        height: 40
      }
      try {
        // 显示加载提示
        const loadingKey = 'startCustomSign'
        // this.$message.loading({ content: '正在准备签名工具，请稍候...', key: loadingKey, duration: 0 });

        this.isCustomSignMode = true

        // 调用组件的startCustomSign方法
        const position = await this.$refs.cloudSign.startCustomSign()

        // 成功获取位置信息后，调用签名接口
        if (position) {
          // 调用签名接口
          const response = await this.$http({
            url: '/investigationSign/signByDrag',
            method: 'post',
            data: {
              id: this.docId[0].id, // 使用当前文档的id
              fileId: this.docId[0].id,
              x: position.x - 25,
              y: position.y - 25,
              pageNo: position.page,
              signFileBase64: base64Data,
              signFileHeight: 40,
              signFileWidth: 100,
              type: this.docId[0].type
            }
          })
          console.log('签名接口返回数据:', response)

          if (response && response.success) {
            this.$message.success({
              content: type == 1 ? '意见应用成功' : type == 2 ? '签名应用成功' : '应用成功',
              key: loadingKey
            })

            // 更新文档状态
            if (response.data) {
              const docId = [{ ...response.data }]

              console.log('更新文档状态:', docId)
              this.emitValueChange(docId)
            }

            // 刷新组件
            this.docRefreshKey += 1

            // 签名成功后清空自定义签章图片和尺寸
            this.customStampImage = null
            this.customStampSize = null
            this.selectedSignatureId = null
            this.selectedSignatureIdForUI = null
          } else {
            this.customStampImage = null
            this.customStampSize = null
            this.$message.error({ content: (response && response.message) || '签名应用失败', key: loadingKey })
          }
        }
      } catch (error) {
        console.error('启动自定义签章失败：', error)
        this.$message.error('启动自定义签章失败，请稍后重试')
      } finally {
        this.isCustomSignMode = false
        this.$message.destroy('startCustomSign')
      }
    },
    // 取消自定义签章
    cancelCustomSign() {
      if (this.isCustomSignMode && this.$refs.cloudSign) {
        this.$refs.cloudSign.cancelCustomSign()
        this.isCustomSignMode = false
      }
    },

    // 应用自定义签章
    async applyCustomSignature(position) {
      console.log('applyCustomSignature', position)
      if (!position || !this.selectedSignatureId || !this.docId || this.docId.length === 0) {
        return
      }
      console.log(position)
      try {
        const loadingKey = 'applySignature'
        this.$message.loading({ content: '正在应用签名，请稍候...', key: loadingKey, duration: 0 })

        // 调用签名接口
        const response = await this.$http({
          url: '/investigationSign/signByPosition',
          method: 'get',
          params: {
            ...position,
            // id: this.detailData.id, // 使用detailData中的id
            id: this.docId[0].id, // 使用当前文档的id
            type: this.docId[0].type,
            x: position.x,
            infoId: this.detailData.id,
            y: position.y,
            pageNo: position.page,
            signId: this.selectedSignatureId,
            // 可能还需要其他参数
            bizType: this.paperType
          }
        })
        console.log(response, this.docId)

        if (response && response.success) {
          this.$message.success({ content: '签名应用成功', key: loadingKey })

          // 更新文档状态
          if (response.data) {
            const docId = [{ ...response.data }]
            this.emitValueChange(docId)
          }

          // 刷新组件
          this.docRefreshKey += 1

          // 签名成功后清空自定义签章图片和尺寸
          this.customStampImage = ''
          this.customStampSize = null
          this.selectedSignatureId = null
          this.selectedSignatureIdForUI = null
        } else {
          this.customStampImage = ''
          this.customStampSize = null
          this.$message.error({ content: (response && response.message) || '签名应用失败', key: loadingKey })
        }
      } catch (error) {
        // 确保使用与loadingKey相同的key关闭loading消息
        const loadingKey = 'applySignature'
        this.$message.error({ content: '应用签名失败，请稍后重试', key: loadingKey })
        console.error('应用签名失败：', error)
      }
    },

    // 使用imageViewer查看PDF
    viewPdf() {
      if (!this.value || this.value.length === 0) return
      console.log(this.detailData)

      // 直接使用value中的文件路径
      const fileList = this.value.map(
        item => {
          const id = this.getIdFromFileName(item.fileObjectName);
          return '/api/sysFileInfo/download?id=' + id + '&name=' + item.fileOriginName
        }
      )
      console.log(fileList)
      // this.$imageViewer.view(fileList, 0);
      this.$imageViewer.view(fileList, 0, '/pdfJsLib/web/viewer.html')
    },
    getIdFromFileName(path) {
      const parts = path.split('/');
      const fullName = parts.pop();
      const lastDotIndex = fullName.lastIndexOf('.');
      return lastDotIndex === -1 ? fullName : fullName.substring(0, lastDotIndex);
    },

    // 下载文档
    downloadDocument(doc) {
      if (!doc || !doc.filePath) return

      const downloadKey = `download-${Date.now()}`
      this.$message.loading({ content: '正在下载，请稍候...', key: downloadKey, duration: 0 })

      // 调用下载接口
      this.$http
        .get('/investigationInfo/downloadFile', {
          params: { filePath: doc.filePath, ...doc },
          responseType: 'blob'
        })
        .then(response => {
          // 使用通用下载工具函数，传递原始文件名作为备用名称
          downloadFile(response, doc.fileOriginName)

          this.$message.success({ content: `文书下载成功`, key: downloadKey, duration: 2 })
        })
        .catch(error => {
          console.error('下载失败:', error)
          this.$message.error({ content: `文书下载失败，请稍后重试`, key: downloadKey, duration: 3 })
        })
    },
    // 导出为Word文档-new
    newExportToWord(id, record) {
      console.log('exportToWord', id, record)
      const exportKey = `export-${Date.now()}`
      this.$message.loading({ content: '正在导出Word文档，请稍候...', key: exportKey, duration: 0 })

      this.$http
        .get(`/investigationInfo/transcriptDownloadWord`, {
          params: {
            id: id,
            type: 1
          },
          responseType: 'blob'
        })
        .then(response => {
          downloadFile(response, `${record.title || '笔录'}.docx`)
          this.$message.success({ content: 'Word文档导出成功', key: exportKey, duration: 2 })
        })
        .catch(error => {
          console.error('导出Word失败:', error)
          this.$message.error({ content: 'Word文档导出失败，请稍后重试', key: exportKey, duration: 3 })
        })
    },
    // 导出为Word文档
    exportToWord(doc) {
      console.log(this.detailData,'导出Word文档')
      if (this.detailData.status == 'PGZT03'&&doc.blId) {
        this.newExportToWord(doc.blId, doc)
        return
      }
      if (!doc || !doc.filePath) return

      const exportKey = `export-${Date.now()}`
      this.$message.loading({ content: '正在导出Word文档，请稍候...', key: exportKey, duration: 0 })

      // 调用导出Word接口
      this.$http
        .get('/investigationInfo/downloadDoc', {
          params: {
            ossUrl: doc.filePath,
            fileName: doc.fileOriginName,
            fileId: doc.id,
            type: doc.type,
            signType: doc.signType,
            ...doc
          },
          responseType: 'blob'
        })
        .then(response => {
          // 使用通用下载工具函数
          downloadFile(response, doc.fileOriginName.replace('.pdf', '.docx'))

          this.$message.success({ content: `Word文档导出成功`, key: exportKey, duration: 2 })
        })
        .catch(error => {
          console.error('导出Word失败:', error)
          this.$message.error({ content: `Word文档导出失败，请稍后重试`, key: exportKey, duration: 3 })
        })
    },

    // 处理创建文书
    async handleCreateDocument() {
      if (this.loading) return
      this.loading = true

      try {
        // 如果没有文书ID，则先创建
        if (!this.docId || this.docId.length === 0) {
          // 如果提供了表单校验函数，先进行校验
          if (this.validateForm) {
            // 显示校验加载提示
            const hideValidateMessage = this.$message.loading('正在校验表单数据...', 0)

            try {
              // 等待校验完成
              const validatedFormData = await this.validateForm()
              validatedFormData.status = this.status
              // 显示生成加载提示
              const hideLoadingMessage = this.$message.loading('正在生成文书，请稍候...', 0)

              try {
                // 使用校验通过的表单数据创建文书
                await this.createDocument(validatedFormData)
              } finally {
                // 确保关闭生成加载提示
                hideLoadingMessage()
              }
            } catch (error) {
              console.error('表单校验失败：', error)
              this.$message.error(error.message || '表单校验失败，请检查必填项')
              this.loading = false
              return
            } finally {
              // 确保关闭校验加载提示
              hideValidateMessage()
            }
          } else {
            // 无校验函数，直接创建
            const hideLoadingMessage = this.$message.loading('正在生成文书，请稍候...', 0)
            try {
              await this.createDocument()
            } finally {
              hideLoadingMessage()
            }
          }
        } else {
          // 如果已有文书ID，也需要获取签章信息
          await this.getSignatureInfo()
        }

        // 显示弹窗
        this.visible = true
      } catch (error) {
        console.error('文书创建失败：', error)
        this.$message.error('文书创建失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 处理重新生成文书
    async handleRegenerateDocument() {
      if (this.regenLoading) return
      this.regenLoading = true

      try {
        // 显示确认对话框
        this.$confirm({
          title: '确认重新生成',
          content: '重新生成将使用最新表单数据生成新的文书，是否继续？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            // 如果提供了表单校验函数，先进行校验
            let latestFormData = { ...this.formData }

            if (this.validateForm) {
              // 显示校验加载提示
              const hideValidateMessage = this.$message.loading('正在校验表单数据...', 0)

              try {
                // 等待校验完成
                latestFormData = await this.validateForm()
              } catch (error) {
                console.error('表单校验失败：', error)
                this.$message.error(error.message || '表单校验失败，请检查必填项')
                this.regenLoading = false
                return
              } finally {
                // 确保关闭校验加载提示
                hideValidateMessage()
              }
            }

            // 显示加载提示
            const hideLoadingMessage = this.$message.loading('正在重新生成文书，请稍候...', 0)

            try {
              // 处理日期格式
              if (latestFormData.noticeTime && latestFormData.noticeTime._isAMomentObject) {
                latestFormData.noticeTime = latestFormData.noticeTime.format('YYYY-MM-DD')
              }
              latestFormData.approvalDeptId = this.userInfo.loginEmpInfo.orgId
              // 文书格式
              console.log('latestFormData', latestFormData)

              if (latestFormData.noticeDocFiles) {
                latestFormData.noticeDocFiles = latestFormData.noticeDocFiles.map(item => item.id).join(',')
              }
              latestFormData.status = this.status

              // 调用创建文书接口
              const response = await this.$http.post(this.createDocumentApi, latestFormData)

              if (response && response.success && response.data) {
                // 更新文书ID - 现在保存完整的文件对象数组
                const fileObj = response.data
                this.docId = [fileObj]

                // 手动触发emit事件，传递完整的文件对象
                this.emitValueChange(fileObj)

                // 获取签章信息
                await this.getSignatureInfo()

                // 增加刷新键值，强制刷新组件
                this.docRefreshKey += 1
                this.$message.success('文书重新生成成功')

                // 如果弹窗未打开，则打开
                if (!this.visible) {
                  this.visible = true
                }
              } else {
                throw new Error((response && response.message) || '重新生成文书失败')
              }
            } finally {
              // 确保关闭加载提示
              hideLoadingMessage()
              this.regenLoading = false
            }
          },
          onCancel: () => {
            this.regenLoading = false
          }
        })
      } catch (error) {
        console.error('重新生成文书失败：', error)
        this.$message.error(error.message || '重新生成文书失败，请稍后重试')
        this.regenLoading = false
      }
    },

    // 创建文书
    async createDocument(validatedFormData = null) {
      try {
        // 获取表单数据，优先使用已验证的数据
        const formData = validatedFormData || { ...this.formData }
        formData.approvalDeptId = this.userInfo.loginEmpInfo.orgId
        // 处理日期格式
        if (formData.noticeTime && formData.noticeTime._isAMomentObject) {
          formData.noticeTime = formData.noticeTime.format('YYYY-MM-DD')
        }
        if (formData.noticeDocFiles) {
          formData.noticeDocFiles = formData.noticeDocFiles.map(item => item.id).join(',')
        }
        // 调用创建文书接口
        const response = await this.$http.post(this.createDocumentApi, formData)

        if (response && response.success && response.data) {
          // 更新文书ID - 现在保存完整的文件对象数组
          const fileObj = response.data
          this.docId = [fileObj]

          // 手动触发emit事件，传递完整的文件对象
          this.emitValueChange(fileObj)

          this.$message.success('文书创建成功')

          // 获取签章信息
          await this.getSignatureInfo()
        } else {
          throw new Error((response && response.message) || '创建文书失败')
        }
      } catch (error) {
        console.error('创建文书失败：', error)
        this.$message.error(error.message || '创建文书失败，请稍后重试')
        throw error
      }
    },

    // 处理取消
    handleCancel() {
      this.visible = false
      this.stopPolling() // 关闭弹窗时停止轮询
    },

    // 处理签名通知成功
    handleSignatureNotificationSuccess() {
      // 处理逻辑
    },
    showSignatureWithDevice() {
      console.log(this.docId, 'this.docId')
      this.$refs.signatureAndSeal.open({ url: this.docId[0].filePath, bizType: this.paperType })
    },
    // 签名捺印
    async handleSignatureAndSeal(imageBase64List) {
      // if (!position || !this.selectedSignatureId || !this.docId || this.docId.length === 0) {
      //   return
      // }
      // console.log(position)
      const signatureImages = []
      if (this.isManualUpload) {
        imageBase64List.forEach(item => {
          signatureImages.push({
            signFileBase64: 'data:image/png;base64,' + item
          })
        })
        this.signList = [...this.signList, ...signatureImages]
        // signSealList =
        return
      }
      try {
        const loadingKey = 'applySignature'
        this.$message.loading({ content: '正在应用签名，请稍候...', key: loadingKey, duration: 0 })

        // 调用签名接口
        const response = await this.$http({
          url: '/investigationSign/signByDevice',
          method: 'post',
          data: {
            imgBase64Signature: imageBase64List.length > 0 ? imageBase64List[0] : null,
            imgBase64Fingerprint: imageBase64List.length > 1 ? imageBase64List[1] : null,
            // id: this.detailData.id, // 使用detailData中的id
            id: this.docId[0].id, // 使用当前文档的id
            infoId: this.detailData.id,
            // 可能还需要其他参数
            bizType: this.paperType
          }
        })
        console.log(response, this.docId)

        if (response && response.success) {
          this.$message.success({ content: '签名应用成功', key: loadingKey })

          // 更新文档状态
          if (response.data) {
            const docId = [{ ...response.data }]
            this.emitValueChange(docId)
          }
          // 刷新组件
          this.docRefreshKey += 1
        } else {
          this.customStampSize = null
          this.$message.error({ content: (response && response.message) || '签名应用失败', key: loadingKey })
        }
      } catch (error) {
        // 确保使用与loadingKey相同的key关闭loading消息
        const loadingKey = 'applySignature'
        this.$message.error({ content: '应用签名失败，请稍后重试', key: loadingKey })
        console.error('应用签名失败：', error)
      }
    },
    // 打开WPS编辑器
    openWpsEditor(doc) {
      if (!doc || !doc.id) {
        this.$message.warning('没有可编辑的文档')
        return
      }

      // 检查文档是否已签章
      if (doc.signType === '1') {
        this.$message.warning('已签章的文档不可编辑')
        return
      }

      // 确保有文件后缀信息
      if (!doc.fileSuffix && doc.fileOriginName) {
        // 从文件名中提取后缀
        const nameParts = doc.fileOriginName.split('.')
        if (nameParts.length > 1) {
          doc.fileSuffix = nameParts[nameParts.length - 1]
        } else {
          // 默认PDF
          doc.fileSuffix = 'pdf'
        }
      }

      // 设置文档对象并显示编辑器
      this.currentEditingDoc = doc
      this.wpsEditorVisible = true
    },

    // 关闭WPS编辑器
    closeWpsEditor() {
      this.wpsEditorVisible = false
      this.currentEditingDoc = null
    },

    // 处理WPS文档保存成功
    handleWpsSaveSuccess(doc) {
      // 更新文档状态
      if (doc) {
        this.$message.success('文档保存成功，正在更新文档状态...')

        // 这里可以添加更新文档状态的请求
        // 例如：重新获取文档列表等

        // 刷新组件
        this.docRefreshKey += 1
      }
    }
  },
  // 组件销毁时清除定时器
  beforeDestroy() {
    this.stopPolling() // 这会清除所有的定时器，包括setTimeout和setInterval
    // 确保清除自定义签章模式
    this.cancelCustomSign()
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
  }
}
</script>

<style lang="less" scoped>
.document-creator {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;

  .document-create-card {
    width: 100%;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;

    .create-card-content {
      display: flex;
      align-items: center;
      padding: 16px;
      background: linear-gradient(120deg, #f0f7ff, #e6f7ff);
      border: 1px dashed #91d5ff;
      border-radius: 12px;
      line-height: 1.5em;
      transition: all 0.3s;

      &:hover {
        background: linear-gradient(120deg, #e6f7ff, #bae7ff);
        border-color: #69c0ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
      }

      .create-icon-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        margin-right: 16px;

        .create-icon {
          font-size: 24px;
          color: white;
        }
      }

      .create-text {
        flex: 1;

        .create-title {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }

        .create-desc {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .document-list-container {
    width: 100%;

    .document-list-item {
      margin-bottom: 8px;
      border-radius: 8px;
      background-color: #f5f7fa;
      overflow: hidden;
      transition: all 0.3s;
      position: relative;

      &:hover {
        // transform: translateY(-2px);
        // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        background-color: #e6f7ff;
      }

      .document-item-content {
        display: flex;
        align-items: center;
        padding: 12px 16px;

        .document-icon {
          margin-right: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 40px;
          height: 40px;
          background-color: #e6f7ff;
          border-radius: 8px;

          .anticon {
            font-size: 20px;
            color: #1890ff;
          }
        }

        .document-info {
          flex: 1;
          overflow: hidden;
          padding-right: 24px;

          .document-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            // margin-bottom: 4px;
          }

          .document-status {
            display: flex;
            gap: 8px;

            .status-tag {
              display: inline-flex;
              align-items: center;
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 12px;
              height: 20px;
              line-height: 16px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              transition: all 0.2s ease;

              .anticon {
                font-size: 12px;
                margin-right: 4px;
              }

              &.manual-upload {
                background: linear-gradient(45deg, #fa8c16, #f56a00);
                color: #fff;
              }

              &.signed {
                background: linear-gradient(45deg, #52c41a, #389e0d);
                color: #fff;
              }

              &.unsigned {
                background: linear-gradient(45deg, #ffa39e, #ff7875);
                color: #fff;
              }
            }
          }
        }

        .document-actions {
          display: flex;
          align-items: center;

          .action-button {
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 6px;
            padding: 0;
            margin-left: 8px;

            .anticon {
              font-size: 16px;
              color: #1890ff;
            }

            &:hover {
              background-color: rgba(24, 144, 255, 0.1);
            }
          }
        }
      }
    }
  }
}

/* 新增签名选择器样式 */
.signature-list-container {
  padding: 10px;

  .signature-empty {
    text-align: center;
    padding: 30px 0;
    color: #999;
  }

  .signature-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;

    .signature-item {
      width: calc(33.33% - 16px);
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      &.signature-item-selected {
        border-color: #1890ff;
        background-color: #e6f7ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      .signature-image {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }

      .signature-name {
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
      }
    }
  }

  .signature-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
.signCard {
  position: absolute;
  right: 0;
  top: 108px;
  z-index: 99;
  color: #fff;
  width: 20%;
  border-left: 1px solid rgba(77, 75, 75, 0.453);
  > div {
    padding: 10px;
  }
  .area {
    font-size: 18px;
    border-bottom: 1px solid rgba(77, 75, 75, 0.453);
  }
  .suggestion {
    background: #ffcc5f4c;
    color: #ffb804;
  }
  .imgList {
    width: 100%;
    .imgList_title {
      width: 100%;
      // height: 20px;
      padding: 3px 0;
      padding-left: 30px;
      font-size: 18px;
      background-image: url('../../../../../../assets/sign/option.png');
      background-size: 100% 100%;
      margin-bottom: 10px;
    }
    .imgList_list {
      width: 100%;
      div {
        width: 100%;
        text-align: center;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
<style lang="less">
.document-creator-modal {
  .ant-drawer-header {
    padding: 15px 16px;
    border-bottom: 1px solid #444;
    background: linear-gradient(to right, #1890ff, #096dd9);
  }

  .ant-drawer-title {
    font-size: 14px;
    line-height: 1.3;
    color: #fff;
  }

  .ant-drawer-close {
    top: 13px;
    color: #fff;
    width: 26px;
    height: 26px;
    line-height: 26px;
  }

  .ant-drawer-close {
    color: #ccc;

    &:hover {
      color: #fff;
    }
  }

  .ant-modal-body {
    padding: 0;
  }

  .ant-modal-content {
    height: 90vh;
    display: flex;
    flex-direction: column;
    background-color: #333;
  }
  // .ant-modal-close-x{
  //   width: 26px;
  //   height: 26px;
  //   line-height: 26px;
  // }
}

/* 深色主题下拉框样式 */
.dark-theme-select {
  .ant-select-selection {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid #444;
    color: #fff;

    &:hover,
    &:focus {
      border-color: #1890ff;
    }

    .ant-select-arrow {
      color: #fff;
    }
  }
}

.dark-theme-select.ant-select-focused .ant-select-selection,
.dark-theme-select .ant-select-selection:focus,
.dark-theme-select .ant-select-selection:active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-select-dropdown {
  .dark-theme-option {
    &.ant-select-dropdown-menu-item {
      color: #333;

      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }

      &-selected,
      &-active {
        background-color: rgba(24, 144, 255, 0.2);
        color: #1890ff;
      }
    }
  }
}

/* 现代化下拉菜单样式 */
.modern-dropdown-menu {
  .ant-dropdown-menu {
    padding: 4px;
    border-radius: 6px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12);

    .ant-dropdown-menu-item {
      border-radius: 4px;
      margin: 2px 0;
      padding: 8px 12px;

      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }

      &.seal-menu-item-selected {
        background-color: rgba(24, 144, 255, 0.15);
        font-weight: 500;
        color: #1890ff;

        &:hover {
          background-color: rgba(24, 144, 255, 0.2);
        }

        .seal-item {
          span {
            color: #1890ff;
          }
        }
      }
    }
  }
}

.seal-selector-btn {
  background: linear-gradient(to right, #1890ff, #096dd9);
  border: none;
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 24px;

  &:hover,
  &:focus {
    background: linear-gradient(to right, #40a9ff, #1890ff);
    color: white;
  }
}

.seal-item {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 150px;
}

/* 签章类型标签样式 */
.seal-type-label {
  color: white;
  font-size: 12px;
  margin-right: 0px;
  display: inline-flex;
  align-items: center;
  height: 30px;
}

.seal-menu-title {
  &.ant-dropdown-menu-item-disabled {
    cursor: default;
    color: rgba(0, 0, 0, 0.65) !important;
    font-weight: 500;
    background-color: rgba(240, 240, 240, 0.5);

    &:hover {
      background-color: rgba(240, 240, 240, 0.5);
    }

    .seal-menu-title-text {
      text-align: center;
      width: 100%;
    }
  }
}
//签名类型
.seal-type-dropdown {
  background-color: red;

  /* WPS编辑器样式 */
  .wps-editor-modal {
    .ant-modal {
      width: 100vw !important;
      height: 100vh !important;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      height: 100vh;
      border-radius: 0;
    }

    .ant-modal-body {
      height: calc(100vh - 55px);
      padding: 0;
    }

    .wps-editor-container {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
<style lang="less">
/* WPS编辑器全局样式 */
.wps-editor-modal {
  .ant-modal-header {
    background: linear-gradient(to right, #1890ff, #096dd9);
    border-bottom: 1px solid #096dd9;
    padding: 12px 24px;

    .ant-modal-title {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .ant-modal-close {
    color: #fff;

    &:hover {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .ant-modal-close-x {
    width: 46px;
    height: 46px;
    line-height: 46px;
  }
}
</style>

<template>
  <div>
    <a-modal
      title="设备签名捺印"
      :visible="visible"
      :footer="null"
      :loading="true"
      @cancel="cancel"
      width="1300px"
      :destroyOnClose="true"
    >
      <div class="content">
        <div class="top">
          <div class="describe">
            <div class="title">说明：</div>
            <div class="text">
              <p>
                1、首次使用设备签名捺印，请下载驱动并查看【配置说明】，按照配置说明运行驱动，驱动未运行无法判断设备是否连接成功。
              </p>
              <p>
                2、设备已连接并且驱动已运行状态，即可进行签名捺印。
              </p>
              <p>
                3、点击【发送签名/捺印】将签名捺印发送至已连接的设备上进行签名捺印，若需要重新签名/
                捺印，点击【重新签名/捺印】即可，重新签名捺印后覆盖当前内容。
              </p>
            </div>
          </div>
          <div class="status">
            <dl :style="{ background: status == 'online' && isConnected ? '#f2f6ff' : '#fff3f4' }">
              <dt>
                <a-icon
                  v-if="status == 'online' && isConnected"
                  :style="{ fontSize: '25px', color: '#2F6AFB' }"
                  type="link"
                />
                <a-icon v-else style="font-size: 25px;color: #f96d6f" type="disconnect" />
              </dt>

              <dd v-if="status == 'online' && isConnected">设备已连接</dd>
              <dd v-else :style="{ color: '#f96d6f' }">设备未连接</dd>
            </dl>
            <dl :style="{ background: status == 'online' ? '#f2f6ff' : '#fff3f4' }">
              <dt>
                <img v-if="status == 'online'" src="@/assets/sign/icon_2.png" alt="" />
                <img v-else src="@/assets/sign/icon_2_1.png" alt="" />
              </dt>
              <dd v-if="status == 'online'">驱动已运行</dd>
              <dd v-else :style="{ color: '#f96d6f' }">驱动未运行</dd>
            </dl>
            <a-spin :spinning="spinning">
              <dl @click.stop="handleDownloadRecord('1938159094547402753')">
                <dt><a-icon type="download" :style="{ fontSize: '25px', color: '#2F6AFB' }" /></dt>
                <dd>驱动下载</dd>
              </dl>
            </a-spin>
            <a-spin :spinning="spinning2">
              <dl @click.stop="handleDownloadRecord('1937819290676809730', 'file')">
                <dt><img src="@/assets/sign/icon_3.png" alt="" /></dt>
                <dd>配置说明</dd>
              </dl>
            </a-spin>
          </div>
        </div>
        <div class="bottom">
          <div class="bottom_left" id="sign_area">
            签名捺印显示区域
            <img id="Image1" />
            <img id="Image2" />
          </div>
          <div class="bottom_right">
            <div class="sign_status">
              <div :style="{ color: signed ? '#2462FB' : ' #F96D6F' }">
                <img :src="signed ? img1 : img2" alt="" />
                {{ signed ? '已签名' : '未签名' }}
              </div>
              <div v-if="bizType == 'BLLX'" :style="{ color: chop ? '#2462FB' : ' #F96D6F' }">
                <img :src="chop ? img1 : img2" alt="" />
                {{ chop ? '已捺印' : '未捺印' }}
              </div>
            </div>
            <div class="sign_btn">
              <a-button v-if="bizType == 'BLLX'" type="primary" ghost @click="SigningWithFinger">{{
                imageBase64List.length == 0 ? '发送签名/捺印' : '重新签名/捺印'
              }}</a-button>
              <a-button v-else type="primary" ghost @click="SigningWithFinger">{{
                imageBase64List.length == 0 ? '发送签名' : '重新签名'
              }}</a-button>
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <div>
          <a-button type="primary" @click="onSubmit">确定</a-button>
          <a-button @click="cancel">取消</a-button>
        </div>
      </div>
    </a-modal>
    <a-modal :footer="null" v-model="describeVisible" title="提示">
      <p>
        当前驱动未运行，请下载驱动并按照配置说明运行驱动，如已完成配置但驱动仍未运行，请联系技术人员。
      </p>
      <div class="footer">
        <a-button type="primary" @click="setModel">确定</a-button>
      </div>
    </a-modal>
    <a-modal :footer="null" width="1300px" v-model="fileVisible" title="设备配置说明">
      <p style=" color: #000000;">
        配置说明：首次使用设备签名捺印前，下载驱动压缩包至电脑上，在解压缩后的文件夹中，找到【启动服务.bat】右键点击选择以管理员身份运行，启动后显示驱动正常运行即可。
      </p>
      <p style=" color: #000000;">只需运行一次，后续关机重启后会自启动驱动。</p>
      <div style="display: flex;">
        <div style="flex: 1; padding: 20px;">
          <div style="color: #2F6AFB;font-weight: 600;margin-bottom: 20px;">第一步<a-icon type="caret-down" /></div>
          <img style="width: 100%;" src="../../../../../../assets/sign/describe1.png" alt="" />
        </div>
        <div style="flex: 1;padding: 20px;">
          <div style="color: #2F6AFB;font-weight: 600;margin-bottom: 20px;">第二步<a-icon type="caret-down" /></div>
          <img style="width: 100%;" src="../../../../../../assets/sign/describe2.png" alt="" />
        </div>
      </div>
      <div class="footer">
        <a-button type="primary" @click="fileVisible = false">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>
<script type="text/javascript" charset="UTF-8" src="jquery-1.12.4.min.js"></script>
<script type="text/javascript" charset="UTF-8" src="lib/jquery-ui/jquery-ui.js"></script>
<script>
import jQuery from 'jquery'
import { downloadFile } from '@/utils/fileUtils'
export default {
  data() {
    return {
      visible: false,
      chop: false,
      signed: false,
      describeVisible: false,
      img1: require('@/assets/sign/icon_5.png'),
      img2: require('@/assets/sign/icon_6.png'),
      TerminalControl: '',
      MediaControl: '',
      BaseUrl: 'http://127.0.0.1:27100/',
      isInit: false,
      log: null,
      status: null,
      isConnected: false,
      deviceInfo: null,
      imageBase64List: [],
      intervalId: null, // 新增定时器ID存储
      url: 'https://lq1990.oss-cn-hangzhou.aliyuncs.com/dataCollaboration/202506/1932614600563343362.docx',
      first: true,
      active: 0,
      isInitialCheck: true, // 新增初始化检测标志
      lastConnectionStatus: null, // 新增上次连接状态跟踪
      filePath: '',
      bizType: null,
      spinning: false,
      spinning2: false,
      key: 'updataBtn',
      fileVisible: false
    }
  },

  mounted() {},
  methods: {
    setModel() {
      this.describeVisible = false
      this.first = false
      this.fileVisible = false
    },
    openUrl() {
      window.open('https://view.officeapps.live.com/op/view.aspx?src=' + this.url, 'config_guide') // 使用固定窗口名称实现标签页复用
    },
    cancel() {
      clearInterval(this.intervalId)
      this.visible = false
      this.chop = false
      this.signed = false
      this.active = 0
      this.imageBase64List = []
      // 新增状态重置逻辑
      this.status = null
      this.isConnected = false
      this.lastWebSocketStatus = null
      this.lastConnectionStatus = null
      this.first = true
      this.describeVisible = false
    },
    open(par) {
      this.visible = true
      this.filePath = par.url
      this.bizType = par.bizType
      // 清除已有定时器
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }

      // 创建新定时器（每5秒检查一次）
      this.intervalId = setInterval(() => {
        this.IsDeviceOnLine()
        this.checkWebSocketService()
      }, 5000)

      jQuery.support.cors = true
      this.IsDeviceOnLine()
      this.$nextTick(() => {
        this.checkWebSocketService()
        this.init()
      })
    },
    onSubmit() {
      this.$emit('change', this.imageBase64List)
      this.cancel()
    },
    checkWebSocketService() {
      // 移除旧版驱动检测提示

      // 新增智能检测提示逻辑
      if (this.lastWebSocketStatus === null) {
        // this.$message.loading({ content: '首次驱动检测中...', duration: 1 })
      }

      // 新增socket实例清理
      if (this.socket) {
        this.socket.close()
        this.socket = null
      }

      this.socket = new WebSocket('ws://127.0.0.1:27100/')

      this.socket.onopen = () => {
        // 强化状态变更检测
        const isStatusChanged = this.lastWebSocketStatus !== 'online'
        const isFirstConnect = this.lastWebSocketStatus === null

        this.$set(this, 'status', 'online')
        this.isConnected = true

        if (isStatusChanged) {
          const message = isFirstConnect ? '驱动启动成功' : '驱动已重新连接'
          // this.$message.success({ content: message, duration: 1.5 })
        }

        this.lastWebSocketStatus = 'online'
        this.socket.close()
      }

      this.socket.onerror = () => {
        // 统一状态更新逻辑
        this.handleConnectionError('offline', '驱动连接异常')
      }

      const timeoutHandler = () => {
        if (this.status === 'loading') {
          this.handleConnectionError('offline', '驱动连接超时')
          this.socket.close()
        }
      }
      setTimeout(timeoutHandler, 3000)
    },

    // 新增错误处理统一方法
    handleConnectionError(status, message) {
      if (this.lastWebSocketStatus !== status) {
        this.$message.error({ content: message })
      }
      this.$set(this, 'status', status)
      this.lastWebSocketStatus = status
      this.active = 0
      if (this.first) {
        this.describeVisible = true
      }
    },
    init() {
      if (!this.isInit) {
        this.isInit = true
        this.log = document.getElementById('sign_area')
        console.log(this.log, 'log')
        if (this.log) {
          return true
        } else {
          return false
        }
      }
    },
    SigningWithFinger() {
      if (this.filePath.includes('https://sjxt.zjsft.gov.cn/oss/zjgz'))
        this.filePath = this.filePath.replace('https://sjxt.zjsft.gov.cn/oss/zjgz', 'http://223.4.75.78/oss')

      if (this.filePath.includes('https://zjgz.oss-cn-hangzhou-zjzwy01-d01-a.cloud-inner.zj.gov.cn'))
        this.filePath = this.filePath.replace(
          'https://zjgz.oss-cn-hangzhou-zjzwy01-d01-a.cloud-inner.zj.gov.cn',
          'http://223.4.75.78/oss'
        )
      this.filePath = this.filePath.replace('https', 'http')
      if (this.status !== 'online' || !this.isConnected) return this.$message.error('请查看驱动/设备是否正常运行！')
      this.imageBase64List = []
      var command = {}
      command.CommandName = 'ShowPdfSign'
      command.Statement = jQuery('#signing_statement').val()
      command.Voice = jQuery('#signing_voice').val()
      command.SignMode = Number(jQuery('#signing_mode').val())
      command.SignImageWidth = jQuery('#signing_imagewidth').val()
      command.SignImageHeight = jQuery('#signing_imageheight').val()
      command.NameImageWidth = jQuery('#signing_nameimagewidth').val()
      command.NameImageHeight = jQuery('#signing_nameimageheight').val()
      command.ImageType = 1
      command.IsImageEqual = 1
      command.Tts = '请核对内容,内容无误后在设备签名区域签署姓名'
      // command.Tts = ''
      command.TimeOut = 120
      // command.PdfPath = this.filePath.replace('https', 'http')
      command.PdfPath = this.filePath
      var url = this.BaseUrl + 'Terminal?t=' + command.CommandName
      console.log(url, 'url')
      jQuery.post(url, JSON.stringify(command), result => {
        this.Log(command.CommandName, result)
        var o = JSON.parse(result)

        this.CleanImages()
        if (o.success) {
          this.SetImageBase64(jQuery('#Image1'), 'data:image/png;base64,' + o.data.NameImage)
          this.imageBase64List.push(o.data.NameImage)
          this.signed = true
          if (this.bizType == 'BLLX') {
            this.$nextTick(() => {
              this.GetFinger()
            })
          }

          // this.SetImageBase64(jQuery('#Image2'), 'data:image/png;base64,' + o.data.FingerImage)
        }
      })
    },
    GetFinger() {
      const that = this // 保存Vue实例的引用
      var command = {}
      command.CommandName = 'GetFinger'
      command.Title = '请将手指平放于采集区域中心进行捺印'
      command.Voice = '请将手指平放于采集区域中心进行捺印'
      // command.Title = ''
      // command.Voice = ''
      command.TimeOut = 120
      command.FileType = 1 //1:png;2:jpg;3:bmp
      command.ImageWidth = 152
      command.ImageHeight = 200
      command.IsImageEqual = 1
      command.FingerPicColour = 'red'
      command.FingerPath = ''
      command.FingerPicColour = '1'
      var url = this.BaseUrl + 'Terminal?t=' + command.CommandName
      console.log(command, ' command')
      jQuery.post(url, JSON.stringify(command), function(result) {
        var o = JSON.parse(result)
        if (o.success) {
          that.SetImageBase64(jQuery('#Image2'), 'data:image/png;base64,' + o.data.FingerImage)
          that.imageBase64List.push(o.data.FingerImage)
          that.chop = true // 使用that引用确保正确更新状态
          that.$forceUpdate() // 强制视图更新
        }
      })
    },
    CleanImages() {
      jQuery('#Image1').attr('src', '')
      jQuery('#Image2').attr('src', '')
    },
    SetImageBase64(ctrl, base64_data) {
      ctrl.attr('src', base64_data)
    },
    IsDeviceOnLine() {
      const that = this
      var url = this.BaseUrl + 'Terminal?t=' + 'IsDeviceOnLine'
      try {
        jQuery.post(url, '{"CommandName":"IsDeviceOnLine"}', function(result) {
          const response = JSON.parse(result)
          const currentStatus = response.errorcode === 0

          // 状态变化检测
          if (that.lastConnectionStatus !== null) {
            if (currentStatus !== that.lastConnectionStatus) {
              that.$message[response.errorcode === 0 ? 'success' : 'error'](
                response.errorcode === 0 ? '设备已重新连接' : '设备已断开连接'
              )
            }
          }

          that.isConnected = currentStatus
          that.lastConnectionStatus = currentStatus // 更新上次连接状态

          // 保留首次检测提示（修复首次检测未触发问题）
          if (that.isInitialCheck) {
            that.$message[response.errorcode === 0 ? 'success' : 'error'](
              response.errorcode === 0 ? '设备已连接' : '设备未连接'
            )
            that.isInitialCheck = false
          }

          that.$forceUpdate()
        })
      } catch (error) {
        console.log(error)
        // 新增：首次检测异常时提示设备未连接
        if (this.isInitialCheck) {
          this.$message.error('设备未连接')
          this.isInitialCheck = false
        } else if (this.lastConnectionStatus) {
          // 仅当之前连接成功过才提示断开
          this.$message.error('设备连接异常')
        }
        this.isConnected = false
        this.lastConnectionStatus = false
      }
    },
    Log(operation, value) {
      var message = ''
      switch (value) {
        case 0:
          message = '成功'
          break
        case -1:
          message = '信道错误,请重新开启服务'
          break
        case -2:
          message = '设备执行失败'
          break
        case -3:
          message = '设备响应报文长度错误'
          break
        case -4:
          message = '软件卸载失败'
          break
        case -5:
          message = '指令执行超时'
          break
        case -6:
          message = '用户撤消'
          break
        case -60:
          message = 'http下载失败'
          break
        case -80:
          message = '无需升级'
          break
        case -81:
          message = 'Apk验证失败'
          break
        case 1:
          message = '内部错误'
          break
        case 2:
          message = '设备不在线'
          break
        case 3:
          message = '参数错误'
          break
        case 4:
          message = '软件没有运行'
          break
        case 9:
          message = '设备未授权'
          break
        case 10:
          message = '文件不存在'
          break
        default:
          message = value
          break
      }
      this.message = message
      // this.log.value = this.log.value + '\r\n' + operation + ':' + message
    },
    handleDownloadRecord(id, type) {
      if (type == 'file') return (this.fileVisible = true)

      this.spinning = true
      this.$message.loading({ content: '下载中，请稍候...', key: this.key, duration: 0 })
      this.$http
        .get(`/sysFileInfo/download?id=${id}`, {
          responseType: 'blob'
        })
        .then(response => {
          if (response) {
            downloadFile(response)
            this.spinning = false
            this.spinning2 = false
            this.$message.success({ content: '下载成功', key: this.key, duration: 2 })
          }
        })
        .catch(error => {
          this.spinning = false
          this.spinning2 = false
          // this.$message.error({ content: '下载失败', key: this.key, duration: 2 })
          // this.$message.error('下载失败，请稍后重试')
        })
    }
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }
}
</script>

<style lang="less" scoped>
.content {
  // padding: 20px;
  .top {
    display: flex;
    justify-content: space-around;
    .describe {
      flex: 2;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .status {
      flex: 1.2;
      display: flex;
      justify-content: space-around;
      dl {
        // width: 95px;
        height: 65px;
        background: #f2f6ff;
        border-radius: 2px 2px 2px 2px;
        display: inline-block;
        text-align: center;
        padding: 10px 20px;
        cursor: pointer;
        dt {
          img {
            width: 25px;
          }
        }
        dd {
          margin: 0;
        }
      }
    }
  }
  .bottom {
    display: flex;
    .bottom_left {
      flex: 10;
      height: 400px;
      border: 2px dashed #dcdcdc;
      color: #dcdcdc;
      text-align: center;
      line-height: 400px;
      font-size: 35px;
    }
    .bottom_right {
      padding: 0 20px;
      flex: 1;
      .sign_status {
        > div {
          margin-bottom: 20px;
          img {
            width: 20px;
          }
        }
      }
      .sign_btn {
        display: flex;
        flex-direction: column;
        button {
          height: 40px;
          width: 120px;
          margin-bottom: 20px;
          border-radius: 6px;
        }
      }
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  div {
    width: 300px;
    button {
      margin: 0 10px;
      font-size: 16px;
    }
    /deep/ .ant-btn-primary {
      width: 102px;
      height: 45px;
      background: linear-gradient(90deg, #3c8eff 0%, #025bf1 100%);
      border-radius: 8px 8px 8px 8px;
    }
    .ant-btn {
      width: 102px;
      height: 45px;
      border-radius: 8px 8px 8px 8px;
    }
  }
}
#sign_area {
  position: relative;
  #Image1 {
    position: absolute;
    top: 0%;
    left: 0%;
  }

  #Image2 {
    position: absolute;
    top: 20%;
    left: 20%;
  }
}
/deep/.ant-spin-nested-loading {
  height: 65px;
}
</style>

<template>
  <div class="assessment-materials-component">
    <div class="materials-files">
      <!-- <div class="table-header">
        <div class="header-actions">
          <a-button v-if="showDownload" type="primary" icon="download" @click="handleBatchDownload" :disabled="materialData.length === 0">
            打包下载
          </a-button>
          <template v-if="!disabled">
            <slot name="headerActions"></slot>
          </template>
        </div>
      </div> -->

      <a-table
        :columns="materialColumns"
        :dataSource="materialData"
        :pagination="false"
        size="middle"
        :rowKey="(record, index) => index"
      >
        <template slot="index" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="action" slot-scope="text, record, index">
          <a @click="handleViewMaterial(record)">查看</a>
          <a-divider type="vertical" />
          <a v-if="!disabled" @click="handleDeleteMaterial(record, index)">删除</a>
          <a-divider type="vertical" v-if="!disabled" />
          <a @click="handleDownloadMaterial(record)">下载</a>
        </template>
      </a-table>

      <div class="add-material-btn" v-if="!disabled">
        <a-upload
          name="file"
          style="width: 100%;"
          :multiple="multiple"
          :fileList="[]"
          :showUploadList="false"
          :beforeUpload="beforeUploadCheck"
          :customRequest="customUpload"
          accept=".pdf,.jpg,.png,.rar"
          @change="handleFileChange"
        >
          <a-button type="dashed" style="width: 100%; margin-top: 8px">
            <a-icon type="plus" /> 添加材料 (支持PDF、JPG、PNG格式、RAR格式)
          </a-button>
        </a-upload>
      </div>
    </div>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/fileUtils'

export default {
  name: 'AssessmentMaterialsComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    showDownload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    uploadUrl: {
      type: String,
      default: '/sysFileInfo/uploadOss'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    maxFileCount: {
      type: Number,
      default: 10
    },
    recordId: {
      type: [String, Number],
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      materialData: [],
      materialColumns: [
        { title: '序号', dataIndex: 'index', width: 80, scopedSlots: { customRender: 'index' } },
        { title: '材料名称', dataIndex: 'fileOriginName', width: '60%' },
        { title: '操作', key: 'action', width: 180, scopedSlots: { customRender: 'action' } }
      ]
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initMaterialData(newVal);
        } else {
          this.materialData = [];
          this.fileList = [];
        }
      }
    }
  },
  methods: {
    initMaterialData(materials) {
      // 初始化材料数据
      this.materialData = materials.map((material, index) => {
        return {
          ...material,
          key: material.id || index
        };
      });

      // 更新内部文件列表
      this.fileList = materials.map((material, index) => {
        return {
          uid: material.id || `-${index}`,
          name: material.fileOriginName || '未命名文件',
          status: 'done',
          url: material.filePath,
          id: material.id
        };
      });
    },

    beforeUploadCheck(file) {
      // 检查文件类型
      const acceptedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      const isAcceptedType = acceptedTypes.includes(file.type);

      // 检查文件扩展名
      const fileName = file.name.toLowerCase();
      const isAcceptedExt = fileName.endsWith('.pdf') || fileName.endsWith('.jpg') ||
                            fileName.endsWith('.png') || fileName.endsWith('.doc') ||
                            fileName.endsWith('.docx');

      if (!isAcceptedType || !isAcceptedExt) {
        this.$message.error('只能上传PDF、JPG、PNG、DOC格式的文件!');
        return false; // 阻止上传
      }

      // 检查文件大小
      const isLt5M = file.size / 1024 / 1024 < 20;
      if (!isLt5M) {
        this.$message.error('文件大小不能超过20MB!');
        return false;
      }

      return true;
    },

    customUpload({ file, onProgress, onSuccess, onError }) {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);

      // 创建上传key，用于消息提示
      const uploadKey = `upload_${file.uid}`;
      this.$message.loading({ content: `正在上传文件：${file.name}`, key: uploadKey, duration: 0 });

      // 使用this.$http上传文件
      this.$http.post(this.uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        // 进度回调
        onUploadProgress: progressEvent => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({ percent }, file);
        }
      })
      .then(response => {
        // 上传成功处理
        this.$message.success({ content: `${file.name} 上传成功`, key: uploadKey, duration: 2 });

        // 服务器返回的响应数据
        const serverData = response.data?.data || response.data;

        // 如果服务器返回了有效数据，立即添加到materialData
        if (serverData) {
          // 构建新的材料数据
          const newMaterial = {
            id: serverData.id,
            fileOriginName: serverData.fileOriginName,
            filePath: serverData.filePath,
            fileSuffix: serverData.fileSuffix || this.getFileSuffix(serverData.fileOriginName),
            key: serverData.id
          };

          // 直接更新材料数据
          this.materialData = [...this.materialData, newMaterial];

          // 添加到文件列表跟踪
          this.fileList.push({
            uid: serverData.id,
            name: serverData.fileOriginName,
            status: 'done',
            url: serverData.filePath,
            id: serverData.id
          });

          // 发送变更通知
          this.emitChange();
        }

        // 构造上传成功的response格式
        const successResponse = {
          data: serverData,
          status: 'done'
        };

        onSuccess(successResponse, file);
      })
      .catch(error => {
        // 上传失败处理
        this.$message.error({ content: `${file.name} 上传失败: ${error.message}`, key: uploadKey, duration: 3 });
        onError(error, null, file);
      });
    },

    getFileSuffix(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      return lastDotIndex > -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
    },

    handleFileChange(info) {
      // 处理上传状态变化
      const file = info.file;

      // 调试信息
      if (file.status === 'done') {
        console.log('文件上传完成：', file.name);
      } else if (file.status === 'error') {
        console.log('文件上传失败：', file.name);
      }
    },

    handleViewMaterial(record) {
      if (!record.filePath) {
        this.$message.warning('文件链接不存在，无法查看');
        return;
      }

      // 创建loading效果
      const loadingKey = `viewMaterial_${record.id}`;
      this.$message.loading({ content: `正在加载文件：${record.fileOriginName}`, key: loadingKey, duration: 0 });

      // 根据文件类型选择不同的查看方式
      if (record.fileSuffix && record.fileSuffix.toLowerCase() === 'pdf') {
        if (process.env.NODE_ENV === 'development') {
          this.$imageViewer.view(
            [record.filePath],
            0,
            '/pdfJsLib/web/viewer.html'
          )
        } else {
          this.$imageViewer.view(
            [record.filePath],
            0,
            '/pdfJsLib/web/viewer.html'
          )
        }
      } else if (['jpg', 'jpeg', 'png'].includes(record.fileSuffix?.toLowerCase())) {
        // 对于图片类型直接查看
        this.$imageViewer.view([record.filePath], 0)
      } else {
        // 其他类型尝试下载
        this.handleDownloadMaterial(record);
      }

      // 关闭loading消息
      setTimeout(() => {
        this.$message.success({ content: `文件已打开：${record.fileOriginName}`, key: loadingKey, duration: 2 });
      }, 1000);
    },

    handleDeleteMaterial(record, index) {
      if (this.disabled) return;

      this.$confirm({
        title: '确定要删除该材料吗？',
        content: '删除后无法恢复',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          // 从材料数据中删除
          this.materialData = this.materialData.filter((_, i) => i !== index);

          // 从文件列表中删除
          if (record.id) {
            this.fileList = this.fileList.filter(file => file.id !== record.id);
          }

          // 发送变更通知
          this.emitChange();

          this.$message.success(`已删除材料：${record.fileOriginName}`);
        }
      });
    },

    handleDownloadMaterial(record) {
      if (!record || !record.filePath) {
        this.$message.warning('无法下载文件，文件路径不存在');
        return;
      }

      // 创建loading效果
      const downloadKey = `download_${record.id}`;
      this.$message.loading({ content: `正在下载文件：${record.fileOriginName}`, key: downloadKey, duration: 0 });

      // 调用下载接口
      this.$http.get('/investigationInfo/downloadFile', {
        params: { filePath: record.filePath, fileOriginName: record.fileOriginName, ...record },
        responseType: 'blob'
      })
      .then(response => {
        // 使用通用下载方法
        downloadFile(response);
        this.$message.success({ content: `${record.fileOriginName} 下载成功`, key: downloadKey, duration: 2 });
      })
      .catch(error => {
        console.error('下载失败:', error);
        this.$message.error({ content: `${record.fileOriginName} 下载失败，请稍后重试`, key: downloadKey, duration: 3 });
      });
    },

    handleBatchDownload() {
      // 如果没有文件，提示用户
      if (this.materialData.length === 0) {
        this.$message.warning('没有可下载的文件');
        return;
      }

      // 检查是否有recordId
      if (!this.recordId) {
        // 如果没有recordId，则逐个下载文件
        this.materialData.forEach(material => {
          this.handleDownloadMaterial(material);
        });
        return;
      }

      // 创建loading效果
      const downloadKey = 'batch_download';
      this.$message.loading({ content: '正在准备下载文件...', key: downloadKey, duration: 0 });

      // 调用打包下载接口
      this.$http.get('/investigationInfo/batchDownLoad', {
        params: { bizId: this.recordId, bizType: this.bizType, fileIds: this.materialData.map(item => item.id).join(',') },
        responseType: 'blob'
      })
      .then(response => {
        // 使用通用下载方法
        downloadFile(response);
        this.$message.success({ content: '文件下载成功', key: downloadKey, duration: 2 });
      })
      .catch(error => {
        console.error('下载失败:', error);
        this.$message.error({ content: '文件下载失败，请稍后重试', key: downloadKey, duration: 3 });
      });
    },

    emitChange() {
      // 将当前材料数据发送给父组件
      this.$emit('change', this.materialData);
      this.$emit('input', this.materialData);
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-upload.ant-upload-select{
  display: block;
  width: 100%;
}

/deep/.ant-table-small > .ant-table-content > .ant-table-body{
  margin:0;
}

/deep/.ant-table-small {
  border-radius: 4px;
}

/deep/.ant-table-row {
  transition: all 0.3s;
  &:hover {
    background-color: #e6f7ff;
  }
}

.assessment-materials-component {
  margin-bottom: 24px;

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;

    .header-actions {
      .ant-btn {
        min-width: 100px;
      }
    }
  }

  .add-material-btn {
    margin-top: 8px;
  }
}
</style>

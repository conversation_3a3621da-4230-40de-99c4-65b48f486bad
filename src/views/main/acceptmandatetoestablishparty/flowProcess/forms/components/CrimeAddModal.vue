<template>
  <a-modal
    :visible="visible"
    :title="title"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
  >
    <div v-if="visible" style="height: 60vh;" class="filter-tree-wrap">
      <a-input
        v-model="filterText"
        placeholder="输入关键字进行过滤"
      />
      <div class="filter-tree">
        <Tree
          ref="tree"
          :data="treeData"
          :props="defaultProps"
          show-checkbox
          node-key="id"
          default-expand-all
          :filter-node-method="filterNode"
          v-loading="loading"
        />
      </div>
    </div>
  </a-modal>
</template>

<script>
import { Tree } from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import { sysDictDataTree } from '@/api/modular/system/dictDataManage'

export default {
  name: 'CrimeAddModal',
  components: {
    Tree
  },
  props: {
    visible: {
      type: <PERSON>olean,
      default: false
    },
    title: {
      type: String,
      default: '添加罪名'
    },
    confirmLoading: {
      type: Boolean,
      default: false
    },
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      filterText: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      loading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    visible(val) {
      if (val) {
        this.getTreeData()
        if (this.initialValues.crimeName) {
          // 编辑模式：设置选中节点
          this.$nextTick(() => {
            const node = this.findNode(this.initialValues.crimeValue)
            if (node) {
              this.$refs.tree.setChecked(node, true)
            }
          })
        }
      }
    }
  },
  methods: {
    // 获取树形数据
    async getTreeData() {
      this.loading = true
      try {
        const res = await sysDictDataTree({ dictTypeId: '1681127536492331009' })
        if (res) {
          // 直接使用返回的数据，不需要转换结构
          this.treeData = res.data
        }
      } catch (error) {
        console.error('获取罪名数据失败:', error)
        this.$message.error('获取罪名数据失败')
      } finally {
        this.loading = false
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.title.indexOf(value) !== -1
    },
    findNode(value) {
      const loop = (data) => {
        for (const item of data) {
          if (item.value === value) return item
          if (item.children) {
            const result = loop(item.children)
            if (result) return result
          }
        }
        return null
      }
      return loop(this.treeData)
    },
    handleOk() {
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      if (checkedNodes.length === 0) {
        this.$message.warning('请至少选择一个罪名')
        return
      }

      // 获取节点的父节点
      const findParentNode = (pid) => {
        return this.treeData.find(item => item.id === pid)
      }

      // 构造每个节点的完整信息
      const nodeInfos = checkedNodes.map(node => ({
        crimeName: node.title,
        crimeValue: node.value,
        crimeType: findParentNode(node.pid)?.title || '',
        crimeDetail: node.title
      }))

      // 构造返回数据
      const result = {
        crimeName: nodeInfos.map(info => info.crimeName).join('、'),
        crimeValue: nodeInfos.map(info => info.crimeValue),
        crimeType: nodeInfos.map(info => info.crimeType).join('、'),
        crimeDetail: nodeInfos.map(info => info.crimeDetail).join('、')
      }

      this.$emit('ok', result)
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
.filter-tree-wrap {
  height: 60vh;
  overflow: hidden;
  flex-flow: column;
  display: flex;

  .filter-tree {
    flex: 1;
    overflow: auto;
    margin-top: 16px;
  }
}
</style>

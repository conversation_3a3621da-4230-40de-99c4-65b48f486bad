<template>
  <a-modal
    title="调查评估笔录表"
    width="80%"
    :visible="modalVisible"
    :footer="null"
    @ok="handleModalOk"
    @cancel="handleModalCancel"
  >
    <sh-cloud-sign v-model="docId" :showUpload="true" :showCloudSign="true" :signParams="signParams" />
  </a-modal>
</template>
<script>
export default {
  data() {
    return {
      modalVisible: false,
      docId: 'doc123',
      signParams: {
        // 签章配置参数
      }
    }
  },
  methods: {
    open() {
      this.modalVisible = true
    },
    handleModalOk() {
      this.modalForm.validateFieldsAndScroll((err, values) => {
        if (!err) {
          this.modalVisible = false
        }
      })
    },
    handleModalCancel() {
      this.modalVisible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-modal-body {
  height: 75vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 12px;
  }
  .question-box {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}
</style>

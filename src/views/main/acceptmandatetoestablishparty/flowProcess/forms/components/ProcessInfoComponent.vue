<template>
  <div class="process-info-container">
    <div class="table-header" v-if="!disabled">
      <div class="header-actions">
        <a-button
          type="danger"
          icon="delete"
          :disabled="!hasSelected"
          @click="onBatchDelete"
          style="margin-right: 10px"
        >
          批量删除
        </a-button>
        <span v-if="hasSelected" style="margin-right: 10px;">
          已选择 {{ selectedRowKeys.length }} 项
        </span>
        <a-button type="primary" icon="plus" @click="handleAdd">
          新增调查过程
        </a-button>
      </div>
    </div>

    <a-table
      :columns="tableColumns"
      :dataSource="dataSource"
      :pagination="false"
      size="small"
      bordered
      :locale="{emptyText: '暂无调查过程信息'}"
      :rowKey="record => record.id"
      :rowSelection="!disabled ? { selectedRowKeys: selectedRowKeys, onChange: onSelectChange } : null"
    >
      <template slot="index" slot-scope="text, record, index">
        <span>{{ index + 1 }}</span>
      </template>
      <template slot="date" slot-scope="text">
        <span>{{ text }}</span>
      </template>
      <template slot="action" slot-scope="text, record">
        <template v-if="!disabled">
          <a type="link" @click="() => handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a type="link" style="color: #ff4d4f" @click="() => handleDelete(record)">删除</a>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :title="isEdit ? '编辑调查过程信息' : '新增调查过程信息'"
      :visible="modalVisible"
      :confirmLoading="confirmLoading"
      @ok="handleModalOk"
      :centered="true"
      :width="800"
      @cancel="handleModalCancel"
    >
      <a-form :form="form" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查日期">
              <a-date-picker
                v-decorator="[
                  'dcsj',
                  { rules: [{ required: true, message: '请选择调查日期' }] }
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="被调查人姓名">
              <a-input
                v-decorator="[
                  'bdcrxm',
                  { rules: [{ required: true, message: '请输入被调查人姓名' }] }
                ]"
                placeholder="请输入被调查人姓名"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="被调查人与被告人(罪犯)关系">
              <a-input
                v-decorator="[
                  'gx',
                  { rules: [{ required: true, message: '请输入关系' }] }
                ]"
                placeholder="请输入关系"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查地点">
              <a-input
                v-decorator="[
                  'dd',
                  { rules: [{ required: true, message: '请输入调查地点' }] }
                ]"
                placeholder="请输入调查地点"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查事项">
              <a-input
                v-decorator="[
                  'noticeTitle',
                  { initialValue: `${this.$options.filters['dictType']('nsysqjzrylx', detailData.psnType)}${detailData.groups?.correctionObjName || ''}实施社区矫正的调查评估`, rules: [{ required: true, message: '请输入调查事项' }] }
                ]"
                placeholder="请输入调查事项"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'ProcessInfoComponent',
  props: {
    value: {
      type: Array,
      default: () => []
    },

    detailData: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedRowKeys: [],
      dataSource: [],
      modalVisible: false,
      confirmLoading: false,
      isEdit: false,
      currentRecord: null,
      form: this.$form.createForm(this),
      columns: [
        { title: '序号', dataIndex: '', width: 50, key: 'index', align: 'center', scopedSlots: { customRender: 'index' } },
        { title: '调查日期', align: 'center', dataIndex: 'dcsj', key: 'dcsj', scopedSlots: { customRender: 'date' } },
        { title: '被调查人姓名', align: 'center', dataIndex: 'bdcrxm', key: 'bdcrxm' },
        { title: '被调查人与被告人(罪犯)关系', align: 'center', dataIndex: 'gx', key: 'gx' },
        { title: '调查地点', align: 'center', dataIndex: 'dd', key: 'dd' },
        { title: '调查事项', align: 'center', dataIndex: 'noticeTitle', key: 'noticeTitle', ellipsis: true },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, align: 'center' }
      ]
    }
  },
  computed: {
    hasSelected() {
      return this.selectedRowKeys.length > 0;
    },
    tableColumns() {
      const baseColumns = [
        { title: '序号', dataIndex: '', width: 50, key: 'index', align: 'center', scopedSlots: { customRender: 'index' } },
        { title: '调查日期', align: 'center', dataIndex: 'dcsj', key: 'dcsj', scopedSlots: { customRender: 'date' } },
        { title: '被调查人姓名', align: 'center', dataIndex: 'bdcrxm', key: 'bdcrxm' },
        { title: '被调查人与被告人(罪犯)关系', align: 'center', dataIndex: 'gx', key: 'gx' },
        { title: '调查地点', align: 'center', dataIndex: 'dd', key: 'dd' },
        { title: '调查事项', align: 'center', dataIndex: 'noticeTitle', key: 'noticeTitle', ellipsis: true }
      ];

      if (!this.disabled) {
        baseColumns.push({ title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, align: 'center' });
      }

      return baseColumns;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initData(newVal);
      },
      immediate: true
    }
  },
  methods: {
    initData(data) {
      if (Array.isArray(data) && data.length > 0) {
        // 转换日期格式并确保ID存在
        this.dataSource = data.map((item, index) => ({
          id: item.id || `temp_${index}`,
          dcsj: item.dcsj || '',
          bdcrxm: item.bdcrxm || '',
          gx: item.gx || '',
          dd: item.dd || '',
          noticeTitle: item.noticeTitle || ''
        }));
      } else {
        this.dataSource = [];
      }
    },

    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    handleAdd() {
      this.isEdit = false;
      this.currentRecord = null;
      this.form.resetFields();
      this.modalVisible = true;
    },

    handleEdit(record) {
      this.isEdit = true;
      this.currentRecord = record;
      this.modalVisible = true;

      // 设置表单值
      this.$nextTick(() => {
        this.form.setFieldsValue({
          dcsj: record.dcsj ? moment(record.dcsj) : null,
          bdcrxm: record.bdcrxm,
          gx: record.gx,
          dd: record.dd,
          noticeTitle: record.noticeTitle
        });
      });
    },

    handleDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条调查过程信息吗？',
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          const newData = this.dataSource.filter(item => item.id !== record.id);
          this.dataSource = newData;
          this.emitChange();
        }
      });
    },

    onBatchDelete() {
      if (!this.selectedRowKeys.length) return;

      this.$confirm({
        title: '批量删除',
        content: `确定要删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          this.dataSource = this.dataSource.filter(item => !this.selectedRowKeys.includes(item.id));
          this.selectedRowKeys = [];
          this.emitChange();
        }
      });
    },

    handleModalOk() {
      this.form.validateFields((err, values) => {
        if (err) return;

        this.confirmLoading = true;

        // 处理日期格式
        const formattedValues = {
          ...values,
          dcsj: values.dcsj ? values.dcsj.format('YYYY-MM-DD') : null
        };

        if (this.isEdit && this.currentRecord) {
          // 编辑模式
          const index = this.dataSource.findIndex(item => item.id === this.currentRecord.id);
          if (index !== -1) {
            this.dataSource[index] = {
              ...this.currentRecord,
              ...formattedValues
            };
          }
        } else {
          // 新增模式
          const newRecord = {
            id: `temp_${Date.now()}`,
            ...formattedValues
          };
          this.dataSource = [...this.dataSource, newRecord];
        }

        this.emitChange();

        // 关闭弹窗
        setTimeout(() => {
          this.confirmLoading = false;
          this.modalVisible = false;
        }, 300);
      });
    },

    handleModalCancel() {
      this.modalVisible = false;
    },

    emitChange() {
      // 直接发送当前数据格式，不需要转换
      this.$emit('input', this.dataSource);
      this.$emit('change', this.dataSource);
      this.$emit('onChange', this.dataSource);
      this.$emit('onchange', this.dataSource);
    }
  }
}
</script>

<style lang="less" scoped>
.process-info-container {
  width: 100%;
  margin-bottom: 24px;

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;

    .header-actions {
      display: flex;
      align-items: center;
      .ant-btn {
        min-width: 100px;
      }
    }
  }

  /deep/.ant-table-small > .ant-table-content > .ant-table-body {
    margin: 0;
  }

  /deep/.ant-table-small {
    border-radius: 4px;
  }

  /deep/.ant-table-row {
    transition: all 0.3s;
    &:hover {
      background-color: #e6f7ff;
    }
  }
}
</style>

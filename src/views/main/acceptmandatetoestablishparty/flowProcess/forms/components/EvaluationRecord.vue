<template>
  <a-drawer title="调查评估笔录" width="80%" :visible="modalVisible" @close="handleModalCancel(false)">
    <div
      v-if="!disabled"
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1
      }"
    >
      <a-button @click="handleModalCancel(false)">
        取消
      </a-button>
      <a-button type="primary" @click="handleModalOk(1)">
        暂存
      </a-button>
      <a-button type="primary" @click="handleModalOk(2)">
        提交
      </a-button>
    </div>
    <a-form :form="evaluationForm">
      <FixedContent ref="FixedContent" :detailData="detailData" :correctionType="correctionType" />
      <a-row class="time-row">
        <a-form-item label="问题1" :labelCol="labelCol1" :wrapperCol="wrapperCol1" class="question-item">
          我们是
          <a-form-item label="">
            <a-input v-decorator="['question1Sfj', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
          司法局
          <a-form-item label="">
            <a-input v-decorator="['question1Sfs', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
          的工作人员（出示证件），
          依据《中华人民共和国社区矫正法》、《中华人民共和国社区矫正法实施办法》及《浙江省社区矫正调查评估办法（试行）》等相关规定，受
          <a-form-item>
            <a-input v-decorator="['question1Delegate', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
          的委托，依法对
          <a-form-item>
            <a-input v-decorator="['personalInfo.name', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
          是否适用社区矫正的情况对你进行调查，你应当如实回答我们的询问并协助调查，不得提供虚假证言，不得伪造、隐匿、毁灭证据，否则将承担法律责任。你有权就被询问的事项自行提供书面材料，有权核对调查笔录，对记载有误或遗漏之处，可提出更正或补充意见。如所回答的问题涉及国家或商业秘密和个人隐私，我们将予以保密。希望你如实反映，是否已听清楚？
        </a-form-item>
        <a-form-item label="答" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
          <a-input v-decorator="['question1Answer', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
        <a-form-item :label="`问题2`" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
          你对本次调查的工作人员需不需要提出回避申请？
        </a-form-item>
        <a-form-item label="答" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
          <a-input v-decorator="['question2Answer', { rules: [{ required: true, message: '请输入' }] }]" />
        </a-form-item>
      </a-row>
      <a-row v-for="(item, index) in questionData" :key="item.uniqueKey" class="question-box">
        <div style="flex:1;">
          <a-form-item :label="`问题${index + 3}`" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
            <span
              v-if="!item.addType && item.topicName"
              style="width: 96%;display: inline-block;"
            >{{ item.topicName }}</span
            >
            <a-input
              v-if="item.addType === '1' || (item.itemList && item.itemList.length === 0)"
              placeholder="请输入问题"
              style="width: 96%;"
              v-decorator="[`topicName${index}`, { rules: [{ required: true, message: '请输入问题' }] }]"
            />
            <a-button
              shape="circle"
              icon="minus"
              size="small"
              v-if="item.addType === '1' || (item.itemList && item.itemList.length === 0)"
              @click="deleteItems(index)"
              class="delete-icon"
            ></a-button>
          </a-form-item>
          <a-form-item
            v-if="
              !item.itemList || item.itemList.length === 0 || (item.itemList.length === 1 && !item.itemList[0].content)
            "
            label="答"
            :labelCol="labelCol1"
            :wrapperCol="wrapperCol1"
          >
            <a-input
              placeholder="请回答问题"
              v-decorator="[`userAnswer${index}`, { rules: [{ required: true, message: '请回答问题' }] }]"
            />
          </a-form-item>
          <div v-else>
            <a-form-item label="答" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-input
                placeholder="请回答问题"
                v-decorator="[
                  `userAnswer${index}`,
                  { rules: [{ required: item.topicName.indexOf('调查人员填写') === -1, message: '请回答问题' }] }
                ]"
              />
            </a-form-item>
            <a-form-item label="选项" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a-radio-group v-decorator="[`userSelectId${index}`, { rules: [{ required: true, message: '请选择' }] }]">
                <a-radio v-for="(option, i) in item.itemList" :key="i" :value="option.id">{{ option.content }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>
        </div>
        <a-button shape="circle" icon="plus" @click="addQustion(index)" size="small" style="margin-top: 8px;">
        </a-button>
      </a-row>
    </a-form>
  </a-drawer>
</template>
<script>
import FixedContent from './FixedContent.vue'
import {
  investigationInfoCommit,
  investigationInfoTranscriptById,
  investigationInfoTranscriptFillInfoById
} from '@/api/modular/main/unitReceiveForm/unitReceiveForm'
export default {
  props: {
    correctionType: {
      type: String,
      default: ''
    }
  },
  components: {
    FixedContent
  },
  data() {
    return {
      modalVisible: false,
      disabled: false,
      evaluationForm: this.$form.createForm(this),
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelCol1: { span: 2 },
      wrapperCol1: { span: 22 },
      detailData: null,
      questionData: []
    }
  },
  methods: {
    generateUniqueKey() {
      return (
        Date.now().toString(36) +
        Math.random()
          .toString(36)
          .substr(2)
      )
    },
    deleteItems(index) {
      const originalFourthQuestion = this.evaluationForm.getFieldsValue()
      this.questionData.splice(index, 1)
      this.detailData.paperMaintenanceParam.topicList.splice(index, 1)
      // 更新剩余问题的 key 值
      this.questionData = this.questionData.map((item, i) => {
        item.uniqueKey = this.generateUniqueKey()
        return item
      })
      console.log(this.detailData, originalFourthQuestion)
      setTimeout(() => {
        this.questionData.forEach((item, i) => {
          // 小标小于等于当前点击的问题下标就赋值为原来的值
          if (i < index) {
            this.evaluationForm.setFieldsValue({
              [`userAnswer${i}`]: originalFourthQuestion[`userAnswer${i}`],
              [`userSelectId${i}`]: originalFourthQuestion[`userSelectId${i}`]
            })
          } else {
            this.evaluationForm.setFieldsValue({
              [`userAnswer${i}`]: originalFourthQuestion[`userAnswer${i + 1}`]
                ? originalFourthQuestion[`userAnswer${i + 1}`]
                : '',
              [`topicName${i}`]: originalFourthQuestion[`topicName${i + 1}`]
                ? originalFourthQuestion[`topicName${i + 1}`]
                : '',
              [`userSelectId${i}`]: originalFourthQuestion[`userSelectId${i + 1}`]
                ? originalFourthQuestion[`userSelectId${i + 1}`]
                : ''
            })
          }
        })
      }, 100)
      this.questionData.forEach((item, i) => {
        this.evaluationForm.setFieldsValue({ [`userAnswer${i}`]: item.userAnswer })
      })
      console.log(index, this.questionData, this.detailData.paperMaintenanceParam.topicList)
    },
    addQustion(index) {
      const originalFourthQuestion = this.evaluationForm.getFieldsValue()
      // const item = this.questionData[index]
      console.log(this.questionData, originalFourthQuestion)
      this.questionData.splice(index + 1, 0, {
        topicName: '',
        userAnswer: '',
        addType: '1',
        userSelectId: '',
        itemList: [],
        uniqueKey: this.generateUniqueKey() // 生成唯一 key
      })
      this.detailData.paperMaintenanceParam.topicList.splice(index + 1, 0, {
        topicName: '',
        userAnswer: '',
        addType: '1',
        userSelectId: '',
        itemList: []
      })
      this.questionData = this.questionData.map(item => {
        return {
          ...item,
          uniqueKey: this.generateUniqueKey() // 生成唯一 key
        }
      })
      setTimeout(() => {
        this.questionData.forEach((item, i) => {
          if (index + 1 === i) {
            this.evaluationForm.setFieldsValue({
              [`userAnswer${i}`]: '',
              [`userSelectId${i}`]: '',
              [`topicName${i}`]: ''
            })
            // 小标小于等于当前点击的问题下标就赋值为原来的值
          } else if (i <= index) {
            this.evaluationForm.setFieldsValue({
              [`userAnswer${i}`]: originalFourthQuestion[`userAnswer${i}`],
              [`userSelectId${i}`]: originalFourthQuestion[`userSelectId${i}`]
            })
          } else {
            this.evaluationForm.setFieldsValue({
              [`userAnswer${i}`]: originalFourthQuestion[`userAnswer${i - 1}`]
                ? originalFourthQuestion[`userAnswer${i - 1}`]
                : '',
              [`topicName${i}`]: originalFourthQuestion[`topicName${i - 1}`]
                ? originalFourthQuestion[`topicName${i - 1}`]
                : '',
              [`userSelectId${i}`]: originalFourthQuestion[`userSelectId${i - 1}`]
                ? originalFourthQuestion[`userSelectId${i - 1}`]
                : ''
            })
          }
        })
        console.log(this.evaluationForm.getFieldsValue(), 2222)
      }, 100)
    },
    open(record) {
      this.modalVisible = true

      investigationInfoTranscriptById({
        id: record.id
      }).then(res => {
        if (res.success) {
          this.detailData = res.data
          this.questionData = res.data.paperMaintenanceParam.topicList.map(item => ({
            ...item,
            uniqueKey: this.generateUniqueKey() // 生成唯一 key
          }))
          const dictDataOptions = this.$options.filters['dictData']('mz')
          const educationOptions = this.$options.filters['dictData']('SQJZ_WHCD')
          console.log('dictDataOptions', dictDataOptions, educationOptions)
          this.$nextTick(() => {
            this.evaluationForm.setFieldsValue(res.data.investigationRecordParam)
            this.questionData.forEach((item, index) => {
              this.evaluationForm.setFieldsValue({ [`userAnswer${index}`]: item.userAnswer })
              this.evaluationForm.setFieldsValue({ [`userSelectId${index}`]: item.userSelectId })
              this.evaluationForm.setFieldsValue({ [`topicName${index}`]: item.topicName })
            })
            this.detailData.investigationRecordParam.investigatorList.forEach((item, index) => {
              this.evaluationForm.setFieldsValue({
                [`orgNames${index}`]: item.sjName ? item.sjName : item.orgNames,
                [`nickName${index}`]: item.nickName
              })
            })
            // if (this.detailData.paperType !== 'BLLX06') {
            //   this.evaluationForm.setFieldsValue({
            //     investigatedName: ''
            //   })
            // }
          })
          if (record.progress === '0') {
            investigationInfoTranscriptFillInfoById({ id: record.id }).then(result => {
              if (result.success) {
                // this.detailData = res.data
                this.$nextTick(() => {
                  // 处理性别和民族
                  const filterData3 =
                    result.data.personalInfo && result.data.personalInfo.nationality
                      ? dictDataOptions.filter(item => item.code === result.data.personalInfo.nationality)
                      : []
                  // 处理学历
                  const educationLevelName =
                    result.data.personalInfo && result.data.personalInfo.educationLevel
                      ? educationOptions.filter(item => item.code === result.data.personalInfo.educationLevel)
                      : []
                  this.evaluationForm.setFieldsValue(result.data)
                  this.evaluationForm.setFieldsValue({
                    investigatedGender:
                      result.data.investigatedGender === '1'
                        ? '男'
                        : result.data.investigatedGender === '2'
                        ? '女'
                        : '',
                    personalInfo: {
                      gender:
                        result.data.personalInfo.gender === '1'
                          ? '男'
                          : result.data.personalInfo.gender === '2'
                          ? '女'
                          : '',
                      nationality: filterData3.length > 0 ? filterData3[0].name : '',
                      educationLevel: educationLevelName.length > 0 ? educationLevelName[0].name : ''
                    }
                  })
                  if (this.detailData.paperType === 'BLLX06') {
                    this.evaluationForm.setFieldsValue({
                      relationship: '本人'
                    })
                  }
                  if (this.detailData.paperType !== 'BLLX06') {
                    this.evaluationForm.setFieldsValue({
                      investigatedName: ''
                    })
                  }
                })
                console.log('res.data', result.data)
              }
            })
          }
        }
      })
    },
    handleModalOk(type) {
      console.log('保存类型:', type)
      // 0 未填写 1 暂存 2 已填写
      if (type === 1) {
        // 检查必要的数据是否存在
        if (!this.detailData || !this.detailData.paperMaintenanceParam) {
          this.$message.error('数据不完整，无法保存')
          return
        }

        const values = this.evaluationForm.getFieldsValue()
        console.log('values', values, this.questionData, this.detailData)

        try {
          // 确保topicList数组存在且长度一致
          if (
            !this.detailData.paperMaintenanceParam.topicList ||
            this.detailData.paperMaintenanceParam.topicList.length !== this.questionData.length
          ) {
            this.detailData.paperMaintenanceParam.topicList = Array(this.questionData.length)
              .fill()
              .map(() => ({}))
          }

          this.questionData.forEach((item, index) => {
            if (!this.detailData.paperMaintenanceParam.topicList[index]) {
              this.detailData.paperMaintenanceParam.topicList[index] = {}
            }
            this.detailData.paperMaintenanceParam.topicList[index].userAnswer = values[`userAnswer${index}`]
            this.detailData.paperMaintenanceParam.topicList[index].userSelectId = values[`userSelectId${index}`]
            if (item.addType === '1' || (item.itemList && item.itemList.length === 0)) {
              this.detailData.paperMaintenanceParam.topicList[index].topicName = values[`topicName${index}`]
              this.detailData.paperMaintenanceParam.topicList[index].addType = '1'
            }
          })

          this.detailData.progress = type
          values.correctionType = this.correctionType

          // 检查investigatorList是否存在
          if (this.detailData.investigationRecordParam && this.detailData.investigationRecordParam.investigatorList) {
            this.detailData.investigationRecordParam.investigatorList.forEach((item, index) => {
              this.detailData.investigationRecordParam.investigatorList[index].orgNames = values[`orgNames${index}`]
              this.detailData.investigationRecordParam.investigatorList[index].nickName = values[`nickName${index}`]
            })
          }

          this.detailData.investigationRecordParam = { ...this.detailData.investigationRecordParam, ...values }

          // 设置modalVisible为false移到接口调用成功后
          investigationInfoCommit({
            ...this.detailData
          })
            .then(res => {
              if (res.success) {
                this.$message.success('暂存成功')
                this.modalVisible = false
                this.$emit('close')
                this.$emit('refresh', res.data || [])
                this.handleModalCancel(true)
              } else {
                this.$message.error(res.message || '暂存失败')
              }
            })
            .catch(error => {
              console.error('暂存请求失败:', error)
              this.$message.error('暂存失败，请稍后重试')
            })
        } catch (error) {
          console.error('暂存处理出错:', error)
          this.$message.error('数据处理出错，请稍后重试')
        }
      } else {
        // 先验证子组件
        this.evaluationForm.validateFields((err, values) => {
          if (!err) {
            try {
              // 检查必要的数据是否存在
              if (!this.detailData || !this.detailData.paperMaintenanceParam) {
                this.$message.error('数据不完整，无法保存')
                return
              }

              console.log('values', values, this.questionData, this.detailData)

              // 确保topicList数组存在且长度一致
              if (
                !this.detailData.paperMaintenanceParam.topicList ||
                this.detailData.paperMaintenanceParam.topicList.length !== this.questionData.length
              ) {
                this.detailData.paperMaintenanceParam.topicList = Array(this.questionData.length)
                  .fill()
                  .map(() => ({}))
              }

              this.questionData.forEach((item, index) => {
                if (!this.detailData.paperMaintenanceParam.topicList[index]) {
                  this.detailData.paperMaintenanceParam.topicList[index] = {}
                }
                this.detailData.paperMaintenanceParam.topicList[index].userAnswer = values[`userAnswer${index}`]
                this.detailData.paperMaintenanceParam.topicList[index].userSelectId = values[`userSelectId${index}`]
                if (item.addType === '1' || (item.itemList && item.itemList.length === 0)) {
                  this.detailData.paperMaintenanceParam.topicList[index].topicName = values[`topicName${index}`]
                  this.detailData.paperMaintenanceParam.topicList[index].addType = '1'
                }
              })

              this.detailData.progress = type
              values.correctionType = this.correctionType

              // 检查investigatorList是否存在
              if (
                this.detailData.investigationRecordParam &&
                this.detailData.investigationRecordParam.investigatorList
              ) {
                this.detailData.investigationRecordParam.investigatorList.forEach((item, index) => {
                  this.detailData.investigationRecordParam.investigatorList[index].orgNames = values[`orgNames${index}`]
                  this.detailData.investigationRecordParam.investigatorList[index].nickName = values[`nickName${index}`]
                })
              }

              this.detailData.investigationRecordParam = { ...this.detailData.investigationRecordParam, ...values }

              // 设置modalVisible为false移到接口调用成功后
              investigationInfoCommit({
                ...this.detailData
              })
                .then(res => {
                  if (res.success) {
                    this.$message.success('提交成功')
                    this.modalVisible = false
                    this.$emit('close')
                    this.$emit('refresh', res.data || [])
                    this.handleModalCancel(true)
                  } else {
                    this.$message.error(res.message || '提交失败')
                  }
                })
                .catch(error => {
                  console.error('提交请求失败:', error)
                  this.$message.error('提交失败，请稍后重试')
                })
            } catch (error) {
              console.error('提交处理出错:', error)
              this.$message.error('数据处理出错，请稍后重试')
            }
          } else {
            // 找到第一个出错的字段
            let firstErrorField = Object.keys(err)[0]
            if (firstErrorField === 'personalInfo') {
              firstErrorField = 'personalInfo.' + Object.keys(err.personalInfo)[0]
            }
            console.log('firstErrorField', firstErrorField, Object.keys(err), this.detailData.investigationRecordParam)
            if (firstErrorField) {
              // 找到对应的表单项元素
              const errorFieldElement = document.getElementById(`${firstErrorField}`)
              if (errorFieldElement) {
                // 滚动到该元素位置
                errorFieldElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center'
                })
                // 聚焦到该元素
                errorFieldElement.focus()
              }
            }
          }
        })
      }
    },
    handleModalCancel(fromSave) {
      if (!fromSave) {
        this.$confirm({
          title: '温馨提示',
          content: '您的编辑内容尚未保存，系统将为您自动暂存数据，确认要离开当前页面吗？',
          okText: '确认离开',
          cancelText: '继续编辑',
          onOk: () => {
            this.handleModalOk(1)
          },
          onCancel: () => {
            // 用户取消关闭，不做任何操作
          }
        })
      } else {
        // 直接关闭页面
        this.modalVisible = false
        this.evaluationForm.resetFields()
        this.questionData = []
      }
    },
    detail(record) {
      this.modalVisible = true
      this.disabled = true
      // this.evaluationForm.setFieldsValue(record)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.ant-drawer-body {
  height: 85vh;
  overflow-y: auto;
  .ant-form-item {
    margin-bottom: 0px;
  }
  .question-box {
    display: flex;
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-input {
      border: 0;
      border-bottom: 1px solid #e8e8e8;
    }
    .ant-input:focus {
      box-shadow: none !important;
    }
    .delete-icon {
      cursor: pointer;
    }
  }
}
.time-row {
  .question-item {
    /deep/.ant-form-item-children {
      display: inline-block;
      .ant-form-item {
        display: inline-block;
        // width: 18%;
      }
    }
  }
  .ant-input {
    border: 0;
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-input:focus {
    box-shadow: none !important;
  }
}
</style>

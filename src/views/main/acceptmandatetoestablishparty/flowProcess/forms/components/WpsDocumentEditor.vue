<template>
  <div>
    <a-modal
      title="WPS文档编辑"
      :visible="visible"
      @cancel="handleCancel"
      :footer="null"
      :maskClosable="false"
      destroyOnClose
      width="100%"
      wrapClassName="wps-editor-modal"
    >
      <div class="wps-editor-container" id="office">
        <!-- WPS编辑器加载状态 -->
        <div v-if="loading" class="wps-loading-mask">
          <a-spin tip="正在加载文档编辑器..." size="large" />
        </div>
        <!-- WPS编辑器会在此渲染 -->
      </div>
    </a-modal>
  </div>
</template>

<script>
// 引入WPS SDK
import OpenSDK from '@/assets/wpsSDK/open-jssdk-v0.0.7.es.js'

export default {
  name: 'WpsDocumentEditor',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 文档对象
    document: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      wpsInstance: null, // WPS实例对象
      isInitializing: false, // 防止重复初始化的标志
      loading: false, // 加载状态
      error: null // 错误信息
    }
  },
  watch: {
    // 只监听visible变化
    visible(newVal) {
      if (newVal && this.document && this.document.id && !this.isInitializing) {
        this.$nextTick(() => {
          this.initWpsEditor();
        });
      }
    }
  },
  methods: {
    // 初始化WPS编辑器
    async initWpsEditor() {
      await this.$nextTick();
      // 防止重复初始化
      if (this.isInitializing || this.wpsInstance) {
        console.log('WPS编辑器已在初始化或已存在实例，跳过');
        return;
      }

      this.isInitializing = true;
      this.loading = true;
      this.error = null;

      try {
        // 从接口获取文档在线预览地址
        const response = await this.$http.get('/wps/edit', {
          params: {
            fieldId: this.document.id,
            filedType: this.document.fileSuffix
          }
        });

        if (!response || !response.success) {
          throw new Error(response?.message || '获取文档预览地址失败');
        }

        // 获取接口返回的文档URL
        const docUrl = response.data?.data?.link;

        if (!docUrl) {
          throw new Error('获取到的文档URL为空');
        }

        console.log('获取到文档预览地址:', docUrl);

        // 初始化WPS SDK
        const instance = OpenSDK.config({
          url: docUrl,
          mount: document.querySelector('#office')
        });

        console.log('WPS实例初始化成功:', instance);
        this.wpsInstance = instance;

        // 监听文档加载完成事件
        this.wpsInstance.on('ready', () => {
          console.log('WPS编辑器加载完成');
          this.loading = false;
        });

        // 监听文档保存事件
        this.wpsInstance.on('save', this.handleDocSave);

        // 监听错误事件
        this.wpsInstance.on('error', (error) => {
          console.error('WPS编辑器错误:', error);
          this.error = error.message || '文档编辑器发生错误';
          this.$message.error(this.error);
        });
      } catch (error) {
        console.error('WPS编辑器初始化失败：', error);
        this.error = error.message || 'WPS编辑器初始化失败，请稍后重试';
        this.$message.error(this.error);
        this.handleCancel();
      } finally {
        this.isInitializing = false;
        // 注意：不在finally中设置loading=false，而是在ready事件中设置
        // 除非发生错误
        if (this.error) {
          this.loading = false;
        }
      }
    },

    // 处理文档保存
    handleDocSave(data) {
      console.log('文档已保存:', data);
      this.$message.success('文档保存成功');
      // 保存成功后可以通知父组件
      this.$emit('save-success', this.document);
    },

    // 关闭WPS编辑器
    handleCancel() {
      // 销毁WPS实例
      if (this.wpsInstance) {
        // 可以在这里添加销毁前的确认逻辑
        this.wpsInstance = null;
      }
      // 重置初始化状态
      this.isInitializing = false;
      this.$emit('cancel');
    }
  },
  // 组件销毁时清除WPS实例
  beforeDestroy() {
    // 清除WPS实例
    if (this.wpsInstance) {
      this.wpsInstance = null;
    }
  }
}
</script>

<style lang="less" scoped>
/* WPS编辑器样式 */
.wps-editor-modal {
  .ant-modal {
    width: 100vw !important;
    height: 100vh !important;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    height: 100vh;
    border-radius: 0;
  }

  .ant-modal-body {
    height: calc(100vh - 55px);
    padding: 0;
  }

  .wps-editor-container {
    width: 100%;
    height: 100%;
    position: relative;

    .wps-loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;
    }
  }
}
</style>
<style lang="less">
/* WPS编辑器全局样式 */
.wps-editor-modal {
  .ant-modal-header {
    background: linear-gradient(to right, #1890ff, #096dd9);
    border-bottom: 1px solid #096dd9;
    padding: 12px 24px;

    .ant-modal-title {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .ant-modal-close {
    color: #fff;

    &:hover {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .ant-modal-close-x {
    width: 46px;
    height: 46px;
    line-height: 46px;
  }
}
</style>

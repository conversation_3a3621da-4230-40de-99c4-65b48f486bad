<template>
  <div class="investigation-subject-info">
    <!-- 是否成年/姓名 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="是否成年" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-radio-group
            v-decorator="['adultStatus', { rules: [{ required: true, message: '请选择是否成年' }] }]"
            :disabled="mode === 'view'"
          >
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-input
            v-decorator="['correctionObjName', { rules: [{ required: true, message: '请输入姓名' }] }]"
            placeholder="请输入姓名"
            :disabled="mode === 'view'"
            @change="handleNameChange"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 性别/出生日期 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <sh-select
            dictType="sex"
            :disabled="mode === 'view'"
            placeholder="请选择性别"
            style="width: 100%;"
            v-decorator="['gender', { initialValue: null, rules: [{ required: true, message: '请选择性别' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-date-picker
            valueFormat="YYYY-MM-DD"
            v-decorator="['birthday', { rules: [{ required: true, message: '请选择出生日期' }] }]"
            style="width: 100%"
            format="YYYY-MM-DD"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 联系电话/证件类型 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['contactTel']" placeholder="请输入联系电话" :disabled="mode === 'view'" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <sh-select
            dictType="zjlx"
            :disabled="mode === 'view'"
            placeholder="请选择证件类型"
            style="width: 100%;"
            v-decorator="['certType', { initialValue: null, rules: [{ required: true, message: '请选择证件类型' }] }]"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 曾用名/别名 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="曾用名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['usedName']" placeholder="请输入曾用名" :disabled="mode === 'view'" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="别名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['nickName']" placeholder="请输入别名" :disabled="mode === 'view'" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 国籍/民族/证件号码 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="mz"
            :disabled="mode === 'view'"
            placeholder="请选择民族"
            v-decorator="['nationality', { initialValue: null, rules: [{ required: false, message: '请选择/民族' }] }]"
            style="width: 100%;"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-input
            v-decorator="['certNum', { rules: [{ required: true, message: '请输入证件号码' }] }]"
            placeholder="请输入证件号码"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 监护人联系电话/住所地 -->
    <a-row :gutter="16">
      <!-- 监护人姓名 -->
      <a-col :span="12">
        <a-form-item label="文化程度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_WHCD"
            :disabled="mode === 'view'"
            placeholder="请选择"
            style="width: 100%;"
            v-decorator="['whcd', { initialValue: null, rules: [{ required: false, message: '请选择文化程度' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="监护人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['guardianName']" placeholder="请输入监护人姓名" :disabled="mode === 'view'" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="监护人联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['guardianContactTel']"
            placeholder="请输入监护人联系电话"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="婚姻状况" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="DCPG_HYZK"
            :disabled="mode === 'view'"
            placeholder="请选择"
            style="width: 100%;"
            v-decorator="['marriage', { initialValue: null, rules: [{ required: false, message: '请选择文化程度' }] }]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="住所地" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <sh-cascader-distpicker
            ref="residenceCodeRef"
            :disabled="mode === 'view'"
            v-decorator="[
              'residenceCode',
              {
                rules: [
                  { required: true, message: '请选择住所地' },
                  { validator: (rule, value, callback) => validateAddress(rule, value, callback, 'residenceCodeRef') }
                ]
              }
            ]"
            @change="(value, selectedOptions) => onAddressChange(value, selectedOptions, 'residence')"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 住所地详细地址 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="住所地详细地址" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <a-input
            v-decorator="['residence', { rules: [{ required: true, message: '请输入详细地址' }] }]"
            placeholder="请输入详细地址"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 户籍所在地/户籍地址详情 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="户籍所在地" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <sh-cascader-distpicker
            ref="registeredAddressRef"
            style="width: 100%;"
            :disabled="mode === 'view'"
            v-decorator="[
              'registeredAddressCode',
              {
                rules: [
                  { required: true, message: '请选择户籍所在地' },
                  {
                    validator: (rule, value, callback) => validateAddress(rule, value, callback, 'registeredAddressRef')
                  }
                ]
              }
            ]"
            @change="(value, selectedOptions) => onAddressChange(value, selectedOptions, 'registeredAddress')"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="户籍地址详情" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <a-input
            v-decorator="['registeredAddress', { rules: [{ required: true, message: '请输入户籍地址详情' }] }]"
            placeholder="请输入户籍地址详情"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 工作单位 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="工作单位" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <a-input
            v-decorator="['companyName', { rules: [{ required: true, message: '请输入工作单位' }] }]"
            placeholder="请输入工作单位"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 主要犯罪事实 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="主要犯罪事实" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-textarea
            v-decorator="['criminalDetail']"
            :rows="4"
            placeholder="请输入主要犯罪事实"
            :disabled="mode === 'view'"
            :autoSize="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 拟适宜社区矫正人员类型/拟适宜社区矫正类别 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="拟适宜社区矫正人员类型" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <sh-select
            dictType="nsysqjzrylx"
            v-decorator="['psnType', { rules: [{ required: true, message: '请选择类型' }] }]"
            placeholder="请选择"
            :disabled="mode === 'view'"
            @change="handlePsnTypeChange"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="拟适宜社区矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <sh-select
            dictType="SQJZ_JZLB_NEW"
            v-decorator="['correctionType', { rules: [{ required: true, message: '请选择类别' }] }]"
            placeholder="请选择"
            :disabled="mode === 'view'"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 是否逃避/是否有限制期限 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="是否速裁" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-select
            v-decorator="['expeditedProcedure', { rules: [{ required: true, message: '请选择是否速裁' }] }]"
            placeholder="请选择"
            :disabled="mode === 'view'"
            @change="handleExpediteChange"
          >
            <a-select-option value="0">否</a-select-option>
            <a-select-option value="1">是</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="是否有原判刑期" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
          <a-select
            v-decorator="['hasInitialSentence', { rules: [{ required: true, message: '请选择是否有原判刑期' }] }]"
            placeholder="请选择"
            :disabled="mode === 'view'"
            @change="handleHasInitialSentenceChange"
          >
            <a-select-option value="0">无</a-select-option>
            <a-select-option value="1">有</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 原判刑期相关信息，仅当"是否有原判刑期"为"有"时显示 -->
    <a-row v-if="showOriginalSentenceInfo" :gutter="16">
      <a-col :span="12">
        <a-form-item label="原判刑期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="mode === 'view'"
            style="width: 100%"
            placeholder="请输入"
            v-model="formModel.initialSentenceDuration"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原判刑期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            :disabled="mode === 'view'"
            valueFormat="YYYY-MM-DD"
            style="width: 100%"
            placeholder="请选择"
            v-model="formModel.initialSentenceStart"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原判刑期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            :disabled="mode === 'view'"
            valueFormat="YYYY-MM-DD"
            style="width: 100%"
            placeholder="请选择"
            v-model="formModel.initialSentenceEnd"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="原判刑罚" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <sh-select
            dictType="SQJZ_YPXF"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="mode === 'view'"
            v-model="formModel.initialPunishment"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="附加刑" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
          <sh-select
            v-model="formModel.supplementaryPunishment"
            apiURL="/sysDictData/list?typeId=1668140386272436225"
            labelKey="value"
            mode="multiple"
            :disabled="mode === 'view'"
            @change="handleAdditionalPenaltyChange"
            style="width: 100%"
            placeholder="请选择附加刑"
          />
        </a-form-item>
      </a-col>

      <a-col :span="24" v-if="showAdditionalPenaltyInfo.includes('102')">
        <a-form-item label="罚金" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="mode === 'view'"
            placeholder="请输入"
            v-model="formModel.fine"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24" v-if="showAdditionalPenaltyInfo.includes('101')">
        <a-form-item label="剥夺政治权利" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="mode === 'view'"
            placeholder="请输入"
            v-model="formModel.bdzzql"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24" v-if="showAdditionalPenaltyInfo.includes('103')">
        <a-form-item label="没收财产" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
          <a-input
            :disabled="mode === 'view'"
            placeholder="请输入"
            v-model="formModel.mscc"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>

      <a-col :span="12">
        <a-form-item label="判决机关" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :disabled="mode === 'view'"
            placeholder="请输入"
            v-model="formModel.sentencingDept"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="判决日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            valueFormat="YYYY-MM-DD"
            style="width: 100%"
            placeholder="请选择"
            :disabled="mode === 'view'"
            :disabledDate="disabledFutureDate"
            v-model="formModel.sentencingDate"
            @change="syncFormModelToParent"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 罪名表格 -->
    <a-row :gutter="16">
      <a-col :span="24">
        <a-form-item label="罪名" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
          <crime-table
            v-decorator="[
              'criminalCharge',
              { rules: [{ type: 'array', required: true, message: '请至少添加一个罪名' }] }
            ]"
            :disabled="mode === 'view'"
            :criminalChargeChinese="initialValues?.criminalChargeChinese"
            @change="handleCrimeTableChange"
          />
        </a-form-item>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import moment from 'moment'
import CrimeTable from './CrimeTable.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  name: 'InvestigationSubjectInfo',
  components: {
    CrimeTable
  },
  props: {
    mode: {
      type: String,
      default: 'edit'
    },
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: null,
      // 表单布局配置
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      selectedAddressOptions: {
        registeredAddress: [],
        residence: []
      },
      showOriginalSentenceInfo: true, // 控制原判刑期相关信息的显示
      showAdditionalPenaltyInfo: [], // 控制附加刑相关信息的显示

      // 用于v-model绑定的表单模型对象
      formModel: {
        initialSentenceDuration: null,
        initialSentenceStart: null,
        initialSentenceEnd: null,
        initialPunishment: null,
        supplementaryPunishment: [],
        fine: null,
        bdzzql: null,
        mscc: null,
        sentencingDept: null,
        sentencingDate: null
      }
    }
  },
  watch: {
    // 监听initialValues变化，确保异步获取数据后能正确初始化
    initialValues: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0 && this.form) {
          this.initForm()
        }
      },
      immediate: false, // 修改为false，避免在created之前执行
      deep: true
    }
  },
  created() {
    // 创建表单
    this.form = this.$form.createForm(this)
  },
  mounted() {
    // 在mounted中初始化表单，确保DOM已渲染完成
    if (this.initialValues && Object.keys(this.initialValues).length > 0) {
      this.initForm()
    }
  },
  methods: {
    disabledFutureDate,
    // 初始化表单数据
    initFormData() {
      // 确保表单已初始化
      if (this.form) {
        this.initForm()
      } else {
        console.warn('表单对象未初始化，无法调用initFormData')
      }
    },

    // 表单初始化逻辑
    async initForm() {
      // 确保表单已经创建
      if (!this.form) {
        console.warn('表单对象未初始化')
        return
      }

      if (!this.initialValues || Object.keys(this.initialValues).length === 0) return

      const values = this.initialValues
      // console.log('初始化表单, 原始数据:', JSON.stringify(values));

      // 先设置显示状态，确保DOM渲染
      this.showOriginalSentenceInfo = values.hasInitialSentence === '1'
      // console.log('是否显示原判刑期信息:', this.showOriginalSentenceInfo, '原判刑期值:', values.hasInitialSentence);

      // 初始化附加刑相关信息
      if (values.supplementaryPunishment) {
        let supplementaryPunishmentArray = values.supplementaryPunishment
        if (typeof values.supplementaryPunishment === 'string') {
          supplementaryPunishmentArray = values.supplementaryPunishment.split(',')
        }
        this.showAdditionalPenaltyInfo = supplementaryPunishmentArray
      }

      // 首先只设置基础表单字段，不包括原判刑期相关字段
      const baseFormValues = {
        adultStatus: values.adultStatus || null, // 是否成年
        correctionObjName: values.correctionObjName || null, // 调查对象姓名
        gender: values.gender || null, // 性别
        whcd: values.whcd || null, // 文化程度
        birthday: values.birthday ? moment(values.birthday) : null, // 出生日期
        contactTel: values.contactTel || null, // 联系电话
        certType: values.certType || null, // 证件类型
        usedName: values.usedName || null, // 曾用名
        nickName: values.nickName || null, // 别名
        nationality: values.nationality || null, // 国籍/民族
        certNum: values.certNum || null, // 证件号码
        guardianName: values.guardianName || null, // 监护人姓名
        guardianContactTel: values.guardianContactTel || null, // 监护人联系电话
        residenceCode: values.residenceCode || null, // 住所地
        residence: values.residence || null, // 住所地详细地址
        registeredAddressCode: values.registeredAddressCode || null, // 户籍所在地
        registeredAddress: values.registeredAddress || null, // 户籍所在地明细
        companyName: values.companyName || null, // 工作单位
        criminalDetail: values.criminalDetail || null, // 主要犯罪事实
        psnType: values.psnType || null, // 拟适宜社区矫正人员类型
        correctionType: values.correctionType || null, // 拟适宜社区矫正类别
        expeditedProcedure: values.expeditedProcedure || null, // 是否速裁
        hasInitialSentence: values.hasInitialSentence || null, // 是否有原判刑期
        criminalCharge: values.criminalCharge || null // 罪名数据
      }

      // console.log('设置基础表单值:', JSON.stringify(baseFormValues));
      this.form.setFieldsValue(baseFormValues)

      // 设置原判刑期相关字段到formModel（v-model绑定的对象）
      if (this.showOriginalSentenceInfo) {
        // 处理日期格式
        let initialSentenceStart = values.initialSentenceStart
        if (initialSentenceStart && typeof initialSentenceStart === 'string') {
          initialSentenceStart = initialSentenceStart.split(' ')[0] // 只取日期部分
        }

        let initialSentenceEnd = values.initialSentenceEnd
        if (initialSentenceEnd && typeof initialSentenceEnd === 'string') {
          initialSentenceEnd = initialSentenceEnd.split(' ')[0] // 只取日期部分
        }

        let sentencingDate = values.sentencingDate
        if (sentencingDate && typeof sentencingDate === 'string') {
          sentencingDate = sentencingDate.split(' ')[0] // 只取日期部分
        }

        // 直接设置到formModel对象上
        this.formModel = {
          initialSentenceDuration: values.initialSentenceDuration || null,
          initialSentenceStart: initialSentenceStart,
          initialSentenceEnd: initialSentenceEnd,
          initialPunishment: values.initialPunishment || null,
          supplementaryPunishment: Array.isArray(values.supplementaryPunishment)
            ? values.supplementaryPunishment
            : values.supplementaryPunishment?.split(',') || [],
          fine: values.fine || null,
          bdzzql: values.bdzzql || null,
          mscc: values.mscc || null,
          sentencingDept: values.sentencingDept || null,
          sentencingDate: sentencingDate
        }

        // console.log('设置原判刑期相关表单值到formModel:', JSON.stringify(this.formModel));
      }
    },

    // 处理地址选择变化
    onAddressChange(value, selectedOptions, addressType) {
      // 保存选中的地址选项，用于验证
      this.selectedAddressOptions[addressType] = selectedOptions
    },

    // 通用的地址验证方法
    validateAddress(rule, v, callback, refName) {
      if (!v) {
        callback(new Error('请选择完整地址'))
        return
      }

      const value = typeof v === 'string' ? v.split(',') : v

      // 判断是否选择到最终级别（没有子级）
      if (Array.isArray(value)) {
        // 检查数组长度，通常省市区三级，县区后可能还有街道/镇
        if (value.length < 3) {
          callback(new Error('请至少选择到区/县级别'))
          return
        }

        // 获取组件实例
        const cascaderRef = this.$refs[refName]
        if (cascaderRef) {
          // 获取组件的cValue和options
          const cValue = cascaderRef.cValue || value
          const options = cascaderRef.options || []

          if (cValue && cValue.length > 0 && options && options.length > 0) {
            // 递归查找对应节点
            const findNode = (nodes, level, targetValues) => {
              if (!nodes || nodes.length === 0 || level >= targetValues.length) {
                return null
              }

              const currentValue = targetValues[level]
              const found = nodes.find(node => String(node.value) === String(currentValue))

              if (!found) {
                return null
              }

              if (level === targetValues.length - 1) {
                return found
              }

              return findNode(found.children, level + 1, targetValues)
            }

            // 从第一级开始查找最后一个节点
            const lastNode = findNode(options, 0, cValue)

            // 判断是否有子节点
            if (lastNode && (!lastNode.children || lastNode.children.length === 0)) {
              // 已经选到最后一级，验证通过
              callback()
              return
            } else {
              // 还有子级可选
              callback(new Error('请选择到最终级别地址'))
              return
            }
          }
        }

        // 获取refName对应的地址类型
        const addressType = refName.replace('Ref', '')

        // 如果无法通过上面的方法判断，使用备选方法
        // 如果有已保存的选项数据，检查最后一项是否有children
        const savedOptions = this.selectedAddressOptions[addressType]
        if (savedOptions && savedOptions.length > 0) {
          const lastOption = savedOptions[savedOptions.length - 1]
          if (lastOption && lastOption.children && lastOption.children.length > 0) {
            callback(new Error('请选择到最终级别地址'))
            return
          }
        }
      } else if (typeof v === 'string' && v.trim() !== '') {
        // 字符串形式的值，根据分隔符判断层级
        const parts = v.split(/,|\/|-/)
        if (parts.length < 3) {
          callback(new Error('请选择完整的地址'))
          return
        }
      }

      // 如果到这里还没有返回，则通过验证
      callback()
    },

    // 自定义验证户籍所在地是否选择到最终级别 - 保留向后兼容
    validateRegisteredAddress(rule, v, callback) {
      return this.validateAddress(rule, v, callback, 'registeredAddressRef')
    },

    // 处理罪名表格变化
    handleCrimeTableChange(value) {
      console.log(value)
      // 这里不需要做额外处理，因为CrimeTable组件已经处理了v-decorator的双向绑定
    },

    // 处理姓名变化
    handleNameChange(e) {
      const name = e.target.value
      // 触发事件通知父组件姓名变化
      this.$emit('subjectInfoChange', { correctionObjName: name })
    },

    // 处理拟适宜社区矫正人员类型变化
    handlePsnTypeChange(value) {
      // 触发事件通知父组件类型变化
      this.$emit('subjectInfoChange', { psnType: value })
    },

    // 提交调查对象信息变更
    submitSubjectInfoChange() {
      // 获取表单值
      this.form.validateFields((err, values) => {
        if (!err) {
          // 处理日期格式
          const formattedValues = { ...values }
          if (formattedValues.birthday) {
            formattedValues.birthday = formattedValues.birthday.format('YYYY-MM-DD')
          }

          // 合并formModel中的值（v-model绑定的表单项）
          if (this.showOriginalSentenceInfo) {
            Object.assign(formattedValues, this.formModel)
          }

          // 触发事件通知父组件
          this.$emit('subjectInfoChange', formattedValues)
        }
      })
    },

    // 处理速裁变化
    handleExpediteChange(value) {
      console.log('速裁变化:', value)
      // 立即触发事件，通知父组件速裁状态变化
      this.$emit('subjectInfoChange', { expeditedProcedure: value })

      // 获取当前表单值
      this.form.validateFields((err, values) => {
        if (!err) {
          // 处理日期格式
          const formattedValues = { ...values }
          if (formattedValues.birthday) {
            formattedValues.birthday = formattedValues.birthday.format('YYYY-MM-DD')
          }

          // 将最新的速裁值设置进去
          formattedValues.expeditedProcedure = value

          // 触发事件通知父组件
          this.$emit('subjectInfoChange', formattedValues)
        }
      })
    },

    // 处理是否有原判刑期变化
    handleHasInitialSentenceChange(value) {
      console.log('原判刑期变化:', value)
      // 更新显示状态
      this.showOriginalSentenceInfo = value === '1'

      // 如果切换到显示，需要确保足够的渲染时间后再清除表单
      if (value === '1') {
        setTimeout(() => {
          // 清空原判刑期相关字段（使用v-model，直接设置formModel）
          this.formModel = {
            initialSentenceDuration: null,
            initialSentenceStart: null,
            initialSentenceEnd: null,
            initialPunishment: null,
            supplementaryPunishment: [],
            fine: null,
            bdzzql: null,
            mscc: null,
            sentencingDept: null,
            sentencingDate: null
          }

          // 同步更新到父组件
          this.syncFormModelToParent()
        }, 200)
      }

      // 触发事件通知父组件
      this.$emit('subjectInfoChange', { hasInitialSentence: value })
    },

    // 处理附加刑变化
    handleAdditionalPenaltyChange(value) {
      console.log('附加刑变化:', value)
      // 更新显示状态
      this.showAdditionalPenaltyInfo = value || []

      // 附加刑变化后同步到父组件
      this.syncFormModelToParent()
    },

    // 添加新方法，用于同步formModel到父组件
    syncFormModelToParent() {
      // 通过事件将formModel中的值同步给父组件
      if (this.showOriginalSentenceInfo) {
        this.$emit('subjectInfoChange', { ...this.formModel })
      }
    },

    // 获取完整的表单数据，包括基础表单和formModel
    getFullFormData() {
      return new Promise((resolve, reject) => {
        this.form.validateFields((err, values) => {
          if (err) {
            reject(err)
            return
          }

          // 处理日期格式
          const formattedValues = { ...values }
          if (formattedValues.birthday) {
            formattedValues.birthday = formattedValues.birthday.format
              ? formattedValues.birthday.format('YYYY-MM-DD')
              : formattedValues.birthday
          }

          // 合并formModel中的值（如果显示原判刑期相关信息）
          if (this.showOriginalSentenceInfo) {
            // 处理supplementaryPunishment，如果是数组则转换为逗号分隔的字符串
            const processedFormModel = { ...this.formModel }
            if (Array.isArray(processedFormModel.supplementaryPunishment)) {
              processedFormModel.supplementaryPunishment = processedFormModel.supplementaryPunishment.join(',')
            }

            Object.assign(formattedValues, processedFormModel)
          } else {
            // 如果不显示原判刑期信息，将相关字段设为null
            formattedValues.initialSentenceDuration = null
            formattedValues.initialSentenceStart = null
            formattedValues.initialSentenceEnd = null
            formattedValues.initialPunishment = null
            formattedValues.supplementaryPunishment = null
            formattedValues.fine = null
            formattedValues.bdzzql = null
            formattedValues.mscc = null
            formattedValues.sentencingDept = null
            formattedValues.sentencingDate = null
          }

          resolve(formattedValues)
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.investigation-subject-info {
  .ant-form-item-required::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    line-height: 1;
    content: '*';
  }
}
</style>

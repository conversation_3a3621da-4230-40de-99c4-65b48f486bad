<template>
  <div class="crime-table-component" ref="viewBox">
    <div class="crime-table">
      <div class="warning-message" v-if="!isNewRecord && record.id.length >= 32">
        <a-alert
          v-if="criminalChargeChinese && !disabled"
          :message="
            `已收到跨部门办案平台推送的案由罪名为：${criminalChargeChinese}，由于标准不一，无法关联，需重新选择`
          "
          type="error"
          show-icon
        />
      </div>
      <a-table
        :columns="crimeColumns"
        :dataSource="crimeDataSource"
        size="small"
        :rowKey="record => record.key"
        :pagination="false"
        bordered
      >
        <template slot="idSlot" slot-scope="text, record, index">
          <span class="num van-handle"> <a-icon type="ordered-list" /> {{ index + 1 }} </span>
        </template>
        <template slot="crimeDetailSlot" slot-scope="text, record, index">
          <div v-if="editingIndex === index">
            <a-input v-model="editingText" @pressEnter="saveEdit(index)" @blur="saveEdit(index)" :disabled="disabled" />
          </div>
          <div v-else class="editable-cell" @click="!disabled && startEdit(index, text)">
            {{ text }}
            <a-icon v-if="!disabled" type="edit" class="edit-icon" />
          </div>
        </template>
        <template slot="operation" slot-scope="text, record, index">
          <a-button
            type="link"
            @click="removeCrime(index)"
            :disabled="disabled"
            v-if="crimeDataSource.length > 1 && !disabled"
          >
            删除
          </a-button>
        </template>
      </a-table>
      <div class="add-crime-btn" v-if="!disabled">
        <a-button type="dashed" style="width: 100%; margin-top: 8px" @click="showAddModal()">
          <a-icon type="plus" /> 添加罪名
        </a-button>
      </div>
    </div>

    <!-- 罪名编辑弹窗 -->
    <crime-add-modal
      :visible="crimeModalVisible"
      :title="crimeModalTitle"
      :confirmLoading="crimeModalLoading"
      :initialValues="currentEditingCrime"
      @ok="handleCrimeModalOk"
      @cancel="handleCrimeModalCancel"
    />
  </div>
</template>

<script>
import CrimeAddModal from './CrimeAddModal.vue'
import Sortable from 'sortablejs'

export default {
  name: 'CrimeTable',
  components: {
    CrimeAddModal
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  inject: ['isNewRecord', 'record'],
  props: {
    criminalChargeChinese: {
      default: '',
      type: String
    },
    value: {
      type: [Array, String],
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    decorator: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      showWarning: true,
      oldList: [],
      newList: [],
      crimeDataSource: [],

      // 罪名编辑弹窗
      crimeModalVisible: false,
      crimeModalTitle: '添加罪名',
      crimeModalLoading: false,
      currentEditingCrimeIndex: -1,
      currentEditingCrime: {},

      // 内部值
      innerValue: [],

      // 行内编辑状态
      editingIndex: -1,
      editingText: '',

      // 排序相关
      sortable: null
    }
  },
  computed: {
    crimeColumns() {
      const columns = [
        {
          title: '序号',
          dataIndex: 'key',
          key: 'key',
          width: '70px',
          scopedSlots: { customRender: 'idSlot' }
        },
        { title: '罪名', dataIndex: 'crimeName', key: 'crimeName', width: '25%' },
        { title: '犯罪类型', dataIndex: 'crimeType', key: 'crimeType', width: '20%' },
        {
          title: '具体罪名',
          dataIndex: 'crimeDetail',
          key: 'crimeDetail',
          scopedSlots: { customRender: 'crimeDetailSlot' }
        }
      ]

      // 只有在非禁用状态下才添加操作列
      if (!this.disabled) {
        columns.push({
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          width: '100px'
        })
      }

      return columns
    }
  },
  created() {
    // 初始化数据
    this.initData()
  },
  mounted() {
    this.$nextTick(() => {
      this.initSortable()
    })
  },
  watch: {
    value: {
      handler(val) {
        this.initData()
      },
      deep: true
    }
  },
  methods: {
    // 初始化数据
    initData() {
      let crimes = []
      try {
        if (this.value) {
          // 如果是字符串，尝试解析JSON
          if (typeof this.value === 'string') {
            crimes = JSON.parse(this.value)
          } else if (Array.isArray(this.value)) {
            crimes = this.value
          }

          if (crimes.length > 0) {
            this.crimeDataSource = crimes.map((crime, index) => ({
              key: this.crimeDataSource.length + index,
              crimeName: crime.crimeName || crime.name || crime.title || '',
              crimeValue: crime.crimeValue || crime.value || '',
              crimeType: crime.crimeType || crime.type || '',
              crimeDetail: crime.crimeDetail || crime.detail || crime.title || ''
            }))
            this.innerValue = [...this.crimeDataSource]
            return
          }
        }

        // 默认值
        this.crimeDataSource = []
        this.innerValue = [...this.crimeDataSource]
      } catch (error) {
        console.error('解析罪名数据失败:', error)
        // 默认值
        this.crimeDataSource = []
        this.innerValue = [...this.crimeDataSource]
      }
    },

    // 初始化排序功能
    initSortable() {
      const el = this.$refs.viewBox.querySelectorAll('.ant-table-tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost',
        handle: '.van-handle',
        animation: 350,
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: async evt => {
          // 获取移动的行
          const targetRow = this.crimeDataSource.splice(evt.oldIndex, 1)[0]
          // 插入到新位置
          this.crimeDataSource.splice(evt.newIndex, 0, targetRow)
          // 更新内部值并触发变更事件
          await this.$nextTick()
          this.innerValue = [...this.crimeDataSource]
          this.emitChange()
        }
      })
    },

    // 开始编辑具体罪名
    startEdit(index, text) {
      this.editingIndex = index
      this.editingText = text
    },

    // 保存编辑
    saveEdit(index) {
      if (this.editingIndex !== -1) {
        this.crimeDataSource[index].crimeDetail = this.editingText
        this.editingIndex = -1
        this.editingText = ''

        // 更新值并触发变更事件
        this.innerValue = [...this.crimeDataSource]
        this.emitChange()
      }
    },

    // 显示添加弹窗
    showAddModal() {
      this.crimeModalVisible = true
      this.crimeModalTitle = '添加罪名'
      this.currentEditingCrimeIndex = -1
      this.currentEditingCrime = {}
    },

    // 显示编辑弹窗
    showEditModal(record, index) {
      this.crimeModalVisible = true
      this.crimeModalTitle = '编辑罪名'
      this.currentEditingCrimeIndex = index
      this.currentEditingCrime = { ...record }
    },

    // 罪名弹窗确认
    handleCrimeModalOk(values) {
      this.crimeModalLoading = true
      console.log(' CrimeModalOk:', values)
      // return
      // 处理多选情况
      // if (values.crimeValue && Array.isArray(values.crimeValue) && values.crimeValue.length > 0) {
      //   if (this.currentEditingCrimeIndex >= 0) {
      //     // 对于编辑模式，仅更新当前编辑的记录
      //     // 将多选的第一个值作为当前记录的值
      //     const crimeNames = values.crimeName.split('、')
      //     const crimeTypes = values.crimeType.split('、')
      //     const crimeDetails = values.crimeDetail.split('、')

      //     this.crimeDataSource[this.currentEditingCrimeIndex] = {
      //       ...this.crimeDataSource[this.currentEditingCrimeIndex],
      //       crimeName: values.crimeName || '',
      //       crimeValue: values.crimeValue[0], // 只取第一个
      //       crimeType: values.crimeType || '',
      //       crimeDetail: values.crimeDetail || values.crimeName || ''
      //     }

      //     // 如果选择了多个值，则从第二个值开始，为每个值创建新记录
      //     if (values.crimeValue.length > 1) {
      //       // 为每个选中的值(从第二个开始)创建一条新记录
      //       const selectedValues = values.crimeValue.slice(1)
      //       const selectedNames = crimeNames.slice(1)
      //       const selectedTypes = crimeTypes.slice(1)
      //       const selectedDetails = crimeDetails.slice(1)

      //       // 获取现有的值集合，用于去重
      //       const existingValues = new Set(this.crimeDataSource.map(item => item.crimeValue))

      //       // 为每个选中的值创建一条新记录
      //       selectedValues.forEach((value, index) => {
      //         // 如果已存在该值，则跳过
      //         if (existingValues.has(value)) return

      //         // 创建新记录
      //         this.crimeDataSource.push({
      //           key: this.crimeDataSource.length,
      //           crimeName: selectedNames[index] || '',
      //           crimeValue: value,
      //           crimeType: selectedTypes[index] || '',
      //           crimeDetail: selectedDetails[index] || selectedNames[index] || ''
      //         })

      //         // 添加到已存在集合中
      //         existingValues.add(value)
      //       })
      //     }
      //   } else {
      //     // 添加模式: 为每个选中的值创建一条新记录

      //     // 获取现有的值集合，用于去重
      //     const existingValues = new Set(this.crimeDataSource.map(item => item.crimeValue))

      //     // 如果只有一个空记录，且其值为空，则清空数据源
      //     if (
      //       this.crimeDataSource.length === 1 &&
      //       (!this.crimeDataSource[0].crimeValue || this.crimeDataSource[0].crimeValue === '')
      //     ) {
      //       this.crimeDataSource = []
      //     }

      //     // 获取选中的值、名称、类型和详情
      //     const selectedValues = values.crimeValue
      //     const selectedNames = values.crimeName.split('、')
      //     const selectedTypes = values.crimeType.split('、')
      //     const selectedDetails = values.crimeDetail.split('、')

      //     // 为每个选中的值创建一条新记录
      //     selectedValues.forEach((value, index) => {
      //       // 如果已存在该值，则跳过
      //       if (existingValues.has(value)) return

      //       // 创建新记录
      //       this.crimeDataSource.push({
      //         key: this.crimeDataSource.length,
      //         crimeName: selectedNames[index] || '',
      //         crimeValue: value,
      //         crimeType: selectedTypes[index] || '',
      //         crimeDetail: selectedDetails[index] || selectedNames[index] || ''
      //       })

      //       // 添加到已存在集合中
      //       existingValues.add(value)
      //     })
      //   }
      // } else {
      // 单选情况的处理
      if (this.currentEditingCrimeIndex >= 0) {
        // 更新现有罪名
        this.crimeDataSource[this.currentEditingCrimeIndex] = {
          ...this.crimeDataSource[this.currentEditingCrimeIndex],
          crimeName: values.crimeName,
          crimeValue: values.crimeValue,
          crimeType: values.crimeType,
          crimeDetail: values.crimeDetail
        }
      } else {
        // 如果只有一个空记录，且其值为空，则清空数据源
        if (
          this.crimeDataSource.length === 1 &&
          (!this.crimeDataSource[0].crimeValue || this.crimeDataSource[0].crimeValue === '')
        ) {
          this.crimeDataSource = []
        }

        // 添加新罪名
        this.crimeDataSource.push({
          key: this.crimeDataSource.length,
          crimeName: values.crimeName,
          crimeValue: values.crimeValue,
          crimeType: values.crimeType,
          crimeDetail: values.crimeDetail
        })
        // }
      }

      // 更新值并触发变更事件
      this.innerValue = [...this.crimeDataSource]
      this.emitChange()

      // 关闭弹窗
      setTimeout(() => {
        this.crimeModalVisible = false
        this.crimeModalLoading = false
      }, 300)
    },

    // 罪名弹窗取消
    handleCrimeModalCancel() {
      this.crimeModalVisible = false
    },

    // 删除罪名
    removeCrime(index) {
      this.crimeDataSource.splice(index, 1)

      // 重新编号
      this.crimeDataSource = this.crimeDataSource.map((item, idx) => ({
        ...item,
        key: idx
      }))

      // 更新值并触发变更事件
      this.innerValue = [...this.crimeDataSource]
      this.emitChange()
    },

    // 触发变更事件
    emitChange() {
      // 为了v-model
      this.$emit('change', this.innerValue)
      this.$emit('onChange', this.innerValue)
      this.$emit('input', this.innerValue)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

.crime-table-component {
  .crime-table {
    width: 100%;

    .warning-message {
      margin-bottom: 16px;
    }

    .add-crime-btn {
      text-align: center;
    }
  }

  .editable-cell {
    padding: 5px;
    cursor: pointer;
    position: relative;

    .edit-icon {
      position: absolute;
      right: 5px;
      visibility: hidden;
      color: #1890ff;
    }

    &:hover {
      background-color: #f8f8f8;

      .edit-icon {
        visibility: visible;
      }
    }
  }
}

// 拖拽相关样式
.van-handle {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.8;
  background: #e6f7ff;
}

/deep/ .ant-table-row {
  td {
    transition: background 0.3s;
  }
}
</style>

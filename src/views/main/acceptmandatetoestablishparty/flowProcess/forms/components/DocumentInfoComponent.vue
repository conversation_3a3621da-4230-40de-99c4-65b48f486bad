<template>
  <div class="document-info-component">
    <div class="document-files">
      <div class="table-header">
        <div class="header-actions">
          <a-button v-if="showDownload" type="primary" icon="download" @click="handleBatchDownload" :disabled="documentData.length === 0">
            打包下载
          </a-button>
          <template v-if="!disabled">
            <slot name="headerActions"></slot>
          </template>
        </div>

      </div>

      <a-table
        :columns="documentColumns"
        :dataSource="documentData"
        size="small"
        :pagination="false"
        bordered
        :locale="{emptyText: '暂无文书数据'}"
      >
        <template slot="fileName" slot-scope="text, record">
          <span class="file-name clickable" @click="viewDocument(record)">
            <a-icon type="file-pdf" theme="filled" style="color: #ff4d4f;" v-if="record.ws && record.ws.toLowerCase().endsWith('.pdf')" />
            <a-icon type="file-word" theme="filled" style="color: #1890ff;" v-else-if="record.ws && (record.ws.toLowerCase().endsWith('.doc') || record.ws.toLowerCase().endsWith('.docx'))" />
            <a-icon type="file-excel" theme="filled" style="color: #52c41a;" v-else-if="record.ws && (record.ws.toLowerCase().endsWith('.xls') || record.ws.toLowerCase().endsWith('.xlsx'))" />
            <a-icon type="file" theme="filled" style="color: #faad14;" v-else />
            <span style="margin-left: 8px;">{{ text }}</span>
          </span>
        </template>
        <template slot="action" slot-scope="text, record">

          <a type="link" @click="viewDocument(record)">查看</a>
          <a-divider type="vertical" />
          <a type="link" @click="downloadSingleDocument(record)">下载</a>
          <template v-if="!disabled">
            <a-divider type="vertical" />
            <a type="link" @click="deleteDocument(record)">删除</a>
          </template>
        </template>
      </a-table>

      <div class="add-document-btn" v-if="!disabled">
        <a-upload
          name="file"
          style="width: 100%;"
          :multiple="multiple"
          :fileList="[]"
          :showUploadList="false"
          :beforeUpload="beforeUploadCheck"
          :customRequest="customUpload"
          accept=".pdf"
          @change="handleFileChange"
        >
          <a-button type="dashed" style="width: 100%; margin-top: 8px">
            <a-icon type="plus" /> 添加文书 (仅支持PDF格式)
          </a-button>
        </a-upload>
      </div>
    </div>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/fileUtils'

export default {
  name: 'DocumentInfoComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    showDownload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: '上传文件'
    },
    uploadUrl: {
      type: String,
      default: '/sysFileInfo/uploadOss'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    maxFileCount: {
      type: Number,
      default: 10
    },
    recordId: {
      type: [String, Number],
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    },
    xm: {
      type: String,
      default: ''
    },
    labelCol: {
      type: Object,
      default: () => ({ xs: { span: 24 }, sm: { span: 4 } })
    },
    wrapperCol: {
      type: Object,
      default: () => ({ xs: { span: 24 }, sm: { span: 20 } })
    }
  },
  data() {
    return {
      fileList: [],
      documentData: [],
      documentColumns: [
        { title: '序号', dataIndex: '', width: 60, key: 'rowIndex', align: 'center', customRender: function (t, r, index) { return parseInt(index) + 1 } },
        {
          title: '文书类型',
          dataIndex: 'wsdm',
          ellipsis: true,
          width: '30%',
          customRender: (text, r) => {
            return r.ws
          }
        },
        {
          title: '文书材料',
          dataIndex: 'ws',
          ellipsis: true,
          width: '40%',
          scopedSlots: { customRender: 'fileName' }
        },
        {
          title: '操作',
          scopedSlots: { customRender: 'action' },
          align: 'center'
        }
      ]
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initDocumentData(newVal);
        } else {
          this.documentData = [];
          this.fileList = [];
        }
      }
    }
  },
  methods: {
    initDocumentData(docList) {
      // 初始化文档数据
      this.documentData = docList.map((doc, index) => {
        return {
          ...doc,

          key: doc.id || index
        };
      });

      // 更新内部文件列表（不显示但用于跟踪）
      this.fileList = docList.map((doc, index) => {
        return {
          uid: doc.id || `-${index}`,
          name: doc.ws || '未命名文件',
          status: 'done',
          url: doc.ossUrl,
          id: doc.id,
          ws: doc.ws,
          wsdm: doc.wsdm
        };
      });
    },

    beforeUploadCheck(file) {
      // 检查文件类型
      const isPDF = file.type === 'application/pdf';
      // 检查文件扩展名
      const isNamePDF = file.name.toLowerCase().endsWith('.pdf');

      if (!isPDF || !isNamePDF) {
        this.$message.error('只能上传PDF格式的文件!');
        return false; // 阻止上传
      }

      // 进一步验证PDF文件的有效性 - 读取文件头部信息
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const arr = new Uint8Array(reader.result).subarray(0, 5);
          const header = Array.from(arr).map(byte => byte.toString(16)).join('');
          // PDF文件头通常以"%PDF-"开头，十六进制为"25504446"
          const isPDFHeader = header.startsWith('255044');

          if (isPDFHeader) {
            resolve(file);
          } else {
            this.$message.error('无效的PDF文件，请上传真实的PDF文档!');
            reject(new Error('无效的PDF文件'));
          }
        };
        reader.onerror = () => {
          this.$message.error('文件读取错误，请重试!');
          reject(new Error('文件读取错误'));
        };
        reader.readAsArrayBuffer(file.slice(0, 5));
      });
    },

    customUpload({ file, onProgress, onSuccess, onError }) {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);

      // 创建上传key，用于消息提示
      const uploadKey = `upload_${file.uid}`;
      this.$message.loading({ content: `正在上传文件：${file.name}`, key: uploadKey, duration: 0 });

      // 使用this.$http上传文件
      this.$http.post('/sysFileInfo/uploadOss', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        // 进度回调
        onUploadProgress: progressEvent => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({ percent }, file);
        }
      })
      .then(response => {
        console.log('上传成功:', response);
        // 上传成功处理
        this.$message.success({ content: `${file.name} 上传成功`, key: uploadKey, duration: 2 });

        // 服务器返回的响应数据，注意这里可能是response.data或response.data.data
        const serverData = response.data?.data || response.data;

        // 如果服务器返回了有效数据，立即添加到documentData
        if (serverData) {
          // 构建新的文档数据
          const newDoc = {
            id: serverData.id,
            ws: serverData.fileOriginName,
            wsdm: serverData.fileOriginName,
            ossUrl: serverData.filePath,
            fileId: serverData.id,
            key: serverData.id,
            // 保存其他可能有用的字段
            fileSizeInfo: serverData.fileSizeInfo,
            fileBucket: serverData.fileBucket,
            fileObjectName: serverData.fileObjectName,
            createTime: serverData.createTime
          };

          // 直接更新文档数据
          this.documentData = [...this.documentData, newDoc];

          // 添加到文件列表跟踪
          this.fileList.push({
            uid: serverData.id,
            name: serverData.fileOriginName,
            status: 'done',
            url: serverData.filePath,
            id: serverData.id,
            wsdm: newDoc.wsdm,
            size: serverData.fileSizeKb * 1024 // 转换为字节
          });

          // 发送变更通知
          this.emitChange();

          console.log('文档已添加到列表:', newDoc);
          console.log('当前文档列表:', this.documentData);
        }

        // 构造一个类似于a-upload默认上传成功的response格式
        const successResponse = {
          data: serverData,
          status: 'done'
        };

        onSuccess(successResponse, file);
      })
      .catch(error => {
        // 上传失败处理
        this.$message.error({ content: `${file.name} 上传失败: ${error.message}`, key: uploadKey, duration: 3 });
        onError(error, null, file);
      });
    },

    handleFileChange(info) {
      // 只处理当前上传的文件
      const file = info.file;

      // 上传状态处理 - 大部分逻辑已移至customUpload方法
      // 这个方法现在主要用于处理常规上传组件的状态变化

      // 注意：由于我们在customUpload中已经处理了文档添加，
      // 所以这里不再需要重复添加文档到documentData
      // 但保留这个方法以防需要处理其他状态变化

      // 调试信息
      if (file.status === 'done') {
        console.log('文件上传完成：', file.name);
      } else if (file.status === 'error') {
        console.log('文件上传失败：', file.name);
      }
    },

    viewDocument(record) {
      console.log('查看文档：', record);

      // 检查文档链接是否存在
      if (!record.ossUrl) {
        this.$message.warning('文档链接不存在，无法查看');
        return;
      }

      // 创建loading效果
      const loadingKey = `viewDocument_${record.id}`;
      this.$message.loading({ content: `正在加载文档：${record.ws}`, key: loadingKey, duration: 0 });

      // 使用$imageViewer.view方法预览文档
      this.$imageViewer.view([record.ossUrl], 0, '/pdfJsLib/web/viewer.html');

      // 关闭loading消息
      setTimeout(() => {
        this.$message.success({ content: `文档已打开：${record.ws}`, key: loadingKey, duration: 2 });
      }, 1000);
    },

    deleteDocument(record) {
      if (this.disabled) return;

      console.log('删除文档：', record);
      // 从文档数据中删除
      this.documentData = this.documentData.filter(item => item.id !== record.id);

      // 从文件列表中删除
      this.fileList = this.fileList.filter(file => file.id !== record.id);

      // 发送变更通知
      this.emitChange();

      this.$message.success(`已删除文档：${record.ws}`);
    },

    handleBatchDownload() {
      // 如果没有文件，提示用户
      if (this.documentData.length === 0) {
        this.$message.warning('没有可下载的文件');
        return;
      }

      // 检查是否有recordId
      if (!this.recordId) {
        this.$message.warning('缺少必要的参数，无法下载文件');
        return;
      }

      // 创建loading效果
      const downloadKey = 'batch_download';
      this.$message.loading({ content: '正在准备下载文件...', key: downloadKey, duration: 0 });

      // 调用打包下载接口
      this.$http.get('/investigationInfo/batchDownLoad', {
        params: { bizId: this.recordId, bizType: this.bizType, xm: this.xm },
        responseType: 'blob'
      })
      .then(response => {
        // 使用通用下载方法
        downloadFile(response);
        this.$message.success({ content: '文件下载成功', key: downloadKey, duration: 2 });
      })
      .catch(error => {
        console.error('下载失败:', error);
        this.$message.error({ content: '文件下载失败，请稍后重试', key: downloadKey, duration: 3 });
      });
    },

    downloadSingleDocument(record) {
      if (!record || !record.ossUrl) {
        this.$message.warning('无法下载文件，文件ossUrl不存在');
        return;
      }

      // 创建loading效果
      const downloadKey = `download_${record.ossUrl}`;
      this.$message.loading({ content: `正在下载文件：${record.ws}`, key: downloadKey, duration: 0 });

      // 调用下载接口
      this.$http.get('/investigationInfo/downloadFile', {
        params: { filePath: record.ossUrl, fileOriginName: record.ws, ...record },
        responseType: 'blob'
      })
      .then(response => {
        // 使用通用下载方法
        downloadFile(response);
        this.$message.success({ content: `${record.ws} 下载成功`, key: downloadKey, duration: 2 });
      })
      .catch(error => {
        console.error('下载失败:', error);
        this.$message.error({ content: `${record.ws} 下载失败，请稍后重试`, key: downloadKey, duration: 3 });
      });
    },

    emitChange() {
      // 将当前文档数据转换为符合API格式的数据并发送
      const docList = this.documentData.map(doc => {
        return {
          id: doc.id,
          contactId: doc.contactId,
          xtbh: doc.xtbh,
          ws: doc.ws,
          wsdm: doc.wsdm,
          uri: doc.uri,
          ossUrl: doc.ossUrl,
          fileId: doc.fileId
        };
      });

      // 触发v-model更新
      this.$emit('change', docList);
      this.$emit('onChange', docList);
      this.$emit('onchange', docList);
      this.$emit('input', docList);
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-upload.ant-upload-select{
  display: block;
  width: 100%;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body{
    margin:0;
}

/deep/.ant-table-small {
  border-radius: 4px;
}

/deep/.ant-table-row {
  transition: all 0.3s;
  &:hover {
    background-color: #e6f7ff;
  }
}

.document-info-component {
  margin-bottom: 24px;

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;

    .header-actions {
      .ant-btn {
        min-width: 100px;
      }
    }
  }

  .add-document-btn {
    margin-top: 8px;
  }

  .empty-text {
    text-align: center;
    color: #999;
    padding: 16px 0;
  }

  .file-name {
    display: inline-flex;
    align-items: center;

    &.clickable {
      cursor: pointer;
      color: #1890ff;
      transition: all 0.3s;

      &:hover {
        opacity: 0.8;
        text-decoration: underline;
      }
    }
  }
}
</style>

<template>
  <div>
    <div v-if="detailData && detailData.paperType === 'BLLX06'">
      <!-- 调查时间 -->
      <a-row class="time-row">
        <div class="label-title">调查时间</div>
        <a-form-item label="">
          <a-input v-decorator="['startYear', { rules: [{ validator: this.validateYear }] }]" style="width: 100px;" />年
        </a-form-item>
        <a-form-item label="">
          <a-input
            v-decorator="['startMonth', { rules: [{ validator: this.validateMonth }] }]"
            style="width: 100px;"
          />月
        </a-form-item>
        <a-form-item label="">
          <a-input v-decorator="['startDay', { rules: [{ validator: this.validateDate }] }]" style="width: 100px;" />日
        </a-form-item>
      </a-row>
      <!-- 调查地点 -->
      <a-row class="time-row">
        <div class="label-title">调查地点</div>
        <a-col :span="21">
          <a-form-item label="">
            <a-input v-decorator="['location', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 调查人信息 -->
      <a-row
        class="time-row"
        v-for="(item, index) in detailData.investigationRecordParam.investigatorList"
        :key="index"
      >
        <div class="label-title">调查人姓名</div>
        <a-col :span="5">
          <a-form-item label="">
            <a-input v-decorator="[`nickName${index}`, { rules: [{ required: false, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="">
            单位<a-input v-decorator="[`orgNames${index}`, { rules: [{ required: false, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
    </div>
    <div v-if="detailData && detailData.paperType !== 'BLLX06'">
      <!-- 调查时间 -->
      <a-row class="time-row">
        <div class="label-title">调查时间</div>
        <a-form-item label="">
          <a-input v-decorator="['startYear', { rules: [{ validator: this.validateYear }] }]" />年
        </a-form-item>
        <a-form-item label="">
          <a-input v-decorator="['startMonth', { rules: [{ validator: this.validateMonth }] }]" />月
        </a-form-item>
        <a-form-item label="">
          <a-input v-decorator="['startDay', { rules: [{ validator: this.validateDate }] }]" />日
        </a-form-item>
      </a-row>
      <!-- 调查地点 -->
      <a-row class="time-row">
        <div class="label-title">调查地点</div>
        <a-col :span="21">
          <a-form-item label="">
            <a-input v-decorator="['location', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 调查人信息 -->
      <a-row
        class="time-row"
        v-for="(item, index) in detailData.investigationRecordParam.investigatorList"
        :key="index"
      >
        <div class="label-title">调查人姓名</div>
        <a-col :span="5">
          <a-form-item label="">
            <a-input v-decorator="[`nickName${index}`, { rules: [{ required: false, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="">
            单位<a-input v-decorator="[`orgNames${index}`, { rules: [{ required: false, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">被调查人姓名</div>
        <a-col :span="21">
          <a-form-item label="">
            <a-input v-decorator="['investigatedName', { rules: [{ message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">身份证号码</div>
        <a-col :span="21">
          <a-form-item label="">
            <!-- { rules: [{ validator: this.validateIdCard }] } -->
            <a-input v-decorator="['investigatedIdCard']" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">地址或单位</div>
        <a-col :span="14">
          <a-form-item label="">
            <a-input v-decorator="['investigatedAddressOrUnit', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="7">
          <a-form-item label=""> 联系电话<a-input v-decorator="['investigatedContactPhone']" /> </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">在场人</div>
        <a-col :span="4">
          <a-form-item label="">
            <a-input v-decorator="['witnessName', { rules: [{ required: false, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="17">
          <a-form-item label="">
            地址或单位<a-input
              v-decorator="['witnessAddressOrUnit', { rules: [{ required: false, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">与拟适用社区矫正人员</div>
        <a-col :span="4">
          <a-form-item label="">
            <a-input v-decorator="['correctionPerson', { rules: [{ required: true, message: '请输入' }] }]" />是
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="">
            <a-input
              v-decorator="['relationship', { rules: [{ required: true, message: '请输入' }] }]"
              style="width: 35%!important;flex: none;"
            />关系
          </a-form-item>
        </a-col>
      </a-row>
    </div>
    <div v-if="detailData && detailData.paperType === 'BLLX06'">
      <!-- 询问本人 -->
      <a-row class="time-row">
        <div class="label-title">被调查人姓名</div>
        <a-col :span="7">
          <a-form-item label="">
            <a-input v-decorator="['personalInfo.name', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="7">
          <a-form-item label="">
            曾用名<a-input
              v-decorator="['personalInfo.formerName', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="7">
          <a-form-item label="">
            性别<a-input v-decorator="['personalInfo.gender', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">出生年月</div>
        <a-col :span="7">
          <a-form-item label="">
            <a-input v-decorator="['personalInfo.birthday', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
        <a-col :span="7">
          <a-form-item label="">
            国籍或民族<a-input
              v-decorator="['personalInfo.nationality', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="7">
          <a-form-item label="">
            籍贯<a-input
              v-decorator="['personalInfo.nativePlace', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">文化程度</div>
        <a-col :span="6">
          <a-form-item label="">
            <a-input
              v-decorator="['personalInfo.educationLevel', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="15">
          <a-form-item label="">
            身份证（护照）号码<a-input
              v-decorator="[
                'personalInfo.idCard',
                {
                  rules: [
                    {
                      validator: this.validateIdCard
                    }
                  ]
                }
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">
          工作单位（职业、职务）
        </div>
        <a-col :span="20" style="width: 81%;">
          <a-form-item label="">
            <a-input v-decorator="['personalInfo.workUnit', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">户籍地址</div>
        <a-col :span="21">
          <a-form-item>
            <a-input
              v-decorator="['personalInfo.registeredAddress', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">经常居住地址</div>
        <a-col :span="16">
          <a-form-item label="">
            <a-input
              v-decorator="['personalInfo.residentialAddress', { rules: [{ required: true, message: '请输入' }] }]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <!-- { rules: [{ validator: this.validatePhone }] } -->
          <a-form-item label=""> 联系电话<a-input v-decorator="['personalInfo.contactPhone']" /> </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">案由</div>
        <a-col :span="21">
          <a-form-item label="">
            <a-input v-decorator="['personalInfo.caseReason', { rules: [{ required: true, message: '请输入' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="time-row">
        <div class="label-title">与拟适用社区矫正人员</div>
        <a-col :span="4">
          <a-form-item label="">
            <a-input v-decorator="['correctionPerson', { rules: [{ required: true, message: '请输入' }] }]" />是
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="">
            <a-input
              v-decorator="['relationship', { rules: [{ required: true, message: '请输入' }] }]"
              style="width: 35%!important;flex: none;"
            />关系
          </a-form-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detailData: {
      type: Object,
      default: () => ({})
    },
    correctionType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formLayout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      }
    }
  },
  methods: {
    // 验证手机号
    validatePhone(rule, value, callback) {
      const phoneRegex = /^1[3456789]\d{9}$/
      if (!phoneRegex.test(value)) {
        callback('请输入有效的手机号')
      } else {
        callback()
      }
    },
    // 验证年份
    validateYear(rule, value, callback) {
      const yearRegex = /^\d{4}$/
      if (!yearRegex.test(value)) {
        callback('请输入有效的 4 位年份')
      } else {
        callback()
      }
    },
    // 验证月份
    validateMonth(rule, value, callback) {
      const monthRegex = /^(0?[1-9]|1[0-2])$/
      if (!monthRegex.test(value)) {
        callback('请输入有效的月份')
      } else {
        callback()
      }
    },
    // 验证日期
    validateDate(rule, value, callback) {
      const dateRegex = /^(0?[1-9]|[12][0-9]|3[01])$/
      if (!dateRegex.test(value)) {
        callback('请输入有效的日期')
      } else {
        callback()
      }
    },
    // 验证小时
    validateHour(rule, value, callback) {
      const hourRegex = /^(0?[0-9]|1[0-9]|2[0-3])$/
      if (!hourRegex.test(value)) {
        callback('请输入有效的小时')
      } else {
        callback()
      }
    },
    // 验证填写的分钟格式
    validateMinute(rule, value, callback) {
      const minuteRegex = /^(0?[0-9]|[1-5][0-9])$/
      if (!minuteRegex.test(value)) {
        callback('请输入有效的分钟')
      } else {
        callback()
      }
    },
    // 身份证验证
    validateIdCard(rule, value, callback) {
      if (!value) {
        return callback('请输入身份证号码或护照号码')
      }
      
      // 身份证正则表达式（15位或18位）
      const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      
      // 护照正则表达式（中国护照：1位字母开头，后跟8位数字或字母）
      const passportReg = /^[a-zA-Z][a-zA-Z0-9]{8}$/
      
      // 如果既不是有效身份证也不是有效护照
      if (!idCardReg.test(value) && !passportReg.test(value)) {
        return callback('请输入有效的身份证号码或护照号码')
      }
      
      // 18 位身份证号校验码验证
      // if (value.length === 18) {
      //   const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      //   const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      //   let sum = 0
      //   for (let i = 0; i < 17; i++) {
      //     sum += parseInt(value[i], 10) * factor[i]
      //   }
      //   const lastChar = value[17].toUpperCase()
      //   if (parity[sum % 11] !== lastChar) {
      //     return callback('请输入有效的身份证号码')
      //   }
      // }
      callback()
    }
  }
}
</script>

<style lang="less" scoped>
/* 可以添加自定义样式 */
/deep/.ant-input {
  border: 0;
  border-bottom: 1px solid #e8e8e8;
}
/deep/.ant-input:focus {
  box-shadow: none !important;
}
.time-row {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  margin-bottom: 10px;
  /deep/.ant-input {
    flex: 1;
  }
  /deep/.ant-form-item {
    // width: 18%;
    margin-bottom: 0 !important;
  }
  /deep/.ant-form-item-children {
    display: flex;
    align-items: center;
  }
}
.label-title {
  width: auto;
  min-width: 80px;
  text-align: right;
}
</style>

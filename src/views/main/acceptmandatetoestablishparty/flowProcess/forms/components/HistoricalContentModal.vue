<template>
  <a-modal
    :visible="visible"
    :title="title"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
  >
    <a-table
      :row-key="record => record.id"
      :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :columns="columns"
      :data-source="data"
      :pagination="{ pageSize: 10 }"
      :scroll="{ y: 240 }"
    />
  </a-modal>
</template>

<script>
const data = []

export default {
  data() {
    return {
      data,
      columns: [
        {
          title: '序号',
          key: 'index',
          width: 60,
          align: 'center',
          // 自定义渲染序号（基于数据索引）
          customRender: (_, __, index) => index + 1 // 索引从0开始，序号从1开始
        },
        {
          title: '主持人',
          dataIndex: 'host',
          width: 150
        },
        {
          title: '评议审核地点',
          dataIndex: 'address',
          width: 150
        },
        {
          title: '评议审核人员',
          dataIndex: 'psnList'
        },
        {
          title: '记录人',
          dataIndex: 'recorder'
        }
      ],
      visible: false,
      title: '历史内容',
      confirmLoading: false,
      record: {},
      selectedRowKeys: [],
      node: null,
      type: 1
    }
  },
  methods: {
    open(record, type) {
      /**
       * @description: 打开模态框
       * @param {type} record: 当前记录
       * @param {type} type: 1: 小组和议 2: 集体评议
       */
      this.visible = true
      this.$nextTick(() => {
        this.type = type
        this.record = record
        this.columns[2].title = type == 1 ? '合议审核地点' : '评议审核地点'
        this.columns[3].title = type == 1 ? '合议审核人员' : '评议审核人员'
        this.title = '历史内容'
        this.confirmLoading = false
        this.getData()
      })
    },
    getData() {
      this.$http
        .get('/investigationInfo/historyList', {
          params: {
            id: this.record.id,
            status: this.record.status
          }
        })
        .then(res => {
          if (res.code === 200) {
            this.data = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.$message.error('Error fetching data')
        })
    },
    handleOk() {
      this.$emit('ok', this.node)
      this.visible = false
    },
    handleCancel() {
      this.visible = false
    },
    onSelectChange(selectedRowKeys, node) {
      this.selectedRowKeys = selectedRowKeys
      console.log('selectedRowKeys changed: ', node[0])
      this.node = node[0]
    }
  }
}
</script>
<style lang="less" scoped>
.filter-tree-wrap {
  height: 60vh;
  overflow: hidden;
  flex-flow: column;
  display: flex;

  .filter-tree {
    flex: 1;
    overflow: auto;
    margin-top: 16px;
  }
}
</style>

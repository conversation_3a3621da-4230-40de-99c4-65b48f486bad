<template>
  <a-modal
    title="选择人员"
    :visible="visible"
    :footer="null"
    centered
    width="800px"
    @cancel="handleCancel"
    :bodyStyle="{ padding: '20px' }"
    :maskStyle="{ background: 'rgba(0,0,0,0.45)' }"
    class="modern-user-selector"
  >
    <div class="user-selector-container">
      <!-- 已选择人员显示区域 -->
      <div class="selected-users-container">
        <div class="selected-users-label">已选择人员：</div>
        <div class="selected-users-content">
          <a-input disabled :value="selectedUsersText" />
        </div>
      </div>

      <!-- 选择类型切换 -->
      <div class="selector-type-container">
        <a-radio-group v-model="selectorType" @change="handleSelectorTypeChange" buttonStyle="solid">
          <a-radio-button :value="1">
            <a-icon type="apartment" />
            浙政钉（司法行政用户）
          </a-radio-button>
          <a-radio-button :value="2">
            <a-icon type="team" />
            浙里办（矫正小组或其他用户）
          </a-radio-button>
        </a-radio-group>
      </div>

      <!-- 浙政钉组织架构选择 -->
      <div v-if="selectorType === 1" class="zhezhengding-container modern-card">
        <div class="org-structure">
          <div class="org-header">
            <div class="org-title">
              <a-icon type="apartment" /> 组织架构
            </div>
            <div class="org-actions">
              <a-button type="link" @click="handleClearSelection">
                <a-icon type="delete" />清空
              </a-button>
            </div>
          </div>

          <div class="org-content">
            <!-- 组织架构树 -->
            <div class="org-tree">
              <a-spin :spinning="orgLoading">
                <a-tree
                  v-if="treeData.length > 0"
                  :treeData="treeData"
                  showLine
                  @select="onTreeSelect"
                  :defaultExpandAll="true"
                  :defaultSelectedKeys="defaultSelectedKeys"
                  :defaultExpandedKeys="defaultExpandedKeys"
                  :selectedKeys="selectedKeys"
                >
                  <template slot="title" slot-scope="{title}">
                    <span>{{ title }}</span>
                  </template>
                </a-tree>
                <a-empty v-else description="暂无组织架构数据" />
              </a-spin>
            </div>

            <!-- 右侧人员列表 -->
            <div class="user-list">
              <a-spin :spinning="userLoading">
                <!-- 添加搜索框 -->
                <div class="search-box">
                  <a-input-search
                    v-model="searchValue"
                    placeholder="搜索人员"
                    @search="handleSearch"
                    allowClear
                    @change="handleSearchChange"
                  />
                </div>
                <template v-if="orgUsers.length > 0">
                  <div class="user-list-content">
                    <div class="user-item" v-for="user in orgUsers" :key="user.id">
                      <a-checkbox :checked="isUserSelected(user)" @change="(e) => toggleUserSelection(user, e.target.checked, 1)">
                        {{ user.name }}
                      </a-checkbox>
                    </div>
                  </div>

                  <!-- 分页器 -->
                  <div class="pagination-container">
                    <a-pagination
                      size="small"
                      :current="pagination.pageNo"
                      :total="pagination.totalRows"
                      :pageSize="pagination.pageSize"
                      :showTotal="total => `共 ${total} 条`"
                      :showSizeChanger="false"
                      :showQuickJumper="false"
                      :hideOnSinglePage="false"
                      @change="handlePageChange"
                    />
                  </div>
                </template>
                <a-empty v-else description="暂无人员数据" />
              </a-spin>
            </div>
          </div>
        </div>
      </div>

      <!-- 浙里办用户选择 -->
      <div v-if="selectorType === 2" class="zheli-container modern-card">
        <div class="zheli-content">
          <!-- 左侧常用人员列表 -->
          <div class="common-users">
            <div class="common-users-header">
              <span><a-icon type="user" /> 常用人员</span>
              <span>{{ loginOrgName }}</span>
            </div>

            <div class="common-users-list">
              <a-spin :spinning="commonUserLoading">
                <template v-if="commonUsers.length > 0">
                  <div v-for="user in commonUsers" :key="user.id" class="common-user-item">
                    <a-checkbox :checked="isUserSelected(user)" @change="(e) => toggleUserSelection(user, e.target.checked, 2)">
                      {{ user.name }}
                    </a-checkbox>
                  </div>
                </template>
                <a-empty v-else description="暂无常用人员" />
              </a-spin>
            </div>
          </div>

          <!-- 右侧表格 -->
          <div class="user-table-container">
            <div class="user-table-header">
              <div class="header-cell">姓名</div>
              <div class="header-cell">手机号</div>
              <div class="header-cell-actions">
                <a-button type="link" class="clear-btn" @click="handleClearSelection">
                  <a-icon type="delete" />清空
                </a-button>
              </div>
            </div>

            <div class="user-table-body">
              <div v-for="(user, index) in zhelibaoSelectedUsers" :key="index" class="user-table-row">
                <div class="table-cell">{{ user.receiveUserName || user.name }}</div>
                <div class="table-cell">{{ user.phone || '-' }}</div>
                <div class="table-cell-actions">
                  <a-button type="link" class="delete-btn" @click="removeUser(user, 2)">
                    <a-icon type="delete" />
                  </a-button>
                </div>
              </div>

              <!-- 新增用户行 -->
              <div v-if="showAddUserForm" class="user-table-row add-user-row">
                <div class="table-cell">
                  <a-form-item :validateStatus="newUser.nameError ? 'error' : ''" :help="newUser.nameError" style="margin-bottom: 0">
                    <a-input
                      v-model="newUser.receiveUserName"
                      placeholder="请输入姓名"
                      :class="{'error-input': newUser.nameError}"
                      @change="validateName"
                    />
                  </a-form-item>
                </div>
                <div class="table-cell">
                  <a-form-item :validateStatus="newUser.phoneError ? 'error' : ''" :help="newUser.phoneError" style="margin-bottom: 0">
                    <a-input
                      v-model="newUser.phone"
                      placeholder="请输入手机号"
                      :class="{'error-input': newUser.phoneError}"
                      @change="validatePhone"
                    />
                  </a-form-item>
                </div>
                <div class="table-cell-actions">
                  <a-button type="link" class="cancel-btn" @click="cancelAddUser">
                    <a-icon type="close-circle" />
                  </a-button>
                  <a-button type="link" class="confirm-btn" @click="confirmAddUser">
                    <a-icon type="check-circle" />
                  </a-button>
                </div>
              </div>

              <!-- 新增用户按钮行 -->
              <div v-if="!showAddUserForm" class="user-table-row add-button-row">
                <div class="table-cell" colspan="2">
                  <a-button type="dashed" block @click="showAddUser" class="add-user-btn">
                    <a-icon type="plus" /> 新增人员
                  </a-button>
                </div>
                <div class="table-cell-actions">
                  <a-button type="link" class="add-btn" @click="showAddUser">
                    <a-icon type="plus-circle" />
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部说明 -->
      <div class="selector-footer">
        <div class="footer-info">
          <p><a-icon type="info-circle" style="color: #1890ff" /> 说明：1. 浙政钉用户签名请前往"矫务通-随行智控-文件签名"下确认并签名。</p>
          <p><a-icon type="info-circle" style="color: #1890ff" /> 2. 浙里办用户签名请前往"浙里办-浙里社区矫正专区-文件签名"下确认并签名。</p>
        </div>

        <div class="footer-actions">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleConfirm">确定</a-button>
          <!-- <a-button type="link" @click="debugLists">调试</a-button> -->
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'UserSelector',
  props: {
    // 初始选择的用户
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      selectorType: 1, // 1: 浙政钉, 2: 浙里办
      selectedOrg: '',
      selectedOrgName: '',
      loginOrgName: '', // 当前登录用户的组织名称
      searchValue: '', // 搜索关键字

      // 分别存储两种类型的已选择用户
      zhezhengdingSelectedUsers: [], // 浙政钉选择的用户
      zhelibaoSelectedUsers: [], // 浙里办选择的用户

      // 组织架构数据
      orgTreeData: [],
      treeData: [],
      orgLoading: false,
      defaultSelectedKeys: [], // 默认选中组织，将在 show 方法中设置
      defaultExpandedKeys: [],
      selectedKeys: [], // 将在 show 方法中设置

      // 用户列表数据
      orgUsers: [],
      userLoading: false,
      pagination: {
        pageNo: 1,
        pageSize: 30,
        totalRows: 0
      },

      // 常用人员数据
      commonUsers: [],
      commonUserLoading: false,

      // 模拟数据
      showAddUserForm: false,
      newUser: {
        receiveUserName: '',
        phone: '',
        nameError: '',
        phoneError: ''
      }
    }
  },
  computed: {
    // 合并两种类型的已选择用户，用于显示
    selectedUsers() {
      return [...this.zhezhengdingSelectedUsers, ...this.zhelibaoSelectedUsers];
    },

    // 已选择用户的文本展示
    selectedUsersText() {
      const zhezhengdingText = this.zhezhengdingSelectedUsers.map(user => `${user.name || user.receiveUserName}(浙政钉)`).join('、');
      const zhelibaoText = this.zhelibaoSelectedUsers.map(user => `${user.receiveUserName || user.name}(浙里办)`).join('、');

      if (zhezhengdingText && zhelibaoText) {
        return `${zhezhengdingText}、${zhelibaoText}`;
      }

      return zhezhengdingText || zhelibaoText;
    },

    // 当前类型的用户列表引用
    currentSelectedUsers() {
      return this.selectorType === 1 ? this.zhezhengdingSelectedUsers : this.zhelibaoSelectedUsers;
    }
  },
  methods: {
    // 显示选择器
    show() {
      this.visible = true;

      // 从传入的值中分离两种类型的用户
      if (this.value) {
        console.log('UserSelector 接收到初始值:', this.value);

        if (Array.isArray(this.value)) {
          // 如果是旧版本传入的数组，则根据userType分类
          this.zhezhengdingSelectedUsers = (this.value || [])
            .filter(user => user.userType === 1)
            .map(user => ({
              ...user,
              userType: 1
            }));

          this.zhelibaoSelectedUsers = (this.value || [])
            .filter(user => user.userType === 2 || !user.userType)
            .map(user => ({
              ...user,
              userType: 2
            }));
        } else if (typeof this.value === 'object') {
          // 如果是新版本传入的对象，直接使用对应字段
          this.zhezhengdingSelectedUsers = (this.value.zhezhengdingUsers || [])
            .map(user => ({
              ...user,
              userType: 1
            }));

          this.zhelibaoSelectedUsers = (this.value.zhelibaoUsers || [])
            .map(user => ({
              ...user,
              userType: 2
            }));
        } else {
          // 初始化为空
          this.zhezhengdingSelectedUsers = [];
          this.zhelibaoSelectedUsers = [];
        }
      } else {
        this.zhezhengdingSelectedUsers = [];
        this.zhelibaoSelectedUsers = [];
      }

      console.log('初始化浙政钉用户:', this.zhezhengdingSelectedUsers.length);
      console.log('初始化浙里办用户:', this.zhelibaoSelectedUsers.length);

      // 获取当前登录用户的组织信息
      const userInfo = this.$store.getters.userInfo || {};
      const loginEmpInfo = userInfo.loginEmpInfo || {};
      this.loginOrgName = loginEmpInfo.orgName || '';

      // 设置默认选中的组织ID为当前用户的组织ID
      const userOrgId = loginEmpInfo.orgId;
      if (userOrgId) {
        this.defaultSelectedKeys = [userOrgId];
        this.selectedKeys = [userOrgId];
        console.log('默认选中组织ID:', userOrgId);
      } else {
        // 如果没有获取到用户组织ID，使用默认值
        this.defaultSelectedKeys = ['DEPT0000000000000000000000900031'];
        this.selectedKeys = ['DEPT0000000000000000000000900031'];
        console.log('未获取到用户组织ID，使用默认值');
      }

      // 如果没有选择记录，默认选择浙政钉
      if (this.selectorType === undefined) {
        this.selectorType = 1;
      }

      // 加载组织架构数据
      if (this.selectorType === 1) {
        this.loadOrgTree();
      } else if (this.selectorType === 2) {
        this.loadCommonUsers();
      }
    },

    // 加载组织架构树
    async loadOrgTree() {
      this.orgLoading = true;
      try {
        const response = await this.$http.get('/sysOrg/tree?remark=all');
        if (response && response.success) {
          this.orgTreeData = response.data || [];
          this.treeData = this.convertToTreeData(this.orgTreeData);
          this.defaultExpandedKeys = this.getAllOrgIds(this.orgTreeData);

          // 默认选中当前用户的组织
          if (this.selectedKeys.length > 0) {
            this.selectOrg(this.selectedKeys[0]);
          } else {
            // 如果没有设置选中的组织，则默认选择第一个
            const firstOrgId = this.defaultExpandedKeys[0];
            if (firstOrgId) {
              this.selectOrg(firstOrgId);
              this.selectedKeys = [firstOrgId];
            }
          }
        } else {
          this.$message.error('获取组织架构失败');
        }
      } catch (error) {
        console.error('加载组织架构失败：', error);
        this.$message.error('加载组织架构失败');
      } finally {
        this.orgLoading = false;
      }
    },

    // 转换为树组件所需格式
    convertToTreeData(data) {
      if (!data || !data.length) return [];

      return data.map(item => {
        const node = {
          title: item.title || item.name,
          key: item.value || item.id,
          scopedSlots: { title: 'title' }
        };

        if (item.children && item.children.length > 0) {
          node.children = this.convertToTreeData(item.children);
        }

        return node;
      });
    },

    // 获取所有组织ID用于默认展开
    getAllOrgIds(data) {
      if (!data || !data.length) return [];

      let ids = [];
      data.forEach(item => {
        ids.push(item.value || item.id);
        if (item.children && item.children.length > 0) {
          ids = ids.concat(this.getAllOrgIds(item.children));
        }
      });

      return ids;
    },

    // 递归查找组织
    findOrgById(orgList, id) {
      if (!orgList || orgList.length === 0) return null;

      for (const org of orgList) {
        if ((org.value || org.id) === id) return org;

        if (org.children && org.children.length > 0) {
          const found = this.findOrgById(org.children, id);
          if (found) return found;
        }
      }

      return null;
    },

    // 树节点选择
    onTreeSelect(selectedKeys, e) {
      if (selectedKeys.length > 0) {
        const orgId = selectedKeys[0];
        this.selectOrg(orgId);
        this.selectedKeys = selectedKeys;
      }
    },

    // 选择组织
    selectOrg(orgId) {
      if (!orgId) return;

      this.selectedOrg = orgId;
      const org = this.findOrgById(this.orgTreeData, orgId);
      this.selectedOrgName = org ? (org.title || org.name) : '';

      // 重置分页参数
      this.pagination.pageNo = 1;
      this.pagination.totalRows = 0;
      // 清空搜索值
      this.searchValue = '';
      // 加载用户
      this.loadOrgUsers(orgId);
    },

    // 处理分页变化
    handlePageChange(page) {
      console.log('页码变化:', page);
      this.pagination.pageNo = page;
      this.loadOrgUsers(this.selectedOrg);
    },

    // 加载组织下的用户
    async loadOrgUsers(orgId) {
      if (!orgId) return;

      this.userLoading = true;
      try {
        // 打印请求参数，方便调试
        console.log('请求参数:', {
          orgId,
          pageNo: this.pagination.pageNo,
          pageSize: this.pagination.pageSize,
          jzjg: orgId,
          searchValue: this.searchValue
        });

        // 调整请求方式为GET，并确保参数正确传递
        const response = await this.$http({
          url: '/sysUser/page',
          method: 'get',
          params: {
            orgId,
            pageNo: this.pagination.pageNo,
            pageSize: this.pagination.pageSize,
            jzjg: orgId,
            searchValue: this.searchValue
          }
        });

        if (response && response.success) {
          // 打印返回数据，便于调试
          console.log('API返回数据:', response.data);

          // 处理用户数据
          const rows = response.data?.rows || [];
          this.orgUsers = rows.map(user => ({
            sendOrgName: user.sjName || user.orgNames || '',
            sendOrgId: user.id,
            id: user.id,
            name: user.name,
            phone: user.phone || user.mobile || '-'
          }));

          // 更新分页信息，确保使用正确的数据类型
          this.pagination = {
            pageNo: parseInt(response.data?.pageNo || 1),
            pageSize: parseInt(response.data?.pageSize || 30),
            totalRows: parseInt(response.data?.totalRows || 0)
          };

          console.log('更新后的分页信息:', this.pagination);
        } else {
          this.$message.error('获取用户列表失败');
          this.orgUsers = [];
        }
      } catch (error) {
        console.error('加载用户列表失败：', error);
        this.$message.error('加载用户列表失败');
      } finally {
        this.userLoading = false;
      }
    },

    // 处理选择器类型变更
    handleSelectorTypeChange() {
      console.log('切换选择器类型为:', this.selectorType);

      // 清空搜索
      this.searchValue = '';

      // 如果切换到浙政钉模式，加载组织架构
      if (this.selectorType === 1 && this.treeData.length === 0) {
        this.loadOrgTree();
      }

      // 如果切换到浙里办模式，加载常用人员
      if (this.selectorType === 2) {
        this.loadCommonUsers();
      }
    },

    // 加载常用人员
    async loadCommonUsers() {
      this.commonUserLoading = true;
      try {
        const userInfo = this.$store.getters.userInfo || {};
        const loginEmpInfo = userInfo.loginEmpInfo || {};
        const orgId = loginEmpInfo.orgId;
        this.loginOrgName = loginEmpInfo.orgName || '';

        if (!orgId) {
          console.warn('未找到当前用户组织ID');
          this.commonUsers = [];
          return;
        }

        console.log('加载常用人员，组织ID:', orgId, '组织名称:', this.loginOrgName);
        const response = await this.$http({
          url: '/investigationSignCommonUser/page',
          method: 'get',
          params: { orgId }
        });

        if (response && response.success) {
          console.log('常用人员数据:', response.data);
          // 处理常用人员数据，使用receiveUserName作为name
          this.commonUsers = (response.data?.rows || []).map(user => ({
            id: user.id,
            name: user.receiveUserName || '',
            phone: user.phone || '-'
          }));
        } else {
          this.$message.error('获取常用人员失败');
          this.commonUsers = [];
        }
      } catch (error) {
        console.error('加载常用人员失败:', error);
        this.$message.error('加载常用人员失败');
        this.commonUsers = [];
      } finally {
        this.commonUserLoading = false;
      }
    },

    // 判断用户是否已选择
    isUserSelected(user) {
      const list = this.selectorType === 1 ? this.zhezhengdingSelectedUsers : this.zhelibaoSelectedUsers;

      // 使用多种ID匹配方式
      return list.some(item => {
        // 严格比较ID
        if (item.id && user.id && item.id === user.id) {
          return true;
        }

        // 比较receiveUserId (如果存在)
        if ((item.receiveUserId && user.id && item.receiveUserId === user.id) ||
            (item.id && user.receiveUserId && item.id === user.receiveUserId)) {
          return true;
        }

        // 如果用户名和手机都匹配，也认为是同一个用户
        if ((item.name && user.name && item.name === user.name) &&
            (item.phone && user.phone && item.phone === user.phone)) {
          return true;
        }

        return false;
      });
    },

    // 切换用户选择状态
    toggleUserSelection(user, checked, type) {
      console.log('切换用户选择:', user, checked, type);
      const targetType = type !== undefined ? type : this.selectorType;
      const selectedUsers = targetType === 1 ? this.zhezhengdingSelectedUsers : this.zhelibaoSelectedUsers;

      console.log('切换用户选择:', user.name, checked ? '选中' : '取消', '类型:', targetType);

      if (checked) {
        // 检查是否已经存在该用户
        const existingUser = this.findExistingUser(user, selectedUsers);

        if (!existingUser) {
          // 不存在则添加，保留原始ID和所有字段
          selectedUsers.push({
            ...user,
            receiveUserName: user.name || user.receiveUserName,
            userType: targetType
          });
          console.log('已添加到列表, 当前数量:', selectedUsers.length);
        }
      } else {
        // 移除用户
        const index = selectedUsers.findIndex(item =>
          (item.id && user.id && item.id === user.id) ||
          (item.receiveUserId && user.id && item.receiveUserId === user.id) ||
          (item.name === user.name && item.phone === user.phone)
        );

        if (index !== -1) {
          selectedUsers.splice(index, 1);
          console.log('已从列表移除, 当前数量:', selectedUsers.length);
        }
      }
    },

    // 查找已存在的用户
    findExistingUser(newUser, userList) {
      return userList.find(item => {
        // 简化匹配方式
        const idMatch = item.id === newUser.id;
        const namePhoneMatch =
          item.name === newUser.name &&
          (item.phone === newUser.phone || (!item.phone && !newUser.phone));

        return idMatch || namePhoneMatch;
      });
    },

    // 切换常用用户选择
    toggleCommonUserSelection(user) {
      this.toggleUserSelection(user, !this.isUserSelected(user), 2); // 常用用户都是浙里办类型
    },

    // 移除用户
    removeUser(user, type) {
      // 如果明确指定了类型，或者用户本身有类型标记
      const userType = type || user.userType || this.selectorType;

      if (userType === 1) {
        this.zhezhengdingSelectedUsers = this.zhezhengdingSelectedUsers
          .filter(item => item.id !== user.id)
          .map(item => ({
            ...item,
            receiveUserName: item.name || item.receiveUserName
          }));
      } else {
        this.zhelibaoSelectedUsers = this.zhelibaoSelectedUsers
          .filter(item => item.id !== user.id)
          .map(item => ({
            ...item,
            receiveUserName: item.name || item.receiveUserName
          }));
      }
    },

    // 清空选择
    handleClearSelection() {
      // 只清空当前类型的用户
      if (this.selectorType === 1) {
        this.zhezhengdingSelectedUsers = [];
      } else {
        this.zhelibaoSelectedUsers = [];
      }
    },

    // 取消
    handleCancel() {
      this.visible = false;
    },

    // 确认选择
    handleConfirm() {
      console.log('确认选择', this.zhezhengdingSelectedUsers, this.zhelibaoSelectedUsers);

      // 格式化浙政钉用户数据，保留所有原始字段
      const zhezhengdingUsers = this.zhezhengdingSelectedUsers.map(user => ({
        ...user, // 保留所有原始字段
        name: user.name || user.receiveUserName,
        phone: user.phone || '-'
      }));

      // 格式化浙里办用户数据，保留所有原始字段
      const zhelibaoUsers = this.zhelibaoSelectedUsers.map(user => ({
        ...user, // 保留所有原始字段
        receiveUserName: user.name || user.receiveUserName,
        phone: user.phone || '-'
      }));

      // 组装结果
      const result = {
        zhezhengdingUsers,
        zhelibaoUsers
      };

      console.log('最终选择结果:', result);

      this.$emit('input', result);
      this.$emit('change', result);
      this.$emit('select', result);
      this.visible = false;
    },

    // 新增用户表单
    showAddUser() {
      this.showAddUserForm = true;
      this.newUser = {
        receiveUserName: '',
        phone: '',
        nameError: '',
        phoneError: ''
      };
    },

    // 验证姓名
    validateName() {
      if (!this.newUser.receiveUserName || this.newUser.receiveUserName.trim() === '') {
        this.newUser.nameError = '姓名不能为空';
        return false;
      }
      this.newUser.nameError = '';
      return true;
    },

    // 验证手机号
    validatePhone() {
      if (!this.newUser.phone) {
        this.newUser.phoneError = '手机号不能为空';
        return false;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.newUser.phone)) {
        this.newUser.phoneError = '请输入有效的手机号';
        return false;
      }
      this.newUser.phoneError = '';
      return true;
    },

    // 表单验证
    validateForm() {
      const nameValid = this.validateName();
      const phoneValid = this.validatePhone();
      return nameValid && phoneValid;
    },

    confirmAddUser() {
      if (this.validateForm()) {
        this.zhelibaoSelectedUsers.push({
          id: Date.now().toString(),
          name: this.newUser.receiveUserName,
          receiveUserName: this.newUser.receiveUserName,
          phone: this.newUser.phone,
          userType: 2 // 新增的都是浙里办类型
        });
        this.newUser = {
          receiveUserName: '',
          phone: '',
          nameError: '',
          phoneError: ''
        };
        this.showAddUserForm = false;
      }
    },

    cancelAddUser() {
      this.showAddUserForm = false;
    },

    // 调试用户列表状态
    debugLists() {
      console.log('当前选择器类型:', this.selectorType);
      console.log('浙政钉用户列表:', JSON.parse(JSON.stringify(this.zhezhengdingSelectedUsers)));
      console.log('浙里办用户列表:', JSON.parse(JSON.stringify(this.zhelibaoSelectedUsers)));
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value;
      // 重置分页并重新加载数据
      this.pagination.pageNo = 1;
      this.loadOrgUsers(this.selectedOrg);
    },

    // 处理搜索框内容变化
    handleSearchChange(e) {
      // 如果清空了搜索框，重新加载数据
      if (!e.target.value) {
        this.searchValue = '';
        this.pagination.pageNo = 1;
        this.loadOrgUsers(this.selectedOrg);
      }
    }
  }
}
</script>

<style lang="less" scoped>
.modern-user-selector {
  /deep/ .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  /deep/ .ant-modal-header {
    background: linear-gradient(to right, #1890ff, #096dd9);
    border-bottom: none;
    padding: 16px 24px;

    .ant-modal-title {
      color: white;
      font-weight: 500;
      font-size: 16px;
    }
  }

  /deep/ .ant-modal-close {
    color: rgba(255, 255, 255, 0.85);

    &:hover {
      color: white;
    }
  }

  /deep/ .ant-modal-body {
    max-height: 700px;
    overflow-y: auto;
    padding: 20px;
  }
}

.user-selector-container {
  display: flex;
  flex-direction: column;
  max-height: 100%;

  .selected-users-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .selected-users-label {
      min-width: 100px;
      color: #333;
      font-weight: 500;
      margin-right: 10px;
    }

    .selected-users-content {
      flex: 1;

      /deep/ .ant-input {
        border-radius: 4px;
        transition: all 0.3s;

        &:hover, &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }

  .selector-type-container {
    margin-bottom: 20px;

    /deep/ .ant-radio-group {
      width: 100%;
      display: flex;

      .ant-radio-button-wrapper {
        flex: 1;
        text-align: center;
        transition: all 0.3s;

        &.ant-radio-button-wrapper-checked {
          background: #1890ff;
          border-color: #1890ff;
          color: white;

          &::before {
            background-color: #1890ff;
          }
        }

        .anticon {
          margin-right: 6px;
        }
      }
    }
  }

  .modern-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
  }

  .zhezhengding-container, .zheli-container {
    overflow: hidden;

    .org-header, .common-users-header, .user-table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;
      background: linear-gradient(to right, #f9f9f9, #f0f0f0);
      .org-title{
        display: flex;
        align-items: center;
      }
      .org-title, .header-cell {
        font-weight: 500;
        color: #333;

        .anticon {
          margin-right: 6px;
          color: #1890ff;
        }
      }
    }

    .org-content {
      display: flex;
      height: 30vh;

      .org-tree {
        width: 50%;
        border-right: 1px solid #e8e8e8;
        padding: 16px;
        overflow: auto;
        height: 100%;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.05);
        }

        /deep/ .ant-tree {
          .ant-tree-node-content-wrapper {
            transition: all 0.2s;
            padding: 0px 8px;
            margin-left: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 90%;

            &.ant-tree-node-selected {
              background-color: #e6f7ff;
              color: #1890ff;
              font-weight: 500;
            }

            &:hover {
              background-color: #f5f5f5;
            }
          }

          .ant-tree-child-tree {
            padding-left: 16px;
          }
        }
      }

      .user-list {
        flex: 1;
        padding: 16px;
        display: flex;
        flex-direction: column;
        height: 30vh;
        position: relative;

        .search-box {
          margin-bottom: 12px;

          /deep/ .ant-input-search .ant-input {
            border-radius: 4px 0 0 4px;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: #40a9ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          /deep/ .ant-input-search-button {
            border-radius: 0 4px 4px 0;
            border-color: #1890ff;
            background-color: #1890ff;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }
        }

        .user-list-content {
          flex: 1;
          overflow-y: auto;
          padding-bottom: 50px;
          height: calc(30vh - 20px - 42px); /* 减去搜索框高度 */

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: rgba(0, 0, 0, 0.05);
          }

          .user-item {
            padding: 10px 0;
            transition: all 0.2s;
            border-radius: 4px;
            padding-left: 10px;

            &:hover {
              background-color: #f5f5f5;
            }
          }
        }

        .pagination-container {
          height: 30px;
          border-top: 1px solid #f0f0f0;
          padding-top: 5px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          position: absolute;
          bottom: 0px;
          left: 0;
          right: 0;
          background-color: #fff;
          text-align: right;

          /deep/ .ant-pagination {
            display: inline-block;
          }

          /deep/ .ant-pagination-total-text {
            float: left;

          }
        }
      }
    }
  }

  .zheli-container {
    .zheli-content {
      display: flex;
      height: 40vh;

      .common-users {
        width: 30%;
        border-right: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        height: 100%;

        .common-users-header {
          flex-shrink: 0;
          height: 57px;

          span:first-child {
            .anticon {
              color: #1890ff;
              margin-right: 6px;
            }
          }
        }

        .common-users-list {
          flex: 1;
          padding: 10px 16px;
          overflow-y: auto;
          max-height: calc(100% - 57px);

          .common-user-item {
            padding: 10px 0;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 4px;
            padding-left: 10px;

            &:hover {
              background-color: #f5f5f5;
            }
          }
        }
      }

      .user-table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;

        .user-table-header {
          display: flex;
          flex-shrink: 0;

          .header-cell {
            flex: 1;
            text-align: center;
            font-weight: 500;
          }

          .header-cell-actions {
            width: 100px;
            text-align: right;
          }
        }

        .user-table-body {
          flex: 1;
          overflow-y: auto;
          max-height: calc(100% - 57px);

          .user-table-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            align-items: center;
            transition: all 0.2s;

            &:hover {
              background-color: rgba(24, 144, 255, 0.05);
            }

            &.add-user-row {
              background-color: rgba(24, 144, 255, 0.08);
              padding: 10px 0;
            }

            &.add-button-row {
              border-bottom: none;
              padding: 16px 0;
            }

            .table-cell {
              flex: 1;
              padding: 12px 16px;
              text-align: center;

              .error-input {
                border-color: #f5222d;

                &:focus {
                  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
                }
              }
            }

            .table-cell-actions {
              width: 100px;
              padding: 12px 16px;
              text-align: right;
              display: flex;
              justify-content: flex-end;

              .delete-btn, .add-btn, .cancel-btn, .confirm-btn {
                padding: 4px;
                border-radius: 50%;
                margin: 0 4px;
                transition: all 0.2s;

                &:hover {
                  background-color: rgba(0, 0, 0, 0.04);
                }

                .anticon {
                  font-size: 16px;
                }
              }

              .delete-btn {
                color: #ff4d4f;

                &:hover {
                  color: #ff7875;
                  background-color: rgba(255, 77, 79, 0.1);
                }
              }

              .add-btn {
                color: #1890ff;

                &:hover {
                  color: #40a9ff;
                  background-color: rgba(24, 144, 255, 0.1);
                }
              }

              .cancel-btn {
                color: #ff4d4f;

                &:hover {
                  color: #ff7875;
                  background-color: rgba(255, 77, 79, 0.1);
                }
              }

              .confirm-btn {
                color: #52c41a;

                &:hover {
                  color: #73d13d;
                  background-color: rgba(82, 196, 26, 0.1);
                }
              }
            }
          }

          .add-user-btn {
            border-radius: 4px;
            border-color: #1890ff;
            color: #1890ff;
            transition: all 0.3s;
            height: 36px;

            &:hover {
              color: #40a9ff;
              border-color: #40a9ff;
              background-color: rgba(24, 144, 255, 0.05);
            }
          }
        }
      }
    }
  }

  .selector-footer {
    .footer-info {
      color: #666;
      font-size: 12px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
      padding: 12px;
      border-radius: 4px;

      p {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .anticon {
          margin-right: 6px;
        }
      }
    }

    .footer-actions {
      display: flex;
      justify-content: center;
      gap: 16px;

      .ant-btn {
        min-width: 80px;
        border-radius: 4px;
        transition: all 0.3s;

        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
    }
  }
}
</style>

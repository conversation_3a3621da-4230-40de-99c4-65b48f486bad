<template>
  <base-form-template
    title="管理评估清单"
    title-icon="solution"
    :mode="mode"
    @ok="handleSubmit"
    @nextStep="handleSubmit"
    @back="onBack"
  >
    <!-- 表单内容区域 -->
    <a-form :form="form" class="flow-form-content">
      <div class="form-section">
        <div class="section-title">
          <span>管理评估清单</span>
        </div>

        <!-- 调查方式切换 -->
        <a-form-item label="调查方式：" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
          <a-radio-group v-model="surveyMethod" :disabled="mode === 'view'">
            <a-radio :value="'system'">在线笔录</a-radio>
            <a-radio :value="'upload'">上传笔录/证明材料</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 系统填写笔录 -->
        <div v-show="surveyMethod === 'system'">
          <a-row type="flex" justify="end" style="margin-bottom: 15px;">
            <a-col>
              <a-button v-if="mode !== 'view'" type="primary" @click="addAssessmentItem">
                <a-icon type="plus" /> 添加清单
              </a-button>
            </a-col>
          </a-row>
          <a-table
            :columns="mode === 'view' ? columnsView : columns"
            :dataSource="assessmentItems"
            :pagination="false"
            size="middle"
            :rowKey="record => record.id"
          >
            <template slot="operation" slot-scope="text, record">
              <a v-if="record.progress !== '2'" @click="toEdit(record)">去填写</a>
              <a v-if="record.progress !== '2'" class="ant-divider" />
              <a @click="removeAssessmentItem(record.id)">删除</a>
              <a v-if="record.progress === '2'" class="ant-divider" />
              <a v-if="record.progress === '2'" @click="toEdit(record)">编辑</a>
              <a v-if="record.progress === '2'" class="ant-divider" />
              <a v-if="record.progress === '2'" @click="handleViewRecord(record)">查看</a>
              <a v-if="record.progress === '2'" class="ant-divider" />
              <a v-if="record.progress === '2'" @click="handleDownloadRecord(record)">下载</a>
              <a v-if="record.progress === '2'" class="ant-divider" />
              <a v-if="record.progress === '2'" @click="exportToWord(record.id, record)">导出Word</a>
            </template>
            <template slot="viewOperation" slot-scope="text, record">
              <a @click="handleViewRecord(record)">查看</a>
              <a class="ant-divider" />
              <a @click="handleDownloadRecord(record)">下载</a>
              <a class="ant-divider" />
              <a @click="exportToWord(record.id, record)">导出Word</a>
            </template>
            <template slot="progress" slot-scope="text, record">
              <div :class="[record.progress === '2' ? 'ytx-bg' : 'wtx-bg']">
                {{ record.progress === '2' ? '已填写' : record.progress === '1' ? '已暂存' : '未填写' }}
              </div>
            </template>
            <template slot="paperType" slot-scope="text, record">
              {{ 'BLLX' | dictType(record.paperType) }}
            </template>
          </a-table>
          <a-alert
            v-if="mode === 'edit'"
            style="margin-top: 10px;"
            message="说明:根据调查对象的信息，已自动生成四份待调查笔录，请根据实际情况进行调查过程填写，至少需填写一份"
            type="info"
            show-icon
          />

          <!-- 系统填写笔录附件 -->
          <div class="form-section" style="margin-top: 16px;">
            <div class="section-title">
              <span>基层组织和辖区派出所意见附件</span>
            </div>

            <a-form-item label="基层组织意见附件" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-file-uploader
                v-model="systemFileList1"
                :url="'/sysFileInfo/uploadOss'"
                :maxFileSize="20 * 1024 * 1024"
                :acceptedFormats="'.pdf'"
                :readonly="mode === 'view'"
                @change="handleChange($event, 1)"
              />
            </a-form-item>
            <a-form-item label="辖区公安派出所意见附件" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-file-uploader
                v-model="systemFileList2"
                :url="'/sysFileInfo/uploadOss'"
                :maxFileSize="20 * 1024 * 1024"
                :acceptedFormats="'.pdf'"
                :readonly="mode === 'view'"
                @change="handleChange($event, 2)"
              />
            </a-form-item>
            <!-- 文书制作-调查评估笔录 -->
            <div class="section-title" style="margin-top: 16px;">
              <span>文书制作-调查评估笔录</span>
            </div>
            <template v-if="localBlList && localBlList.length > 0">
              <a-form-item
                v-for="(item, index) in localBlList"
                :key="item.id + index"
                :label="'BLLX' | dictType(item.fileBucket)"
                :labelCol="labelCol2"
                :wrapperCol="wrapperCol2"
              >
                <document-creator
                  v-decorator="[`blListFiles_${index}`, { initialValue: [item] }]"
                  :formData="form.getFieldsValue()"
                  :disabled="mode === 'view'"
                  :status="detailData.status"
                  :create-document-api="'/investigationInfo/createBl'"
                  :detailData="detailData"
                  :enableCloudSign="false"
                  :showStatusTag="false"
                  :isShowAuthSign="false"
                  paperType="BLLX"
                  :showUpload="false"
                  :validate-form="() => item"
                  @docToWord="exportToWord"
                />
              </a-form-item>
            </template>
            <div class="section-title">
              <span>调查补充附件</span>
            </div>

            <a-form-item label="附件" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-file-uploader
                v-model="systemFileList"
                :url="'/sysFileInfo/uploadOss'"
                :maxFileSize="20 * 1024 * 1024"
                :acceptedFormats="'.jpg,.png,.pdf,.zip,.rar'"
                :readonly="mode === 'view'"
              />
            </a-form-item>
          </div>
        </div>

        <!-- 上传笔录/证明材料表单 -->
        <div v-show="surveyMethod === 'upload'" class="upload-survey-form">
          <!-- 上传笔录/证明材料附件 - 移到前面 -->
          <div>
            <a-form-item label="笔录/证明材料" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
              <sh-file-uploader
                v-model="uploadFileList"
                :url="'/sysFileInfo/uploadOss'"
                :maxFileSize="20 * 1024 * 1024"
                :acceptedFormats="'.jpg,.png,.pdf,.zip,.rar'"
                :readonly="mode === 'view'"
              />
            </a-form-item>
          </div>
          <div class="form-section">
            <div class="section-title">
              <span>调查评估表信息登记</span>
            </div>

            <!-- 调查对象 -->
            <div>
              <h3 class="category-title" @click="toggleSection('investigationObject')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.investigationObject }">》</span>
                调查对象
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.investigationObject">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-input
                          v-decorator="['correctionObjName', { rules: [{ required: true, message: '请输入姓名' }] }]"
                          :disabled="true"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <sh-select
                          v-decorator="['nation']"
                          dictType="mz"
                          style="width: 100%;"
                          allowClear
                          placeholder="请选择"
                          :disabled="mode === 'view'"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="曾用名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-input v-decorator="['usedName']" :disabled="mode === 'view'" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="别名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-input v-decorator="['nickName']" :disabled="mode === 'view'" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-input v-decorator="['contactTel']" :disabled="mode === 'view'" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 家庭和社会关系 -->
            <div>
              <h3 class="category-title" @click="toggleSection('familySocial')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.familySocial }">》</span>
                家庭和社会关系
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.familySocial">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="家庭成员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['jtcy']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="社会交往情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['shjwqk']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="主要社会关系" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['zyshgx']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="未成年对象的其他情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['wcndxdqtqk']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 个性特点 -->
            <div>
              <h3 class="category-title" @click="toggleSection('personalityTraits')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.personalityTraits }">》</span>
                个性特点
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.personalityTraits">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="生理状况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['slzk']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="心理特征" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['xltz']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="性格类型" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <sh-select
                          v-decorator="['xglx']"
                          dictType="xglx"
                          style="width: 100%;"
                          allowClear
                          placeholder="请选择"
                          :disabled="mode === 'view'"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="爱好特长" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['ahtc']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 一般表现 -->
            <div>
              <h3 class="category-title" @click="toggleSection('generalPerformance')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.generalPerformance }">》</span>
                一惯表现
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.generalPerformance">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="工作（学习）表现" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['gzxxbx']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="遵纪守法情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['zjsfqk']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="有无不良嗜好行为恶习" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['ywblshxwex']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 犯罪情况和悔罪表现 -->
            <div>
              <h3 class="category-title" @click="toggleSection('crimeSituation')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.crimeSituation }">》</span>
                犯罪情况和悔罪表现
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.crimeSituation">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="犯罪原因" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['fzyy']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="主观恶性" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['zgex']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="是否有犯罪前科" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['sfyfzqk']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="认罪悔罪态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['rzhztd']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 社会反响 -->
            <div>
              <h3 class="category-title" @click="toggleSection('socialResponse')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.socialResponse }">》</span>
                社会反响
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.socialResponse">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="被害人或其家属态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['bhrhqqstd']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="社会公众态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['shgztd']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 监管条件 -->
            <div>
              <h3 class="category-title" @click="toggleSection('supervisionConditions')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.supervisionConditions }">》</span>
                监管条件
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.supervisionConditions">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="家庭成员态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['jtcytd']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="经济生活状况和环境" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['jjshzkhhj']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item
                        label="工作单位、就读学校和村（社区）基层组织意见"
                        :labelCol="labelCol2"
                        :wrapperCol="wrapperCol2"
                      >
                        <a-textarea
                          v-decorator="['gzdwjdxxhcjczzjyj']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="基层组织意见附件:" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <sh-file-uploader
                          v-model="systemFileList1"
                          :url="'/sysFileInfo/uploadOss'"
                          :maxFileSize="20 * 1024 * 1024"
                          :acceptedFormats="'.pdf'"
                          :readonly="mode === 'view'"
                          @change="handleChange($event, 1)"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="拟禁止的事项" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea
                          v-decorator="['ywjzdsx']"
                          :disabled="mode === 'view'"
                          :rows="3"
                          placeholder="请输入"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>

            <!-- 辖区公安派出所意见 -->
            <div>
              <h3 class="category-title" @click="toggleSection('policeOpinion')">
                <span class="collapse-icon" :class="{ collapsed: !collapsedSections.policeOpinion }">》</span>
                辖区公安派出所意见
              </h3>
              <transition name="slide">
                <div class="category-content" v-show="collapsedSections.policeOpinion">
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="辖区公安派出所意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <a-textarea v-decorator="['gayj']" :disabled="mode === 'view'" :rows="3" placeholder="请输入" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="16">
                    <a-col :span="24">
                      <a-form-item label="辖区公安派出所意见附件:" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                        <sh-file-uploader
                          v-model="systemFileList2"
                          :url="'/sysFileInfo/uploadOss'"
                          :maxFileSize="20 * 1024 * 1024"
                          :acceptedFormats="'.pdf'"
                          :readonly="mode === 'view'"
                          @change="handleChange($event, 2)"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </div>

      <a-modal title="新增评估清单" width="60%" :visible="modalVisible" @ok="handleModalOk" @cancel="handleModalCancel">
        <a-form :form="modalForm">
          <a-form-item label="请选择" :labelCol="{ span: 4 }" :wrapperCol="{ span: 18 }">
            <sh-select
              dictType="LJLX"
              style="width: 100%;"
              placeholder="请选择"
              disabled
              v-decorator="['correctionType', { rules: [{ required: true, message: '请选择' }] }]"
            />
          </a-form-item>
          <a-form-item label="评估表单类型" :labelCol="{ span: 4 }" :wrapperCol="{ span: 18 }">
            <sh-select
              dictType="BLLX"
              style="width: 100%;"
              placeholder="请选择"
              v-decorator="['paperType', { rules: [{ required: true, message: '请选择评估表单类型' }] }]"
              @change="changeBllx"
            />
          </a-form-item>
          <a-form-item label="笔录名称" :labelCol="{ span: 4 }" :wrapperCol="{ span: 18 }">
            <a-select
              v-decorator="['paperId', { rules: [{ required: true, message: '请选择笔录名称' }] }]"
              placeholder="请选择"
              :options="titleOption"
            ></a-select>
          </a-form-item>
          <a-form-item label="设置数量" :labelCol="{ span: 4 }" :wrapperCol="{ span: 18 }">
            <a-input
              v-decorator="['setCount', { initialValue: 1, rules: [{ required: true, message: '请输入数量' }] }]"
              placeholder="设置数量"
              @change="validateScore"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <!-- <a-button style="margin-right: 8px" @click="handleReset">取消</a-button> -->
        <a-button style="margin-right: 8px" @click="handleDraft">暂存</a-button>
        <a-button style="margin-right: 8px" @click="handlePrevStep">上一步</a-button>
        <a-button type="primary" @click="handleSubmit">保存并下一步</a-button>
      </div>
      <div v-else>
        <a-button @click="onBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
    <EvaluationRecord ref="EvaluationRecord" @refresh="loadData" :correctionType="correctionType" />
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import EvaluationRecord from './components/EvaluationRecord'
import DocumentCreator from './components/DocumentCreator.vue'
import { paperMaintenancePage } from '@/api/modular/main/papermaintenance/paperMaintenanceManage'
import {
  investigationInfoTranscriptAdd,
  investigationInfoTranscriptList,
  groupInvestigateList,
  investigationInfoTranscriptDelete,
  investigationInfoTranscriptType
} from '@/api/modular/main/unitReceiveForm/unitReceiveForm'
import { downloadFile } from '@/utils/fileUtils'
export default {
  components: {
    BaseFormTemplate,
    EvaluationRecord,
    DocumentCreator
  },
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit' // 'edit' 或 'view'
    }
  },
  data() {
    return {
      moment,
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      form: this.$form.createForm(this),
      modalForm: this.$form.createForm(this),
      modalVisible: false,
      surveyMethod: 'system', // 默认为系统填写
      pgqdFileList: [], // 旧的文件列表，保留用于兼容
      systemFileList: [], // 系统填写笔录的附件
      systemFileList1: [], // 基层组织意见附件
      systemFileList2: [], // 辖区公安派出所意见附件
      uploadFileList: [], // 上传笔录/证明材料的附件
      localBlList: [], // 本地存储的blList，而不是直接修改props
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          textAlign: 'center',
          width: 60,
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        { title: '清单名称', dataIndex: 'title' },
        { title: '评估表单类型', dataIndex: 'paperType', scopedSlots: { customRender: 'paperType' } },
        { title: '状态', dataIndex: 'progress', width: 100, scopedSlots: { customRender: 'progress' } },
        { title: '操作', dataIndex: 'operation', scopedSlots: { customRender: 'operation' } }
      ],
      columnsView: [
        {
          title: '序号',
          dataIndex: 'index',
          textAlign: 'center',
          width: 60,
          customRender: (text, record, index) => {
            return index + 1
          }
        },
        { title: '清单名称', dataIndex: 'title' },
        { title: '评估表单类型', dataIndex: 'paperType', scopedSlots: { customRender: 'paperType' } },
        { title: '状态', dataIndex: 'progress', width: 100, scopedSlots: { customRender: 'progress' } },
        { title: '操作', dataIndex: 'operation', scopedSlots: { customRender: 'viewOperation' } }
      ],
      assessmentItems: [],
      nextId: 6,
      titleOption: [],
      correctionType: '',
      collapsedSections: {
        investigationObject: true,
        familySocial: true,
        personalityTraits: true,
        generalPerformance: true,
        crimeSituation: true,
        socialResponse: true,
        supervisionConditions: true,
        policeOpinion: true
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    console.log(this.userInfo, 'userInfo')
    // 初始化本地blList
    if (this.detailData && this.detailData.blList) {
      this.localBlList = [...this.detailData.blList]
    }
    this.loadData()
    this.initBltype()
    // 如果有记录，则加载数据
    if (this.record && this.record.id) {
      this.loadFormData()
    }
  },
  methods: {
    handleChange(info, index) {
      this.$set(this, `systemFileList${index}`, [info[info.length - 1]])
      if (info.length > 1) return this.$message.warning('只允许上传一个文件,已自动替换为最新上传文件！')

      // this.systemFileList(index )= [info[info.length - 1]]
    },
    // 添加上一步处理方法
    handlePrevStep() {
      // 弹出确认框，询问是否返回上一步
      this.$confirm({
        title: '操作提示',
        content: '确定要返回上一步吗？当前未保存的数据将会丢失。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 触发父组件中的上一步事件
          this.$emit('prevStep', 'PGZT03_1')
        }
      })
    },
    handleViewRecord(record, index) {
      console.log('record', record, index)
      if (process.env.NODE_ENV === 'development') {
        this.$imageViewer.view(
          ['/api/investigationInfo/transcriptDownload?id=' + record.id + '&fileType=' + '.pdf'],
          0,
          '/pdfJsLib/web/viewer.html'
        )
      } else {
        this.$imageViewer.view(
          ['/api/investigationInfo/transcriptDownload?id=' + record.id + '&fileType=' + '.pdf'],
          0,
          '/pdfJsLib/web/viewer.html'
        )
      }
    },

    handleDownloadRecord(record) {
      this.$message.info(`下载中，请稍候...`)
      this.$http
        .get(`/investigationInfo/transcriptDownload?id=${record.id}&type=1`, {
          responseType: 'blob'
        })
        .then(response => {
          if (response) {
            downloadFile(response)
            this.$message.success('下载成功')
          }
        })
        .catch(error => {
          console.error('下载失败：', error)
          this.$message.error('下载失败，请稍后重试')
        })
    },

    exportToWord(id, record) {
      console.log('exportToWord', id, record)
      const exportKey = `export-${Date.now()}`
      this.$message.loading({ content: '正在导出Word文档，请稍候...', key: exportKey, duration: 0 })

      this.$http
        .get(`/investigationInfo/transcriptDownloadWord`, {
          params: {
            id: id,
            type: 1
          },
          responseType: 'blob'
        })
        .then(response => {
          downloadFile(response, `${record.title || '笔录'}.docx`)
          this.$message.success({ content: 'Word文档导出成功', key: exportKey, duration: 2 })
        })
        .catch(error => {
          console.error('导出Word失败:', error)
          this.$message.error({ content: 'Word文档导出失败，请稍后重试', key: exportKey, duration: 3 })
        })
    },

    loadData(data) {
      // 当data参数存在且有长度时，更新本地blList
      if (data) {
        this.localBlList = data
        console.log(this.localBlList, 'color:pin2k')
      } else if (this.detailData.blList && this.detailData.blList.length) {
        // 初始化时，如果detailData中有blList数据，则复制到本地
        this.localBlList = [...this.detailData.blList]
      }

      investigationInfoTranscriptList({ id: this.record.id }).then(res => {
        if (res.success) {
          this.assessmentItems = res.data.filter(item => item.tag === 1)
        }
      })
    },
    validateScore(e) {
      console.log(e.target.value)
      const reg = /^[1-9]\d*$/
      if (!reg.test(e.target.value)) {
        this.$message.warning('请输入正确的正整数')
        e.target.value = ''
      }
    },
    initBltype() {
      investigationInfoTranscriptType({ id: this.record.id }).then(res => {
        if (res.success) {
          this.correctionType = res.data // 'LJLX02'
        }
      })
    },
    changeBllx(e) {
      console.log(this.modalForm.getFieldsValue(), e, 2222)
      this.initBlData(e)
    },
    initBlData(type) {
      paperMaintenancePage({
        pageNo: 1,
        pageSize: 999999,
        paperType: this.correctionType,
        blType: type
      }).then(res => {
        if (res.success) {
          this.titleOption = res.data.rows.map(item => ({ value: item.id, label: item.title }))
        }
      })
    },
    loadFormData() {
      setTimeout(async () => {
        console.log(this.detailData.groups.pgqdFileList, 123333)
        this.$nextTick(async () => {
          this.modalForm.setFieldsValue({
            correctionType: this.record.correctionType || '',
            pgqdFileList: this.detailData.groups.pgqdFileList || []
          })

          // 兼容旧数据，根据调查方式设置不同的文件列表
          if (this.detailData.groups.pgqdFileList) {
            if (this.detailData.groups.surveyMethod === 'upload') {
              this.uploadFileList = this.detailData.transcripts.filter(item => item.tag === 2)
            } else {
              this.systemFileList = this.detailData.groups.pgqdFileList
              this.systemFileList1 = this.detailData.groups.jcyjFileList
              this.systemFileList2 = this.detailData.groups.gayjFileList
            }
            this.pgqdFileList = this.detailData.groups.pgqdFileList
          }

          // 如果存在上传表单的数据，设置表单数据
          if (this.detailData.groups.uploadSurveyData) {
            this.surveyMethod = 'upload'
            this.form.setFieldsValue(this.detailData.groups.uploadSurveyData)
          }

          // 如果有明确指定的调查方式，则使用指定的方式
          if (this.detailData.groups.surveyMethod) {
            this.surveyMethod = this.detailData.groups.surveyMethod
          }
          if (this.mode !== 'view') {
            this.$nextTick(() => {
              console.log(this.detailData, 1200)
              this.form.setFieldsValue({
                correctionObjName: this.detailData.correctionObjName,
                nation: this.detailData.nationality || '',
                usedName: this.detailData.usedName || '',
                nickName: this.detailData.nickName || '',
                contactTel: this.detailData.contactTel || ''
              })
            })
          }

          // 处理transcripts数据
          if (this.detailData.transcripts && this.detailData.transcripts.length > 0) {
            await this.$nextTick()
            const firstTranscript = this.detailData.transcripts[0]
            console.log(firstTranscript, 123333)
            // 根据tag设置调查方式
            if (this.detailData.transcriptTag === 2) {
              this.surveyMethod = 'upload'

              // 解析context字段
              if (firstTranscript.context) {
                try {
                  const contextData = JSON.parse(firstTranscript.context)
                  console.log(contextData, 45678)
                  this.form.setFieldsValue(contextData)
                } catch (e) {
                  console.error('解析context失败:', e)
                }
              }

              // 设置附件列表
              if (firstTranscript.blFileList && firstTranscript.blFileList.length > 0) {
                this.uploadFileList = firstTranscript.blFileList
              }
            }
          }
        })
      }, 100)
    },
    addAssessmentItem() {
      // this.$confirm({
      //   title: '提示',
      //   content: '添加清单后，【上传笔录/证明材料】的附件内容会被清空,是否继续?',
      //   onOk: () => {
      //     this.modalVisible = true
      //     this.modalForm.resetFields()
      //     this.$nextTick(() => {
      //       this.modalForm.setFieldsValue({
      //         correctionType: this.correctionType
      //       })
      //     })
      //   }
      // })
      this.modalVisible = true
      this.modalForm.resetFields()
      this.$nextTick(() => {
        this.modalForm.setFieldsValue({
          correctionType: this.correctionType
        })
      })
    },
    handleModalOk() {
      this.modalForm.validateFields((err, values) => {
        if (!err) {
          values.pid = this.record.id
          values.progress = 0 // 默认传0
          investigationInfoTranscriptAdd(values).then(res => {
            if (res.success) {
              this.$message.success('添加成功')
              this.uploadFileList = []
              this.modalVisible = false
              this.modalForm.resetFields()
              this.loadData()
            }
          })
        }
      })
    },
    handleModalCancel() {
      this.modalVisible = false
      this.modalForm.resetFields()
    },
    removeAssessmentItem(id) {
      investigationInfoTranscriptDelete({ id: id }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.loadData(res.data)
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    onPrevStep() {
      this.$emit('prevStep')
    },
    handleSubmit() {
      console.log('handleSubmit', this.systemFileList1, this.systemFileList2, '----------')
      if (this.surveyMethod === 'system') {
        let flag = false

        if (this.assessmentItems.length > 0) {
          // 判断列表中是否有未填写的 数组全满足条件并且有表单数据
          flag = this.assessmentItems.every(item => item.progress === '2')
        } else {
          flag = false
          this.$message.warning('您未生成清单')
          return
        }

        if (flag) {
          // 获取所有表单值
          const values = this.form.getFieldsValue()

          // 提取所有blListFiles_*字段的文件id
          const blFileIds = Object.keys(values)
            .filter(key => key.startsWith('blListFiles_'))
            .map(key => values[key])
            .flat()
            .filter(item => item && item.id)
            .map(item => item.id)
            .join(',')

          groupInvestigateList({
            id: this.record.id,
            approvalDeptId: this.userInfo.loginEmpInfo.orgId,
            approvalDeptName: this.userInfo.loginEmpInfo.orgName,
            otherFiles:
              this.systemFileList &&
              this.systemFileList
                .map(item => {
                  return item && item.id
                })
                .toString(),
            jcyjFile:
              this.systemFileList1.length > 0
                ? this.systemFileList1
                    .map(item => {
                      return item && item.id
                    })
                    .toString()
                : '',
            gayjFile:
              this.systemFileList2.length > 0
                ? this.systemFileList2
                    .map(item => {
                      return item && item.id
                    })
                    .toString()
                : '',
            surveyMethod: this.surveyMethod,
            tag: this.surveyMethod === 'system' ? 1 : 2,
            blFileIds: blFileIds || ''
          }).then(res => {
            if (res.success) {
              this.$message.success('评估清单提交成功')
              this.$emit('nextStep')
            }
          })
        } else {
          this.$message.warning('您还有未填写的清单')
        }
      } else {
        // 上传笔录模式提交
        this.form.validateFields((err, values) => {
          if (!err) {
            // 检查是否上传了附件
            if (!this.uploadFileList || this.uploadFileList.length === 0) {
              this.$message.warning('请上传笔录或证明材料')
              return
            }
            console.log('values', this.systemFileList1.length, this.systemFileList2.length)
            groupInvestigateList({
              id: this.detailData.id,
              transcriptId: this.detailData?.transcripts[0]?.id || '',
              approvalDeptId: this.userInfo.loginEmpInfo.orgId,
              approvalDeptName: this.userInfo.loginEmpInfo.orgName,
              blFileIds:
                this.uploadFileList &&
                this.uploadFileList
                  .map(item => {
                    return item && item.id
                  })
                  .toString(),
              jcyjFile:
                this.systemFileList1.length > 0
                  ? this.systemFileList1
                      .map(item => {
                        return item && item.id
                      })
                      .toString()
                  : '',
              gayjFile:
                this.systemFileList2.length > 0
                  ? this.systemFileList2
                      .map(item => {
                        return item && item.id
                      })
                      .toString()
                  : '',
              surveyMethod: this.surveyMethod,
              tag: this.surveyMethod === 'system' ? 1 : 2,
              uploadSurveyData: values,
              context: JSON.stringify(values)
            }).then(res => {
              if (res.success) {
                this.$message.success('评估清单提交成功')
                this.$emit('nextStep')
              }
            })
          } else {
            this.$message.warning('请完善表单信息')
          }
        })
      }
    },
    handleReset() {
      this.form.resetFields()
      this.surveyMethod = 'system' // 重置调查方式
      // 重置评估项目
      this.assessmentItems = [
        { id: 1, index: 1, name: '社会关系', description: '家庭、社会关系稳定性评估', weight: 3 },
        { id: 2, index: 2, name: '心理状况', description: '心理健康程度评估', weight: 2 },
        { id: 3, index: 3, name: '现实表现', description: '社区矫正期间现实表现评估', weight: 3 },
        { id: 4, index: 4, name: '再犯风险', description: '再犯风险评估', weight: 4 },
        { id: 5, index: 5, name: '遵纪守法', description: '遵守法律法规情况评估', weight: 3 }
      ]
      this.nextId = 6
      this.systemFileList = []
      this.systemFileList1 = []
      this.systemFileList2 = []
      this.uploadFileList = []
      this.pgqdFileList = []
      this.loadFormData()
      this.$emit('close')
    },
    onBack() {
      this.$emit('back')
    },
    toEdit(record) {
      // 跳转到评估表单页面
      this.$refs.EvaluationRecord.open(record)
    },
    handleDraft() {
      if (this.surveyMethod === 'system') {
        // 获取所有表单值
        const values = this.form.getFieldsValue()

        // 提取所有blListFiles_*字段的文件id
        const blFileIds = Object.keys(values)
          .filter(key => key.startsWith('blListFiles_'))
          .map(key => values[key])
          .flat()
          .filter(item => item && item.id)
          .map(item => item.id)
          .join(',')

        // 系统填写模式直接提交，不检查是否已填写完成
        groupInvestigateList({
          id: this.record.id,
          approvalDeptId: this.userInfo.loginEmpInfo.orgId,
          approvalDeptName: this.userInfo.loginEmpInfo.orgName,
          otherFiles: this.systemFileList && this.systemFileList.map(item => item.id).toString(),
          jcyjFile:
            this.systemFileList1.length > 0
              ? this.systemFileList1
                  .map(item => {
                    return item && item.id
                  })
                  .toString()
              : '',
          gayjFile:
            this.systemFileList2.length > 0
              ? this.systemFileList2
                  .map(item => {
                    return item && item.id
                  })
                  .toString()
              : '',
          surveyMethod: this.surveyMethod,
          tag: this.surveyMethod === 'system' ? 1 : 2,
          blFileIds: blFileIds || '',
          isDraft: 1
        }).then(res => {
          if (res.success) {
            this.$message.success('暂存成功！')
            this.$emit('close')
          }
        })
      } else {
        // 上传笔录模式不验证表单，直接获取值
        const values = this.form.getFieldsValue()
        groupInvestigateList({
          id: this.detailData.id,
          transcriptId: this.detailData?.transcripts[0]?.id || '',
          approvalDeptId: this.userInfo.loginEmpInfo.orgId,
          approvalDeptName: this.userInfo.loginEmpInfo.orgName,
          blFileIds:
            this.uploadFileList &&
            this.uploadFileList
              .map(item => {
                return item && item.id
              })
              .toString(),
          jcyjFile:
            this.systemFileList1.length > 0
              ? this.systemFileList1
                  .map(item => {
                    return item && item.id
                  })
                  .toString()
              : '',
          gayjFile:
            this.systemFileList2.length > 0
              ? this.systemFileList2
                  .map(item => {
                    return item && item.id
                  })
                  .toString()
              : '',
          surveyMethod: this.surveyMethod,
          tag: this.surveyMethod === 'system' ? 1 : 2,
          uploadSurveyData: values,
          context: JSON.stringify(values),
          isDraft: 1
        }).then(res => {
          if (res.success) {
            this.$message.success('暂存成功！')
            this.$emit('close')
          }
        })
      }
    },
    toggleSection(sectionKey) {
      this.collapsedSections[sectionKey] = !this.collapsedSections[sectionKey]
    }
  }
}
</script>

<style lang="less" scoped>
/* 添加折叠动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
  max-height: 2000px; /* 根据实际内容高度调整 */
  overflow: hidden;
}

.slide-enter,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

/* 保持原有样式不变 */
.category-content {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  max-height: 2000px; /* 根据实际内容高度调整 */

  &-enter,
  &-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  &-enter-active,
  &-leave-active {
    transition: all 0.3s ease-in-out;
  }
}

// 保持原有样式不变
.ytx-bg {
  text-align: center;
  background: rgba(18, 191, 92, 0.08);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(18, 191, 92, 0.13);
  color: #11af54;
  width: 75%;
}
.wtx-bg {
  background: rgba(255, 24, 24, 0.08);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(255, 22, 22, 0.08);
  text-align: center;
  color: #ff1616;
  width: 75%;
}
.flow-form-content {
  padding: 0;
  overflow-x: hidden;
}

// 表单区域样式
.form-section {
  background: #fff;
  // padding: 16px;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.section-line {
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

// 类别标题样式
.category-title {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #1890ff;
  &:hover {
    color: #1890ff;
  }
}

// 上传表单布局
.upload-survey-form {
  .ant-form-item {
    margin-bottom: 12px;
  }
}

.file-table-container {
  margin-bottom: 20px;
}

// 新增旋转图标样式
.collapse-icon {
  display: inline-block;
  transition: transform 0.3s ease-in-out;
  margin-right: 8px;

  &.collapsed {
    transform: rotate(0deg);
  }

  &:not(.collapsed) {
    transform: rotate(90deg);
  }
}
</style>

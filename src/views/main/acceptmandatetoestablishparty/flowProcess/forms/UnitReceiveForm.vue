<template>
  <base-form-template
    title="调查单位接收"
    title-icon="notification"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack">
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>

      <!-- 调查单位接收 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查单位接收</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="接收结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-tree-select
                v-decorator="['approvalResult', {
                  rules: [{ required: true, message: '请选择接收结果' }]
                }]"
                @change="handleApprovalResultChange"
                apiURL="/sysDictData/treeSp"
                :params="{ dictTypeId: '1732298638446571522',status: detailData.status }"
                :disabled="mode === 'view'"
                placeholder="请选择接收结果"
                showSearch />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="showInvestigationFields">
            <a-form-item label="调查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-tree-select
                v-decorator="['inveDept', { rules: [{ required: showInvestigationFields, message: '请选择调查单位' }] }]"
                apiURL="/sysOrg/tree"
                :params="{ level: 4 }"
                placeholder="选择本区县管辖的下级司法所"
                :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 当接收结果为1时显示的内容 -->
        <template v-if="showInvestigationFields">
          <a-row :gutter="16">

            <a-col :span="24">
              <a-form-item label="调查人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <sh-person-selector
                  :disabled="mode === 'view'"
                  :treeUrl="'/sysOrg/tree?remark=all'"
                  treeLabelKey="title"
                  treeValueKey="id"
                  :list-url="'/sysUser/page'"
                  :name-field="'nickName'"
                  :org-field="'orgNames'"
                  v-decorator="[
                    'invePsnList',
                    {
                      rules: [{ type: 'array', required: showInvestigationFields, message: '请选择调查人员!' }]
                    }
                  ]" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="收到委托时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  v-decorator="['entrustmentReceiveTime', { rules: [{ required: showInvestigationFields, message: '请选择收到委托时间' }] }]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  :disabledDate="disabledFutureDate"
                  placeholder="默认收到调查时间，可修改"
                  :disabled="true" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调查截止时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  v-decorator="['inveTimeLimit', { rules: [{ required: showInvestigationFields, message: '请选择调查截止时间' }] }]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  :disabled="true" />
              </a-form-item>
            </a-col>
          </a-row>
        </template>

        <!-- 当接收结果不为1时显示的内容 -->
        <template v-if="!showInvestigationFields">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="退回意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="['returnRemark', { rules: [{ required: !showInvestigationFields, message: '请输入退回意见' }] }]"
                  :rows="4"
                  placeholder="请输入退回意见"
                  :disabled="mode === 'view'" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="退回材料" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <sh-file-uploader
                  v-decorator="['returnFiles', { rules: [{ type: 'array', required: false, message: '请上传退回材料' }] }]"
                  :url="'/sysFileInfo/uploadOss'"
                  :maxFileSize="20 * 1024 * 1024"
                  :acceptedFormats="'.jpg,.png,.gif,.docx,.pdf,.zip,.rar'"
                  :readonly="mode === 'view'" />
              </a-form-item>
            </a-col>
          </a-row>
        </template>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-textarea v-decorator="['approvalRemark']" :rows="4" placeholder="请输入备注" :disabled="mode === 'view'" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审核人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalPsn']" placeholder="默认当前登录人" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalTime']" placeholder="2025-02-28 15:30:00" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />{{ isApprovalResultOne ? '保存并下一步' : '提交' }}
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack">
          <a-icon type="left" />返回
        </a-button>
      </div>
    </template>
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  components: {
    BaseFormTemplate
  },
  props: {
    currentNode: {
      type: Object,
      default: () => ({})
    },
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      showInvestigationFields: true, // 默认显示调查机构相关字段
      isApprovalResultOne: true, // 默认值为true
      investigationFieldsCache: {} // 新增：用于缓存调查评估相关字段的数据
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData();
  },
  methods: {
    disabledFutureDate,
    handleApprovalResultChange(value) {
      // 当值变更前，缓存当前表单中调查评估相关字段的值
      if (this.showInvestigationFields) {
        const currentValues = this.form.getFieldsValue([
          'inveDept',
          'invePsnList',
          'entrustmentReceiveTime',
          'inveTimeLimit'
        ]);
        this.investigationFieldsCache = { ...currentValues };
      } else {
        // 当要切回显示时，缓存退回相关字段的值
        const currentValues = this.form.getFieldsValue([
          'returnRemark',
          'returnFiles'
        ]);
        this.investigationFieldsCache = { ...currentValues };
      }

      // 更新显示状态
      this.showInvestigationFields = value === '1';
      this.isApprovalResultOne = value === '1';

      // 当状态切换后，恢复之前缓存的值
      if (Object.keys(this.investigationFieldsCache).length > 0) {
        this.$nextTick(() => {
          this.form.setFieldsValue(this.investigationFieldsCache);
        });
      }
    },

    initFormData() {
      // 使用父组件传入的detailData作为表单数据源
      const formValues = this.detailData || {};
      const groups = formValues.groups || {};

      // 设置表单的初始值
      this.$nextTick(async() => {
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
        // 在非查看模式下，设置当前用户为审核人，当前时间为审核时间
        const approvalPsn = this.mode !== 'view' ? (this.userInfo?.name || '当前用户') : (groups.approvalPsn || '默认当前登录人');
        const approvalTime = this.mode !== 'view' ? currentTime : (groups.approvalTime ? moment(groups.approvalTime).format('YYYY-MM-DD HH:mm:ss') : currentTime);

        // 初始化显示调查机构字段状态

        if ((this.currentNode?.rawData?.type === 9 && this.currentNode?.rawData?.draft === 1) || this.mode === 'view') {
          this.showInvestigationFields = groups.approvalResult === '1';
          await this.$nextTick();
        } else {
          await this.$nextTick();
          groups.approvalResult = '1';
        }

        // 设置表单默认值
        this.form.setFieldsValue({
          id: formValues.id || null,
          approvalResult: groups.approvalResult || null,
          inveDept: groups.inveDept || null,
          invePsnList: groups.invePsnList || [],
          entrustmentReceiveTime: groups.entrustmentReceiveTime,
          inveTimeLimit: groups.inveTimeLimit ? moment(groups.inveTimeLimit) : moment().add(30, 'days'),
          returnRemark: groups.returnRemark || '',
          returnFiles: groups.returnFiles || [],
          approvalRemark: groups.approvalRemark || '',
          approvalPsn: approvalPsn,
          approvalTime: approvalTime
        });
      });
    },

    handleOk() {
      this.handleSubmit(true);
    },

    // 通用提交方法，参数showConfirm控制是否显示确认对话框
    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return; // 如果正在提交且是直接下一步模式，阻止操作

      // 设置提交加载状态
      this.submitLoading = true;

      this.form.validateFields((err, values) => {
        if (!err) {
          // 准备表单数据
          const preparedData = this.prepareFormData(values);

          // 使用$http进行请求
          this.$http.post('/investigationInfo/groupInvestigateAccept', preparedData)
            .then(response => {
              console.log('保存成功：', response.data);

              // 请求成功，关闭加载状态
              this.submitLoading = false;

              // 判断接口返回是否成功
              if (response && response.success) {
                // 处理成功响应
                this.$message.success('提交成功');
                // 根据approvalResult的值决定是关闭还是进入下一步
                if (!this.isApprovalResultOne) {
                  this.handleClose();
                } else {
                  this.$emit('nextStep', preparedData);
                }
              } else {
                // 接口返回失败
                const errorMsg = (response && response.message) || '提交失败，请稍后重试';
                this.$message.error(errorMsg);
              }
            })
            .catch(error => {
              console.error('保存失败：', error);
              this.$message.error('提交失败，请稍后重试');
              this.submitLoading = false;
            });
        } else {
          // 表单验证失败，重置加载状态
          this.submitLoading = false;
          console.error('表单验证失败：', err);
          this.$message.error('请完善必填信息');
        }
      });
    },

    // 处理表单数据的通用方法
    prepareFormData(values, isDraft = false) {
      // 拷贝表单数据，避免直接修改原对象
      const preparedData = { ...values };

      // 将数据封装到groups对象中
      const groups = {
        approvalResult: preparedData.approvalResult,
        inveDept: preparedData.inveDept,
        invePsnList: preparedData.invePsnList,
        returnRemark: preparedData.returnRemark,
        returnFiles: preparedData.returnFiles,
        approvalRemark: preparedData.approvalRemark,
        approvalPsn: preparedData.approvalPsn,
        approvalTime: preparedData.approvalTime
      };

      // 处理日期格式
      if (preparedData.entrustmentReceiveTime) {
        groups.entrustmentReceiveTime = moment(preparedData.entrustmentReceiveTime).format('YYYY-MM-DD');
      }

      if (preparedData.inveTimeLimit) {
        groups.inveTimeLimit = moment(preparedData.inveTimeLimit).format('YYYY-MM-DD');
      }

      // 处理invePsnList，只保留id、nickName和orgNames字段
      if (groups.invePsnList && groups.invePsnList.length > 0) {
        groups.invePsnList = groups.invePsnList.map(person => ({
          id: person.id,
          nickName: person.nickName,
          sjName: person.sjName,
          orgNames: person.orgNames,
          name: person.orgFullName
        }));
      }

      // 构造提交数据
      const submitData = {
        id: this.record && this.record.id ? this.record.id : null,
        ...groups
      };

      // 如果是暂存，添加标记
      if (isDraft) {
        submitData.isDraft = 1;
      }

      // 添加登录人机构id和机构名称
      submitData.approvalDeptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : (this.userInfo?.orgId || '');
      submitData.approvalDeptName = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgName : (this.userInfo?.orgName || '');

      return submitData;
    },

    handleTemporarily() {
      // 暂存功能不需要验证表单，直接获取当前表单值
      this.tempLoading = true;

      // 获取当前表单值，不进行验证
      const values = this.form.getFieldsValue();

      // 准备表单数据，传入isDraft=true参数
      const preparedData = this.prepareFormData(values, true);

      // 使用$http发送请求，使用与提交相同的接口
      this.$http.post('/investigationInfo/groupInvestigateAccept', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data);
          this.tempLoading = false;

          // 判断接口返回是否成功
          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close');
          } else {
            // 接口返回失败
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试';
            this.$message.error(errorMsg);
          }
        })
        .catch(error => {
          console.error('暂存失败：', error);
          this.$message.error('暂存失败，请稍后重试');
          this.tempLoading = false;
        });
    },

    handleNextStep() {
      this.handleSubmit(false);
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return; // 如果正在提交且不是查看模式，阻止返回操作

      this.$emit('back');
    },

    // 处理关闭弹框
    handleClose() {
      this.$emit('close');
    }
  }
}
</script>

<style lang="less" scoped>
// 可以添加特定于本表单的样式（如果有必要）</style>

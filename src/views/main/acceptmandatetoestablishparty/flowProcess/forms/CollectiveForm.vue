<template>
  <base-form-template
    title="审批/集体评议"
    title-icon="solution"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack"
  >
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>

      <!-- 区县司法局审批信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>区县司法局审批信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审批意见" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <sh-tree-select
                v-decorator="['approvalResult', { rules: [{ required: true, message: '请选择审批意见' }] }]"
                apiURL="/sysDictData/treeSp"
                :params="{ dictTypeId: '1732298638446571522', status: detailData.status }"
                placeholder="请选择"
                :disabled="mode === 'view'"
                @change="handleInitialOpinionChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="审批/退回意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
              <a-textarea
                v-decorator="['approvalRemark', { rules: [{ required: true, message: '请输入审批/退回意见' }] }]"
                :rows="4"
                placeholder="请输入"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审批人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalPsn', { initialValue: '默认当前登录人' }]" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="审批时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalTime', { initialValue: '2025-03-03 11:35:00' }]" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 以下部分仅在审批意见=1时显示 -->
      <template v-if="showInvestigationFields">
        <!-- 集体评议审核 -->
        <div class="form-section">
          <div class="section-title">
            <div class="section-line"></div>
            <span>集体评议审核</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调查单位" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <sh-tree-select
                  v-decorator="['inveDept', { rules: [{ required: true, message: '请选择调查单位' }] }]"
                  apiURL="/sysOrg/tree"
                  :params="{ level: 4 }"
                  placeholder="请选择"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调查评估对象" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-input
                  v-decorator="['correctionObjName', { rules: [{ required: true, message: '请输入调查评估对象' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="评议审核事项" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-input
                  v-decorator="['matter', { rules: [{ required: true, message: '请输入评议审核事项' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="主持人" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-input
                  v-decorator="['host', { rules: [{ required: true, message: '请输入主持人' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="mode == 'edit'">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-button type="primary" @click="handleOpenHistoryModal()">历史内容填充</a-button>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="评议审核地点" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-input
                  v-decorator="['address', { rules: [{ required: true, message: '请输入评议审核地点' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="评议审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-date-picker
                  v-decorator="['reviewTime', { rules: [{ required: true, message: '请选择评议审核时间' }] }]"
                  :disabled="mode === 'view'"
                  :disabledDate="disabledFutureDate"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="评议审核人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-input
                  v-decorator="['psnList', { rules: [{ required: true, message: '请输入评议审核人员' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="记录人" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-input
                  v-decorator="['recorder', { rules: [{ required: true, message: '请输入记录人' }] }]"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="评议审核情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="['context', { rules: [{ required: true, message: '请输入评议审核情况' }] }]"
                  :rows="4"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="评议审核意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="['opinion', { rules: [{ required: true, message: '请输入评议审核意见' }] }]"
                  :rows="4"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                <a-input v-decorator="['remark']" placeholder="请输入" :disabled="mode === 'view'" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" v-show="true">
            <a-col :span="24">
              <a-form-item label="集体评议意见表" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <document-creator
                  v-decorator="['opinionFiles', { rules: [{ required: true, message: '请选择社区矫正调查评估表' }] }]"
                  :formData="form.getFieldsValue()"
                  :disabled="mode === 'view'"
                  :status="detailData.status"
                  :create-document-api="'/investigationInfo/createPdfReview'"
                  :detailData="detailData"
                  doc-list-path="review.opinionFilesList"
                  :validate-form="validateFormBeforeCreateDocument"
                  paperType="opinion_review"
                />
                <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                  (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="社区矫正调查评估表" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <document-creator
                  v-decorator="[
                    'evaluationFiles',
                    { rules: [{ required: true, message: '请选择社区矫正调查评估表' }] }
                  ]"
                  :formData="form.getFieldsValue()"
                  :disabled="mode === 'view'"
                  :status="detailData.status"
                  :showSmartSign="false"
                  :create-document-api="'/investigationInfo/inveFormPdf'"
                  :detailData="detailData"
                  doc-list-path="review.evaluationFiles"
                  :validate-form="validateFormBeforeCreateDocument"
                  paperType="eval_review"
                />
                <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                  (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 调查评估初步意见 -->
        <div class="form-section">
          <div class="section-title">
            <div class="section-line"></div>
            <span>调查评估初步意见</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="调查评估文书号" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-input
                  v-decorator="['docNum', { rules: [{ required: true, message: '请输入调查评估文书号' }] }]"
                  placeholder=""
                  :disabled="true"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <sh-select
                  dictType="dcpgyj"
                  style="width: 100%;"
                  :disabled="mode === 'view'"
                  placeholder="请选择"
                  v-decorator="['conclusion', { rules: [{ required: true, message: '请选择' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="调查评估意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="['particular', { rules: [{ required: true, message: '请输入调查评估意见' }] }]"
                  :rows="4"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </template>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />提交
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
    <HistoricalContentModal @ok="handelHistoryData" ref="HistoricalContentModal" />
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import DocumentCreator from './components/DocumentCreator.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
import HistoricalContentModal from './components/HistoricalContentModal.vue'
export default {
  components: {
    BaseFormTemplate,
    DocumentCreator,
    HistoricalContentModal
  },
  props: {
    currentNode: {
      type: Object,
      default: () => ({})
    },
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      manualUploadVisible: false,
      manualUploadVisible2: false,
      showInvestigationFields: true,
      investigationFieldsCache: {}
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData()
  },
  methods: {
    disabledFutureDate,
    async initFormData() {
      // 使用父组件传入的detailData.review作为表单数据源
      const formValues = this.detailData || {}
      const review = formValues.review || {}
      const groups = formValues.groups || {} // 从groups获取调查单位和对象名称

      // 从现有的approvalResult值设置是否显示调查机构相关字段
      if ((this.currentNode?.rawData?.type === 9 && this.currentNode?.rawData?.draft === 1) || this.mode === 'view') {
        this.showInvestigationFields = review.approvalResult === '1'
        await this.$nextTick()
      } else {
        await this.$nextTick()
        review.approvalResult = '1'
      }

      // 设置表单的初始值
      this.$nextTick(async () => {
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss')
        // 在非查看模式下，设置当前用户为审核人，当前时间为审核时间
        const approvalPsn =
          this.mode !== 'view' ? this.userInfo?.name || '当前用户' : review.approvalPsn || '默认当前登录人'
        const approvalTime = this.mode !== 'view' ? currentTime : review.approvalTime || '2025-03-03 11:35:00'
        await this.$nextTick()
        // 直接使用后端字段名，不做映射
        this.form.setFieldsValue({
          id: formValues.id || null,

          // 区县司法局审批信息部分
          approvalResult: review.approvalResult || null,
          approvalRemark: this.mode !== 'view' && !review.approvalRemark && formValues.deliberations ? formValues.deliberations.approvalRemark || null : review.approvalRemark || null,
          approvalPsn: approvalPsn,
          approvalTime: approvalTime,

          // 从groups中获取调查单位和对象名称
          inveDept: groups.inveDept || review.inveDept || null,
          correctionObjName: groups.correctionObjName || review.correctionObjName || '',

          // 集体评议审核部分
          matter: this.mode !== 'view' && !review.matter && formValues.correctionObjName
            ? `关于${formValues.correctionObjName}调查评估的评议事项`
            : review.matter || null,
          host: review.host || null,
          address: review.address || null,
          reviewTime: this.mode !== 'view' && !review.reviewTime
            ? moment().format('YYYY-MM-DD')
            : review.reviewTime,
          psnList: review.psnList || null,
          recorder: review.recorder || null,
          context: review.context || null,
          opinion: review.opinion || null,
          remark: review.remark || null,

          // 文件上传部分
          // opinionFiles: review.opinionFiles ? [review.opinionFiles] : [],
          // evaluationFiles: review.evaluationFiles ? [review.evaluationFiles] : [],
          opinionFiles: review.opinionFilesList && review.opinionFilesList[0] ? review.opinionFilesList : [],
          evaluationFiles: review.evaluationFiles && review.evaluationFiles[0] ? review.evaluationFiles : [],
          // 调查评估初步意见部分
          docNum: review.docNum || '',
          conclusion: review.conclusion || null,
          particular: review.particular || null
        })
      })
    },

    handleInitialOpinionChange(value) {
      // 当值变更前，缓存当前表单中调查评估相关字段的值
      if (this.showInvestigationFields) {
        const currentValues = this.form.getFieldsValue([
          'inveDept',
          'correctionObjName',
          'matter',
          'host',
          'address',
          'reviewTime',
          'psnList',
          'recorder',
          'context',
          'opinion',
          'remark',
          'opinionFiles',
          'evaluationFiles',
          'docNum',
          'conclusion',
          'particular'
        ])
        this.investigationFieldsCache = { ...currentValues }
      }

      // 更新显示状态
      this.showInvestigationFields = value === '1'
      console.log('审核结果变更为:', value, '显示调查机构字段:', this.showInvestigationFields)

      // 当调查评估字段重新显示时，恢复之前缓存的值
      if (this.showInvestigationFields && Object.keys(this.investigationFieldsCache).length > 0) {
        this.$nextTick(() => {
          this.form.setFieldsValue(this.investigationFieldsCache)
        })
      }
    },

    handleCreateDocument() {
      this.$message.info('开始制作集体评议意见表')
      // 实现集体评议意见表文书制作逻辑
    },

    handleManualUpload() {
      this.manualUploadVisible = !this.manualUploadVisible
    },

    handleCreateDocument2() {
      this.$message.info('开始制作社区矫正调查评估表')
      // 实现社区矫正调查评估表文书制作逻辑
    },

    handleManualUpload2() {
      this.manualUploadVisible2 = !this.manualUploadVisible2
    },

    handleOk() {
      this.handleSubmit(true)
    },

    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return

      this.submitLoading = true

      this.form.validateFields((err, values) => {
        if (!err) {
          const preparedData = this.prepareFormData(values)

          // 检查是否有未签名内容
          this.$http
            .get('/investigationSign/check', {
              params: {
                id: this.detailData.id || '',
                bizType: this.detailData.status
              }
            })
            .then(checkRes => {
              if (checkRes && !checkRes.success) {
                // 有未签名内容，弹出确认框
                this.$confirm({
                  title: '提示',
                  content: checkRes.message || '有未签名的内容，是否继续提交?',
                  okText: '继续提交',
                  cancelText: '取消',
                  onOk: () => {
                    this.submitFormData(preparedData)
                  },
                  onCancel: () => {
                    this.submitLoading = false
                  }
                })
              } else {
                // 没有未签名内容，直接提交
                this.submitFormData(preparedData)
              }
            })
            .catch(error => {
              console.error('签名检查失败：', error)
              // 检查失败也继续提交
              this.submitFormData(preparedData)
            })
        } else {
          this.submitLoading = false
          console.error('表单验证失败：', err)
          this.$message.error('请完善必填信息')
        }
      })
    },

    // 提交表单数据
    submitFormData(preparedData) {
      this.$http
        .post('/investigationInfo/review', preparedData)
        .then(response => {
          console.log('保存成功：', response.data)
          this.submitLoading = false

          if (response && response.success) {
            this.$message.success('提交成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '提交失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('保存失败：', error)
          this.$message.error('提交失败，请稍后重试')
          this.submitLoading = false
        })
    },

    prepareFormData(values, isDraft = false) {
      const preparedData = { ...values }

      // 处理日期时间格式
      if (preparedData.reviewTime) {
        preparedData.reviewTime = moment(preparedData.reviewTime).format('YYYY-MM-DD')
      }

      // 处理文件字段格式
      // if (preparedData.opinionFiles && Array.isArray(preparedData.opinionFiles)) {
      //   preparedData.opinionFiles = preparedData.opinionFiles.join(',');
      // }

      // if (preparedData.evaluationFiles && Array.isArray(preparedData.evaluationFiles)) {
      //   preparedData.evaluationFiles = preparedData.evaluationFiles.join(',');
      // }
      preparedData.entrustmentReceiveTime = this.detailData.entrustmentReceiveTime || null
      // 添加额外字段
      preparedData.approvalPsnId = this.userInfo?.id || null
      // 添加登录人机构id和机构名称
      preparedData.approvalDeptId = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgId
        : this.userInfo?.orgId || ''
      preparedData.approvalDeptName = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgName
        : this.userInfo?.orgName || ''
      preparedData.deleted = 0 // 默认未删除

      if (preparedData.opinionFiles) {
        preparedData.opinionFiles = preparedData.opinionFiles.map(item => item.id).join(',')
      }
      if (preparedData.evaluationFiles) {
        preparedData.evaluationFiles = preparedData.evaluationFiles.map(item => item.id).join(',')
      }
      const submitData = {
        otherInfo: this.detailData,
        id: this.record && this.record.id ? this.record.id : null,
        ...preparedData
      }

      if (isDraft) {
        submitData.isDraft = 1
      }

      return submitData
    },

    handleTemporarily() {
      this.tempLoading = true

      const values = this.form.getFieldsValue()
      const preparedData = this.prepareFormData(values, true)

      this.$http
        .post('/investigationInfo/review', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data)
          this.tempLoading = false

          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('暂存失败：', error)
          this.$message.error('暂存失败，请稍后重试')
          this.tempLoading = false
        })
    },

    handleNextStep() {
      this.handleSubmit(false)
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return
      this.$emit('back')
    },

    // 表单校验函数，排除opinionFiles字段
    validateFormBeforeCreateDocument() {
      return new Promise((resolve, reject) => {
        // 定义不需要校验的字段列表
        const excludeFields = ['opinionFiles', 'evaluationFiles', 'id', 'conclusion', 'particular', 'docNum']

        // 获取表单所有字段名并排除不需要校验的字段
        const fieldsToValidate = Object.keys(this.form.getFieldsValue()).filter(field => !excludeFields.includes(field))

        // 只校验非排除字段
        this.form.validateFields(fieldsToValidate, (errors, values) => {
          if (errors) {
            reject(new Error('请先完善必填项信息'))
          } else {
            const formValues = this.form.getFieldsValue()
            if (formValues.opinionFiles) {
              formValues.opinionFiles = formValues.opinionFiles.map(item => item.id).join(',')
            }
            if (formValues.evaluationFiles) {
              formValues.evaluationFiles = formValues.evaluationFiles.map(item => item.id).join(',')
            }
            formValues.reviewTime = moment(formValues.reviewTime).format('YYYY-MM-DD')
            // 将所有表单值传递给文书组件，包括未经校验的字段
            resolve(formValues)
          }
        })
      })
    },
    handleOpenHistoryModal() {
      this.$refs.HistoricalContentModal.open(this.record, 2)
    },
    handelHistoryData(data) {
      // 处理历史数据
      console.log('历史数据:', data)
      this.form.setFieldsValue({
        // matter: data.matter,
        host: data.host,
        address: data.address,
        psnList: data.psnList,
        recorder: data.recorder
        // context: data.context,
        // opinion: data.opinion,
        // remark: data.remark
      })
    }
  }
}
</script>

<style lang="less" scoped>
.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-help-text {
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
}
</style>

<template>
  <base-form-template
    title="评估结果"
    title-icon="solution"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack"
  >
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>

      <!-- 评估结果 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>评估结果</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12" v-if="!isUploadMode">
            <a-form-item label="评估得分" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                style="width: 100%;"
                :max="100"
                v-decorator="['totalScore', { rules: [{ required: !isUploadMode, message: '请输入评估得分' }] }]"
                placeholder="请输入评估得分"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="评估人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-person-selector
                :disabled="true"
                :treeUrl="'/sysOrg/tree?remark=all'"
                treeLabelKey="title"
                treeValueKey="id"
                :list-url="'/sysUser/page'"
                :name-field="'nickName'"
                :org-field="'orgNames'"
                v-decorator="[
                  'assessmentPsnList',
                  {
                    rules: [{ type: 'array', required: true, message: '请选择评估人员!' }]
                  }
                ]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-select
                dictType="dcpgyj"
                style="width: 100%;"
                :disabled="mode === 'view'"
                v-decorator="['conclusion', { rules: [{ required: true, message: '请输入' }] }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查评估日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['inveTime', { rules: [{ required: true, message: '请选择调查评估日期' }] }]"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="mode === 'view'"
                :disabledDate="disabledFutureDate"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查评估意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-textarea
                v-decorator="['opinion', { rules: [{ required: true, message: '请输入调查评估意见' }] }]"
                :rows="6"
                placeholder="请输入调查评估意见"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 评估笔录 -->
      <div class="form-section" v-show="!isUploadMode">
        <div class="section-title">
          <div class="section-line"></div>
          <span>评估笔录</span>
        </div>
        <a-table
          :columns="columns"
          :dataSource="assessmentItems"
          :pagination="false"
          size="middle"
          :rowKey="record => record.id"
        >
          <template slot="action" slot-scope="text, record, index">
            <a @click="handleViewRecord(record, index)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleDownloadRecord(record)">下载</a>
          </template>
        </a-table>
      </div>

      <!-- 调查评估过程材料（上传笔录模式） -->
      <div class="form-section" v-show="isUploadMode">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查评估过程材料</span>
        </div>
        <assessment-materials-component
          v-model="uploadMaterials"
          :disabled="mode === 'view'"
          @change="handleMaterialsChange"
        />
      </div>
      <div class="form-section" v-if="detailData.transcriptTag == 1">
        <div class="section-title">
          <span>调查评估表信息</span>
        </div>

        <!-- 调查对象 -->
        <div>
          <h3 class="category-title" @click="toggleSection('investigationObject')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.investigationObject }">》</span>
            调查对象
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.investigationObject">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      v-decorator="['pgForm.correctionObjName', { rules: [{ required: true, message: '请输入姓名' }] }]"
                      :disabled="true"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      v-decorator="['pgForm.nation', { rules: [{ required: true, message: '请输入民族' }] }]"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="曾用名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['pgForm.usedName']" :disabled="mode === 'view'" placeholder="请输入" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="别名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['pgForm.nickName']" :disabled="mode === 'view'" placeholder="请输入" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['pgForm.contactTel']" :disabled="mode === 'view'" placeholder="请输入" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 家庭和社会关系 -->
        <div>
          <h3 class="category-title" @click="toggleSection('familySocial')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.familySocial }">》</span>
            家庭和社会关系
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.familySocial">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="家庭成员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.jtcy']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="社会交往情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.shjwqk']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="主要社会关系" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.zyshgx']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="未成年对象的其他情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.wcndxdqtqk']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 个性特点 -->
        <div>
          <h3 class="category-title" @click="toggleSection('personalityTraits')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.personalityTraits }">》</span>
            个性特点
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.personalityTraits">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="生理状况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.slzk']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="心理特征" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.xltz']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="性格类型" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.xglx']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="爱好特长" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.ahtc']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 一般表现 -->
        <div>
          <h3 class="category-title" @click="toggleSection('generalPerformance')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.generalPerformance }">》</span>
            一惯表现
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.generalPerformance">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="工作（学习）表现" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.gzxxbx']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="遵纪守法情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.zjsfqk']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="有无不良嗜好行为恶习" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.ywblshxwex']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 犯罪情况和悔罪表现 -->
        <div>
          <h3 class="category-title" @click="toggleSection('crimeSituation')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.crimeSituation }">》</span>
            犯罪情况和悔罪表现
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.crimeSituation">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="犯罪原因" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.fzyy']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="主观恶性" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.zgex']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="是否有犯罪前科" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.sfyfzqk']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="认罪悔罪态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.rzhztd']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 社会反响 -->
        <div>
          <h3 class="category-title" @click="toggleSection('socialResponse')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.socialResponse }">》</span>
            社会反响
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.socialResponse">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="被害人或其家属态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.bhrhqqstd']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="社会公众态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.shgztd']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 监管条件 -->
        <div>
          <h3 class="category-title" @click="toggleSection('supervisionConditions')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.supervisionConditions }">》</span>
            监管条件
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.supervisionConditions">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="家庭成员态度" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.jtcytd']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="经济生活状况和环境" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.jjshzkhhj']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item
                    label="工作单位、就读学校和村（社区）基层组织意见"
                    :labelCol="labelCol2"
                    :wrapperCol="wrapperCol2"
                  >
                    <a-textarea
                      v-decorator="['pgForm.gzdwjdxxhcjczzjyj']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="拟禁止的事项" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.ywjzdsx']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>

        <!-- 辖区公安派出所意见 -->
        <div>
          <h3 class="category-title" @click="toggleSection('policeOpinion')">
            <span class="collapse-icon" :class="{ collapsed: !collapsedSections.policeOpinion }">》</span>
            辖区公安派出所意见
          </h3>
          <transition name="slide">
            <div class="category-content" v-show="collapsedSections.policeOpinion">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="辖区公安派出所意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
                    <a-textarea
                      v-decorator="['pgForm.gayj']"
                      :disabled="mode === 'view'"
                      :rows="3"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </transition>
        </div>
      </div>
      <!-- 调查评估表初稿 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查评估表初稿</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查评估表初稿" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <document-creator
                v-decorator="['evaluationFiles', { rules: [{ required: true, message: '请上传文件' }] }]"
                :formData="form.getFieldsValue()"
                :disabled="mode === 'view'"
                :status="detailData.status"
                :create-document-api="'/investigationInfo/inveFormPdf'"
                :detailData="detailData"
                :enableCloudSign="false"
                :alwaysShowSignatureBtn="false"
                :isShowAuthSign="false"
                :isShowAuthSign2="false"
                :showStatusTag="false"
                doc-list-path="groups.evaluationFiles"
                :validate-form="validateFormBeforeCreateDocument"
              />
              <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <a-form-item label="调查补充材料" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
        <sh-file-uploader
          v-decorator="['dctjFileList', { rules: [{ type: 'array', required: false, message: '请上传' }] }]"
          :url="'/sysFileInfo/uploadOss'"
          :maxFileSize="20 * 1024 * 1024"
          :acceptedFormats="'.jpg,.png,.pdf,.zip,.rar'"
          :readonly="mode === 'view'"
        />
      </a-form-item>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button style="margin-right: 8px" @click="handlePrevStep">上一步</a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />提交
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { downloadFile } from '@/utils/fileUtils'
import DocumentCreator from './components/DocumentCreator.vue'
import AssessmentMaterialsComponent from './components/AssessmentMaterialsComponent.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  components: {
    BaseFormTemplate,
    DocumentCreator,
    AssessmentMaterialsComponent
  },
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    isUploadMode() {
      return (
        this.detailData &&
        this.detailData.transcripts &&
        this.detailData.transcripts.length > 0 &&
        this.detailData.transcripts[0].tag === 2
      )
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      manualUploadVisible: false,
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 50,
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '评估类型',
          dataIndex: 'paperType',
          width: 150,
          customRender: text => {
            return text ? this.$options.filters['dictType']('BLLX', text) : ''
          }
        },
        {
          title: '完成情况',
          dataIndex: 'progress',
          width: 200,
          customRender: text => {
            if (text === '0') return '未完成'
            if (text === '1') return '暂存'
            if (text === '2') return '完成'
            return ''
          }
        },
        // { title: '分数', dataIndex: 'score', width: 100 },
        {
          title: '操作',
          key: 'action',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],
      assessmentItems: [],
      uploadMaterials: [],
      collapsedSections: {
        investigationObject: true,
        familySocial: false,
        personalityTraits: false,
        generalPerformance: false,
        crimeSituation: false,
        socialResponse: false,
        supervisionConditions: false,
        policeOpinion: false
      },
      arr: [
        {
          id: '1910180064054480898',
          indexName: '家庭成员',
          paperMaintenanceId: '1910180063836377090',
          serialNumber: 1,
          topicName: '你的家庭基本情况说一下？（家庭成员的关系、姓名、年龄、职业、住址、经济状况、联系方式等）',
          topicScore: 0,
          topicType: 'jtcy',
          userAnswer: '11'
        }
      ]
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData()
  },
  methods: {
    disabledFutureDate,
    // 添加上一步处理方法
    handlePrevStep() {
      // 弹出确认框，询问是否返回上一步
      this.$confirm({
        title: '操作提示',
        content: '确定要返回上一步吗？当前未保存的数据将会丢失。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 触发父组件中的上一步事件
          this.$emit('prevStep', 'PGZT03_2')
        }
      })
    },
    async initFormData() {
      // 使用父组件传入的detailData作为表单数据源
      const formValues = this.detailData || {}
      const groups = formValues.groups || {}
      console.log(this.mode === 'view', 'mode')
      // 设置评估笔录数据
      this.assessmentItems = formValues.transcripts || []
      console.log(this.detailData, 'this.detailData')
      // 如果是上传模式，设置上传的材料数据
      if (this.isUploadMode && formValues.transcripts && formValues.transcripts[0].blFileList) {
        this.uploadMaterials = formValues.transcripts[0].blFileList || []
      }
      //调查评估表信息
      let context = this.detailData.pgForm
      //手动上传
      if (this.detailData.transcriptTag == 2) {
        context = JSON.parse(this.detailData.transcripts[0].context)
      }
      //在线笔录
      if (this.detailData.transcriptTag == 1 && !this.detailData.pgForm) {
        await this.$http.get('/investigationTranscript/getPgForm' + '?id=' + this.detailData.id).then(res => {
          context = res.data
        })
      }

      // 设置表单的初始值
      this.$nextTick(() => {
        const _conclusion = groups.conclusion ? groups.conclusion : formValues.score < 80 ? '2' : '1'
        this.form.setFieldsValue({
          id: formValues.id || null,
          totalScore: formValues.score || '',
          assessmentPsnList: groups.invePsnList || [],
          conclusion: this.isUploadMode && this.mode !== 'view' && !groups.conclusion ? undefined : _conclusion,

          // conclusion: (this.isUploadMode && this.mode !== 'view' && !groups.conclusion) ? undefined : _conclusion,
          inveTime: this.mode === 'view' ? moment(groups.inveTime) : moment(),
          dctjFileList: groups.dctjFileList || [],
          opinion: groups.opinion || '',
          evaluationFiles: groups.evaluationFiles && groups.evaluationFiles[0] ? groups.evaluationFiles : [],
          pgForm: context
        })
        console.log('setInvestigateFormData', this.form.getFieldsValue())
      })
    },
    getBasicInfo(arr, type, content) {
      return JSON.parse(arr.filter(v => v.paperType == type)[0][content])
    },
    handleCreateDocument() {
      this.$message.info('开始制作文书')
      // 实现文书制作逻辑
    },

    handleManualUpload() {
      this.manualUploadVisible = !this.manualUploadVisible
    },

    handleViewRecord(record, index) {
      console.log('record', record, index)
      // this.$viewRecord(record.id);
      if (process.env.NODE_ENV === 'development') {
        this.$imageViewer.view(
          ['/api/investigationInfo/transcriptDownload?id=' + record.id + '&fileType=' + '.pdf'],
          0,
          '/pdfJsLib/web/viewer.html'
        )
      } else {
        this.$imageViewer.view(
          ['/api/investigationInfo/transcriptDownload?id=' + record.id + '&fileType=' + '.pdf'],
          0,
          '/pdfJsLib/web/viewer.html'
        )
      }

      this.$message.info(`查看评估笔录: ${record.id}`)
      // 实现查看逻辑
    },

    handleDownloadRecord(record) {
      this.$message.info(`下载中，请稍候...`)
      this.$http
        .get(`/investigationInfo/transcriptDownload?id=${record.id}&type=1`, {
          responseType: 'blob'
        })
        .then(response => {
          if (response) {
            downloadFile(response)
            this.$message.success('下载成功')
          }
        })
        .catch(error => {
          console.error('下载失败：', error)
          this.$message.error('下载失败，请稍后重试')
        })
    },

    handleOk() {
      this.handleSubmit(true)
    },

    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return

      this.submitLoading = true

      this.form.validateFields((err, values) => {
        if (!err) {
          const preparedData = this.prepareFormData(values)

          this.$http
            .post('/investigationInfo/groupInvestigateCommit', preparedData)
            .then(response => {
              console.log('保存成功：', response.data)
              this.submitLoading = false

              if (response && response.success) {
                this.$message.success('提交成功')
                this.$emit('close')
              } else {
                const errorMsg = (response && response.message) || '提交失败，请稍后重试'
                this.$message.error(errorMsg)
              }
            })
            .catch(error => {
              console.error('保存失败：', error)
              this.$message.error('提交失败，请稍后重试')
              this.submitLoading = false
            })
        } else {
          this.submitLoading = false
          console.error('表单验证失败：', err)
          this.$message.error('请完善必填信息')
        }
      })
    },

    prepareFormData(values, isDraft = false) {
      const preparedData = { ...values }

      const groups = {
        totalScore: preparedData.totalScore,
        assessmentPsnList: preparedData.assessmentPsnList,
        conclusion: preparedData.conclusion,
        opinion: preparedData.opinion,
        evaluationFiles: preparedData.evaluationFiles.map(item => item.id).join(','),
        noticeDocNum: this.detailData?.groups?.noticeDocNum
      }

      if (preparedData.inveTime) {
        groups.inveTime = preparedData.inveTime.format('YYYY-MM-DD')
      }

      // 处理assessmentPsnList，只保留id、nickName和orgNames字段
      if (groups.assessmentPsnList && groups.assessmentPsnList.length > 0) {
        groups.assessmentPsnList = groups.assessmentPsnList.map(person => ({
          id: person.id,
          nickName: person.nickName,
          sjName: person.sjName,
          orgNames: person.orgNames,
          name: person.orgFullName
        }))
      }

      if (values.dctjFileList && values.dctjFileList.length > 0) {
        groups.otherFiles = values.dctjFileList && values.dctjFileList.map(item => item.id).join(',')
      }
      const submitData = {
        id: this.record && this.record.id ? this.record.id : null,
        ...groups,
        otherInfo: this.detailData,
        transcripts: this.assessmentItems,
        uploadMaterials: this.uploadMaterials,
        blFileIds: this.uploadMaterials.map(item => item.id).join(','),
        pgForm: JSON.stringify(values.pgForm)
      }

      if (isDraft) {
        submitData.isDraft = 1
      }
      // 添加登录人机构id和机构名称
      submitData.approvalDeptId = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgId
        : this.userInfo?.orgId || ''
      submitData.approvalDeptName = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgName
        : this.userInfo?.orgName || ''
      return submitData
    },

    handleTemporarily() {
      this.tempLoading = true

      const values = this.form.getFieldsValue()
      const preparedData = this.prepareFormData(values, true)
      console.log('暂存数据：', values)
      this.$http
        .post('/investigationInfo/groupInvestigateCommit', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data)
          this.tempLoading = false

          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('暂存失败：', error)
          this.$message.error('暂存失败，请稍后重试')
          this.tempLoading = false
        })
    },

    handleNextStep() {
      this.handleSubmit(false)
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return
      this.$emit('back')
    },

    handleMaterialsChange(materials) {
      this.uploadMaterials = materials
      // 更新transcript中的blFileList
      if (this.detailData && this.detailData.transcripts && this.detailData.transcripts.length > 0) {
        this.detailData.transcripts[0].blFileList = materials
      }
    },

    validateFormBeforeCreateDocument() {
      return new Promise((resolve, reject) => {
        // 定义不需要校验的字段列表
        const excludeFields = ['evaluationFiles', 'id']

        // 获取表单所有字段名并排除不需要校验的字段
        const fieldsToValidate = Object.keys(this.form.getFieldsValue()).filter(field => !excludeFields.includes(field))

        // 只校验非排除字段
        this.form.validateFields(fieldsToValidate, (errors, values) => {
          if (errors) {
            reject(new Error('请先完善必填项信息'))
          } else {
            // 将所有表单值传递给文书组件，包括未经校验的字段
            const formValues = this.form.getFieldsValue()
            if (formValues.inveTime) {
              formValues.inveTime = moment(formValues.inveTime).format('YYYY-MM-DD')
            }
            formValues.evaluationFiles = formValues.evaluationFiles.map(item => item.id).join(',')
            formValues.blFileIds = this.uploadMaterials.map(item => item.id).join(',')
            // if (this.detailData.transcriptTag == 1) {
            formValues.contactTel = this.detailData.contactTel
            formValues.correctionObjName = this.detailData.correctionObjName
            formValues.nickName = this.detailData.nickName
            formValues.usedName = this.detailData.usedName

            resolve(formValues)
          }
        })
      })
    },
    toggleSection(sectionKey) {
      this.collapsedSections[sectionKey] = !this.collapsedSections[sectionKey]
    }
  }
}
</script>

<style lang="less" scoped>
.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-help-text {
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
}
/* 添加折叠动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
  max-height: 2000px; /* 根据实际内容高度调整 */
  overflow: hidden;
}

.slide-enter,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}
/* 保持原有样式不变 */
.category-content {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  max-height: 2000px; /* 根据实际内容高度调整 */

  &-enter,
  &-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  &-enter-active,
  &-leave-active {
    transition: all 0.3s ease-in-out;
  }
}
// 类别标题样式
.category-title {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #1890ff;
  &:hover {
    color: #1890ff;
  }
}

// 新增旋转图标样式
.collapse-icon {
  display: inline-block;
  transition: transform 0.3s ease-in-out;
  margin-right: 8px;

  &.collapsed {
    transform: rotate(0deg);
  }

  &:not(.collapsed) {
    transform: rotate(90deg);
  }
}
</style>

<template>
  <div class="flow-form">
    <div class="flow-form-header">
      <h3>评估表单</h3>
      <!-- 此组件用于"查看和提交评估结果"流程节点，用于填写评估信息 -->
      <div class="flow-form-info">
        <span>评估编号：{{ formData.no }}</span>
        <span>评估时间：{{ formData.time }}</span>
        <span>处理人员：{{ formData.handler }}</span>
        <span>处理时间：{{ formData.handleTime }}</span>
      </div>
    </div>
    <a-form :form="form" class="flow-form-content">
      <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="社区矫正对象姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['name']"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptName']"/>
          </a-form-item>
        </a-col>
      </a-row>

      <div class="section-title">评估内容</div>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="评估类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select :disabled="mode === 'view'" v-decorator="['assessmentType', { rules: [{ required: true, message: '请选择评估类型' }] }]">
              <a-select-option value="初始评估">初始评估</a-select-option>
              <a-select-option value="阶段评估">阶段评估</a-select-option>
              <a-select-option value="终结评估">终结评估</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="评估日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker
              :disabled="mode === 'view'"
              v-decorator="['assessmentDate', { rules: [{ required: true, message: '请选择评估日期' }] }]"
              style="width: 100%"
              format="YYYY-MM-DD"
              :disabledDate="disabledFutureDate"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="评估内容" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="mode === 'view'"
              v-decorator="['content', { rules: [{ required: true, message: '请输入评估内容' }] }]"
              :autoSize="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入评估内容"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="评估结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select :disabled="mode === 'view'" v-decorator="['result', { rules: [{ required: true, message: '请选择评估结果' }] }]">
              <a-select-option value="良好">良好</a-select-option>
              <a-select-option value="一般">一般</a-select-option>
              <a-select-option value="较差">较差</a-select-option>
              <a-select-option value="需要干预">需要干预</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="评估人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input :disabled="mode === 'view'" v-decorator="['assessor', { rules: [{ required: true, message: '请输入评估人' }] }]"/>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="mode === 'view'"
              v-decorator="['remark']"
              :autoSize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item :wrapperCol="{ span: 24 }" style="text-align: center" v-if="mode !== 'view'">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
        <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
      </a-form-item>
      <a-form-item :wrapperCol="{ span: 24 }" style="text-align: center" v-else>
        <a-button @click="onBack">返回</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  // 此组件为评估表单，用于"查看和提交评估结果"流程节点
  // 主要功能是填写社区矫正对象的评估内容和结果
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit' // 'edit' 或 'view'
    }
  },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      form: this.$form.createForm(this),
      formData: {
        no: 'PG' + new Date().getTime(),
        time: moment().format('YYYY-MM-DD'),
        handler: '评估专员',
        handleTime: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      loading: false
    }
  },
  mounted() {
    // 如果有记录，则加载数据
    if (this.record && this.record.id) {
      this.fetchData();
    }
  },
  methods: {
    disabledFutureDate,
    fetchData() {
      this.loading = true;
      // 模拟API请求获取数据
      setTimeout(() => {
        this.loadFormData();
        this.loading = false;
        console.log('评估表单数据已加载');
      }, 1000);
    },
    loadFormData() {
      // 模拟后台返回的数据
      const responseData = {
        id: this.record.id,
        name: this.record.name || '',
        deptName: this.record.deptName || '',
        assessmentType: '初始评估',
        assessmentDate: moment(),
        content: '',
        result: '良好',
        assessor: '评估专员',
        remark: ''
      };

      // 设置表单值
      this.form.setFieldsValue(responseData);
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('表单值：', values)
          // 模拟保存成功
          this.$message.success('提交成功')

          // 显示确认对话框，询问用户是否继续下一步流程
          this.$confirm({
            title: '操作提示',
            content: '评估表单提交成功，是否立即进行下一步流程？',
            okText: '继续下一步',
            cancelText: '留在当前页面',
            onOk: () => {
              // 通知父组件进入下一步流程
              this.$emit('nextStep')
            }
          })
        }
      })
    },
    handleReset() {
      this.form.resetFields()
      this.loadFormData()
    },
    onBack() {
      this.$emit('back');
    }
  }
}
</script>

<style lang="less" scoped>

// 可以添加特定于本表单的样式（如果有必要）
</style>

<template>
  <base-form-template
    title="区县司法局负责人审批"
    title-icon="solution"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack"
  >
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>

      <!-- 区县司法局负责人审批信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>区县司法局负责人审批信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审批意见" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <sh-tree-select
                v-decorator="['approvalResult', { rules: [{ required: true, message: '请选择审批意见' }] }]"
                apiURL="/sysDictData/treeSp"
                :params="{ dictTypeId: '1732298638446571522', status: detailData.status }"
                placeholder="请选择"
                :disabled="mode === 'view'"
                @change="handleInitialOpinionChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="审批/退回意见" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
              <a-textarea
                v-decorator="['approvalRemark', { rules: [{ required: true, message: '请输入审批/退回意见' }] }]"
                :rows="4"
                placeholder="请输入"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="审批人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalPsn', { initialValue: '默认当前登录人' }]" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="审批时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['approvalTime', { initialValue: '2025-03-03 11:35:00' }]" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 以下部分仅在审批意见=1时显示 -->
      <template v-if="showInvestigationFields">
        <!-- 调查评估审批意见 -->
        <div class="form-section">
          <div class="section-title">
            <div class="section-line"></div>
            <span>调查评估审批意见</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调查评估开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  v-decorator="[
                    'entrustmentReceiveTime',
                    { rules: [{ required: showInvestigationFields, message: '请选择调查评估开始日期' }] }
                  ]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  placeholder="调查评估开始日期"
                  :disabled="mode === 'view'"
                  :disabledDate="disabledFutureDate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调查评估结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-date-picker
                  v-decorator="[
                    'endTime',
                    { rules: [{ required: showInvestigationFields, message: '请选择调查评估结束日期' }] }
                  ]"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  :disabled="mode === 'view'"
                  :disabledDate="disabledFutureDate"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调查评估文书号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <a-input
                  v-decorator="['docNum', { rules: [{ required: true, message: '请输入调查评估文书号' }] }]"
                  placeholder=""
                  :disabled="true"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调查评估结论" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
                <sh-select
                  dictType="dcpgyj"
                  style="width: 100%;"
                  :disabled="mode === 'view'"
                  placeholder="请选择"
                  v-decorator="['conclusion', { rules: [{ required: true, message: '请输入' }] }]"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="调查评估情况" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <a-textarea
                  v-decorator="['particular', { rules: [{ required: true, message: '请输入调查评估情况' }] }]"
                  :rows="4"
                  placeholder="请输入"
                  :disabled="mode === 'view'"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 文书制作部分 -->
        <div class="form-section">
          <div class="section-title">
            <div class="section-line"></div>
            <span>文书制作-调查评估意见书</span>
          </div>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="调查评估意见书" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <document-creator
                  v-decorator="['opinionFiles', { rules: [{ required: true, message: '请选择调查评估意见书' }] }]"
                  :formData="form.getFieldsValue()"
                  :disabled="mode === 'view'"
                  :status="detailData.status"
                  :create-document-api="'/investigationInfo/createPdfOpinion'"
                  :detailData="detailData"
                  doc-list-path="approval.opinionFilesList"
                  :validate-form="validateFormBeforeCreateDocument"
                />
                <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                  (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="社区矫正调查评估表" :labelCol="labelCol2" :wrapperCol="wrapperCol2" required>
                <document-creator
                  v-decorator="['evaluationFiles', { rules: [{ required: true, message: '请选择调查合议意见书' }] }]"
                  :formData="form.getFieldsValue()"
                  :disabled="mode === 'view'"
                  :status="detailData.status"
                  :create-document-api="'/investigationInfo/inveFormPdf'"
                  :detailData="detailData"
                  doc-list-path="approval.evaluationFiles"
                  :showSmartSign="false"
                  :alwaysShowSignatureBtn="true"
                  :validate-form="validateFormBeforeCreateDocument"
                  paperType="eval_approval"
                />
                <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                  (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </template>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />提交
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import DocumentCreator from './components/DocumentCreator.vue'
import { disabledFutureDate } from '@/utils/dateUtils'
export default {
  components: {
    BaseFormTemplate,
    DocumentCreator
  },
  props: {
    currentNode: {
      type: Object,
      default: () => ({})
    },
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      manualUploadVisible: false,
      manualUploadVisible2: false,
      showInvestigationFields: true,
      investigationFieldsCache: {}
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData()
  },
  methods: {
    disabledFutureDate,
    async initFormData() {
      // 使用父组件传入的detailData.approval作为表单数据源
      const formValues = this.detailData || {}
      const approval = formValues.approval || {}
      const groups = formValues.groups || {}

      // 从现有的approvalResult值设置是否显示调查机构相关字段
      if ((this.currentNode?.rawData?.type === 9 && this.currentNode?.rawData?.draft === 1) || this.mode === 'view') {
        this.showInvestigationFields = approval.approvalResult === '1'
        await this.$nextTick()
      } else {
        await this.$nextTick()
        approval.approvalResult = '1'
      }

      // 设置表单的初始值
      this.$nextTick(() => {
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss')
        // 在非查看模式下，设置当前用户为审核人，当前时间为审核时间
        const approvalPsn =
          this.mode !== 'view' ? this.userInfo?.name || '当前用户' : approval.approvalPsn || '默认当前登录人'
        const approvalTime = this.mode !== 'view' ? currentTime : approval.approvalTime || '2025-03-03 11:35:00'

        // 直接使用后端字段名，不做映射
        this.form.setFieldsValue({
          id: formValues.id || null,

          // 区县司法局负责人审批信息
          approvalResult: approval.approvalResult || null,
          approvalRemark: approval.approvalRemark ? approval.approvalRemark : formValues.review.approvalRemark || '',
          approvalPsn: approvalPsn,
          approvalTime: approvalTime,

          // 调查评估审批意见
          docNum: groups.noticeDocNum || '',
          conclusion: approval.conclusion || null,
          particular: approval.particular || null,
          entrustmentReceiveTime: approval.entrustmentReceiveTime || null,
          endTime: approval.endTime || null,
          // 文件上传部分
          opinionFiles: approval.opinionFilesList && approval.opinionFilesList.length ? approval.opinionFilesList : [],
          evaluationFiles: approval.evaluationFiles && approval.evaluationFiles.length ? approval.evaluationFiles : []

          // opinionFiles: approval.opinionFiles ? [approval.opinionFiles] : [],
          // evaluationFiles: approval.evaluationFiles ? [approval.evaluationFiles] : []
        })
      })
    },

    handleInitialOpinionChange(value) {
      // 当值变更前，缓存当前表单中调查评估相关字段的值
      if (this.showInvestigationFields) {
        const currentValues = this.form.getFieldsValue([
          'entrustmentReceiveTime',
          'endTime',
          'docNum',
          'conclusion',
          'particular',
          'opinionFiles',
          'evaluationFiles'
        ])
        this.investigationFieldsCache = { ...currentValues }
      }

      // 更新显示状态
      this.showInvestigationFields = value === '1'
      console.log('审核结果变更为:', value, '显示调查机构字段:', this.showInvestigationFields)

      // 当调查评估字段重新显示时，恢复之前缓存的值
      if (this.showInvestigationFields && Object.keys(this.investigationFieldsCache).length > 0) {
        this.$nextTick(() => {
          this.form.setFieldsValue(this.investigationFieldsCache)
        })
      }
    },

    handleCreateDocument() {
      this.$message.info('开始制作调查合议意见书')
      // 实现调查合议意见书文书制作逻辑
    },

    handleManualUpload() {
      this.manualUploadVisible = !this.manualUploadVisible
    },

    handleCreateDocument2() {
      this.$message.info('开始制作社区矫正调查评估表')
      // 实现社区矫正调查评估表文书制作逻辑
    },

    handleManualUpload2() {
      this.manualUploadVisible2 = !this.manualUploadVisible2
    },

    handleOk() {
      this.handleSubmit(true)
    },

    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return

      this.submitLoading = true

      this.form.validateFields((err, values) => {
        if (!err) {
          const preparedData = this.prepareFormData(values)

          // 检查是否有未签名内容
          this.$http
            .get('/investigationSign/check', {
              params: {
                id: this.detailData.id || '',
                bizType: this.detailData.status
              }
            })
            .then(checkRes => {
              if (checkRes && !checkRes.success) {
                // 有未签名内容，弹出确认框
                this.$confirm({
                  title: '提示',
                  content: checkRes.message || '有未签名的内容，是否继续提交?',
                  okText: '继续提交',
                  cancelText: '取消',
                  onOk: () => {
                    this.submitFormData(preparedData)
                  },
                  onCancel: () => {
                    this.submitLoading = false
                  }
                })
              } else {
                // 没有未签名内容，直接提交
                this.submitFormData(preparedData)
              }
            })
            .catch(error => {
              console.error('签名检查失败：', error)
              // 检查失败也继续提交
              this.submitFormData(preparedData)
            })
        } else {
          this.submitLoading = false
          console.error('表单验证失败：', err)
          this.$message.error('请完善必填信息')
        }
      })
    },

    // 提交表单数据
    submitFormData(preparedData) {
      this.$http
        .post('/investigationInfo/approval', preparedData)
        .then(response => {
          console.log('保存成功：', response.data)
          this.submitLoading = false

          if (response && response.success) {
            this.$message.success('提交成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '提交失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('保存失败：', error)
          this.$message.error('提交失败，请稍后重试')
          this.submitLoading = false
        })
    },

    prepareFormData(values, isDraft = false) {
      const preparedData = { ...values }

      // 处理文件字段格式
      // if (preparedData.opinionFiles && Array.isArray(preparedData.opinionFiles)) {
      //   preparedData.opinionFiles = preparedData.opinionFiles.join(',');
      // }

      if (preparedData.evaluationFiles && Array.isArray(preparedData.evaluationFiles)) {
        preparedData.evaluationFiles = preparedData.evaluationFiles.map(item => item.id).join(',')
      }
      if (preparedData.opinionFiles && Array.isArray(preparedData.opinionFiles)) {
        preparedData.opinionFiles = preparedData.opinionFiles.map(item => item.id).join(',')
      }

      // 添加额外字段
      preparedData.approvalPsnId = this.userInfo?.id || null
      // 添加登录人机构id和机构名称
      preparedData.approvalDeptId = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgId
        : this.userInfo?.orgId || ''
      preparedData.approvalDeptName = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgName
        : this.userInfo?.orgName || ''
      preparedData.deleted = 0 // 默认未删除
      if (preparedData.entrustmentReceiveTime) {
        preparedData.entrustmentReceiveTime = moment(preparedData.entrustmentReceiveTime).format('YYYY-MM-DD')
      }
      if (preparedData.endTime) {
        preparedData.endTime = moment(preparedData.endTime).format('YYYY-MM-DD')
      }
      const submitData = {
        otherInfo: this.detailData,
        id: this.record && this.record.id ? this.record.id : null,
        ...preparedData
      }

      if (isDraft) {
        submitData.isDraft = 1
      }
      console.log(submitData)
      return submitData
    },

    handleTemporarily() {
      this.tempLoading = true

      const values = this.form.getFieldsValue()
      const preparedData = this.prepareFormData(values, true)

      this.$http
        .post('/investigationInfo/approval', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data)
          this.tempLoading = false

          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close')
          } else {
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('暂存失败：', error)
          this.$message.error('暂存失败，请稍后重试')
          this.tempLoading = false
        })
    },

    handleNextStep() {
      this.handleSubmit(false)
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return
      this.$emit('back')
    },
    // 表单校验函数，排除opinionFiles字段
    validateFormBeforeCreateDocument() {
      return new Promise((resolve, reject) => {
        // 定义不需要校验的字段列表
        const excludeFields = ['opinionFiles', 'evaluationFiles', 'id']

        // 获取表单所有字段名并排除不需要校验的字段
        const fieldsToValidate = Object.keys(this.form.getFieldsValue()).filter(field => !excludeFields.includes(field))

        // 只校验非排除字段
        this.form.validateFields(fieldsToValidate, (errors, values) => {
          if (errors) {
            reject(new Error('请先完善必填项信息'))
          } else {
            if (values.opinionFiles) {
              values.opinionFiles = values.opinionFiles.map(item => item.id).join(',')
            }
            if (values.evaluationFiles) {
              values.evaluationFiles = values.evaluationFiles.map(item => item.id).join(',')
            }
            values.id = this.detailData.id
            values.correctionObjName = this.detailData.correctionObjName
            // 将所有表单值传递给文书组件，包括未经校验的字段
            resolve(values)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.upload-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-help-text {
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
}
</style>

<template>
  <div class="flow-form">
    <div class="flow-form-header">
      <h3>{{ title }}</h3>
      <!-- 此组件是通用表单模板，可用于快速创建新的表单组件 -->
      <div class="flow-form-info">
        <span>编号：{{ formData.no }}</span>
        <span>时间：{{ formData.time }}</span>
        <span>处理人员：{{ formData.handler }}</span>
        <span>处理时间：{{ formData.handleTime }}</span>
      </div>
    </div>
    <a-form :form="form" class="flow-form-content">
      <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="社区矫正对象姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['name']"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="矫正单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input disabled v-decorator="['deptName']"/>
          </a-form-item>
        </a-col>
      </a-row>

      <div class="section-title">表单内容</div>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="内容" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="mode === 'view'"
              v-decorator="['content', { rules: [{ required: true, message: '请输入内容' }] }]"
              :autoSize="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入内容"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
            <a-textarea
              :disabled="mode === 'view'"
              v-decorator="['remark']"
              :autoSize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item :wrapperCol="{ span: 24 }" style="text-align: center" v-if="mode !== 'view'">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
        <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
      </a-form-item>
      <a-form-item :wrapperCol="{ span: 24 }" style="text-align: center" v-else>
        <a-button @click="onBack">返回</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  // 此组件是通用表单模板，可用于任何流程节点
  // 主要目的是提供一个基础模板，方便快速创建新的表单组件
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: '表单'
    },
    mode: {
      type: String,
      default: 'edit' // 'edit' 或 'view'
    }
  },
  data() {
    return {
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 3 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      form: this.$form.createForm(this),
      formData: {
        no: 'No' + new Date().getTime(),
        time: moment().format('YYYY-MM-DD'),
        handler: '管理员',
        handleTime: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      loading: false
    }
  },
  mounted() {
    // 如果有记录，则加载数据
    if (this.record && this.record.id) {
      this.fetchData();
    }
  },
  methods: {
    fetchData() {
      this.loading = true;
      // 模拟API请求获取数据
      setTimeout(() => {
        this.loadFormData();
        this.loading = false;
        console.log('通用表单数据已加载');
      }, 1000);
    },
    loadFormData() {
      // 模拟后台返回的数据
      const responseData = {
        id: this.record.id,
        name: this.record.name || '',
        deptName: this.record.deptName || '',
        content: '',
        remark: ''
      };

      // 设置表单值
      this.form.setFieldsValue(responseData);
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('表单值：', values)
          // 模拟保存成功
          this.$message.success('提交成功')

          // 显示确认对话框，询问用户是否继续下一步流程
          this.$confirm({
            title: '操作提示',
            content: '表单提交成功，是否立即进行下一步流程？',
            okText: '继续下一步',
            cancelText: '留在当前页面',
            onOk: () => {
              // 通知父组件进入下一步流程
              this.$emit('nextStep')
            }
          })
        }
      })
    },
    handleReset() {
      this.form.resetFields()
      this.loadFormData()
    },
    onBack() {
      this.$emit('back');
    }
  }
}
</script>

<style lang="less" scoped>

// 可以添加特定于本表单的样式（如果有必要）
</style>

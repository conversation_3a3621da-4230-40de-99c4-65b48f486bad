<template>
  <base-form-template
    title="调查小组公告"
    title-icon="notification"
    :mode="mode"
    @ok="handleOk"
    @nextStep="handleNextStep"
    @back="handleBack"
  >
    <!-- 表单内容区域 -->
    <a-form :form="form" :layout="formLayout">
      <a-form-item v-show="false"><a-input v-decorator="['id']"/></a-form-item>
      <a-form-item v-show="false"><a-input v-decorator="['noticeDocNumThree']"/></a-form-item>
      <a-form-item v-show="false"><a-input v-decorator="['noticeDocNumOne']"/></a-form-item>

      <!-- 调查小组信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查小组信息</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调查评估对象" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['correctionObjName', { rules: [{ required: true, message: '请输入调查评估对象' }] }]"
                placeholder="请输入调查评估对象姓名"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <sh-tree-select
                v-decorator="['inveDept', { rules: [{ required: true, message: '请选择调查单位' }] }]"
                apiURL="/sysOrg/tree"
                :params="{ level: 4 }"
                placeholder="选择本区县管辖的下级司法所"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查小组名称" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-input
                v-decorator="['inveGroupName', { rules: [{ required: true, message: '请输入调查小组名称' }] }]"
                placeholder="关于成立（类型）（姓名）实施社区矫正调查评估小组"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-person-selector
                :disabled="mode === 'view'"
                :treeUrl="'/sysOrg/tree?remark=all'"
                treeLabelKey="title"
                treeValueKey="id"
                :list-url="'/sysUser/page'"
                :name-field="'nickName'"
                :org-field="'orgNames'"
                v-decorator="[
                  'invePsnList',
                  {
                    rules: [{ type: 'array', required: true, message: '请选择调查人员!' }]
                  }
                ]"
                @change="handleInvePsnChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 调查小组公告 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>调查小组公告</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="公告标题" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-input
                v-decorator="['noticeTitle', { rules: [{ required: true, message: '请输入公告标题' }] }]"
                placeholder="关于成立犯罪嫌疑人王某某实施社区矫正调查评估小组"
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="公告时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                v-decorator="['noticeTime', { rules: [{ required: true, message: '请选择公告时间' }] }]"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="mode === 'view'"
                :disabledDate="disabledFutureDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="调查文书号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <div style="display: flex; align-items: center;">
                <span>{{ detailData.groups?.noticeDocNumOne || '(2025)杭余矫调评字第' }}</span>
                <a-input
                  v-decorator="[
                    'noticeDocNumTwo',
                    {
                      rules: [{ required: true, message: '请输入编号' }],
                      validateTrigger: ['blur'],
                      initialValue: this.detailData?.groups?.noticeDocNumTwo || ''
                    }
                  ]"
                  style="width: 80px; margin: 0 4px;"
                  :disabled="mode === 'view'"
                  @blur="handleSerialNumberBlur"
                />
                <span>{{ detailData.groups?.noticeDocNumThree || '号' }}</span>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="公告正文" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-textarea
                style="text-indent: 2em;"
                v-decorator="['noticeContext', { rules: [{ required: true, message: '请输入公告正文' }] }]"
                :rows="18"
                placeholder="根据某某县人民检察院委托调查函，委托德清县司法局对被告人王某某进行社区矫正调查评估，根据《浙江省社区矫正调查评估办法（试行）》浙司（2020）63号文件要求，成立对被告人王某某实施社区矫正调查评估小组，现将有关事项公告如下：..."
                :disabled="mode === 'view'"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 文书制作-调查小组公告 -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>文书制作-调查小组公告</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="调查小组公告" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <document-creator
                v-decorator="[
                  'noticeDocFiles',
                  { rules: [{ type: 'array', required: true, message: '请选择调查小组公告' }] }
                ]"
                :formData="form.getFieldsValue()"
                :disabled="mode === 'view'"
                :status="detailData.status"
                :detailData="detailData"
                :showStatusTag="false"
                :enableCloudSign="true"
                :validate-form="validateFormBeforeCreateDocument"
                @update:docId="updateDocId"
              />
              <div v-if="mode !== 'view'" style="color: #999; font-size: 12px;line-height: 1.5em;">
                (说明：点击"制作文书"可自动生成规范文书进行电子签章、电子签名；未授权电子签章用户可使用手动上传已盖章的文书扫描件，文件类型只支持.pdf格式）
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <!-- 流程流转提醒（浙政钉） -->
      <div class="form-section">
        <div class="section-title">
          <div class="section-line"></div>
          <span>流程流转提醒（浙政钉）</span>
        </div>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="流程提醒通知" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <div class="modern-notification-box">
                <div class="notification-icon">
                  <a-icon type="notification" />
                </div>
                <div class="notification-content">
                  <p>
                    您收到一条流程流转提醒，请及时查看调查评估系统。
                  </p>
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="提醒人员" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <sh-person-selector
                :disabled="mode === 'view'"
                :treeUrl="'/sysOrg/tree?remark=all'"
                treeLabelKey="title"
                treeValueKey="id"
                :list-url="'/sysUser/page'"
                :name-field="'nickName'"
                :org-field="'orgNames'"
                v-decorator="[
                  'msgPsnList',
                  {
                    rules: [{ type: 'array', required: false, message: '请选择提醒人员!' }]
                  }
                ]"
                @change="handleInvePsnChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>

    <!-- 自定义按钮区域 -->
    <template #actions>
      <div v-if="mode !== 'view'">
        <a-button v-if="record.id.length >= 32" @click="handlePrevStep" style="margin-right: 8px">
          <a-icon type="left" />上一步
        </a-button>
        <a-button @click="handleTemporarily" :loading="tempLoading" style="margin-right: 8px">
          <a-icon v-if="!tempLoading" type="save" />暂存
        </a-button>
        <a-button type="primary" @click="handleSubmit(true)" :loading="submitLoading">
          <a-icon v-if="!submitLoading" type="check" />提交
        </a-button>
      </div>
      <div v-else>
        <a-button @click="handleBack"> <a-icon type="left" />返回 </a-button>
      </div>
    </template>
  </base-form-template>
</template>

<script>
import BaseFormTemplate from './BaseFormTemplate.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import DocumentCreator from './components/DocumentCreator.vue'
import { disabledFutureDate } from '@/utils/dateUtils'

export default {
  components: {
    BaseFormTemplate,
    DocumentCreator
  },
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formLayout: 'horizontal',
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
      labelCol2: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol2: { xs: { span: 24 }, sm: { span: 20 } },
      submitLoading: false,
      tempLoading: false,
      docId: null
    }
  },
  mounted() {
    // 使用传入的detailData初始化表单
    this.initFormData()
  },
  methods: {
    disabledFutureDate,
    // 处理文书号输入框失去焦点
    handleSerialNumberBlur() {
      const value = this.form.getFieldValue('noticeDocNumTwo')
      if (!value) return

      // 获取登录人机构id
      const deptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : this.userInfo?.orgId || ''

      // 调用接口验证文书号
      this.form.setFields({
        noticeDocNumTwo: {
          value: value,
          validating: true
        }
      })

      this.$http
        .get('/documentserial/checkSerialNum', {
          params: {
            deptId: this.detailData.receiveDeptId || deptId,
            type: 'WSLX01',
            referId: this.detailData.id || '',
            serialNumber: value
          }
        })
        .then(response => {
          if (response && response.success) {
            // 校验通过
            this.form.setFields({
              noticeDocNumTwo: {
                value: value,
                errors: null
              }
            })
          } else {
            // 校验不通过
            const errorMsg = response.message || '文书号已存在，请更换'
            this.form.setFields({
              noticeDocNumTwo: {
                value: value,
                errors: [new Error(errorMsg)]
              }
            })
          }
        })
        .catch(error => {
          console.error('文书号校验失败：', error)
          this.form.setFields({
            noticeDocNumTwo: {
              value: value,
              errors: [new Error('校验文书号时发生错误，请稍后重试')]
            }
          })
        })
    },

    // 验证文书号 - 保留但不直接使用
    validateSerialNumber(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 获取登录人机构id
      const deptId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : this.userInfo?.orgId || ''

      // 调用接口验证文书号
      this.$http
        .get('/documentserial/checkSerialNum', {
          params: {
            deptId: this.detailData.receiveDeptId || deptId,
            type: 'WSLX01',
            referId: this.detailData.id || '',
            serialNumber: value
          }
        })
        .then(response => {
          if (response && response.success) {
            callback() // 校验通过
          } else {
            const errorMsg = response.message || '文书号已存在，请更换'
            callback(new Error(errorMsg)) // 校验不通过
          }
        })
        .catch(error => {
          console.error('文书号校验失败：', error)
          callback(new Error('校验文书号时发生错误，请稍后重试'))
        })
    },

    initFormData() {
      // 使用父组件传入的detailData作为表单数据源
      const formValues = this.detailData || {}
      const groups = formValues.groups || {}
      console.log('initFormData', formValues, groups)
      // 设置表单的初始值
      this.$nextTick(() => {
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss')
        // 在非查看模式下，设置当前用户为处理人，当前时间为处理时间
        const auditor =
          this.mode !== 'view' ? this.userInfo?.name || '当前用户' : groups.approvalPsn || '默认当前登录人'
        const auditTime =
          this.mode !== 'view'
            ? currentTime
            : groups.approvalTime
            ? moment(groups.approvalTime).format('YYYY-MM-DD HH:mm:ss')
            : currentTime

        // 设置登录人机构id和机构名称
        const orgId = this.mode !== 'view' ? this.userInfo?.orgId || '' : groups.orgId || ''
        const orgName = this.mode !== 'view' ? this.userInfo?.orgName || '' : groups.orgName || ''

        // 处理文书号
        const noticeDocNumTwo = groups.noticeDocNumTwo

        // 设置表单默认值
        this.form.setFieldsValue({
          id: formValues.id || null,
          correctionObjName: groups.correctionObjName || null,
          inveDept: groups.inveDept || null,
          inveGroupName:
            groups.inveGroupName ||
            `${this.$options.filters['dictType']('nsysqjzrylx', formValues.psnType)}${groups.correctionObjName ||
              ''}实施社区矫正调查评估小组`,

          invePsnList: groups.invePsnList || [],
          msgPsnList: groups.msgPsnList ? JSON.parse(groups.msgPsnList) : groups.invePsnList,
          noticeTitle:
            groups.noticeTitle ||
            `关于成立${this.$options.filters['dictType'](
              'nsysqjzrylx',
              formValues.psnType
            )}${groups.correctionObjName || ''}实施社区矫正调查评估小组的公告`,
          noticeTime: groups.noticeTime ? moment(groups.noticeTime) : moment(),
          noticeDocNumTwo: noticeDocNumTwo,
          noticeDocNumThree: groups.noticeDocNumThree,
          noticeDocNumOne: groups.noticeDocNumOne,
          noticeContext: groups.noticeContext || this.generateDefaultNoticeContext(formValues, groups),
          noticeDocFiles: groups.noticeDocList && groups.noticeDocList[0] ? groups.noticeDocList : [],
          orgId: orgId,
          orgName: orgName,
          approvalPsn: auditor,
          approvalTime: auditTime
        })
        // console.log('this.form.getFieldsValue()', this.generateDefaultNoticeContext(formValues, groups));
        // 设置文书ID
        // this.docId = groups.noticeDocFiles || null;
      })
    },

    // 生成默认公告正文
    generateDefaultNoticeContext(formValues, groups) {
      console.log(formValues.inveTimeLimit, 'formValues.inveTimeLimit')

      console.log(this.$options.filters['dictType']('nsysqjzrylx', formValues.psnType), '------')
      return `根据${formValues.entrustmentDeptName || '某某县人民检察院'}委托调查函，委托${formValues.wslk ||
        formValues.receiveDeptName}对${this.$options.filters['dictType'](
        'nsysqjzrylx',
        formValues.psnType
      )}${groups.correctionObjName ||
        ''}进行社区矫正调查评估，根据《浙江省社区矫正调查评估办法（试行）》浙司（2020）63号文件要求，成立对${this.$options.filters[
        'dictType'
      ]('nsysqjzrylx', formValues.psnType)}${groups.correctionObjName ||
        ''}实施社区矫正调查评估小组，现将有关事项公告如下：
（一）调查小组成员名单：
 ${
   groups.invePsnList
     ? groups.invePsnList
         .map(
           (person, index) =>
             `${index > 0 ? ' ' : ''}${person.nickName} ${person.sjName ? person.sjName : person.orgNames}`
         )
         .join('\n')
     : ''
 }
（二）调查小组成员有下列情形之一的，应当回避，任何单位和个人可向社区矫正中心反映:
 1、属本案当事人或者是当事人近亲属的；
 2、本人或者其近亲属与本案有利害关系的；
 3、曾担任本案证人、鉴定人、辩护人、诉讼代理人的
 4、与本案当事人有其他关系，可能影响调查评估公正性的。
（三）调查时间
    ${moment(formValues.entrustmentReceiveTime).format('YYYY年M月D日')}至${moment(formValues.inveTimeLimit).format(
        'YYYY年M月D日'
      )}
 调查小组成员因工作或职务变动，由${formValues.wslk ||
   formValues.receiveDeptName}指派另外成员，不另公告；调查工作结束后，调查小组自行撤销，不另公告。`
    },

    handleOk() {
      this.handleSubmit(true)
    },

    // 通用提交方法，参数showConfirm控制是否显示确认对话框
    handleSubmit(showConfirm = true) {
      if (this.submitLoading && !showConfirm) return // 如果正在提交且是直接下一步模式，阻止操作

      // 设置提交加载状态
      this.submitLoading = true

      this.form.validateFields((err, values) => {
        if (!err) {
          // 检查noticeDocNumTwo是否存在有效值
          if (!values.noticeDocNumTwo) {
            this.submitLoading = false
            this.$message.error('请输入编号')
            return
          }

          // 准备表单数据
          const preparedData = this.prepareFormData(values)

          // 使用$http进行请求
          this.$http
            .post('/investigationInfo/publishNotice', preparedData)
            .then(response => {
              console.log('保存成功：', response.data)

              // 请求成功，关闭加载状态
              this.submitLoading = false

              // 判断接口返回是否成功
              if (response && response.success) {
                // 处理成功响应
                this.$message.success('提交成功')
                this.handleClose()
              } else {
                // 接口返回失败
                const errorMsg = (response && response.message) || '提交失败，请稍后重试'
                this.$message.error(errorMsg)
              }
            })
            .catch(error => {
              console.error('保存失败：', error)
              this.$message.error('提交失败，请稍后重试')
              this.submitLoading = false
            })
        } else {
          // 表单验证失败，重置加载状态
          this.submitLoading = false
          console.error('表单验证失败：', err)
          this.$message.error('请完善必填信息')
        }
      })
    },

    // 处理表单数据的通用方法
    prepareFormData(values, isDraft = false) {
      // 拷贝表单数据，避免直接修改原对象
      const preparedData = { ...values }

      // 将数据封装到groups对象中
      const groups = {
        correctionObjName: preparedData.correctionObjName,
        inveDept: preparedData.inveDept,
        inveGroupName: preparedData.inveGroupName,
        invePsnList: preparedData.invePsnList,
        msgPsnList: preparedData.msgPsnList,
        noticeTitle: preparedData.noticeTitle,
        noticeDocNumTwo: preparedData.noticeDocNumTwo,
        noticeContext: preparedData.noticeContext
      }

      // 获取文书号前后缀
      const noticeDocNumOne = this.detailData.groups?.noticeDocNumOne || '(2025)杭余矫调评字第'
      const noticeDocNumThree = this.detailData.groups?.noticeDocNumThree || '号'

      // 保存到groups中便于后续使用
      groups.noticeDocNumOne = noticeDocNumOne
      groups.noticeDocNumThree = noticeDocNumThree

      // 组合文书号
      groups.noticeDocNum = `${noticeDocNumOne}${preparedData.noticeDocNumTwo}${noticeDocNumThree}`

      // 处理日期格式
      if (preparedData.noticeTime) {
        groups.noticeTime = preparedData.noticeTime.format('YYYY-MM-DD')
      }
      // 文书
      console.log('preparedData.noticeDocFiles', preparedData.noticeDocFiles)
      if (preparedData.noticeDocFiles) {
        groups.noticeDocFiles = preparedData.noticeDocFiles.map(item => item.id).join(',')
      }

      // 处理invePsnList，只保留id、nickName和orgNames字段
      if (groups.invePsnList && groups.invePsnList.length > 0) {
        groups.invePsnList = groups.invePsnList.map(person => ({
          id: person.id,
          nickName: person.nickName,
          sjName: person.sjName,
          orgNames: person.orgNames,
          name: person.orgFullName
          // orgId: person.orgId,
        }))
      }
      // 处理msgPsnList，只保留id、nickName和orgNames字段
      if (groups.msgPsnList && groups.msgPsnList.length > 0) {
        groups.msgPsnList = groups.msgPsnList.map(person => ({
          id: person.id,
          nickName: person.nickName,
          sjName: person.sjName,
          orgNames: person.orgNames,
          name: person.orgFullName
          // orgId: person.orgId,
        }))
      }
      // 添加当前用户和时间
      if (this.mode !== 'view') {
        groups.approvalPsn = this.userInfo?.name || '当前用户'
        groups.approvalTime = moment().format('YYYY-MM-DD HH:mm:ss')
      }

      // 添加登录人机构id和机构名称
      groups.orgId = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : this.userInfo?.orgId || ''
      groups.orgName = this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgName : this.userInfo?.orgName || ''

      // 构造提交数据
      const submitData = {
        id: this.record && this.record.id ? this.record.id : null,
        ...groups
      }

      // 如果是暂存，添加标记
      if (isDraft) {
        submitData.isDraft = 1
      }
      // 添加登录人机构id和机构名称
      submitData.approvalDeptId = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgId
        : this.userInfo?.orgId || ''
      submitData.approvalDeptName = this.userInfo.loginEmpInfo
        ? this.userInfo.loginEmpInfo.orgName
        : this.userInfo?.orgName || ''

      return submitData
    },

    handleTemporarily() {
      // 暂存功能不需要验证表单，直接获取当前表单值
      this.tempLoading = true

      // 获取当前表单值，不进行验证
      const values = this.form.getFieldsValue()

      // 准备表单数据，传入isDraft=true参数
      const preparedData = this.prepareFormData(values, true)

      // 使用$http发送请求，使用与提交相同的接口
      this.$http
        .post('/investigationInfo/publishNotice', preparedData)
        .then(response => {
          console.log('暂存成功：', response.data)
          this.tempLoading = false

          // 判断接口返回是否成功
          if (response && response.success) {
            this.$message.success('暂存成功')
            this.$emit('close')
          } else {
            // 接口返回失败
            const errorMsg = (response && response.message) || '暂存失败，请稍后重试'
            this.$message.error(errorMsg)
          }
        })
        .catch(error => {
          console.error('暂存失败：', error)
          this.$message.error('暂存失败，请稍后重试')
          this.tempLoading = false
        })
    },

    handleNextStep() {
      this.handleSubmit(false)
    },

    // 添加上一步处理方法
    handlePrevStep() {
      // 弹出确认框，询问是否返回上一步
      this.$confirm({
        title: '操作提示',
        content: '确定要返回上一步吗？当前未保存的数据将会丢失。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 触发父组件中的上一步事件
          this.$emit('prevStep', null, '/investigationTranscript/stepToReveive')
          //  this.$http
          // .get('/investigationTranscript/stepToReveive' + '?id=' + this.record.id)
          // .then(res => {
          //   this.formMode = 'edit'
          //   this.activeNodeKey = this.currentNodeKey
          //   // 获取最新的详情数据
          //   this.fetchDetailData(this.record.id)
          //   this.confirmLoading = false
          // })
        }
      })
    },

    handleReset() {
      if (this.submitLoading) return // 如果正在提交，阻止重置操作

      this.form.resetFields()
      this.initFormData()
    },

    handleBack() {
      if (this.submitLoading && this.mode !== 'view') return // 如果正在提交且不是查看模式，阻止返回操作

      this.$emit('back')
    },

    // 处理关闭弹框
    handleClose() {
      this.$emit('close')
    },

    // 更新文书ID
    updateDocId(id) {
      // this.docId = id;
      // // 更新表单中的文书文件字段
      // this.form.setFieldsValue({
      //   noticeDocFiles: id
      // });
    },
    // 表单校验函数，排除opinionFiles字段
    validateFormBeforeCreateDocument() {
      return new Promise((resolve, reject) => {
        // 定义不需要校验的字段列表
        const excludeFields = ['noticeDocFiles', 'evaluationFiles', 'id', 'conclusion', 'particular']

        // 获取表单所有字段名并排除不需要校验的字段
        const fieldsToValidate = Object.keys(this.form.getFieldsValue()).filter(field => !excludeFields.includes(field))

        // 只校验非排除字段
        this.form.validateFields(fieldsToValidate, (errors, values) => {
          if (errors) {
            reject(new Error('请先完善必填项信息'))
          } else {
            // 将所有表单值传递给文书组件，包括未经校验的字段
            const formValues = this.form.getFieldsValue()

            resolve(formValues)
          }
        })
      })
    },

    handleInvePsnChange(value) {
      console.log('handleInvePsnChange', value)
      // 获取当前的公告正文
      const currentContext = this.form.getFieldValue('noticeContext')
      if (!currentContext) return

      // 生成新的成员名单文本
      const newMembersList =
        value && value.length > 0
          ? value.map(person => ` ${person.nickName} ${person.sjName ? person.sjName : person.orgNames}`).join('\n')
          : ''

      // 使用正则表达式查找并替换成员名单部分
      // 匹配（一）调查小组成员名单：后面直到（二）之前的内容
      const updatedContext = currentContext.replace(
        /(（一）调查小组成员名单：\n)[\s\S]*?(?=\n（二）|$)/,
        `$1${newMembersList}`
      )

      // 更新表单中的公告正文
      this.form.setFieldsValue({
        noticeContext: updatedContext
      })
      const msgPsnList = this.form.getFieldValue('msgPsnList')
      const newMsgPsnList = [...msgPsnList, ...value]
      this.form.setFieldsValue({
        msgPsnList: this.uniqueObjArraySimple(newMsgPsnList, 'id')
      })
    },
    uniqueObjArraySimple(arr, key) {
      return arr.filter((current, index) => {
        // 查找当前对象之前是否存在相同 key 值的对象
        const firstIndex = arr.findIndex(item => item[key] === current[key])
        // 仅保留第一次出现的对象
        return index === firstIndex
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 可以添加特定于本表单的样式（如果有必要）
.modern-notification-box {
  display: flex;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f7ff 100%);
  border-radius: 12px;
  padding: 16px;
  //box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #91d5ff;
  transition: all 0.3s ease;

  &:hover {
    // box-shadow: 0 8px 20px -10px rgba(0, 0, 0, 0.12), 0 12px 32px 0 rgba(0, 0, 0, 0.08);
    // transform: translateY(-2px);
  }

  .notification-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    margin-right: 16px;
    flex-shrink: 0;

    .anticon {
      font-size: 20px;
      color: white;
    }
  }

  .notification-content {
    flex: 1;
    display: flex;
    align-items: center;

    p {
      margin: 0;
      line-height: 1.6;
      color: #262626;
      font-size: 14px;
    }
  }
}
</style>

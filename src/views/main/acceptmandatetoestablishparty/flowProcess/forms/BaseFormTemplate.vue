<template>
  <div class="flow-form">
    <div class="flow-title" v-show="false">
      <a-icon :type="titleIcon" class="title-icon" />
      <span class="title-text">{{ title }}</span>
      <!-- <span class="title-tip">
        <a-icon type="info-circle" />
        {{ mode === 'view' ? '查看详情' : '编辑提交表单' }}
      </span> -->
    </div>

    <div class="flow-form-content">
      <!-- 表单内容插槽 -->
      <slot></slot>
    </div>

    <div class="flow-form-footer" v-if="mode !== 'view'">
      <!-- 自定义按钮区域 -->
      <slot name="actions">
        <!-- 默认按钮 -->
        <a-button v-if="showBackBtn && mode !== 'view'" @click="onBack">
          <a-icon type="left" />返回
        </a-button>
        <a-button v-if="showSaveBtn && mode !== 'view'" type="primary" @click="onSave">
          <a-icon type="save" />保存
        </a-button>
        <a-button v-if="showNextBtn && mode !== 'view'" type="primary" @click="onNext">
          下一步<a-icon type="right" />
        </a-button>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseFormTemplate',
  props: {
    // 表单标题
    title: {
      type: String,
      required: true
    },
    // 标题图标
    titleIcon: {
      type: String,
      default: 'form'
    },
    // 表单模式：'edit' 或 'view'
    mode: {
      type: String,
      default: 'edit'
    },
    // 是否显示返回按钮
    showBackBtn: {
      type: Boolean,
      default: true
    },
    // 是否显示保存按钮
    showSaveBtn: {
      type: Boolean,
      default: true
    },
    // 是否显示下一步按钮
    showNextBtn: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    // 返回事件
    onBack() {
      this.$emit('back');
    },
    // 保存事件
    onSave() {
      this.$emit('ok');
    },
    // 下一步事件
    onNext() {
      this.$emit('nextStep');
    }
  }
};
</script>

<style lang="less" scoped>

.flow-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  // border-radius: 6px;
  // box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.flow-form-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 16px;
}

.flow-form-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  margin-top: 8px;
  background: linear-gradient(to bottom, rgba(240, 242, 245, 0) 0%, rgba(240, 242, 245, 0.5) 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0 0 6px 6px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;

  button {
    margin-left: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &[type="primary"] {
      background: linear-gradient(to right, #1890ff, #096dd9);
      border: none;

      &:hover {
        background: linear-gradient(to right, #40a9ff, #0e77e2);
      }
    }

    .anticon {
      transition: all 0.3s ease;
    }

    &:hover .anticon {
      transform: scale(1.1);
    }
  }
}
</style>

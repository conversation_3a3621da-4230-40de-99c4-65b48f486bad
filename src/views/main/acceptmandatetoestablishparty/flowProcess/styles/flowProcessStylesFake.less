
.flow-controller{
    .flow-container{
        background: none !important;
        border-radius: 0px !important;
        box-shadow: none !important;
    }
    .flow-title{
        background: none !important;
        border-radius: 0px !important;
        box-shadow: none !important;
        padding-left: 42px;
        color: rgba(51, 51, 51, 1);
        border-bottom: 1px solid rgba(217, 217, 217, 1);
        position: relative;
        font-size: 18px;
        &:after{
            content: '';
            position: absolute;
            bottom: 50%;
            transform: translateY(50%);
            left: 30px;
            width: 4px;
            height: 27px;
            background: #1690FF;
         
        }
        .title-tip{
            color: rgba(93, 93, 93, 1);
        }
    }
    .flow-main-node{
        padding: 0;
        border-radius: 0px;
        border: none !important;
        background: none !important;
        &.completed-main{
            color: #1890ff !important;  
            .flow-node-subtitle{
                // color: #1890ff !important;  
            }

        }
        .flow-sub-node .flow-sub-node-details{
            border: none !important;
        }
        .flow-main-node-content{
            // margin-bottom: 10px;
        }
        .flow-node-title{
            .node-icon{
                display: none !important;
            }
        }
    } 
    .flow-main-node .flow-main-node-left{
        margin-left: -30px;
    }
    .flow-sub-node .ant-badge-status{
        margin-left: -16px;
    }
    .flow-sub-node{
        box-shadow: none !important;
    }
    .flow-main-connector{
        bottom: 38px !important;
    }
    .flow-sub-nodes .flow-sub-node{
        padding: 0;
        border-radius: 0px;
        border: none !important;
        background: none !important;
        .anticon{
            display: none !important;
        }
    }

    .flow-sub-node{
        margin: 0 !important;
        .flow-sub-node-main{
            font-weight: bold;
            padding: 10px 0 !important;
        }
        .flow-sub-node-details{
            margin: 0;
            padding-left: 10px;
            padding-top: 0;
            border: none !important;
        }
        &.completed{
            .flow-sub-node-main{
                color: #1890ff !important;  
            }
            .detail-value{
                color: rgba(0, 0, 0, 0.45);
          
            }
          
        }
        &.current-active{
            // font-weight: bold;
            .flow-sub-node-main{
                color: #096dd9 !important;  
            }
            .detail-value{
                color: rgba(0, 0, 0, 0.45);
          
            }
        }
    }
}
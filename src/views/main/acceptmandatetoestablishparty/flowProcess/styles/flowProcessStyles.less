/* 流程处理组件共享样式 */

// 流程标题样式
.flow-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(to right, #1890ff, #096dd9);
  color: white;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

  .title-icon {
    font-size: 20px;
    margin-right: 10px;
  }

  .title-text {
    margin-right: 12px;
    letter-spacing: 1px;
  }

  .title-tip {
    font-size: 12px;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.85);
    display: flex;
    align-items: center;

    .anticon {
      margin-right: 4px;
    }
  }
}

// 流程容器样式
.flow-container {
  height: 100%;
  background-color: #f0f2f5;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
}

// 表单容器样式
.flow-controller-container {
  padding: 24px;
  height: 100%;
}
.flow-form-container {
  height: 100%;
  padding: 24px;
  padding-top: 0;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 4px;
  position: relative;
  .flow-form-toolbar {
    position: absolute;
    bottom: 34px;
    left: 34px;
  }
}

// 提示信息样式
.flow-tip-box {
  // margin: 10px 0;
  // padding: 10px;
  // border-radius: 4px;

  &.info {
    // background-color: #e6f7ff;
    // border: 1px solid #91d5ff;
  }

  &.success {
    // background-color: #f6ffed;
    // border: 1px solid #b7eb8f;
  }

  &.warning {
    // background-color: #fffbe6;
    // border: 1px solid #ffe58f;
  }
}

// 空状态提示
.flow-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  flex-direction: column;

  .anticon {
    font-size: 32px;
    margin-bottom: 16px;
    color: #d9d9d9;
  }
}

// 标题下的工具栏
.flow-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

// 表单头部信息
.flow-form-info {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 3px solid #1890ff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);

  span {
    margin-right: 16px;
    color: rgba(0, 0, 0, 0.65);
    display: inline-block;
    margin-bottom: 4px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 表单分段样式
.form-section {
  background-color: #fff;
  border-radius: 4px;
  padding: 0 0 8px 0;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

// 现代化的分段标题样式
// .section-title {
//   position: relative;
//   display: flex;
//   align-items: center;
//   margin-bottom: 22px;
//   padding: 10px 0 10px 12px;
//   background: linear-gradient(
//     to right,
//     rgba(240, 242, 245, 0.8) 0%,
//     rgba(240, 242, 245, 0.2) 80%,
//     rgba(240, 242, 245, 0) 100%
//   );
//   border-radius: 6px 0 0 6px;
//   border-radius: 4px;
//   border-left: 3px solid #1890ff;

//   &::before {
//     // content: '';
//     // position: absolute;
//     // left: 0;
//     // top: 0;
//     // bottom: 0;
//     // width: 4px;
//     // background: linear-gradient(to bottom, #1890ff, #40a9ff);
//     // // border-radius: 2px;
//     // box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
//   }

//   .section-line {
//     display: none; // 不再需要单独的线条元素
//   }

//   span {
//     position: relative;
//     font-size: 16px;
//     font-weight: 600;
//     color: #262626;
//     letter-spacing: 0.5px;
//     padding-left: 8px;
//     transition: all 0.3s ease;

//     &::after {
//       content: '';
//       position: absolute;
//       left: 8px;
//       bottom: -4px;
//       width: 0;
//       height: 2px;
//       background: linear-gradient(to right, #1890ff, #40a9ff);
//       transition: width 0.3s ease;
//       opacity: 0.7;
//       border-radius: 1px;
//     }
//   }

//   &:hover {
//     // span::after {
//     //   width: calc(100% - 8px);
//     // }
//   }

//   // 添加一个半透明的装饰元素
//   &::after {
//     content: '';
//     position: absolute;
//     right: 16px;
//     top: 50%;
//     transform: translateY(-50%);
//     width: 24px;
//     height: 24px;
//     background-image: linear-gradient(
//       135deg,
//       rgba(24, 144, 255, 0.2) 25%,
//       transparent 25%,
//       transparent 50%,
//       rgba(24, 144, 255, 0.2) 50%,
//       rgba(24, 144, 255, 0.2) 75%,
//       transparent 75%,
//       transparent
//     );
//     background-size: 12px 12px;
//     border-radius: 50%;
//     opacity: 0.3;
//   }
// }
.section-title  {
  overflow: hidden;
  /* 字体大小 */
  font-weight: bold;
  font-size: 18px;
  /* 字体粗细 */
  color: #333;
  /* 字体颜色 */
  /* 文本阴影 */
  position: relative;
  margin-bottom: 20px;
  padding-left: 16px;
  &::after {
    display: block;
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 4px;
    height: 27px;
    background: #1690FF;
    transform: translateY(-50%);

  }
}


// 文档文件区样式
.document-files {
  margin-bottom: 16px;

  .file-actions {
    margin-bottom: 12px;
    display: flex;
    gap: 8px;
  }
}

// 表单动作按钮区
.form-actions {
  margin-top: 24px;
  text-align: center;

  button {
    min-width: 100px;
  }
}
.flow-form-header-unit {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  h3 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 0;
    display: flex;
    align-items: center;
  }
  .line {
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #409eff;
    margin-right: 10px;
  }
}

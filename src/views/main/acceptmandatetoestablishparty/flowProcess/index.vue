<template>
  <sh-drawer
    :visible="visible"
    :footer="null"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
    :bodyStyle="{ padding: 0 }"
  >
    <div class="flow-title drawer-title">
      <img style="width: 22px;margin-right: 6px;" src="@/assets/logo.png" class="title-logo" />
      <span class="title-text">{{ dynamicTitle }}</span>
      <!-- <span class="title-tip">
        <a-icon type="info-circle" />
        {{ isViewOnlyMode ? '查看流程处理记录' : '编辑并提交流程处理' }}
      </span> -->
    </div>
    <main v-if="visible" class="flow-main">
      <a-spin :spinning="confirmLoading" tip="数据加载中...">
        <a-row style="height: 100%;" v-if="!confirmLoading">
          <!-- 左侧表单内容 -->
          <a-col :span="16" style="height: 100%;">
            <div class="flow-form-container">
              <keep-alive v-if="currentComponent">
                <component
                  :is="currentComponent"
                  :record="record"
                  :detailData="detailData"
                  :mode="formMode"
                  :currentNode="currentNode"
                  @ok="handleOk"
                  @nextStep="goToNextStep"
                  @prevStep="goToPrevStep"
                  @back="setEditMode"
                  @close="handleCancel"
                  :key="currentNodeKey + '-' + (currentNode && currentNode.rawData ? currentNode.rawData.id : '')"
                ></component>
              </keep-alive>
              <div v-else class="flow-empty-state flow-form-toolbar">
                <a-icon type="select" />
                请选择左侧流程节点
              </div>

              <!-- 显示查看模式提示 -->
              <!-- <div v-if="isViewOnlyMode" class="flow-tip-box info flow-form-toolbar">
                <a-alert type="info" show-icon>
                  <template slot="message">
                    当前处于查看模式，所有操作仅供查看，无法编辑或提交
                  </template>
                </a-alert>
              </div> -->
              <!-- 添加返回当前活跃节点的按钮 -->
            </div>
          </a-col>
          <!-- 右侧流程节点 -->
          <a-col :span="8" style="height: 100%;">
            <div class="flow-controller-container" style="padding: 0;">
              <flow-controller
                v-model="currentNodeKey"
                :completedSteps="completedSteps"
                :activeStep="activeNodeKey"
                :showFlowChart="false"
                :record="record"
                :isViewMode="isViewOnlyMode"
                @component-change="onComponentChange"
                ref="flowController"
              />
            </div>
          </a-col>
        </a-row>
        <div v-else class="loading-container">
          <a-empty description="数据加载中，请稍候..." />
        </div>
      </a-spin>
    </main>
  </sh-drawer>
</template>

<script>
import moment from 'moment'
import './styles/flowProcessStyles.less'
// 流程控制器组件
import FlowController from './components/FlowController'

// 接收调查评估委托表单 PGZT01
import ReceiveForm from './forms/ReceiveForm'
// 调查小组公告表单 PGZT02
import AnnouncementForm from './forms/AnnouncementForm'
// 调查单位接收表单 PGZT03_1
import UnitReceiveForm from './forms/UnitReceiveForm'
// 评估清单管理表单 PGZT03_2
import AssessmentListForm from './forms/AssessmentListForm'
// 评估结果查看与提交表单 PGZT03_3
import AssessmentResultForm from './forms/AssessmentResultForm'
// 初审/小组合议表单 PGZT04
import PreliminaryForm from './forms/PreliminaryForm'
// 初审/集体评议表单 PGZT05
import CollectiveForm from './forms/CollectiveForm'
// 审批表单 PGZT06
import ApprovalForm from './forms/ApprovalForm'
// 反馈表单 PGZT07
import FeedbackForm from './forms/FeedbackForm'

export default {
  components: {
    FlowController,
    ReceiveForm,
    AnnouncementForm,
    UnitReceiveForm,
    AssessmentListForm,
    AssessmentResultForm,
    PreliminaryForm,
    CollectiveForm,
    ApprovalForm,
    FeedbackForm
  },
  data() {
    return {
      currentNode: {},
      visible: false,
      confirmLoading: false,
      record: null,
      detailData: null, // 添加detailData存储详情数据
      originalDetailData: null, // 存储从fetchDetailData获取的原始数据
      currentNodeKey: '', // 用户当前查看的节点ID
      activeNodeKey: '', // 真正的当前活跃节点ID
      currentComponent: null, // 当前显示的组件名称
      formMode: 'edit', // 'edit' 或 'view'
      completedSteps: [], // 已完成的步骤ID
      isViewOnlyMode: false // 是否是纯查看模式，通过handleViewFlowNode方法打开的
    }
  },
  computed: {
    dynamicTitle() {
      if (!this.record) return '流程处理'

      const status = this.record.status
      const isNewRecord = this.record.addType === 'new'

      // 提取常用变量 - 从detailData获取，不再从record获取
      const name = this.detailData?.correctionObjName || ''
      const dept = this.detailData?.entrustmentDeptName || ''

      // 从detailData.groups获取docNum和inveTimeLimit
      const docNum = this.detailData?.deliberations?.docNum || ''
      const timeLimit = this.detailData?.groups?.inveTimeLimit
        ? moment(this.detailData.groups.inveTimeLimit).format('YYYY-MM-DD')
        : ''

      const expedited = this.detailData?.expeditedProcedure === '1' ? '速裁' : ''
      const entrustTime = this.detailData?.entrustmentTime
        ? moment(this.detailData.entrustmentTime).format('YYYY-MM-DD')
        : ''

      // 辅助函数：安全拼接字符串，空值时不添加连接符
      const safeConcat = parts => {
        return parts.filter(part => part && part.trim() !== '').join('-')
      }

      // 规则1：新增调查评估
      if (isNewRecord) {
        return '新增调查评估'
      }

      // 规则2：PGZT01, PGZT02
      if (['PGZT01', 'PGZT02'].includes(status)) {
        return safeConcat([name, dept ? `${dept}委托审前调查` : '', entrustTime])
      }

      // 规则3：PGZT03, PGZT03_1, PGZT03_2, PGZT03_3
      if (['PGZT03', 'PGZT03_1', 'PGZT03_2', 'PGZT03_3'].includes(status)) {
        return safeConcat([
          name,
          dept ? `${dept}委托审前调查` : '',
          timeLimit ? `${timeLimit}截止` : '',
          docNum,
          expedited
        ])
      }

      // 规则4：PGZT04, PGZT05, PGZT06
      if (['PGZT04', 'PGZT05', 'PGZT06'].includes(status)) {
        return safeConcat([
          name,
          dept ? `${dept}委托审前调查` : '',
          timeLimit ? `${timeLimit}截止` : '',
          docNum,
          expedited
        ])
      }

      // 规则5：PGZT07, PGZT08
      if (['PGZT07', 'PGZT08'].includes(status)) {
        return safeConcat([name, dept ? `${dept}委托审前调查` : '', docNum, expedited])
      }

      // 规则6：PGZT99
      if (status === 'PGZT99') {
        return safeConcat([name, dept ? `${dept}委托审前调查` : '', '流程终止'])
      }

      // 默认返回
      return '流程处理'
    }
  },
  methods: {
    async open(record, nodeKey, viewMode = false) {
      console.log('nodeKey', nodeKey)
      this.visible = true
      this.record = record
      this.confirmLoading = true

      try {
        // 调用获取详情数据的接口
        await this.fetchDetailData(record.id)
      } catch (error) {
        console.error('获取详情数据失败:', error)
        this.$message.error('获取详情数据失败')
      } finally {
        this.confirmLoading = false
      }

      // 保存当前是否是纯查看模式（通过handleViewFlowNode方法打开的）
      this.isViewOnlyMode = viewMode

      // 设置表单模式
      this.formMode = viewMode ? 'view' : 'edit'

      // 判断是否是新增操作，如果是新增操作，就直接设置组件为 ReceiveForm
      const isNewRecord = record.status === 'PGZT01' && this.detailData.id.length < 32
      console.log('isNewRecord', isNewRecord)
      if (isNewRecord) {
        this.currentNodeKey = 'receive'
        this.activeNodeKey = 'receive'
        this.currentComponent = 'ReceiveForm'
        return
      }

      // 非新增操作时，由FlowController根据流程数据自动确定节点
      this.currentNodeKey = ''
      this.activeNodeKey = ''
      this.currentComponent = null
    },

    // 获取详情数据
    async fetchDetailData(id) {
      try {
        const approvalDeptId = this.$store.getters.userInfo.loginEmpInfo.orgId
        const params = {
          id: id
        }
        if (this.record.addType === 'new') {
          params.approvalDeptId = approvalDeptId
        }
        console.log('params', params)
        const res = await this.$http.get('/investigationInfo/detail', {
          params: params
        })

        if (res.success) {
          this.detailData = res.data
          this.originalDetailData = JSON.parse(JSON.stringify(res.data)) // 深拷贝保存原始数据
        } else {
          this.$message.error(res.message || '获取详情数据失败')
        }
      } catch (error) {
        console.error('获取详情数据失败:', error)
        throw error
      }
    },

    // 返回当前活跃节点
    returnToActiveNode() {
      if (this.activeNodeKey) {
        this.currentNodeKey = this.activeNodeKey
        // 传递isViewOnlyMode作为forceView参数，确保在查看模式下保持查看状态
        this.setComponentByNodeKey(this.activeNodeKey, this.isViewOnlyMode)

        // 仅在非查看模式下才设置为编辑模式
        if (!this.isViewOnlyMode) {
          this.formMode = 'edit'
        }

        this.$message.info('已返回到当前活跃节点')
      }
    },

    // 根据节点ID设置当前显示的组件
    setComponentByNodeKey(nodeKey, forceView = false) {
      const flowController = this.$refs.flowController
      if (flowController) {
        flowController.selectNode(nodeKey, forceView || this.isViewOnlyMode)
      } else {
        // 如果flowController还未加载，则手动设置组件
        switch (nodeKey) {
          case 'receive':
            this.currentComponent = 'ReceiveForm'
            break
          case 'announcement':
            this.currentComponent = 'AnnouncementForm'
            break
          case 'unitReceive':
            this.currentComponent = 'UnitReceiveForm'
            break
          case 'assessmentList':
            this.currentComponent = 'AssessmentListForm'
            break
          case 'assessmentResult':
            this.currentComponent = 'AssessmentResultForm'
            break
          case 'preliminary':
            this.currentComponent = 'PreliminaryForm'
            break
          case 'collective':
            this.currentComponent = 'CollectiveForm'
            break
          case 'approval':
            this.currentComponent = 'ApprovalForm'
            break
          case 'feedback':
            this.currentComponent = 'FeedbackForm'
            break
          default:
            this.currentComponent = null
        }

        // 如果是强制查看模式或是纯查看模式，则设置表单模式为查看
        if (forceView || this.isViewOnlyMode) {
          this.formMode = 'view'
        }
      }
    },

    // 处理组件变更事件
    onComponentChange(componentName, viewMode = 'edit', currentNode) {
      this.currentComponent = componentName
      this.currentNode = currentNode
      console.log('currentNode', currentNode)

      // 如果是查看模式，则保持查看模式
      if (this.isViewOnlyMode) {
        this.formMode = 'view'
      } else {
        // 否则根据FlowController传递的参数设置表单模式
        this.formMode = viewMode
      }

      // 根据模式和节点设置detailData
      if (currentNode && currentNode.rawData) {
        // 判断当前节点是否是活跃节点（type===9的节点）
        const isActiveNode = currentNode.rawData.type === 9

        if (this.formMode === 'edit' && isActiveNode) {
          // 编辑模式下的活跃节点（type===9）使用原始数据
          if (this.originalDetailData) {
            this.detailData = JSON.parse(JSON.stringify(this.originalDetailData))
          }
        } else {
          const isActiveNode = currentNode.rawData.type === 9
          if (isActiveNode) {
            this.detailData = JSON.parse(JSON.stringify(this.originalDetailData))
          } else {
            // 查看模式下的任何节点或编辑模式下的非活跃节点使用formVal
            if (currentNode.rawData.formVal) {
              const formVal = JSON.parse(JSON.stringify(currentNode.rawData.formVal))
              //修改后的调查评估表信息
              if (formVal.groups.pgForm) {
                this.detailData.pgForm = JSON.parse(formVal.groups.pgForm)
              }

              this.detailData = {
                ...this.detailData,
                ...formVal
              }
              console.log(' this.detailData', this.detailData)
            }
          }
        }
      }
    },

    handleCancel() {
      this.$emit('ok')
      this.visible = false
      this.record = null
      this.currentNodeKey = ''
      this.activeNodeKey = ''
      this.currentComponent = null
      this.completedSteps = []
      this.isViewOnlyMode = false // 重置查看模式状态
    },

    handleOk() {
      // 添加当前步骤到已完成列表
      if (this.currentNodeKey && !this.completedSteps.includes(this.currentNodeKey)) {
        this.completedSteps.push(this.currentNodeKey)
      }

      this.$emit('ok')
    },
    async goToNextStep() {
      // 如果是查看模式，提示用户
      if (this.isViewOnlyMode) {
        this.$message.info('查看模式下不能进行下一步操作')
        return
      }

      // 添加当前活跃步骤到已完成列表
      if (this.activeNodeKey && !this.completedSteps.includes(this.activeNodeKey)) {
        this.completedSteps.push(this.activeNodeKey)
      }

      try {
        // 跳转到下一步前，先重新获取最新的流程数据
        if (this.record && this.record.id) {
          this.confirmLoading = true
          // 获取最新的详情数据
          await this.fetchDetailData(this.record.id)
          // 此时 fetchDetailData 已经更新了 originalDetailData

          this.confirmLoading = false
        }

        // 使用流程控制器前进到下一步
        const flowController = this.$refs.flowController
        if (flowController) {
          // 获取最新的流程节点数据
          await flowController.fetchFlowNodes()
          // 前进到下一步
          const success = flowController.goToNextStep()
          if (success) {
            // 自动跳转到下一步后，设置编辑模式
            this.formMode = 'edit'
            // 更新当前活跃节点和查看节点
            this.activeNodeKey = this.currentNodeKey

            // 编辑模式下获取当前节点时使用fetchDetailData获取的数据
            // 由于currentNode已经在goToNextStep中的selectNode方法中设置
            // 这里不需要额外设置detailData，由onComponentChange处理

            this.$message.info(`已自动跳转到下一步：${this.currentComponent}`)
          }
        }

        // 无论是否跳转到下一步，都通知父组件更新
        this.$emit('ok')
      } catch (error) {
        console.error('获取最新流程数据失败:', error)
        this.$message.error('获取最新流程数据失败，请刷新页面重试')
        this.confirmLoading = false
      }
    },

    // 处理返回上一步操作
    async goToPrevStep(stepCode, url) {
      // 如果是查看模式，提示用户
      if (this.isViewOnlyMode) {
        this.$message.info('查看模式下不能进行上一步操作')
        return
      }

      try {
        // 先获取最新数据，确保切换回活跃节点时有完整数据
        // if (this.record && this.record.id) {
        //   this.confirmLoading = true
        //   // 获取最新的详情数据
        //   await this.fetchDetailData(this.record.id)
        //   this.confirmLoading = false
        // }
        //url  小组公告上一步接口
        this.confirmLoading = true
        this.$http
          .get(
            url
              ? url + '?id=' + this.record.id
              : '/investigationTranscript/previousStep' + '?id=' + this.record.id + '&stepCode=' + stepCode
          )
          .then(res => {
            this.formMode = 'edit'
            this.activeNodeKey = this.currentNodeKey
            // 获取最新的详情数据
            this.fetchDetailData(this.record.id)
            this.confirmLoading = false
          })
        return
        // 使用流程控制器返回上一步
        const flowController = this.$refs.flowController
        if (flowController) {
          // 返回上一步
          const success = flowController.goToPreviousStep()
          if (success) {
            // 设置编辑模式
            this.formMode = 'edit'
            // 更新当前活跃节点和查看节点
            this.activeNodeKey = this.currentNodeKey

            // 由于上一步可能是已完成状态，这里不主动设置detailData
            // 由onComponentChange方法根据节点状态决定数据来源

            this.$message.info(`已返回上一步：${this.currentComponent}`)
          } else {
            this.$message.info('已经是第一步，无法返回上一步')
          }
        }
      } catch (error) {
        console.error('返回上一步失败:', error)
        this.$message.error('返回上一步失败，请刷新页面重试')
      }
    },

    // 设置表单为查看模式
    setViewMode() {
      this.formMode = 'view'
    },

    // 设置表单为编辑模式
    setEditMode() {
      // 如果是通过查看流程功能打开的，则不允许切换到编辑模式
      if (this.isViewOnlyMode) {
        this.$message.info('当前处于查看模式，不能进行编辑操作')
        return
      }

      // 只有当前查看的节点是活跃节点时才允许切换到编辑模式
      if (this.currentNodeKey === this.activeNodeKey) {
        this.formMode = 'edit'
      } else {
        this.$message.info('只有当前活跃节点可以编辑，请返回活跃节点')
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-spin-nested-loading,
/deep/.ant-spin-container {
  width: 100%;
  height: 100%;
}
.flow-main {
  height: calc(100% - 49px);
  overflow: hidden;
}

.drawer-title {
  margin-bottom: 0;
  border-radius: 0;
}
/deep/.ant-drawer-close {
  height: 49px;
  color: rgba(255, 255, 255, 0.75);
  &:hover {
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 1);
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px 0;
}
</style>

<template>
  <div>
    <a-modal
      title="流程终止"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      :width="1000"
      :maskClosable="false"
    >
      <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" v-if="visible">
        <a-form-item label="流程终止原因" required>
          <a-textarea
            v-decorator="['approvalRemark', { rules: [{ required: true, message: '请输入流程终止原因' }] }]"
            placeholder="请输入"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="附件">
          <sh-file-uploader
            v-decorator="['fileIds', { valuePropName: 'fileList' }]"
            :url="'/sysFileInfo/uploadOss'"
            :maxFileSize="50 * 1024 * 1024"
            :acceptedFormats="'.jpg,.png,.gif,.pdf'"
            :multiple="true"
            :listType="'text'"

          />

        </a-form-item>
      </a-form>

      <template slot="footer">
        <a-button key="back" @click="handleCancel">取消</a-button>
        <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleSubmit">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'StopProcessModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      confirmLoading: false,
      fileList: []
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    visible(val) {
      // if (val) {
        // 每次打开重置表单
        this.$nextTick(() => {
          this.form.resetFields()
          this.fileList = []
        })
      // }
    }
  },
  methods: {
    handleCancel() {
      this.form.resetFields()
      this.fileList = []
      this.$emit('cancel')
    },
    handleFileChange(info) {
    //   this.fileList = info.fileList

    //   // 处理文件上传状态
    //   if (info.file.status === 'done') {
    //     this.$message.success(`${info.file.name} 上传成功`);
    //   } else if (info.file.status === 'error') {
    //     this.$message.error(`${info.file.name} 上传失败`);
    //   }
    },
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (err) {
          return
        }

        this.confirmLoading = true

        // 处理文件IDs
        const fileIds = values.fileIds

        // 构建请求参数
        const params = {
          id: this.record.id,
          approvalDeptId: this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgId : (this.userInfo?.orgId || ''),
          approvalDeptName: this.userInfo.loginEmpInfo ? this.userInfo.loginEmpInfo.orgName : (this.userInfo?.orgName || ''),
          approvalRemark: values.approvalRemark,
          returnFiles: fileIds,
          stepCode: this.record.status, // 当前状态码
          stepName: this.record.statusName || this.$options.filters['dictType']('PGZT', this.record.status) // 当前状态名称
        }

        // 发送请求
        this.$http.post('/investigationInfo/stop', params)
          .then(res => {
            this.confirmLoading = false
            if (res.success) {
              this.$message.success('流程终止成功')
              this.$emit('success')
              this.handleCancel()
            } else {
              this.$message.error(res.message || '流程终止失败')
            }
          })
          .catch(error => {
            this.confirmLoading = false
            this.$message.error('流程终止失败：' + (error.message || '未知错误'))
          })
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>

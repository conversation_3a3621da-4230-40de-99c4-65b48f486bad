<template>
  <sh-drawer
    title="详情"
    :visible="visible"
    :footer="null"
    :confirmLoading="confirmLoading"
    @ok="handleCancel"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="社区矫正对象姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['name']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['xbName']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['deptName']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="矫正类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['jzlbName']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="社区矫正开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['sqjzksrq']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="社区矫正结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['sqjzjsrq']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="信访类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['letterTypeName']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="信访渠道" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['letterChannelName']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="是否异地信访" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['offsite']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="信访日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['letterTime']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="信访内容" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
              <a-textarea disabled v-decorator="['letterContext']"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="更新时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['updateTime']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="信访编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['letterNo']"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </sh-drawer>
</template>

<script>
  export default {
    data () {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 6 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 17 } },
        labelCol2: {
                xs: { span: 24 },
                sm: { span: 3 }
            },
            wrapperCol2: {
                xs: { span: 24 },
                sm: { span: 20 }
            },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              name: record.name,
              xbName: record.xbName,
              jzlbName: record.jzlbName,
              sqjzksrq: record.sqjzksrq,
              sqjzjsrq: record.sqjzjsrq,
              letterContext: record.letterContext,
              offsite: record.offsite,
              deptName: record.deptName,
              updateTime: record.updateTime,
              letterTime: record.letterTime,
              letterNo: record.letterNo,
              letterChannelName: record.letterChannelName,
              letterTypeName: record.letterTypeName
            }
          )
        }, 100)
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>

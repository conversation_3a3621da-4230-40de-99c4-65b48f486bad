import Vue from 'vue'
import router from './router'
import store from './store'

import '@/components/NProgress/nprogress.less'; // progress bar custom style
import { ACCESS_TOKEN, ALL_APPS_MENU } from '@/store/mutation-types'
import NProgress from 'nprogress'; // progress bar

import { showOrgSelector } from '@/utils/orgSelector'
import { axios } from '@/utils/request'
import { timeFix } from '@/utils/util'; /// es/notification
import { Modal, notification } from 'ant-design-vue'; // NProgress Configuration
NProgress.configure({ showSpinner: false })
const whiteList = ['login', 'register', 'registerResult'] // no redirect whitelist
// 无默认首页的情况
const defaultRoutePath = '/welcome'

router.beforeEach((to, from, next) => {
  if (to.path === '/welcome') {
    store.dispatch('ToggleLayoutMode', 'topmenu')
    store.dispatch('ToggleFixedHeader', true)
 } else {
   store.dispatch('ToggleLayoutMode', 'sidemenu')
   store.dispatch('ToggleFixedHeader', false)

   // store.dispatch('ToggleContentWidth', 'Fixed')
 }
  NProgress.start() // start progress bar
  if (to.query.authCode) {
    axios({
      url: '/loginAuthCode',
      params: { authCode: to.query.authCode }
    }).then(({ data }) => {
      store.dispatch('dictTypeData')
      Vue.ls.set(ACCESS_TOKEN, data, 7 * 24 * 60 * 60 * 1000)
      next({ path: defaultRoutePath })
      NProgress.done()
      // if (whiteList.includes(to.name)) {
      //   // 在免登录白名单，直接进入
      //   next()
      // } else {
      //   next({ path: '/user/login', query: { redirect: to.fullPath } })
      //   NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      // }
    })
    return
  }
  if (to.query.s_token) {
    axios({
      url: '/loginSso',
      params: { token: to.query.s_token }
    }).then(async ({ data }) => {
      // await showOrgSelector(data.empVoList)
      store.dispatch('dictTypeData')
      Vue.ls.set(ACCESS_TOKEN, data.token, 7 * 24 * 60 * 60 * 1000)
      if (data.empVoList.length > 1) {
        const orgId = await showOrgSelector(data.empVoList)
        console.log(orgId)
      }
      next({ path: defaultRoutePath })
      NProgress.done()
      // if (whiteList.includes(to.name)) {
      //   // 在免登录白名单，直接进入
      //   next()
      // } else {
      //   next({ path: '/user/login', query: { redirect: to.fullPath } })
      //   NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      // }
    })
    return
  }
  document.title = '浙里矫正数据协同系统'
  // to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title}  ${domTitle}`))
  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* has token */
    if (to.path === '/user/login') {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        store
          .dispatch('GetInfo')
          .then(res => {
            if (res.menus.length < 1) {
              Modal.error({
                title: '提示：',
                content: '无菜单权限，请联系管理员',
                okText: '确定',
                onOk: () => {
                  store.dispatch('Logout').then(() => {
                    window.location.reload()
                  })
                }
              })
              return
            }
            // eslint-disable-next-line camelcase
            const all_app_menu = Vue.ls.get(ALL_APPS_MENU)
            let antDesignmenus
            // eslint-disable-next-line camelcase
            if (all_app_menu == null) {
              const applocation = []
              res.apps.forEach(item => {
                const apps = { 'code': '', 'name': '', 'active': '', 'menu': '' }
                if (item.active) {
                  apps.code = item.code
                  apps.name = item.name
                  apps.active = item.active
                  apps.menu = res.menus
                  antDesignmenus = res.menus
                } else {
                  apps.code = item.code
                  apps.name = item.name
                  apps.active = item.active
                  apps.menu = ''
                }
                applocation.push(apps)
              })
              Vue.ls.set(ALL_APPS_MENU, applocation, 7 * 24 * 60 * 60 * 1000)
              // 延迟 1 秒显示欢迎信息
              setTimeout(() => {
                notification.success({
                  message: '欢迎',
                  description: `${timeFix()}，欢迎回来`
                })
              }, 1000)
            } else {
              antDesignmenus = Vue.ls.get(ALL_APPS_MENU)[0].menu
            }
            store.dispatch('GenerateRoutes', { antDesignmenus }).then(() => {
              // 动态添加可访问路由表
              store.getters.addRouters.forEach(item => {
                router.addRoute(item)
              })
              // 请求带有 redirect 重定向时，登录自动重定向到该地址
              const redirect = decodeURIComponent(from.query.redirect || to.path)
              if (to.path === redirect) {
                next({ path: redirect })
                // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
                next({ ...to, replace: true })
              } else {
                // 跳转到目的路由
                next({ path: redirect })
              }
            })
          })
          .catch(() => {
            store.dispatch('Logout').then(() => {
              next({ path: '/user/login', query: { redirect: to.fullPath } })
            })
          })
      } else {
        next()
      }
    }
  } else {
    if (whiteList.includes(to.name)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // next({ path: '/user/login' })
      // NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      if (process.env.NODE_ENV !== 'production') {
        next({ path: '/user/login' })
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      } else {
        window.location = 'https://zlsqjz.zjsft.gov.cn/login'
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})

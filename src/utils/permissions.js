import store from '@/store'

export function actionToObject (json) {
  try {
    return JSON.parse(json)
  } catch (e) {
    console.log('err', e.message)
  }
  return []
}

/**
 * 控制按钮是否显示
 *
 * <AUTHOR>
 * @date 2020/06/27 02:34
 */
export function hasBtnPermission (permission) {
  const myBtns = store.getters.buttons
  const admintype = store.getters.admintype
  // eslint-disable-next-line eqeqeq
  if (admintype == '1') {
     return true
  }
  return myBtns.indexOf(permission) > -1
}
// export function hasRole (permission) {
//   const roles = store.getters.userInfo.roles
//   const admintype = store.getters.admintype
//   // eslint-disable-next-line eqeqeq
//   if (admintype == '1') {
//      return true
//   }
//   return roles.findIndex(item => { return item.code === permission }) > -1
// }

export function hasRole(permission) {
  const roles = store.getters.userInfo.roles
  const admintype = store.getters.admintype

  if (admintype === 1) {
    // 不确定是数字1i
    return true; // Assuming admin always has access
  }

  if (typeof permission === 'string') {
    // If permission is a string, check if it exists in user's roles
    return roles.findIndex(item => item.code === permission) > -1;
  } else if (Array.isArray(permission)) {
    // If permission is an array, check if any of the permissions exist in user's roles
    return permission.some(p => roles.findIndex(item => item.code === p) > -1);
  } else {
    // Handle other cases, like if permission is neither a string nor an array
    return false;
  }
}

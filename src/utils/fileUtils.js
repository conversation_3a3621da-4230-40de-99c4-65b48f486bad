/**
 * 文件处理相关工具函数
 */

/**
 * 下载文件
 * @param {Object} res - 接口响应对象，包含data和headers
 */
export function downloadFile(res) {
  var blob = new Blob([res.data], { type: 'application/octet-stream;charset=UTF-8' })
  var contentDisposition = res.headers['content-disposition']
  var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
  var result = patt.exec(contentDisposition)
  var filename = result[1]
  var downloadElement = document.createElement('a')
  var href = window.URL.createObjectURL(blob) // 创建下载的链接
  var reg = /^["](.*)["]$/g
  downloadElement.style.display = 'none'
  downloadElement.href = href
  downloadElement.download = decodeURI(filename.replace(reg, '$1')) // 下载后文件名
  document.body.appendChild(downloadElement)
  downloadElement.click() // 点击下载
  document.body.removeChild(downloadElement) // 下载完成移除元素
  window.URL.revokeObjectURL(href)
}

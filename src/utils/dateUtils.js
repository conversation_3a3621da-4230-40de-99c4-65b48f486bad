import moment from 'moment'

/**
 * 日期工具函数集合
 */

/**
 * 禁用未来日期
 * 用于日期选择器的disabledDate属性
 * @param {moment} current - 当前日期
 * @returns {boolean} - 当日期大于今天时返回true表示禁用
 */
export function disabledFutureDate(current) {
  // 禁用当天以后的日期
  return current && current > moment().endOf('day')
}

/**
 * 格式化日期
 * @param {date|string|moment} date - 需要格式化的日期
 * @param {string} format - 格式化模式，默认为'YYYY-MM-DD'
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  return moment(date).format(format)
}

/**
 * 格式化日期时间
 * @param {date|string|moment} datetime - 需要格式化的日期时间
 * @param {string} format - 格式化模式，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} - 格式化后的日期时间字符串
 */
export function formatDateTime(datetime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!datetime) return ''
  return moment(datetime).format(format)
}

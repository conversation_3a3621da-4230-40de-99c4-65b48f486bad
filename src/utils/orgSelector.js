import Vue from 'vue'
import ChangeDepartForm from '@/views/system/user/changeDepartForm.vue'

let instance

/**
 * 打开机构选择弹窗
 * @param {Array} orgList 机构列表
 * @returns {Promise} 返回选择的机构信息
 */
export function showOrgSelector(orgList) {
  return new Promise((resolve, reject) => {
    const div = document.createElement('div')
    document.body.appendChild(div)
    const Constructor = Vue.extend(ChangeDepartForm)
    instance = new Constructor({
      propsData: {}
    })

    instance.$on('ok', (data) => {
      resolve(data)
      destroyDialog()
    })

    instance.$on('stepCaptchaCancel', () => {
      reject(new Error('用户取消选择'))
      destroyDialog()
    })

    instance.$mount(div)

    instance.open(orgList)
  })
}

function destroyDialog() {
  if (instance) {
    instance.$destroy()
    if (instance.$el && instance.$el.parentNode) {
      instance.$el.parentNode.removeChild(instance.$el)
    }
    instance = null
  }
}

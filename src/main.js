import 'core-js/stable'
import 'regenerator-runtime/runtime'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/'
import { VueAxios } from './utils/request'
import bootstrap from './core/bootstrap'
import './core/lazy_use'
import './permission'
import './utils/filter'
import './components/global.less'
import { Dialog } from '@/components'
import { hasBtnPermission, hasRole } from './utils/permissions'
import '@jiamt/component-library/dist/jia-component-library.css'
import JiaComponentLibrary from '@jiamt/component-library'

import { sysApplication } from './utils/applocation'
import { Carousel, Progress } from 'ant-design-vue' // 按需引入组件并注册
import '@/components/globalComponents'

import PdfDrawer from '@/components/PdfDrawer/drawer';
import countTo from 'vue-count-to'
import * as echarts from 'echarts'
Vue.use(JiaComponentLibrary)
Vue.prototype.$echarts = echarts
Vue.component('countTo', countTo)
Vue.use(Carousel)
Vue.use(Progress)
Vue.use(VueAxios)
Vue.use(Dialog)
Vue.prototype.hasPerm = hasBtnPermission
Vue.prototype.hasRole = hasRole
Vue.prototype.applocation = sysApplication
Vue.config.productionTip = false
Vue.prototype.$PdfDrawer = PdfDrawer;
new Vue({
  router,
  store,
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')

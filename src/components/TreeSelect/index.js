import { TreeSelect } from 'ant-design-vue'

export default {
  name: 'CustomTreeSelect',
  props: {
    ...TreeSelect.props,
    apiURL: String,
    placeholder: {
      type: String,
      default: '请选择'
    },
    allowClear: {
      default: true,
      type: Boolean
    },
    showSearch: {
      default: true,
      type: <PERSON>olean
    },
    dropdownStyle: {
      default: () => {
        return {
          maxHeight: '400px',
          overflow: 'auto',
          maxWidth: '800px'
        }
      },
      type: Object
    },

    params: {
      default: () => { },
      type: Object
    }

    // 其他 TreeSelect 的原生 props
    // 例如 allowClear, autoClearSearchValue 等
  },
  data() {
    return {
      cValue: '',
      localTreeData: [],
      localTreeDefaultExpandedKeys: [],
      isLoading: false

    }
  },
  created() {
    if (this.value) {
      this.cValue = this.value
    }
    if (this.apiURL) {
      // const cachedData = localStorage.getItem(`${this.apiURL}treeData`)
      // if (cachedData) {
      //     const { data, timestamp } = JSON.parse(cachedData)
      //     if (Date.now() - timestamp < 2 * 60 * 60 * 1000) {
      //         // 如果在有效期内，则使用缓存的数据
      //         this.getTheDefaultExpansion(data)
      //         this.localTreeData = data

      //         return
      //     }
      // }
      // 如果没有缓存或者缓存过期，则发起请求获取最新数据
      this.fetchTreeData()
    } else {
      this.getTheDefaultExpansion(this.treeData)
      this.localTreeData = this.treeData
    }
  },
  methods: {
    filterNode(input, treeNode) {
      // console.log(input, treeNode)
      // 返回 true 表示保留该节点
      return treeNode.data.props.title.toLowerCase().indexOf(input.toLowerCase()) !== -1
    },
    getTheDefaultExpansion(data) {
      // 默认展开2级
      data.forEach((item) => {
        // 因为0的顶级
        if (item.parentId === '0') {
          this.localTreeDefaultExpandedKeys.push(item.id)
          // 取到下级ID
          // if (item.children) {
          //     item.children.forEach((items) => {
          //         this.localTreeDefaultExpandedKeys.push(items.id)
          //     })
          // }
        }
      })
    },
    async fetchTreeData() {
      try {
        this.isLoading = true
        const response = await this.$http({
          url: this.apiURL,
          params: {
            ...this.params
          }
          // data: {
          //   ...this.params
          // }
        })
        localStorage.setItem(`${this.apiURL}treeData`, JSON.stringify({ data: response.data, timestamp: Date.now() }))

        this.getTheDefaultExpansion(response.data)
        this.localTreeData = response.data
      } catch (error) {
        console.error('Error fetching tree data:', error)
      } finally {
        this.isLoading = false
      }
    },
    handleOnchange(value, label, extra) {
      this.$emit('input', value)
      this.$emit('change', value)
      this.$emit('onChange', { value, label, extra })
    }

  },
  watch: {
    value: {
      handler() {
        this.cValue = this.value
      }
    }
  },
  render() {
    let props = {}
    const localKeys = Object.keys(this.$data)
    Object.keys(TreeSelect.props).forEach(k => {
      const localKey = `local${k.substring(0, 1).toUpperCase()}${k.substring(1)}`
      if (localKeys.includes(localKey)) {
        props[k] = this[localKey]
        return props[k]
      }

      this[k] && (props[k] = this[k])
      // 此处配置
      props = {
        ...props,
        value: this.cValue,
        filterTreeNode: this.filterNode,
        loading: this.isLoading

      }
      return props[k]
    })
    // console.log(props, this.$props)
    // on={this.$listeners}
    return (
      <TreeSelect
        style="width: 100%;"
        {...this.$attrs}
        {...{ props }}
        onChange={this.handleOnchange}

      // {...{ ...this.$props, treeData: this.dataSource, loading: this.isLoading }}

      />
    )
  }
}

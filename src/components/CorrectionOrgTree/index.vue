<template>
  <a-tree-select
    v-bind="$attrs"
    v-on="$listeners"
    v-model="orgId"
    show-search
    :filterTreeNode="filterTreeNode"
    style="width: 100%"
    tree-node-filter-prop="title"
    :treeDefaultExpandedKeys="treeDefaultExpandedKeys"
    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
    :treeData="orgTree"
    placeholder="请选择">
  </a-tree-select>
</template>

<script>
import { getOrgTree } from '@/api/modular/system/orgManage'

export default {
    props: {
        params: {
            default: () => { },
            type: Object
        },
        value: String // 定义 value 属性作为输入
    },
    data() {
        return {
            treeDefaultExpandedKeys: [],
            orgTree: []
        }
    },
    computed: {
        orgId: {
            get() {
                return this.value; // 获取 orgId 值
            },
            set(newValue) {
                this.$emit('input', newValue); // 通过 v-model 更新 orgId 值
                this.$emit('change', newValue); // 通过 v-model 更新 orgId 值
            }
        }
    },
    created() {
        this.getOrgTree()
    },
    methods: {
        filterTreeNode(input, option) {
            console.log(input, option)
            // 自定义搜索逻辑，根据 label 包含输入的字符串进行匹配
            return option.data.props.title.toLowerCase().includes(input.toLowerCase());
        },
        getOrgTree() {
            return getOrgTree(this.params).then((res) => {
                if (res.success) {
                    // 默认展开2级
                    res.data.forEach((item) => {
                        // 因为0的顶级
                        if (item.parentId === '0') {
                            this.treeDefaultExpandedKeys.push(item.id)
                            // 取到下级ID
                            // if (item.children) {
                            //   item.children.forEach((items) => {
                            //     this.defaultExpandedKeys.push(items.id)
                            //   })
                            // }
                        }
                    })
                    this.orgTree = res.data
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>

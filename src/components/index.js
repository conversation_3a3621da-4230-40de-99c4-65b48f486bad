// chart
import Bar from '@/components/Charts/Bar'
import ChartCard from '@/components/Charts/ChartCard'
import Liquid from '@/components/Charts/Liquid'
import MiniArea from '@/components/Charts/MiniArea'
import MiniSmoothArea from '@/components/Charts/MiniSmoothArea'
import MiniBar from '@/components/Charts/MiniBar'
import MiniProgress from '@/components/Charts/MiniProgress'
import Radar from '@/components/Charts/Radar'
import RankList from '@/components/Charts/RankList'
import TransferBar from '@/components/Charts/TransferBar'
import TagCloud from '@/components/Charts/TagCloud'

// pro components
import AvatarList from '@/components/AvatarList'
import CountDown from '@/components/CountDown'
import Ellipsis from '@/components/Ellipsis'
import FooterToolbar from '@/components/FooterToolbar'
import NumberInfo from '@/components/NumberInfo'
import DescriptionList from '@/components/DescriptionList'
import Tree from '@/components/Tree/Tree'
import Trend from '@/components/Trend'
import STable from '@/components/Table'
import MultiTab from '@/components/MultiTab'
import Result from '@/components/Result'
import IconSelector from '@/components/IconSelector'
import TagSelect from '@/components/TagSelect'
import ExceptionPage from '@/components/Exception'
import StandardFormRow from '@/components/StandardFormRow'
import ArticleListContent from '@/components/ArticleListContent'
import AntdEditor from '@/components/Editor/WangEditor'
import Dialog from '@/components/Dialog'

// xn components
import XCard from '@/components/xnComponents/XCard'
import XDown from '@/components/xnComponents/XDown'

export {
  AvatarList,
  Bar,
  ChartCard,
  Liquid,
  MiniArea,
  MiniSmoothArea,
  MiniBar,
  MiniProgress,
  Radar,
  TagCloud,
  RankList,
  TransferBar,
  Trend,
  CountDown,
  Ellipsis,
  FooterToolbar,
  NumberInfo,
  DescriptionList,
  // 兼容写法，请勿继续使用
  DescriptionList as DetailList,
  Tree,
  STable,
  MultiTab,
  Result,
  ExceptionPage,
  IconSelector,
  TagSelect,
  StandardFormRow,
  ArticleListContent,
  AntdEditor,
  Dialog,
  XCard,
  XDown
}

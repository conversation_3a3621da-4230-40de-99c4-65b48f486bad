import Vue from 'vue'

import Select from './Select'
import Distpicker from './Distpicker'
import CorrectionOrgTree from './CorrectionOrgTree'// 矫正机构
import FileUpload from './FileUpload'
import ChooseSqjzryRadio from './ChooseSqjzryRadio'// 选择矫正对象
import Cascader from './Cascader'
import TreeSelect from './TreeSelect/index'
const components = {
    'sh-select': Select,
    'sh-distpicker': Distpicker,
    'sh-correction-org-tree': CorrectionOrgTree,
    'sh-file-upload': FileUpload,
    'sh-choose-sqjzry-radio': ChooseSqjzryRadio,
    'sh-cascader-distpicker': Cascader,
   'sh-tree-select': TreeSelect

}
console.log('components-----', components)
    // 將引入的所有公共組建挂載到vue中使用時加組建名
Vue.use({
    install: Vue => {
        Object.keys(components).forEach(key => {
            Vue.component(`${key}`, components[key])
        })
    }
})

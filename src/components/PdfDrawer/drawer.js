import Vue from 'vue'
import PdfDrawer from './index.vue'

const Drawer = Vue.extend(PdfDrawer)
// console.log(new modal({data}))
const instance = new Drawer({}).$mount()
document.body.appendChild(instance.$el)
console.log(PdfDrawer, 'PdfDrawer')
PdfDrawer.viewPdf = function(option) {
  console.log(option, 'viewPdf')
  const fileExtension = option.url.split('.').pop().toLowerCase();
  if (fileExtension !== 'pdf') {
    window.open(option.url, '_blank');
    return false
  }

  Object.assign(instance, option, { type: 'viewPdf' }) // type调用方法来确定类型的，
  instance.visible = true
}
PdfDrawer.info = function(option) {
  console.log(option, 'info')
  Object.assign(instance, option, { type: 'info' })
  instance.visible = true
}
export default PdfDrawer

<template>
  <a-drawer
    :title="title"
    wrapClassName="pdf-wrapper"
    placement="right"
    :closable="true"
    :width="width"
    :visible="visible"
    :after-visible-change="afterVisibleChange"
    @close="onClose">
    <!-- 预览pdf/word文件 -->
    <div v-if="url" style="width: 100%;height: 100%;">
      <embed v-if="url.slice(-4) == 'docx'" style="width:100%;height:100%" :src="url" />
      <embed v-else :src="url" type="application/pdf" style="width:100%;height:100%" />
    </div>

    <a-empty style="margin-top:10%" description="文档地址url不存在" v-else />

  </a-drawer>
</template>

<script>
import DocxPreview from 'docx-preview'
export default {
    components: {
        DocxPreview
    },
    data() {
        return {
            options: {
                width: '100%', // 预览窗口的宽度
                height: '800px' // 预览窗口的高度
            },
            width: window.innerWidth - 200,
            visible: false,
            // http://qp_23_100.bmwae.cn/preview/other?id=407887&url=mqrcode/203355/202311130852559996425.pdf&short=bN7ZYi&domain=f.afbkw.cn&sign=&d=true&durl=mqrcode/203355/1699865571_3639357198_%E8%B5%84%E6%BA%90%E7%94%B3%E8%AF%B7%E8%A1%A8.docx
            url: 'http://qp_23_100.bmwae.cn/preview/pdf?id=407910&url=mqrcode/203355/1699865904_2877960402_%E2%80%9C%E6%B5%99%E9%87%8C%E5%8A%9E%E2%80%9D%E7%BB%9F%E4%B8%80%E5%8D%95%E7%82%B9%E7%99%BB%E5%BD%95%E6%8E%A5%E5%85%A5%E6%8C%87%E5%BC%95%EF%BC%8820230316%EF%BC%89.pdf',
            title: 'pdf预览'
        }
    },

    methods: {
        afterVisibleChange(val) {
            console.log('visible', val);
        },
        showDrawer() {
            this.visible = true;
        },
        onClose() {
            this.visible = false;
            if (this.onCancel) {
                this.onCancel();
            }
        }
    }
}
</script>

<style lang="less" >
.pdf-wrapper {
&.ant-drawer{
    z-index: 9005;
}
    .ant-drawer-wrapper-body {
        overflow: hidden;
        display: flex;
        flex-flow: column;

        .ant-drawer-body {
            flex: 1;
            padding: 0;
            overflow: hidden;
        }
    }
}
</style>

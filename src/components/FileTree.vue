<template>
  <div class="file-tree">
    <div class="search-box">
      <div class="search-wrapper">
        <a-input-search
          v-model="searchText"
          size="large"
          placeholder="搜索文件或目录"
          @change="handleSearch"
          allowClear
        />
      </div>
      <div class="operation-wrapper">
        <slot name="operations"></slot>
      </div>
    </div>
    <div class="tree-wrapper">
      <a-tree
        class="draggable-tree"
        :tree-data="filteredTreeData"
        :draggable="true"
        :expandedKeys="expandedKeys"
        :autoExpandParent="autoExpandParent"
        @expand="onExpand"
        @dragstart="onDragStart"
        @drop="onDrop"
        @rightClick="onRightClick"
        @select="onSelect"
        :selectedKeys="selectedKeys"
        multiple
      >
        <template #title="{ title, isFile, key }">
          <span class="tree-node-content">
            <span class="node-main" :title="title">
              <a-icon class="node-icon" style="margin-left: 6px;" :type="isFile ? 'file-pdf' : 'folder'" />
              <span :class="{'folder-title': !isFile}" :style="{ cursor: isFile ? 'pointer' : 'default' }">{{ title }}</span>
            </span>
            <span v-if="isFile" class="node-actions">
              <template v-if="editable">
                <a-button type="link" size="small" @click.stop="handleRename({ title, key })">
                  <a-icon type="edit" />
                </a-button>
                <a-button type="link" size="small" @click.stop="handleDelete({ key })">
                  <a-icon type="delete" />
                </a-button>
              </template>

              <a-icon v-else-if="selectedKeys.includes(key)" type="check" class="selected-icon" />
            </span>
          </span>
        </template>
      </a-tree>

      <a-menu
        v-if="contextMenu.visible"
        :style="contextMenuStyle"
        class="context-menu"
      >
        <a-menu-item key="rename" @click="() => handleRename(null)">
          <a-icon type="edit" />重命名
        </a-menu-item>
        <a-menu-item key="delete" @click="() => handleDelete(null)">
          <a-icon type="delete" />删除
        </a-menu-item>
      </a-menu>
    </div>
  </div></template>

<script>
import { Modal } from 'ant-design-vue'

export default {
  name: 'FileTree',
  props: {
    treeData: {
      type: Array,
      required: true
    },
    // 选中的文件列表（用于v-model）
    value: {
      type: Array,
      default: () => []
    },
    // 是否允许编辑（重命名和删除）
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      formattedTreeData: [],
      searchText: '',
      expandedKeys: [],
      autoExpandParent: true,
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        node: null
      },
      selectedKeys: []
    }
  },

  computed: {
    contextMenuStyle() {
      return {
        position: 'fixed',
        left: this.contextMenu.x + 'px',
        top: this.contextMenu.y + 'px',
        zIndex: 1000
      }
    },
    filteredTreeData() {
      if (!this.searchText) {
        return this.formattedTreeData
      }
      return this.filterTreeData(this.formattedTreeData, this.searchText.toLowerCase())
    }
  },
  watch: {
    treeData: {
      immediate: true,
      handler(val) {
        this.formattedTreeData = this.formatTreeData(val)
        // 当数据更新时，重新设置展开所有节点
        if (val && val.length > 0) {
          this.expandedKeys = this.getAllKeys(this.formattedTreeData)
        }
      }
    },
    value: {
      immediate: true,
      handler(val) {
        this.selectedKeys = val.map(file => file.id)
      }
    }
  },
  methods: {
    // 获取所有节点的key
    getAllKeys(data) {
      const keys = []
      const getKeys = (items) => {
        items.forEach(item => {
          keys.push(item.key)
          if (item.children) {
            getKeys(item.children)
          }
        })
      }
      getKeys(data)
      return keys
    },
    formatTreeData(data) {
      const formattedData = data.map(item => {
        console.log(item.catalogName)
        return {
        key: item.id,
        title: item.catalogName,
        isFile: item.isFile,
        children: item.children ? this.formatTreeData(item.children) : undefined,
        scopedSlots: {
          title: 'title'
        },
        selectable: item.isFile,
        disableCheckbox: !item.isFile
      }
      })

      return formattedData
    },
    // 查找指定key的节点数据
    findNodeData(key) {
      const loop = (data) => {
        for (const item of data) {
          if (item.key === key) {
            return {
              id: item.key,
              name: item.title,
              isFile: item.isFile,
              children: item.children ? this.restoreTreeData(item.children) : undefined
            }
          }
          if (item.children) {
            const found = loop(item.children)
            if (found) return found
          }
        }
        return null
      }
      return loop(this.formattedTreeData)
    },
    onDragStart({ node }) {
      // 只允许拖动文件，不允许拖动文件夹
      return node.dataRef.isFile
    },
    onDrop(info) {
      // 如果拖动的不是文件，直接返回
      if (!info.dragNode.dataRef.isFile) return

      const dragNode = info.dragNode
      const dropNode = info.node

      // 获取拖动节点和目标节点的层级路径
      const dropPos = dropNode.pos.split('-')

      const dropKey = dropNode.eventKey
      const dragKey = dragNode.eventKey
      const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])

      const loop = (data, key, callback) => {
        data.forEach((item, index, arr) => {
          if (item.key === key) {
            callback(item, index, arr)
            return
          }
          if (item.children) {
            loop(item.children, key, callback)
          }
        })
      }

      const data = [...this.formattedTreeData]
      let dragObj
      loop(data, dragKey, (item, index, arr) => {
        arr.splice(index, 1)
        dragObj = item
      })

      if (!dropNode.dataRef.isFile) {
        // 如果目标是目录，直接放入该目录
        loop(data, dropKey, item => {
          item.children = item.children || []
          // 根据dropPosition决定放在目录的开始还是结尾
          if (dropPosition === -1) {
            item.children.unshift(dragObj)
          } else {
            item.children.push(dragObj)
          }
        })
      } else {
        // 如果目标是文件，放到该文件所在的目录中
        let ar
        let i
        loop(data, dropKey, (item, index, arr) => {
          ar = arr
          i = index
        })
        if (dropPosition === -1) {
          ar.splice(i, 0, dragObj)
        } else {
          ar.splice(i + 1, 0, dragObj)
        }
      }

      // 更新树结构
      this.formattedTreeData = data
      // 发出树结构变更事件
      this.$emit('update:treeData', this.restoreTreeData(data))
    },
    restoreTreeData(data) {
      return data.map(item => ({
        id: item.key,
        name: item.title,
        isFile: item.isFile,
        children: item.children ? this.restoreTreeData(item.children) : undefined
      }))
    },
    onRightClick({ event, node }) {
      console.log(node)
      event.preventDefault()
      this.contextMenu = {
        visible: true,
        x: event.clientX,
        y: event.clientY,
        node: {
          key: node.eventKey,
          title: node.dataRef.title,
          isFile: node.dataRef.isFile
        }
      }
      console.log(this.contextMenu)
    },
    onSelect(selectedKeys, { selected, node }) {
      if (!node.dataRef.isFile) return

      this.selectedKeys = selectedKeys
      // 构建选中的文件列表
      const selectedFiles = selectedKeys.map(key => this.findNodeData(key)).filter(Boolean)
      this.$emit('input', selectedFiles)
    },
    handleRename(node) {
      const targetNode = node || this.contextMenu.node
      console.log(node, targetNode, this.contextMenu)
      if (!targetNode) return

      Modal.confirm({
        title: '重命名',
        content: this.$createElement('a-input', {
          props: {
            defaultValue: targetNode.title
          },
          ref: 'renameInput'
        }),
        onOk: () => {
          const newName = this.$refs.renameInput.stateValue
          if (newName && newName !== targetNode.title) {
            // emit重命名事件到父级处理
            this.$emit('rename', {
              id: targetNode.key,
              newName: newName,
              oldName: targetNode.title
            })
          }
        }
      })
      this.closeContextMenu()
    },
    handleDelete(node) {
      const targetNode = node || this.contextMenu.node
      if (!targetNode) return

      Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件"${targetNode.title}"吗？删除后文书将从档案中去除是否继续执行删除操作`,
        okType: 'danger',
        onOk: () => {
          // emit删除事件到父级处理
          this.$emit('delete', {
            id: targetNode.key,
            name: targetNode.title
          })
        }
      })
      this.closeContextMenu()
    },
    closeContextMenu() {
      this.contextMenu.visible = false
    },
    // 搜索过滤方法
    filterTreeData(data, searchText) {
      const filtered = []

      for (const item of data) {
        if (item.title.toLowerCase().includes(searchText)) {
          // 如果当前节点匹配，直接添加
          filtered.push({ ...item })
          // 添加当前节点的key到展开列表
          if (!this.expandedKeys.includes(item.key)) {
            this.expandedKeys.push(item.key)
          }
        } else if (item.children) {
          // 如果有子节点，递归搜索
          const children = this.filterTreeData(item.children, searchText)
          if (children.length > 0) {
            filtered.push({
              ...item,
              children
            })
            // 如果子节点匹配，展开父节点
            if (!this.expandedKeys.includes(item.key)) {
              this.expandedKeys.push(item.key)
            }
          }
        }
      }

      return filtered
    },

    // 搜索处理方法
    handleSearch() {
      if (!this.searchText) {
        // 清空搜索时，重新展开所有节点
        this.expandedKeys = this.getAllKeys(this.formattedTreeData)
      }
      this.autoExpandParent = true
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    }
  },
  mounted() {
    document.addEventListener('click', this.closeContextMenu)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.closeContextMenu)
  }
}
</script>

<style scoped lang="less">
.file-tree {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
  gap: 12px;
  .tree-wrapper{
    flex: 1;
    overflow: auto;

  }
  .folder-title{
    flex:1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.search-box {
  // margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .search-wrapper {
    width: 100%;
  }

  .operation-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.folder-title {
  font-weight: bold;
  font-size: 16px;
}

.context-menu {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-menu-item {
  height: 32px;
  line-height: 32px;
}
/deep/.ant-tree-treenode-switcher-open,/deep/.ant-tree-treenode-selected,/deep/.ant-tree-treenode-switcher-close{
    display: flex;
    flex-wrap: wrap; /* 允许元素换行 */
    .ant-tree-node-content-wrapper {
        flex:1;
    }
    .ant-tree-child-tree{
        width: 100%;
    }
}

.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 26px;
  line-height: 26px;
  .node-main {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
    gap: 8px;
    font-weight: 400;
    color: #333333;
    span{
      flex:1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .node-icon{
        font-size: 15px;
    }
  }

  .node-actions {
    display: flex;
    align-items: center;

    // 编辑按钮默认隐藏，hover 时显示
    .ant-btn {
      opacity: 0;
      transition: opacity 0.3s;
      padding: 0 4px;
      color: rgba(0, 0, 0, 0.45);

      &:hover {
        color: #1890ff;
      }
    }

    // 选中图标始终显示
    .selected-icon {
      opacity: 1;
    //   color: #52c41a;
      font-size: 14px;
      margin-right: 4px;
    }
  }

  &:hover .node-actions {
    .ant-btn {
      opacity: 1;
    }
  }
}
/deep/.ant-tree li span.ant-tree-switcher, /deep/.ant-tree li span.ant-tree-iconEle{
    height: 30px;
    width: 30px;
}
/deep/.ant-tree li .ant-tree-node-content-wrapper{
    height: 30px;
    line-height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/deep/.anticon{
    vertical-align: -0.35em;
}

/deep/.ant-tree li.ant-tree-treenode-selected .ant-tree-node-content-wrapper{
    color: rgba(22, 144, 255, 1);
    .node-main{
        color: rgba(22, 144, 255, 1);
        .folder-title{
            color: rgba(22, 144, 255, 1);
        }
    }
}
/deep/.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected{
    // background-color: #f1f8ff;//设计稿就这颜色 暂时不用
}

</style>

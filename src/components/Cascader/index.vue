<template>
  <a-cascader
    v-model="cValue"
    :options="options"
    placeholder="请选择"
    v-bind="$attrs"
    @change="handleChange"
    :disabled="disabled"
  />
</template>

<script>
import options from '@/utils/province'

export default {
  props: {
    params: {
      default: () => {},
      type: Object
    },
    disabled: {
      default: false,
      type: Boolean
    },
    value: '' // 定义 value 属性作为输入
  },
  watch: {
    value: {
      handler() {
        if (this.value) {
          this.cValue = this.value.split(',')
        } else {
          this.cValue = []
        }
      }
    }
  },
  mounted() {
    if (this.value) {
      this.cValue = this.value.split(',')
    } else {
      this.cValue = []
    }
  },
  data() {
    return {
      options,
      cValue: []
    }
  },
  computed: {
    // orgId: {
    //   get() {
    //     return this.value // 获取 orgId 值
    //   },
    //   set(newValue) {
    //     this.$emit('input', newValue) // 通过 v-model 更新 orgId 值
    //     this.$emit('change', newValue) // 通过 v-model 更新 orgId 值
    //   }
    // }
  },
  created() {
    // this.getOrgTree()
  },
  methods: {
    handleChange() {
      this.$nextTick(() => {
        this.$emit('input', this.cValue.toString())
        this.$emit('change', this.cValue.toString())
        this.$emit('onChange', this.cValue.toString())
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>

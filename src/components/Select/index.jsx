
import Select from 'ant-design-vue/es/select'

export default {
  components: {
    [Select.name]: Select,
    [Select.Option.name]: Select.Option
  },
  props: {
    ...Select.props,
    filterOption: {
      default: (input, option) => {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      type: Function
    },
    showSearch: {
      default: true,
      type: Boolean
    },
    whetherToAddAll: {
      default: false,
      type: Boolean
    },
    dictType: {
      default: '',
      type: String
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    api: {
      type: String,
      default: null
    },
    options: {
      type: Array, // 手动传入的下拉选项数组
      default: () => []
    },
    valueKey: {
      type: String,
      default: 'code'
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    allowClear: {
      default: true,
      type: <PERSON><PERSON>an
    }
  },
  data() {
    return {
      selectedValue: this.value || undefined,
      fetchedOptions: [] // 存储从 API 获取的选项数据
    }
  },
  watch: {
    value(newValue) {
      this.selectedValue = newValue
    },
    selectedValue(newValue) {
      this.$emit('input', newValue)
      // this.$emit('change', newValue)
    }
  },
  created() {
    // 如果存在 API URL，优先使用 API 请求获取数据
    if (this.api) {
      this.fetchOptionsFromApi(this.api)
    } else if (this.dictType && this.$options.filters && this.$options.filters['dictData']) {
      // 如果没有 API，但存在 dictData 过滤器，则使用动态传入的值获取 dictData 数据
      const dictDataOptions = this.$options.filters['dictData'](this.dictType)
      if (dictDataOptions && dictDataOptions.length > 0) {
        this.fetchedOptions = dictDataOptions
      }
    } else {
      this.fetchedOptions = this.options
    }
    if (this.whetherToAddAll && !this.value) {
      this.selectedValue = ''
    }
    // console.log('',this.$options.filters['dictData']('yes_or_no'))
  },
  methods: {
    async fetchOptionsFromApi(apiUrl) {
      try {
        const response = await this.$http({ url: apiUrl })
        this.fetchedOptions = response.data // 将 API 响应数据存储在 fetchedOptions
      } catch (error) {
        console.error('Error fetching options from API:', error)
        this.fetchedOptions = [] // 处理错误时清空 fetchedOptions
      }
    },
    filterOptions(input, option) {
      console.log(option)
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    handleSelectChange(value) {
      this.selectedValue = value
      this.$emit('change', value)
      this.$emit('onChange', value)
    },
    getOptionKey(option) {
      return option[this.valueKey]
    },
    getOptionValue(option) {
      return option[this.valueKey]
    },
    getOptionLabel(option) {
      return option[this.labelKey]
    }
  },
  render() {
    // 使用 fetchedOptions（从 API 获取的数据）或 options（手动传入的数据）
    // let optionsToRender = this.api ? this.fetchedOptions : this.options
    let optionsToRender = this.fetchedOptions
    if (this.whetherToAddAll) {
      optionsToRender = [{ code: '', name: '全部' }, ...optionsToRender]
    }
    const props = {}
    Object.keys(Select.props).map(key => {
      if (key === 'value' || key === 'options') {
        // 避免重复变量
        return
      }
      if (this[key]) {
        props[key] = this[key]
      }
    })
    props.filterOption = this.filterOptions
    // filterOption: this.filterOption,
    // filterOption: this.filterOptions,
    return (
      <a-select
        {...this.$attrs}
        {...{ props, scopedSlots: { ...this.$scopedSlots } }}
        v-model={this.selectedValue}
        onChange={this.handleSelectChange}
      >
        {optionsToRender.map((option) => (
          <a-select-option
            key={this.getOptionKey(option)}

            value={this.getOptionValue(option)}
            label={this.getOptionLabel(option)}
          >
            {this.getOptionLabel(option)}
          </a-select-option>
        ))}
      </a-select>
    )
  }
}

# 下拉选择 下拉选择

可以传入dictType 和请求api 和直接传入数组



引用方式：

```javascript
import Select from '@/components/Select'

export default {
    components: {
       'ShSelect': Select
    }
}
```



## 代码演示  [demo](1)

```html
    <sh-select
                  dictType="sex"
                  style="width: 100%;"
                  placeholder="请选择"
                  v-decorator="['xb', { rules: [{ required: true, message: '请输入' }] }]" />
```

## 代码演示  [demo](2)

```html
    <sh-select
                  api="/api/orgTree"
                  style="width: 100%;"
                  placeholder="请选择"
                  labelKey="label"
                  valueKey="code"
                  v-decorator="['xb', { rules: [{ required: true, message: '请输入' }] }]" />
```
## 代码演示  [demo](2)

```html
    <sh-select
                
                  style="width: 100%;"
                  :options="[{label:1,value:1}]"
                  placeholder="请选择"
                  labelKey="label"
                  valueKey="value"
                  v-decorator="['xb', { rules: [{ required: true, message: '请输入' }] }]" />
```
## API


参数 | 说明 | 类型 | 默认值
----|------|-----|------
labelKey | 返回数据列表对应的名字属性key 默认取name
valueKey |  返回数据列表对应的value属性key 默认取code
options | 可以直接传入一个数组 [{name:1,value:1}]
dictType | 可以传入字典编码
api | 可以传入url
whetherToAddAll | 最前面添加个全部 布尔
如果传入多个 api第一优先 第二为 dictType 第三为 options


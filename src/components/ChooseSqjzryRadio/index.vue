<template>
  <div>
    <a-input :disabled="disabled" @click="handleFocus" placeholder="选择" :value="corrName" />

    <a-modal
      title="选择矫正对象"
      :width="950"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :destroyOnClose="true">

      <div>
        <a-card :bordered="false" :bodyStyle="tstyle">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col :md="8" :sm="24">
                  <a-form-item label="矫正单位">
                    <a-tree-select
                      v-model="queryParam.jzjg"
                      style="width: 100%"
                      :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
                      :treeData="orgTree"
                      placeholder="请选择矫正单位">
                      <span slot="title" slot-scope="{ id }">{{ id }}</span>
                      <a-input v-show="false" v-model="queryParam.jzjg" />
                    </a-tree-select>
                  </a-form-item>
                </a-col>

                <a-col :md="8" :sm="24">
                  <a-form-item label="姓名">
                    <a-input v-model="queryParam.xm" allow-clear placeholder="请输入姓名" />
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="24">
                  <span class="table-page-search-submitButtons">
                    <a-button type="primary" @click="$refs.table2.refresh(true)">查询</a-button>
                    <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <a-card :bordered="false">
          <s-table
            ref="table2"
            :columns="columns"
            :data="loadData"
            :alert="true"
            :rowKey="(record) => record.id"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }">
          </s-table>
          <!-- <view-main ref="viewMain" @ok="handleOk" /> -->
        </a-card>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { STable } from '@/components'
import moment from 'moment'
import { correctionObjectInformationPage } from '@/api/modular/main/correctionobjectinformation/correctionObjectInformationManage'
import { getOrgTree } from '@/api/modular/system/orgManage'

export default {
  props: {
    disabled: {
      default: false,
      type: Boolean
    },
    value: {
      default: {},
      type: String || Object
    }
  },
  // inject: ['getSqjzry'],
  components: { STable },
  data() {
    return {
      corrName: '',
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      confirmLoading: false,
      loading: false,
      orgTree: [],
      jzlbDictTypeDropDown: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          align: 'center',
          width: 10,
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '矫正单位',
          align: 'center',
          width: 125,
          dataIndex: 'jzjgName'
        },
        {
          title: '姓名',
          align: 'center',
          width: 65,
          dataIndex: 'xm'
        },
        {
          title: '身份证号',
          align: 'center',
          width: 90,
          dataIndex: 'sfzh'
        },
        {
          title: '矫正类别',
          align: 'center',
          width: 75,
          dataIndex: 'jzlbName'
        },
        {
          title: '入矫时间',
          align: 'center',
          dataIndex: 'rujiaoriqi',
          width: 85,
          customRender: (val, record) => {
            if (val != null && val !== '') {
              return moment(val).format('YYYY-MM-DD')
            }
          }
        }
      ],
      tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        return correctionObjectInformationPage(Object.assign(parameter, JSON.parse(JSON.stringify(this.queryParam)))).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      visible: false,
      tag: ''
    }
  },
  created() {
    this.getOrgTree()
  },
  computed: {
    hasSelected() {
      return this.selectedRowKeys.length > 0
    }
  },
  watch: {
    value: {
      handler(nval) {
        if (typeof nval === 'string') {
          console.log('myValue 是一个字符串。');
          this.corrName = nval
          this.selectedRows = [{
            xm: nval
          }]
        }
        if (typeof nval === 'object' && nval !== null) {
          this.selectedRows = nval
          this.corrName = nval.xm
        }
      }
    }
  },
  methods: {
    moment,
    handleFocus() {
      this.visible = true
    },
    getOrgTree() {
      return getOrgTree().then((res) => {
        if (res.success) {
          this.orgTree = res.data
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    initrOrgName(value) {
      this.form.getFieldDecorator('queryParam.jzjgName', { initialValue: this.jzjgNameList.find(item => value === item.id).name })
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleOk() {
      this.$refs.table2.refresh()
    },
    choose(tag) {
      this.visible = true
      this.tag = tag
    },
    handleCancel() {
      this.selectedRowKeys = []
      this.$refs.table2.refresh()
      this.visible = false
    },
    handleSubmit() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warn('请选择一个矫正对象')
        return
      }
      this.$emit('input', this.selectedRows[0])
      this.$emit('change', this.selectedRows[0])
      this.$emit('onChange', this.selectedRows[0])
      console.log(this.selectedRows[0])
      this.corrName = this.selectedRows[0].xm
      this.visible = false
    }
  }
}
</script>
<style lang="less">
.table-operator {
  margin-bottom: 18px;
}

button {
  margin-right: 8px;
}
</style>

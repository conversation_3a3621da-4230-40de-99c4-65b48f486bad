<template>
  <div class="dict-wrapper">
    <a-select
      class="dict-item"
      v-model="selectedProvince"
      @change="handleProvinceChange"
      placeholder="请选择省份"
      :disabled="readonly">
      <a-select-option v-for="(provinceName, provinceCode) in provinces" :key="provinceCode" :value="provinceCode">
        {{ provinceName }}
      </a-select-option>
    </a-select>

    <a-select
      class="dict-item"
      v-model="selectedCity"
      @change="handleCityChange"
      placeholder="请选择城市"
      :disabled="!selectedProvince || readonly">
      <a-select-option v-for="(cityName, cityCode) in cities[selectedProvince]" :key="cityCode" :value="cityCode">
        {{ cityName }}
      </a-select-option>
    </a-select>

    <a-select
      class="dict-item"
      v-model="selectedDistrict"
      placeholder="请选择区县"
      @change="handleDistrictChange"
      :disabled="!selectedCity || readonly">
      <a-select-option
        v-for="(districtName, districtCode) in districts[selectedCity]"
        :key="districtCode"
        :value="districtCode">
        {{ districtName }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import DEFAULT_PROVINCE from './province'
import DEFAULT_CITY from './city'
import DEFAULT_AREA from './area'
export default {
    props: {
        value: Object,
        readonly: Boolean // 是否只读
    },
    data() {
        return {
            selectedProvince: undefined,
            selectedCity: undefined,
            selectedDistrict: undefined,
            provinces: DEFAULT_PROVINCE,
            cities: DEFAULT_CITY,
            districts: DEFAULT_AREA
        };
    },
    watch: {
        value: {
            handler(newValue) {
                this.selectedProvince = newValue.selectedProvince || undefined;
                this.selectedCity = newValue.selectedCity || undefined;
                this.selectedDistrict = newValue.selectedDistrict || undefined;
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        handleProvinceChange(value) {
            this.selectedCity = undefined;
            this.selectedDistrict = undefined;
            this.$emit('input', {
                selectedProvince: value,
                selectedCity: undefined,
                selectedDistrict: undefined
            });
            this.$emit('change', {
                selectedProvince: value,
                selectedCity: undefined,
                selectedDistrict: undefined
            });
        },
        handleCityChange(value) {
            this.selectedDistrict = undefined;
            this.$emit('input', {
                selectedProvince: this.selectedProvince,
                selectedCity: value,
                selectedDistrict: undefined
            });
            this.$emit('change', {
                selectedProvince: this.selectedProvince,
                selectedCity: value,
                selectedDistrict: undefined
            });
        },
        handleDistrictChange(value) {
            this.$emit('input', {
                selectedProvince: this.selectedProvince,
                selectedCity: this.selectedCity,
                selectedDistrict: value
            });
            this.$emit('change', {
                selectedProvince: this.selectedProvince,
                selectedCity: this.selectedCity,
                selectedDistrict: value
            });
        }

    }
};
</script>
<style lang="less" scoped>
.dict-wrapper {
    display: flex;
    align-items: center;
    height: 40px;
    justify-content: space-between;
    /* 在子元素之间创建20px间距 */
}

.dict-item {
    flex: 1;
    /* 让子元素自适应宽度 */
    margin-right: 20px;
    /* 添加20px的右边距，除了最后一个子元素 */
}

.dict-item:last-child {
    margin-right: 0;
    /* 最后一个子元素不设置右边距 */
}
</style>

<template>
  <a-upload
    v-bind="$attrs"
    :action="uploadUrl"
    :multiple="multiple"
    :disabled="disabled"
    :file-list="fileList"
    :headers="headers"
    :accept="accept"
    :beforeUpload="beforeUpload"
    @change="handleChange"
  >
    <!-- <div> -->
    <a-button v-if="!disabled"> <a-icon type="upload" /> {{ btnText }} </a-button>
    <!-- </div> -->
  </a-upload>
</template>

  <script>
import Vue from 'vue'
import { Upload } from 'ant-design-vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
export default {
  props: {
    value: Array, // 使用v-model绑定的值
    accept: {
      default: ''
    },
    uploadUrl: String,
    disabled: {
      default: false,
      type: Boolean
    },

    btnText: {
      default: '上传文件',
      type: String
    },
    multiple: Boolean,
    maxSizeMB: Number,
    maxFileCount: {
      default: 5,
      type: Number
    }
  },
  data() {
    return {
      fileList: this.value || [],
      headers: {}
    }
  },
  watch: {
    value(newValue) {
      this.fileList = newValue || []
    }
  },
  mounted() {
    this.headers.Authorization = 'Bearer ' + Vue.ls.get(ACCESS_TOKEN)
  },
  methods: {
    beforeUpload(file) {
      if (file.type === 'application/pdf') {
        return new Promise((resolve, reject) => {
          // 检查文件类型
          //   if (file.type !== 'application/pdf') {
          //     this.$message.error('文件格式必须为 PDF!')
          //     return reject(false) // 阻止上传
          //   }

          // 使用 FileReader 读取文件内容
          const reader = new FileReader()
          reader.onload = (e) => {
            const uint8Array = new Uint8Array(e.target.result)
            const header = uint8Array.subarray(0, 4)
            const isPdfContent = header[0] === 0x25 && header[1] === 0x50 && header[2] === 0x44 && header[3] === 0x46

            if (isPdfContent) {
              resolve(true) // 允许上传
            } else {
              this.$message.error('文件内容不是有效的 PDF 格式!')
              reject(new Error('文件内容不是有效的 PDF 格式!')) // 阻止上传并给出错误信息
            }
          }

          reader.onerror = () => {
            this.$message.error('读取文件失败')
            reject(new Error('读取文件失败')) // 阻止上传并给出错误信息
            // reject(false) // 阻止上传
          }

          reader.readAsArrayBuffer(file) // 读取文件内容
        })
      }
      const { type } = file
      console.log(this.accept, type, type.split('/')[1])
      if (this.accept) {
        if (this.accept.includes(type.split('/')[1])) {
          return Promise.resolve()
        } else {
          this.$message.error(`上传文件类型错误当前支持${this.accept}格式`)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject()
        }
      } else {
        return Promise.resolve()
      }
      // 在此处可以添加文件上传前的自定义逻辑，例如文件类型和大小的验证
      // if (this.maxSizeMB && file.size / 1024 / 1024 > this.maxSizeMB) {
      //   this.$message.error('文件大小超出限制')
      //   return false
      // }

      // if (this.maxCount && this.fileList.length >= this.maxCount) {
      //   // 如果上传文件数量已经达到最大限制，删除文件列表中的第一个文件再加入新的文件
      //   this.fileList.shift()
      // }

      // return true
    },
    handleChange(info) {
      // console.log(info)
      let fileList = [...info.fileList]

      // 1. 限制上传文件的数量
      //    只显示最近上传的两个文件，旧文件将被新文件替换
      fileList = fileList.slice(-this.maxFileCount)

      // 2. 从响应中读取文件链接并显示
      fileList = fileList.map((file) => {
        console.log(file)
        if (file.response && file.response.success) {
          // Component will show file.url as link
          const resData = file.response.data
          file.url = resData.filePath
          file.name = resData.fileOriginName
          file.uid = resData.id
          file.id = resData.id
        }
        return file
      })

      this.fileList = fileList

      // 触发v-model的更新
      this.$emit('input', fileList)
      this.$emit('change', fileList)
      this.$emit('onChange', fileList)
    }
  }
}
</script>

<template>
  <transition name="showHeader">
    <div v-if="visible" class="header-animat">
      <a-layout-header
        v-if="visible"
        :class="[fixedHeader && 'ant-header-fixedHeader ', $route.path === '/welcome' ? 'cus-wrap' : '', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed',]"
        :style="{ padding: '0', height: '55px' }">
        <div v-if="mode === 'sidemenu'" class="header">

          <a-menu style="height: 55px; border-bottom: 0px;" mode="horizontal" :default-selected-keys="this.defApp">
            <a-icon
              v-if="device === 'mobile'"
              class="trigger"
              :type="collapsed ? 'menu-fold' : 'menu-unfold'"
              @click="toggle" />
            <a-icon
              v-else
              class="trigger"
              :type="collapsed ? 'menu-unfold' : 'menu-fold'"
              @click="toggle"
              style="padding-left: 20px; padding-right: 20px;" />

            <a-menu-item
              v-for="(item) in userInfo.apps"
              :key="item.code"
              style="top:0px; line-height: 55px; padding-left: 10px; padding-right: 10px"
              @click="switchApp(item.code)">
              {{ item.name }}
            </a-menu-item>
            <user-menu></user-menu>
          </a-menu>

        </div>
        <div v-else :class="['top-nav-header-index', theme]">

          <div class="header-index-wide">
            <div class="header-index-left">
              <logo class="top-nav-header" :show-title="device !== 'mobile'" />
              <s-menu v-if="device !== 'mobile'" mode="horizontal" :menu="menus" :theme="theme" />
              <a-icon v-else class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click="toggle" />
            </div>
            <user-menu class="header-index-right"></user-menu>
          </div>
        </div>
      </a-layout-header>
    </div>
  </transition>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../Menu/'
import Logo from '../tools/Logo'
import { mixin } from '@/utils/mixin'
import { mapActions, mapGetters } from 'vuex'
import { ALL_APPS_MENU } from '@/store/mutation-types'
import Vue from 'vue'
import { message } from 'ant-design-vue/es'

export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.defApp.push(Vue.ls.get(ALL_APPS_MENU)[0].code)
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data() {
    return {
      visible: true,
      oldScrollTop: 0,
      defApp: []
    }
  },
  mounted() {
    document.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  methods: {
    ...mapActions(['MenuChange']),

    /**
     * 应用切换
     */
    switchApp(appCode) {
      this.defApp = []
      const applicationData = this.userInfo.apps.filter(item => item.code === appCode)
      const hideMessage = message.loading('正在切换应用！', 0)
      this.MenuChange(applicationData[0]).then((res) => {
        hideMessage()
        // eslint-disable-next-line handle-callback-err
      }).catch((err) => {
        message.error('应用切换异常')
      })
    },
    handleScroll() {
      if (!this.autoHideHeader) {
        return
      }

      const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
      if (!this.ticking) {
        this.ticking = true
        requestAnimationFrame(() => {
          if (this.oldScrollTop > scrollTop) {
            this.visible = true
          } else if (scrollTop > 300 && this.visible) {
            this.visible = false
          } else if (scrollTop < 300 && !this.visible) {
            this.visible = true
          }
          this.oldScrollTop = scrollTop
          this.ticking = false
        })
      }
    },
    toggle() {
      this.$emit('toggle')
    }
  },
  beforeDestroy() {
    document.body.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>
<style lang="less" scoped>
.cus-wrap {

  /deep/.ant-menu-horizontal>.ant-menu-item:hover,
  /deep/.ant-menu-horizontal>.ant-menu-submenu:hover,
  /deep/.ant-menu-horizontal>.ant-menu-item-active,
  /deep/.ant-menu-horizontal>.ant-menu-submenu-active,
  /deep/.ant-menu-horizontal>.ant-menu-item-open,
  /deep/.ant-menu-horizontal>.ant-menu-submenu-open,
  /deep/.ant-menu-horizontal>.ant-menu-item-selected,
  /deep/.ant-menu-horizontal>.ant-menu-submenu-selected {
    color: #fff !important;
  }

  /deep/.ant-menu-item:hover,
  /deep/.ant-menu-item-active,
  /deep/.ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  /deep/.ant-menu-submenu-active,
  /deep/.ant-menu-submenu-title:hover {
    color: #fff !important;
  }

  /deep/.ant-menu-submenu-horizontal {
    color: #ecebeb;

    &:hover {
      // color: red;
    }
  }

  /deep/.ant-menu-submenu-active {
    // color: red !important;
  }
  /deep/.header-index-left .logo h1{
    color: #fff !important;
    font-size: 24px !important;
font-family: Microsoft YaHei, Microsoft YaHei !important;
font-weight: bold !important;
  }

  .top-nav-header-index .header-index-wide{
    width: 100% !important;
    max-width: 100% !important;
  }
  .header-index-wide .header-index-left{
    overflow: hidden;
    flex:1 !important;
    padding-left: 26px;
  }
  .header-index-wide .ant-menu.ant-menu-horizontal{
    flex:1;
    overflow: hidden;
  }
  .header-index-wide .ant-menu.ant-menu-horizontal{
    flex:1 !important;
    max-width: inherit !important;
  }
  .header-index-wide .header-index-right{
    padding-right: 20px;
    color: #fff;
  }
  /deep/.user-wrapper .action{
    color: #fff !important;
  }
}

.layout.ant-layout {
  .cus-wrap {
    background: none;

    .top-nav-header-index.light {
      background: url('~@/assets/header-bg.png') no-repeat center;
      background-size: 100% 100%;
    }

    .ant-menu {
      background: transparent;
    }

    .ant-menu-horizontal::after {
      display: none;
    }

  }
}
</style>

<style lang="less">
@import '../index.less';

.header-animat {
  position: relative;
  z-index: @ant-global-header-zindex;
}

.showHeader-enter-active {
  transition: all 0.25s ease;
}

.showHeader-leave-active {
  transition: all 0.5s ease;
}

.showHeader-enter,
.showHeader-leave-to {
  opacity: 0;
}
</style>

var e={192:function(e,t,n){var r;e.exports=(r=n(751),n(20),n(716),n(147),n(575),function(){var e=r,t=e.lib.BlockCipher,n=e.algo,i=[],o=[],a=[],s=[],c=[],u=[],l=[],f=[],h=[],d=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,i[n]=p,o[p]=n;var v=e[n],y=e[v],g=e[y],m=257*e[p]^16843008*p;a[n]=m<<24|m>>>8,s[n]=m<<16|m>>>16,c[n]=m<<8|m>>>24,u[n]=m,m=16843009*g^65537*y^257*v^16843008*n,l[p]=m<<24|m>>>8,f[p]=m<<16|m>>>16,h[p]=m<<8|m>>>24,d[p]=m,n?(n=v^e[e[e[g^v]]],r^=e[e[r]]):n=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],v=n.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*((this._nRounds=n+6)+1),o=this._keySchedule=[],a=0;a<r;a++)a<n?o[a]=t[a]:(u=o[a-1],a%n?n>6&&a%n==4&&(u=i[u>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u]):(u=i[(u=u<<8|u>>>24)>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u],u^=p[a/n|0]<<24),o[a]=o[a-n]^u);for(var s=this._invKeySchedule=[],c=0;c<r;c++){if(a=r-c,c%4)var u=o[a];else u=o[a-4];s[c]=c<4||a<=4?u:l[i[u>>>24]]^f[i[u>>>16&255]]^h[i[u>>>8&255]]^d[i[255&u]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,c,u,i)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,l,f,h,d,o),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,i,o,a,s){for(var c=this._nRounds,u=e[t]^n[0],l=e[t+1]^n[1],f=e[t+2]^n[2],h=e[t+3]^n[3],d=4,p=1;p<c;p++){var v=r[u>>>24]^i[l>>>16&255]^o[f>>>8&255]^a[255&h]^n[d++],y=r[l>>>24]^i[f>>>16&255]^o[h>>>8&255]^a[255&u]^n[d++],g=r[f>>>24]^i[h>>>16&255]^o[u>>>8&255]^a[255&l]^n[d++],m=r[h>>>24]^i[u>>>16&255]^o[l>>>8&255]^a[255&f]^n[d++];u=v,l=y,f=g,h=m}v=(s[u>>>24]<<24|s[l>>>16&255]<<16|s[f>>>8&255]<<8|s[255&h])^n[d++],y=(s[l>>>24]<<24|s[f>>>16&255]<<16|s[h>>>8&255]<<8|s[255&u])^n[d++],g=(s[f>>>24]<<24|s[h>>>16&255]<<16|s[u>>>8&255]<<8|s[255&l])^n[d++],m=(s[h>>>24]<<24|s[u>>>16&255]<<16|s[l>>>8&255]<<8|s[255&f])^n[d++],e[t]=v,e[t+1]=y,e[t+2]=g,e[t+3]=m},keySize:8});e.AES=t._createHelper(v)}(),r.AES)},575:function(e,t,n){var r;e.exports=(r=n(751),n(147),void(r.lib.Cipher||function(e){var t=r,n=t.lib,i=n.Base,o=n.WordArray,a=n.BufferedBlockAlgorithm,s=t.enc,c=(s.Utf8,s.Base64),u=t.algo.EvpKDF,l=n.Cipher=a.extend({cfg:i.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){a.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?w:g}return function(t){return{encrypt:function(n,r,i){return e(r).encrypt(t,n,r,i)},decrypt:function(n,r,i){return e(r).decrypt(t,n,r,i)}}}}()}),f=(n.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),t.mode={}),h=n.BlockCipherMode=i.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),d=f.CBC=function(){var t=h.extend();function n(t,n,r){var i,o=this._iv;o?(i=o,this._iv=e):i=this._prevBlock;for(var a=0;a<r;a++)t[n+a]^=i[a]}return t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;n.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,o=e.slice(t,t+i);r.decryptBlock(e,t),n.call(this,e,t,i),this._prevBlock=o}}),t}(),p=(t.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,i=r<<24|r<<16|r<<8|r,a=[],s=0;s<r;s+=4)a.push(i);var c=o.create(a,r);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},v=(n.BlockCipher=l.extend({cfg:l.cfg.extend({mode:d,padding:p}),reset:function(){var e;l.reset.call(this);var t=this.cfg,n=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(r,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),n.CipherParams=i.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),y=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?o.create([1398893684,1701076831]).concat(n).concat(t):t).toString(c)},parse:function(e){var t,n=c.parse(e),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=o.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),v.create({ciphertext:n,salt:t})}},g=n.SerializableCipher=i.extend({cfg:i.extend({format:y}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var i=e.createEncryptor(n,r),o=i.finalize(t),a=i.cfg;return v.create({ciphertext:o,key:n,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),m=(t.kdf={}).OpenSSL={execute:function(e,t,n,r){r||(r=o.random(8));var i=u.create({keySize:t+n}).compute(e,r),a=o.create(i.words.slice(t),4*n);return i.sigBytes=4*t,v.create({key:i,iv:a,salt:r})}},w=n.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:m}),encrypt:function(e,t,n,r){var i=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize);r.iv=i.iv;var o=g.encrypt.call(this,e,t,i.key,r);return o.mixIn(i),o},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var i=r.kdf.execute(n,e.keySize,e.ivSize,t.salt);return r.iv=i.iv,g.decrypt.call(this,e,t,i.key,r)}})}()))},751:function(e,t,n){var r;e.exports=(r=r||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==n.g&&n.g.crypto&&(r=n.g.crypto),!r)try{r=n(480)}catch(e){}var i=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),a={},s=a.lib={},c=s.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=s.WordArray=c.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||f).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<i;o++){var a=n[o>>>2]>>>24-o%4*8&255;t[r+o>>>2]|=a<<24-(r+o)%4*8}else for(var s=0;s<i;s+=4)t[r+s>>>2]=n[s>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(i());return new u.init(t,e)}}),l=a.enc={},f=l.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new u.init(n,t/2)}},h=l.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new u.init(n,t)}},d=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},p=s.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,i=r.words,o=r.sigBytes,a=this.blockSize,s=o/(4*a),c=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,l=e.min(4*c,o);if(c){for(var f=0;f<c;f+=a)this._doProcessBlock(i,f);n=i.splice(0,c),r.sigBytes-=l}return new u.init(n,l)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),v=(s.Hasher=p.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new v.HMAC.init(e,n).finalize(t)}}}),a.algo={});return a}(Math),r)},20:function(e,t,n){var r;e.exports=(r=n(751),function(){var e=r,t=e.lib.WordArray;function n(e,n,r){for(var i=[],o=0,a=0;a<n;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2|r[e.charCodeAt(a)]>>>6-a%4*2;i[o>>>2]|=s<<24-o%4*8,o++}return t.create(i,o)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var i=[],o=0;o<n;o+=3)for(var a=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;s<4&&o+.75*s<n;s++)i.push(r.charAt(a>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,r=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<r.length;o++)i[r.charCodeAt(o)]=o}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return n(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),r.enc.Base64)},320:function(e,t,n){var r;e.exports=(r=n(751),function(){var e=r,t=e.lib.WordArray;function n(e,n,r){for(var i=[],o=0,a=0;a<n;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2|r[e.charCodeAt(a)]>>>6-a%4*2;i[o>>>2]|=s<<24-o%4*8,o++}return t.create(i,o)}e.enc.Base64url={stringify:function(e,t=!0){var n=e.words,r=e.sigBytes,i=t?this._safe_map:this._map;e.clamp();for(var o=[],a=0;a<r;a+=3)for(var s=(n[a>>>2]>>>24-a%4*8&255)<<16|(n[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|n[a+2>>>2]>>>24-(a+2)%4*8&255,c=0;c<4&&a+.75*c<r;c++)o.push(i.charAt(s>>>6*(3-c)&63));var u=i.charAt(64);if(u)for(;o.length%4;)o.push(u);return o.join("")},parse:function(e,t=!0){var r=e.length,i=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var a=0;a<i.length;a++)o[i.charCodeAt(a)]=a}var s=i.charAt(64);if(s){var c=e.indexOf(s);-1!==c&&(r=c)}return n(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),r.enc.Base64url)},814:function(e,t,n){var r;e.exports=(r=n(751),function(){var e=r,t=e.lib.WordArray,n=e.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i+=2){var o=t[i>>>2]>>>16-i%4*8&65535;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var n=e.length,r=[],i=0;i<n;i++)r[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return t.create(r,2*n)}},n.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o+=2){var a=i(t[o>>>2]>>>16-o%4*8&65535);r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var n=e.length,r=[],o=0;o<n;o++)r[o>>>1]|=i(e.charCodeAt(o)<<16-o%2*16);return t.create(r,2*n)}}}(),r.enc.Utf16)},147:function(e,t,n){var r,i,o,a,s,c,u,l;e.exports=(l=n(751),n(653),n(498),i=(r=l).lib,o=i.Base,a=i.WordArray,s=r.algo,c=s.MD5,u=s.EvpKDF=o.extend({cfg:o.extend({keySize:4,hasher:c,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,r=this.cfg,i=r.hasher.create(),o=a.create(),s=o.words,c=r.keySize,u=r.iterations;s.length<c;){n&&i.update(n),n=i.update(e).finalize(t),i.reset();for(var l=1;l<u;l++)n=i.finalize(n),i.reset();o.concat(n)}return o.sigBytes=4*c,o}}),r.EvpKDF=function(e,t,n){return u.create(n).compute(e,t)},l.EvpKDF)},573:function(e,t,n){var r,i,o,a;e.exports=(a=n(751),n(575),i=(r=a).lib.CipherParams,o=r.enc.Hex,r.format.Hex={stringify:function(e){return e.ciphertext.toString(o)},parse:function(e){var t=o.parse(e);return i.create({ciphertext:t})}},a.format.Hex)},498:function(e,t,n){var r,i,o,a;e.exports=(r=n(751),o=(i=r).lib.Base,a=i.enc.Utf8,void(i.algo.HMAC=o.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,c=o.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=o.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))},406:function(e,t,n){var r;e.exports=(r=n(751),n(235),n(654),n(814),n(20),n(320),n(716),n(653),n(846),n(438),n(28),n(450),n(159),n(945),n(498),n(121),n(147),n(575),n(81),n(107),n(769),n(191),n(946),n(969),n(311),n(970),n(243),n(239),n(573),n(192),n(627),n(579),n(339),n(251),r)},654:function(e,t,n){var r;e.exports=(r=n(751),function(){if("function"==typeof ArrayBuffer){var e=r.lib.WordArray,t=e.init,n=e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var n=e.byteLength,r=[],i=0;i<n;i++)r[i>>>2]|=e[i]<<24-i%4*8;t.call(this,r,n)}else t.apply(this,arguments)};n.prototype=e}}(),r.lib.WordArray)},716:function(e,t,n){var r;e.exports=(r=n(751),function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,a=e[t+0],c=e[t+1],d=e[t+2],p=e[t+3],v=e[t+4],y=e[t+5],g=e[t+6],m=e[t+7],w=e[t+8],b=e[t+9],_=e[t+10],k=e[t+11],x=e[t+12],B=e[t+13],S=e[t+14],E=e[t+15],C=o[0],A=o[1],O=o[2],T=o[3];C=u(C,A,O,T,a,7,s[0]),T=u(T,C,A,O,c,12,s[1]),O=u(O,T,C,A,d,17,s[2]),A=u(A,O,T,C,p,22,s[3]),C=u(C,A,O,T,v,7,s[4]),T=u(T,C,A,O,y,12,s[5]),O=u(O,T,C,A,g,17,s[6]),A=u(A,O,T,C,m,22,s[7]),C=u(C,A,O,T,w,7,s[8]),T=u(T,C,A,O,b,12,s[9]),O=u(O,T,C,A,_,17,s[10]),A=u(A,O,T,C,k,22,s[11]),C=u(C,A,O,T,x,7,s[12]),T=u(T,C,A,O,B,12,s[13]),O=u(O,T,C,A,S,17,s[14]),C=l(C,A=u(A,O,T,C,E,22,s[15]),O,T,c,5,s[16]),T=l(T,C,A,O,g,9,s[17]),O=l(O,T,C,A,k,14,s[18]),A=l(A,O,T,C,a,20,s[19]),C=l(C,A,O,T,y,5,s[20]),T=l(T,C,A,O,_,9,s[21]),O=l(O,T,C,A,E,14,s[22]),A=l(A,O,T,C,v,20,s[23]),C=l(C,A,O,T,b,5,s[24]),T=l(T,C,A,O,S,9,s[25]),O=l(O,T,C,A,p,14,s[26]),A=l(A,O,T,C,w,20,s[27]),C=l(C,A,O,T,B,5,s[28]),T=l(T,C,A,O,d,9,s[29]),O=l(O,T,C,A,m,14,s[30]),C=f(C,A=l(A,O,T,C,x,20,s[31]),O,T,y,4,s[32]),T=f(T,C,A,O,w,11,s[33]),O=f(O,T,C,A,k,16,s[34]),A=f(A,O,T,C,S,23,s[35]),C=f(C,A,O,T,c,4,s[36]),T=f(T,C,A,O,v,11,s[37]),O=f(O,T,C,A,m,16,s[38]),A=f(A,O,T,C,_,23,s[39]),C=f(C,A,O,T,B,4,s[40]),T=f(T,C,A,O,a,11,s[41]),O=f(O,T,C,A,p,16,s[42]),A=f(A,O,T,C,g,23,s[43]),C=f(C,A,O,T,b,4,s[44]),T=f(T,C,A,O,x,11,s[45]),O=f(O,T,C,A,E,16,s[46]),C=h(C,A=f(A,O,T,C,d,23,s[47]),O,T,a,6,s[48]),T=h(T,C,A,O,m,10,s[49]),O=h(O,T,C,A,S,15,s[50]),A=h(A,O,T,C,y,21,s[51]),C=h(C,A,O,T,x,6,s[52]),T=h(T,C,A,O,p,10,s[53]),O=h(O,T,C,A,_,15,s[54]),A=h(A,O,T,C,c,21,s[55]),C=h(C,A,O,T,w,6,s[56]),T=h(T,C,A,O,E,10,s[57]),O=h(O,T,C,A,g,15,s[58]),A=h(A,O,T,C,B,21,s[59]),C=h(C,A,O,T,v,6,s[60]),T=h(T,C,A,O,k,10,s[61]),O=h(O,T,C,A,d,15,s[62]),A=h(A,O,T,C,b,21,s[63]),o[0]=o[0]+C|0,o[1]=o[1]+A|0,o[2]=o[2]+O|0,o[3]=o[3]+T|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var o=e.floor(r/4294967296),a=r;n[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,i,o,a){var s=e+(t&n|~t&r)+i+a;return(s<<o|s>>>32-o)+t}function l(e,t,n,r,i,o,a){var s=e+(t&r|n&~r)+i+a;return(s<<o|s>>>32-o)+t}function f(e,t,n,r,i,o,a){var s=e+(t^n^r)+i+a;return(s<<o|s>>>32-o)+t}function h(e,t,n,r,i,o,a){var s=e+(n^(t|~r))+i+a;return(s<<o|s>>>32-o)+t}t.MD5=o._createHelper(c),t.HmacMD5=o._createHmacHelper(c)}(Math),r.MD5)},81:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,n,r){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,r.encryptBlock(i,0);for(var a=0;a<n;a++)e[t+a]^=i[a]}return e.Encryptor=e.extend({processBlock:function(e,n){var r=this._cipher,i=r.blockSize;t.call(this,e,n,i,r),this._prevBlock=e.slice(n,n+i)}}),e.Decryptor=e.extend({processBlock:function(e,n){var r=this._cipher,i=r.blockSize,o=e.slice(n,n+i);t.call(this,e,n,i,r),this._prevBlock=o}}),e}(),r.mode.CFB)},769:function(e,t,n){var r;e.exports=(r=n(751),n(575),
/** @preserve
   * Counter block mode compatible with  Dr Brian Gladman fileenc.c
   * derived from CryptoJS.mode.CTR
   * <NAME_EMAIL>
   */
r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}else e+=1<<24;return e}function n(e){return 0===(e[0]=t(e[0]))&&(e[1]=t(e[1])),e}var i=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,o=this._iv,a=this._counter;o&&(a=this._counter=o.slice(0),this._iv=void 0),n(a);var s=a.slice(0);r.encryptBlock(s,0);for(var c=0;c<i;c++)e[t+c]^=s[c]}});return e.Decryptor=i,e}(),r.mode.CTRGladman)},107:function(e,t,n){var r,i,o;e.exports=(o=n(751),n(575),o.mode.CTR=(r=o.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var a=o.slice(0);n.encryptBlock(a,0),o[r-1]=o[r-1]+1|0;for(var s=0;s<r;s++)e[t+s]^=a[s]}}),r.Decryptor=i,r),o.mode.CTR)},946:function(e,t,n){var r,i;e.exports=(i=n(751),n(575),i.mode.ECB=((r=i.lib.BlockCipherMode.extend()).Encryptor=r.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),r.Decryptor=r.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),r),i.mode.ECB)},191:function(e,t,n){var r,i,o;e.exports=(o=n(751),n(575),o.mode.OFB=(r=o.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),n.encryptBlock(o,0);for(var a=0;a<r;a++)e[t+a]^=o[a]}}),r.Decryptor=i,r),o.mode.OFB)},969:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,r=4*t,i=r-n%r,o=n+i-1;e.clamp(),e.words[o>>>2]|=i<<24-o%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Ansix923)},311:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.pad.Iso10126={pad:function(e,t){var n=4*t,i=n-e.sigBytes%n;e.concat(r.lib.WordArray.random(i-1)).concat(r.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Iso10126)},970:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971)},239:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding)},243:function(e,t,n){var r;e.exports=(r=n(751),n(575),r.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){var t=e.words,n=e.sigBytes-1;for(n=e.sigBytes-1;n>=0;n--)if(t[n>>>2]>>>24-n%4*8&255){e.sigBytes=n+1;break}}},r.pad.ZeroPadding)},121:function(e,t,n){var r,i,o,a,s,c,u,l,f;e.exports=(f=n(751),n(653),n(498),i=(r=f).lib,o=i.Base,a=i.WordArray,s=r.algo,c=s.SHA1,u=s.HMAC,l=s.PBKDF2=o.extend({cfg:o.extend({keySize:4,hasher:c,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,r=u.create(n.hasher,e),i=a.create(),o=a.create([1]),s=i.words,c=o.words,l=n.keySize,f=n.iterations;s.length<l;){var h=r.update(t).finalize(o);r.reset();for(var d=h.words,p=d.length,v=h,y=1;y<f;y++){v=r.finalize(v),r.reset();for(var g=v.words,m=0;m<p;m++)d[m]^=g[m]}i.concat(h),c[0]++}return i.sigBytes=4*l,i}}),r.PBKDF2=function(e,t,n){return l.create(n).compute(e,t)},f.PBKDF2)},251:function(e,t,n){var r;e.exports=(r=n(751),n(20),n(716),n(147),n(575),function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=[],o=[],a=[],s=n.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)c.call(this);for(i=0;i<8;i++)r[i]^=n[i+4&7];if(t){var o=t.words,a=o[0],s=o[1],u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=u>>>16|4294901760&l,h=l<<16|65535&u;for(r[0]^=u,r[1]^=f,r[2]^=l,r[3]^=h,r[4]^=u,r[5]^=f,r[6]^=l,r[7]^=h,i=0;i<4;i++)c.call(this)}},_doProcessBlock:function(e,t){var n=this._X;c.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),e[t+r]^=i[r]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,n=0;n<8;n++)o[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],i=65535&r,s=r>>>16,c=((i*i>>>17)+i*s>>>15)+s*s,u=((4294901760&r)*r|0)+((65535&r)*r|0);a[n]=c^u}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=t._createHelper(s)}(),r.RabbitLegacy)},339:function(e,t,n){var r;e.exports=(r=n(751),n(20),n(716),n(147),n(575),function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=[],o=[],a=[],s=n.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,n=0;n<4;n++)c.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(t){var o=t.words,a=o[0],s=o[1],u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=u>>>16|4294901760&l,h=l<<16|65535&u;for(i[0]^=u,i[1]^=f,i[2]^=l,i[3]^=h,i[4]^=u,i[5]^=f,i[6]^=l,i[7]^=h,n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(e,t){var n=this._X;c.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),e[t+r]^=i[r]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,n=0;n<8;n++)o[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],i=65535&r,s=r>>>16,c=((i*i>>>17)+i*s>>>15)+s*s,u=((4294901760&r)*r|0)+((65535&r)*r|0);a[n]=c^u}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=t._createHelper(s)}(),r.Rabbit)},579:function(e,t,n){var r;e.exports=(r=n(751),n(20),n(716),n(147),n(575),function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=n.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,r=this._S=[],i=0;i<256;i++)r[i]=i;i=0;for(var o=0;i<256;i++){var a=i%n,s=t[a>>>2]>>>24-a%4*8&255;o=(o+r[i]+s)%256;var c=r[i];r[i]=r[o],r[o]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,n=this._j,r=0,i=0;i<4;i++){n=(n+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[n],e[n]=o,r|=e[(e[t]+e[n])%256]<<24-8*i}return this._i=t,this._j=n,r}e.RC4=t._createHelper(i);var a=n.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});e.RC4Drop=t._createHelper(a)}(),r.RC4)},945:function(e,t,n){var r;e.exports=(r=n(751),
/** @preserve
  (c) 2012 by Cédric Mesnil. All rights reserved.
  	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
  	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
  	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  */
function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),u=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),f=i.create([0,1518500249,1859775393,2400959708,2840853838]),h=i.create([1352829926,1548603684,1836072691,2053994217,0]),d=a.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,a,d,b,_,k,x,B,S,E,C,A=this._hash.words,O=f.words,T=h.words,P=s.words,D=c.words,j=u.words,I=l.words;for(k=o=A[0],x=a=A[1],B=d=A[2],S=b=A[3],E=_=A[4],n=0;n<80;n+=1)C=o+e[t+P[n]]|0,C+=n<16?p(a,d,b)+O[0]:n<32?v(a,d,b)+O[1]:n<48?y(a,d,b)+O[2]:n<64?g(a,d,b)+O[3]:m(a,d,b)+O[4],C=(C=w(C|=0,j[n]))+_|0,o=_,_=b,b=w(d,10),d=a,a=C,C=k+e[t+D[n]]|0,C+=n<16?m(x,B,S)+T[0]:n<32?g(x,B,S)+T[1]:n<48?y(x,B,S)+T[2]:n<64?v(x,B,S)+T[3]:p(x,B,S)+T[4],C=(C=w(C|=0,I[n]))+E|0,k=E,E=S,S=w(B,10),B=x,x=C;C=A[1]+d+S|0,A[1]=A[2]+b+E|0,A[2]=A[3]+_+k|0,A[3]=A[4]+o+x|0,A[4]=A[0]+a+B|0,A[0]=C},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,o=i.words,a=0;a<5;a++){var s=o[a];o[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return i},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function p(e,t,n){return e^t^n}function v(e,t,n){return e&t|~e&n}function y(e,t,n){return(e|~t)^n}function g(e,t,n){return e&n|t&~n}function m(e,t,n){return e^(t|~n)}function w(e,t){return e<<t|e>>>32-t}t.RIPEMD160=o._createHelper(d),t.HmacRIPEMD160=o._createHmacHelper(d)}(Math),r.RIPEMD160)},653:function(e,t,n){var r,i,o,a,s,c,u,l;e.exports=(l=n(751),i=(r=l).lib,o=i.WordArray,a=i.Hasher,s=r.algo,c=[],u=s.SHA1=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],s=n[4],u=0;u<80;u++){if(u<16)c[u]=0|e[t+u];else{var l=c[u-3]^c[u-8]^c[u-14]^c[u-16];c[u]=l<<1|l>>>31}var f=(r<<5|r>>>27)+s+c[u];f+=u<20?1518500249+(i&o|~i&a):u<40?1859775393+(i^o^a):u<60?(i&o|i&a|o&a)-1894007588:(i^o^a)-899497514,s=a,a=o,o=i<<30|i>>>2,i=r,r=f}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=a._createHelper(u),r.HmacSHA1=a._createHmacHelper(u),l.SHA1)},438:function(e,t,n){var r,i,o,a,s,c;e.exports=(c=n(751),n(846),i=(r=c).lib.WordArray,o=r.algo,a=o.SHA256,s=o.SHA224=a.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=4,e}}),r.SHA224=a._createHelper(s),r.HmacSHA224=a._createHmacHelper(s),c.SHA224)},846:function(e,t,n){var r;e.exports=(r=n(751),function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,i=0;i<64;)t(r)&&(i<8&&(s[i]=n(e.pow(r,.5))),c[i]=n(e.pow(r,1/3)),i++),r++}();var u=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],s=n[4],l=n[5],f=n[6],h=n[7],d=0;d<64;d++){if(d<16)u[d]=0|e[t+d];else{var p=u[d-15],v=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,y=u[d-2],g=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;u[d]=v+u[d-7]+g+u[d-16]}var m=r&i^r&o^i&o,w=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),b=h+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+c[d]+u[d];h=f,f=l,l=s,s=a+b|0,a=o,o=i,i=r,r=b+(w+m)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=e.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(l),t.HmacSHA256=o._createHmacHelper(l)}(Math),r.SHA256)},159:function(e,t,n){var r;e.exports=(r=n(751),n(235),function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.x64.Word,s=t.algo,c=[],u=[],l=[];!function(){for(var e=1,t=0,n=0;n<24;n++){c[e+5*t]=(n+1)*(n+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)u[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,o=0;o<24;o++){for(var s=0,f=0,h=0;h<7;h++){if(1&i){var d=(1<<h)-1;d<32?f^=1<<d:s^=1<<d-32}128&i?i=i<<1^113:i<<=1}l[o]=a.create(s,f)}}();var f=[];!function(){for(var e=0;e<25;e++)f[e]=a.create()}();var h=s.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,r=this.blockSize/2,i=0;i<r;i++){var o=e[t+2*i],a=e[t+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(A=n[i]).high^=a,A.low^=o}for(var s=0;s<24;s++){for(var h=0;h<5;h++){for(var d=0,p=0,v=0;v<5;v++)d^=(A=n[h+5*v]).high,p^=A.low;var y=f[h];y.high=d,y.low=p}for(h=0;h<5;h++){var g=f[(h+4)%5],m=f[(h+1)%5],w=m.high,b=m.low;for(d=g.high^(w<<1|b>>>31),p=g.low^(b<<1|w>>>31),v=0;v<5;v++)(A=n[h+5*v]).high^=d,A.low^=p}for(var _=1;_<25;_++){var k=(A=n[_]).high,x=A.low,B=c[_];B<32?(d=k<<B|x>>>32-B,p=x<<B|k>>>32-B):(d=x<<B-32|k>>>64-B,p=k<<B-32|x>>>64-B);var S=f[u[_]];S.high=d,S.low=p}var E=f[0],C=n[0];for(E.high=C.high,E.low=C.low,h=0;h<5;h++)for(v=0;v<5;v++){var A=n[_=h+5*v],O=f[_],T=f[(h+1)%5+5*v],P=f[(h+2)%5+5*v];A.high=O.high^~T.high&P.high,A.low=O.low^~T.low&P.low}A=n[0];var D=l[s];A.high^=D.high,A.low^=D.low}},_doFinalize:function(){var t=this._data,n=t.words,r=(this._nDataBytes,8*t.sigBytes),o=32*this.blockSize;n[r>>>5]|=1<<24-r%32,n[(e.ceil((r+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*n.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,u=[],l=0;l<c;l++){var f=a[l],h=f.high,d=f.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),u.push(d),u.push(h)}return new i.init(u,s)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});t.SHA3=o._createHelper(h),t.HmacSHA3=o._createHmacHelper(h)}(Math),r.SHA3)},450:function(e,t,n){var r,i,o,a,s,c,u,l;e.exports=(l=n(751),n(235),n(28),i=(r=l).x64,o=i.Word,a=i.WordArray,s=r.algo,c=s.SHA512,u=s.SHA384=c.extend({_doReset:function(){this._hash=new a.init([new o.init(3418070365,3238371032),new o.init(1654270250,914150663),new o.init(2438529370,812702999),new o.init(355462360,4144912697),new o.init(1731405415,4290775857),new o.init(2394180231,1750603025),new o.init(3675008525,1694076839),new o.init(1203062813,3204075428)])},_doFinalize:function(){var e=c._doFinalize.call(this);return e.sigBytes-=16,e}}),r.SHA384=c._createHelper(u),r.HmacSHA384=c._createHmacHelper(u),l.SHA384)},28:function(e,t,n){var r;e.exports=(r=n(751),n(235),function(){var e=r,t=e.lib.Hasher,n=e.x64,i=n.Word,o=n.WordArray,a=e.algo;function s(){return i.create.apply(i,arguments)}var c=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],u=[];!function(){for(var e=0;e<80;e++)u[e]=s()}();var l=a.SHA512=t.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],s=n[4],l=n[5],f=n[6],h=n[7],d=r.high,p=r.low,v=i.high,y=i.low,g=o.high,m=o.low,w=a.high,b=a.low,_=s.high,k=s.low,x=l.high,B=l.low,S=f.high,E=f.low,C=h.high,A=h.low,O=d,T=p,P=v,D=y,j=g,I=m,L=w,H=b,R=_,z=k,M=x,N=B,F=S,W=E,U=C,K=A,V=0;V<80;V++){var X,q,G=u[V];if(V<16)q=G.high=0|e[t+2*V],X=G.low=0|e[t+2*V+1];else{var J=u[V-15],Y=J.high,Z=J.low,Q=(Y>>>1|Z<<31)^(Y>>>8|Z<<24)^Y>>>7,$=(Z>>>1|Y<<31)^(Z>>>8|Y<<24)^(Z>>>7|Y<<25),ee=u[V-2],te=ee.high,ne=ee.low,re=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,ie=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),oe=u[V-7],ae=oe.high,se=oe.low,ce=u[V-16],ue=ce.high,le=ce.low;q=(q=(q=Q+ae+((X=$+se)>>>0<$>>>0?1:0))+re+((X+=ie)>>>0<ie>>>0?1:0))+ue+((X+=le)>>>0<le>>>0?1:0),G.high=q,G.low=X}var fe,he=R&M^~R&F,de=z&N^~z&W,pe=O&P^O&j^P&j,ve=T&D^T&I^D&I,ye=(O>>>28|T<<4)^(O<<30|T>>>2)^(O<<25|T>>>7),ge=(T>>>28|O<<4)^(T<<30|O>>>2)^(T<<25|O>>>7),me=(R>>>14|z<<18)^(R>>>18|z<<14)^(R<<23|z>>>9),we=(z>>>14|R<<18)^(z>>>18|R<<14)^(z<<23|R>>>9),be=c[V],_e=be.high,ke=be.low,xe=U+me+((fe=K+we)>>>0<K>>>0?1:0),Be=ge+ve;U=F,K=W,F=M,W=N,M=R,N=z,R=L+(xe=(xe=(xe=xe+he+((fe+=de)>>>0<de>>>0?1:0))+_e+((fe+=ke)>>>0<ke>>>0?1:0))+q+((fe+=X)>>>0<X>>>0?1:0))+((z=H+fe|0)>>>0<H>>>0?1:0)|0,L=j,H=I,j=P,I=D,P=O,D=T,O=xe+(ye+pe+(Be>>>0<ge>>>0?1:0))+((T=fe+Be|0)>>>0<fe>>>0?1:0)|0}p=r.low=p+T,r.high=d+O+(p>>>0<T>>>0?1:0),y=i.low=y+D,i.high=v+P+(y>>>0<D>>>0?1:0),m=o.low=m+I,o.high=g+j+(m>>>0<I>>>0?1:0),b=a.low=b+H,a.high=w+L+(b>>>0<H>>>0?1:0),k=s.low=k+z,s.high=_+R+(k>>>0<z>>>0?1:0),B=l.low=B+N,l.high=x+M+(B>>>0<N>>>0?1:0),E=f.low=E+W,f.high=S+F+(E>>>0<W>>>0?1:0),A=h.low=A+K,h.high=C+U+(A>>>0<K>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(r+128>>>10<<5)]=Math.floor(n/4294967296),t[31+(r+128>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(l),e.HmacSHA512=t._createHmacHelper(l)}(),r.SHA512)},627:function(e,t,n){var r;e.exports=(r=n(751),n(20),n(716),n(147),n(575),function(){var e=r,t=e.lib,n=t.WordArray,i=t.BlockCipher,o=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=o.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var r=a[n]-1;t[n]=e[r>>>5]>>>31-r%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var u=i[o]=[],l=c[o];for(n=0;n<24;n++)u[n/6|0]|=t[(s[n]-1+l)%28]<<31-n%6,u[4+(n/6|0)]|=t[28+(s[n+24]-1+l)%28]<<31-n%6;for(u[0]=u[0]<<1|u[0]>>>31,n=1;n<7;n++)u[n]=u[n]>>>4*(n-1)+3;u[7]=u[7]<<5|u[7]>>>27}var f=this._invSubKeys=[];for(n=0;n<16;n++)f[n]=i[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),h.call(this,1,1431655765);for(var r=0;r<16;r++){for(var i=n[r],o=this._lBlock,a=this._rBlock,s=0,c=0;c<8;c++)s|=u[c][((a^i[c])&l[c])>>>0];this._lBlock=a,this._rBlock=o^s}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,h.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function d(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}e.DES=i._createHelper(f);var p=o.TripleDES=i.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=f.createEncryptor(n.create(t)),this._des2=f.createEncryptor(n.create(r)),this._des3=f.createEncryptor(n.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(p)}(),r.TripleDES)},235:function(e,t,n){var r;e.exports=(r=n(751),function(e){var t=r,n=t.lib,i=n.Base,o=n.WordArray,a=t.x64={};a.Word=i.extend({init:function(e,t){this.high=e,this.low=t}}),a.WordArray=i.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:8*t.length},toX32:function(){for(var e=this.words,t=e.length,n=[],r=0;r<t;r++){var i=e[r];n.push(i.high),n.push(i.low)}return o.create(n,this.sigBytes)},clone:function(){for(var e=i.clone.call(this),t=e.words=this.words.slice(0),n=t.length,r=0;r<n;r++)t[r]=t[r].clone();return e}})}(),r)},658:function(e,t,n){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(){i=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),s=new C(r||[]);return o(a,"_invoke",{value:x(e,n,s)}),a}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=f;var d={};function p(){}function v(){}function y(){}var g={};l(g,s,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(A([])));w&&w!==t&&n.call(w,s)&&(g=w);var b=y.prototype=p.prototype=Object.create(g);function _(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function i(o,a,s,c){var u=h(e[o],e,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==r(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,s,c)}),(function(e){i("throw",e,s,c)})):t.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return i("throw",e,s,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return a=a?a.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return{value:void 0,done:!0}}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=B(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=h(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function B(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,B(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var i=h(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,d;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function A(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}return v.prototype=y,o(b,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:v,configurable:!0}),v.displayName=l(y,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,u,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},_(k.prototype),l(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new k(f(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},_(b),l(b,u,"Generator"),l(b,s,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=A,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function o(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function s(e){o(a,r,i,s,c,"next",e)}function c(e){o(a,r,i,s,c,"throw",e)}s(void 0)}))}}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,u(r.key),r)}}function c(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){var t=function(e,t){if("object"!=r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==r(t)?t:String(t)}n.r(t),n.d(t,{default:function(){return f}});var l=new(function(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),c(this,"init",(function(e){var n=e.url,r=e.token,i=e.refreshToken,o=e.mount;return t.instance&&t.instance.destroy(),n&&(t.fileLink=n,t.iframe=t.initIframe(t.fileLink,o),t.initPostMessage(),r&&t.initToken(r),i&&(t.refreshTokenCallback=i)),t.instance={fileLink:n,setToken:t.setToken,destroy:t.destroy,mount:t.iframe,print:t.print},t.instance})),c(this,"destroy",(function(){t.instance&&(t.iframe.parentElement.removeChild(t.iframe),t.iframe=void 0,t.root&&document.body.removeChild(t.root),window.removeEventListener("message",t.handleMessage),t.instance=void 0)})),c(this,"initToken",(function(e){t.refreshToken(e)})),c(this,"setToken",(function(e){t.refreshToken(e)})),c(this,"refreshToken",(function(e){e.timeout&&t.refreshTokenCallback&&(clearTimeout(t.refreshTokenTimer),t.refreshTokenTimer=setTimeout((function(){t.refreshTokenCallback().then((function(n){t.refreshToken({token:n,timeout:e.timeout})}))}),Math.max(e.timeout-3e4,3e4))),t.token=e.token||e,document.cookie="x-user-token=".concat(t.token,";path=/;Domain=").concat(document.domain,";Expires=").concat(new Date(Date.now()+e.timeout||3e5)),t.postMessage("token.refresh.back",t.token)})),c(this,"initIframe",(function(e,n){var r=document.createElement("iframe");return r.style.cssText="width:100%;height:100%;border: 0;display: block;",r.setAttribute("allowfullscreen",!0),r.src=e,n?n.appendChild(r):(t.root=document.createElement("div"),t.root.classList.add("lite-preview-mount"),t.root.style.cssText="height: 100%;width: 100%;",document.body.appendChild(t.root),t.root.appendChild(r)),r})),c(this,"handleMessage",(function(e){if(t.fileLink.match(e.origin)){var n=e.data,r=n.eventName,i=n.data;switch(r){case"ready":t.postMessage("performance",JSON.stringify(window.performance.timing)),t.token?t.postMessage("token.set",t.token):t.postMessage("token.undefined");break;case"token.refresh":t.refreshTokenCallback&&t.refreshTokenCallback().then((function(e){t.postMessage("token.refresh.back",e)}));break;case"print.response":t.printResolve&&(clearTimeout(t.printTimer),delete t.printTimer,i&&t.printResolve(i),delete t.printResolve)}}})),c(this,"initPostMessage",(function(){window.addEventListener("message",t.handleMessage),t.postMessage=function(e,n){return t.iframe.contentWindow.postMessage({eventName:e,data:n},"*")}})),c(this,"print",a(i().mark((function e(){var n,r=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=r.length>0&&void 0!==r[0]?r[0]:{},t.printTimer||t.printResolve){e.next=5;break}return e.abrupt("return",new Promise((function(e,r){t.postMessage("print.request",n),clearTimeout(t.printTimer),t.printTimer=setTimeout((function(){delete t.printResolve,delete t.printTimer,r("ERROR:导出接口超时")}),6e5),t.printResolve=e})));case 5:console.error("WARN:已存在未完成的导出任务，请稍后重试");case 6:case"end":return e.stop()}}),e)}))))})));window.litePreviewSDK={config:l.init};var f={config:l.init}},257:function(e,t,n){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}n.r(t),n.d(t,{config:function(){return le}});var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function o(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function a(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var s=function(){function e(){}return e.add=function(t){e.HANDLE_LIST.push(t),window.addEventListener("message",t,!1)},e.remove=function(t){var n=e.HANDLE_LIST.indexOf(t);n>=0&&e.HANDLE_LIST.splice(n,1),window.removeEventListener("message",t,!1)},e.empty=function(){for(;e.HANDLE_LIST.length;)window.removeEventListener("message",e.HANDLE_LIST.shift(),!1)},e.parse=function(e){try{return"object"==r(e)?e:e?JSON.parse(e):e}catch(t){return console.log("Message.parse Error:",t),e}},e.HANDLE_LIST=[],e}();function c(e){return"[object Function]"==={}.toString.call(e)}var u,l,f,h,d,p={origin:""};function v(e,t){p[e]=t}function y(e){return p[e]}function g(e){var t=y("origin");return!!function(e,t){return e!==t&&(e.replace(/www\./i,"").toLowerCase()!==t.replace(/www\./i,"").toLowerCase()||(e.match("www.")?void 0:(v("origin",t),!1)))}(t,e.origin)&&(console.warn("postMessage 域名检查不通过",{safeOrigin:t,eventOrigin:e.origin}),!0)}(d=u||(u={})).unknown="unknown",d.spreadsheet="s",d.writer="w",d.presentation="p",d.pdf="f",function(e){e.wps="w",e.et="s",e.presentation="p",e.pdf="f"}(l||(l={})),function(e){e.nomal="nomal",e.simple="simple"}(f||(f={})),function(e){e[e.requestFullscreen=1]="requestFullscreen",e[e.exitFullscreen=0]="exitFullscreen"}(h||(h={}));var m,w,b,_=(m=0,function(){return m+=1}),k=function(e,t,n){void 0===n&&(n=!0);var r=t;if(!w){var i=function e(t){var n=t.clientHeight,r=t.clientWidth;0!==n||0!==r||b?0===n&&0===r||!b||(b.disconnect(),b=null):window.ResizeObserver&&(b=new ResizeObserver((function(n){e(t)}))).observe(t),w.style.cssText+="height: "+n+"px; width: "+r+"px"}.bind(null,r);(w=document.createElement("iframe")).classList.add("web-office-iframe");var o={id:"office-iframe",src:e,scrolling:"no",frameborder:"0",allowfullscreen:"allowfullscreen",webkitallowfullscreen:"true",mozallowfullscreen:"true",allow:"clipboard-read; clipboard-write"};for(var a in r?(o.style="width: "+r.clientWidth+"px; height: "+r.clientHeight+"px;",n&&window.addEventListener("resize",i)):((r=document.createElement("div")).classList.add("web-office-default-container"),function(e){var t=document.createElement("style");document.head.appendChild(t);var n=t.sheet;n.insertRule(".web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100%; height: 100%; left: 0; top: 0;}",n.cssRules.length)}(),document.body.appendChild(r),o.style="position: fixed; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;"),o)w.setAttribute(a,o[a]);r.appendChild(w),w.destroy=function(){w.parentNode.removeChild(w),w=null,window.removeEventListener("resize",i),b&&(b.disconnect(),b=null)}}return w},x=function(e){k().contentWindow&&k().contentWindow.postMessage(JSON.stringify(e),y("origin"))};function B(e,t,n){return new Promise((function(r){var i=_();s.add((function e(t){if(!g(t)){var o=s.parse(t.data);o.eventName===n&&o.msgId===i&&(r(o.data),s.remove(e))}})),x({data:e,msgId:i,eventName:t})}))}var S=function(e){return B(e,"wps.jssdk.api","wps.api.reply")},E=function(e){return B(e,"api.basic","api.basic.reply")},C={idMap:{}};function A(e){return o(this,void 0,void 0,(function(){var t,n,r,i,o,c,u,l,f,h;return a(this,(function(a){switch(a.label){case 0:return g(e)?[2]:(t=s.parse(e.data),n=t.eventName,r=t.callbackId,i=t.data,r&&(o=C.idMap[r])?(c=o.split(":"),u=c[0],l=c[1],"api.callback"===n&&C[u]&&C[u][l]?[4,(h=C[u][l]).callback.apply(h,i.args)]:[3,2]):[3,2]);case 1:f=a.sent(),x({result:f,callbackId:r,eventName:"api.callback.reply"}),a.label=2;case 2:return[2]}}))}))}var O=function(e){return o(void 0,void 0,void 0,(function(){function t(){return Object.keys(C.idMap).find((function(e){return C.idMap[e]===r+":"+n}))}var n,r,i,o,c,u,l,f,h;return a(this,(function(a){switch(a.label){case 0:return n=e.prop,r=e.parentObjId,[4,P([i=e.value])];case 1:return o=a.sent(),c=o[0],u=o[1],e.value=c[0],l=Object.keys(u)[0],f=C[r],null===i&&f&&f[n]&&((h=t())&&delete C.idMap[h],delete f[n],Object.keys(f).length||delete C[r],Object.keys(C.idMap).length||s.remove(A)),l&&(Object.keys(C.idMap).length||s.add(A),C[r]||(C[r]={}),C[r][n]={callbackId:l,callback:u[l]},(h=t())&&delete C.idMap[h],C.idMap[l]=r+":"+n),[2]}}))}))},T=function(e,t,n,r){return o(void 0,void 0,void 0,(function(){var c,u,l,f,h,d,p,v;return a(this,(function(y){switch(y.label){case 0:return c=_(),f=new Promise((function(e,t){u=e,l=t})),h={},t.args?[4,P(t.args)]:[3,2];case 1:d=y.sent(),p=d[0],v=d[1],t.args=p,h=v,y.label=2;case 2:return"api.setter"!==e?[3,4]:[4,O(t)];case 3:y.sent(),y.label=4;case 4:return function(e){var t=e[0],n=e[1];"function"==typeof(t=i({},t)).data&&(t.data=t.data()),n(),x(t)}([{eventName:e,data:t,msgId:c},function(){var t=this;return s.add((function i(f){return o(t,void 0,void 0,(function(){var t,o,d;return a(this,(function(a){switch(a.label){case 0:return g(f)?[2]:"api.callback"===(t=s.parse(f.data)).eventName&&t.callbackId&&h[t.callbackId]?[4,h[t.callbackId].apply(h,t.data.args)]:[3,2];case 1:o=a.sent(),x({result:o,eventName:"api.callback.reply",callbackId:t.callbackId}),a.label=2;case 2:return t.eventName===e+".reply"&&t.msgId===c&&(t.error?((d=new Error("")).stack=t.error+"\n"+n,r&&r(),l(d)):u(t.result),s.remove(i)),[2]}}))}))})),f}]),[2,f]}}))}))};function P(e){return o(this,void 0,void 0,(function(){var t,n,r,i,o,s,c,u,l,f,h;return a(this,(function(a){switch(a.label){case 0:t={},n=[],r=e.slice(0),a.label=1;case 1:return r.length?(i=void 0,[4,r.shift()]):[3,13];case 2:return(o=a.sent())&&o.done?[4,o.done()]:[3,4];case 3:a.sent(),a.label=4;case 4:if(!function(e){if(!e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(i))return[3,11];for(c in i={},s=[],o)s.push(c);u=0,a.label=5;case 5:return u<s.length?(l=s[u],f=o[l],/^[A-Z]/.test(l)?f&&f.done?[4,f.done()]:[3,7]:[3,8]):[3,10];case 6:a.sent(),a.label=7;case 7:f&&f.objId?f={objId:f.objId}:"function"==typeof f&&(h=_(),t[h]=f,f={callbackId:h}),a.label=8;case 8:i[l]=f,a.label=9;case 9:return u++,[3,5];case 10:return[3,12];case 11:o&&o.objId?i={objId:o.objId}:"function"==typeof o&&void 0===o.objId?(h=_(),t[h]=o,i={callbackId:h}):i=o,a.label=12;case 12:return n.push(i),[3,1];case 13:return[2,[n,t]]}}))}))}var D=function(e,t){void 0===t&&(t=!0);var n=i({},e),o=n.headers,a=void 0===o?{}:o,s=n.subscriptions,c=void 0===s?{}:s,u=n.mode,l=void 0===u?f.nomal:u,h=n.commonOptions,d=a.backBtn,p=void 0===d?{}:d,v=a.shareBtn,y=void 0===v?{}:v,g=a.otherMenuBtn,m=void 0===g?{}:g,w=function(e,n){e.subscribe&&"function"==typeof e.subscribe&&(e.callback=n,c[n]=e.subscribe,t&&delete e.subscribe)};if(w(p,"wpsconfig_back_btn"),w(y,"wpsconfig_share_btn"),w(m,"wpsconfig_other_menu_btn"),m.items&&Array.isArray(m.items)){var b=[];m.items.forEach((function(e,t){switch(void 0===e&&(e={}),e.type){case"export_img":e.type=1,e.callback="export_img";break;case"export_pdf":e.type=1,e.callback="export_pdf";break;case"save_version":e.type=1,e.callback="save_version";break;case"about_wps":e.type=1,e.callback="about_wps";break;case"split_line":e.type=2;break;case"custom":e.type=3,w(e,"wpsconfig_other_menu_btn_"+t),b.push(e)}})),b.length&&(H||R)&&(m.items=b)}n.url=n.url||n.wpsUrl;var _=[];if((l===f.simple||h&&!1===h.isShowTopArea)&&_.push("simple","hidecmb"),n.debug&&_.push("debugger"),n.url&&_.length&&(n.url=n.url+(n.url.indexOf("?")>=0?"&":"?")+_.join("&")),h&&(h.isParentFullscreen||h.isBrowserViewFullscreen)&&(document.addEventListener("fullscreenchange",z),document.addEventListener("webkitfullscreenchange",z),document.addEventListener("mozfullscreenchange",z)),n.wordOptions&&(n.wpsOptions=n.wordOptions),n.excelOptions&&(n.etOptions=n.excelOptions),n.pptOptions&&(n.wppOptions=n.pptOptions),"object"==r(c.print)){var k="wpsconfig_print";"function"==typeof c.print.subscribe&&(c[k]=c.print.subscribe,n.print={callback:k},void 0!==c.print.custom&&(n.print.custom=c.print.custom)),delete c.print}return"function"==typeof c.exportPdf&&(c[k="wpsconfig_export_pdf"]=c.exportPdf,n.exportPdf={callback:k},delete c.exportPdf),n.commandBars&&I(n.commandBars,!1),i(i({},n),{subscriptions:c})},j=function(e){void 0===e&&(e="");var t="";if(!t&&e){var n=e.toLowerCase();-1!==n.indexOf("/office/s/")&&(t=u.spreadsheet),-1!==n.indexOf("/office/w/")&&(t=u.writer),-1!==n.indexOf("/office/p/")&&(t=u.presentation),-1!==n.indexOf("/office/f/")&&(t=u.pdf)}if(!t){var r=e.match(/[\?&]type=([a-z]+)/)||[];t=l[r[1]]||""}return t};function I(e,t){void 0===t&&(t=!0);var n=e.map((function(e){var t=e.attributes;if(!Array.isArray(t)){var n=[];for(var r in t)if(t.hasOwnProperty(r)){var i={name:r,value:t[r]};n.push(i)}e.attributes=n}return e}));return t&&x({data:n,eventName:"setCommandBars"}),n}var L=window.navigator.userAgent.toLowerCase(),H=/Android|webOS|iPhone|iPod|BlackBerry|iPad/i.test(L),R=function(){try{return-1!==window._parent.location.search.indexOf("from=wxminiprogram")}catch(e){return!1}}();function z(){var e={status:h.requestFullscreen},t=document,n=t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement;e.status=n?h.requestFullscreen:h.exitFullscreen,x({data:e,eventName:"fullscreenchange"})}var M=function(){C.idMap={}};function N(){console.group("JSSDK 事件机制调整说明"),console.warn("jssdk.on、jssdk.off 和 jssdk.Application.Sub 将在后续版本中被弃用，建议使用改进后的 ApiEvent"),console.warn("具体请参考：https://wwo.wps.cn/docs/front-end/basic-usage/events/intro/"),console.groupEnd()}var F=0,W=new Set;function U(e){return F+=1,!e&&function(e){W.forEach((function(t){return t(e)}))}(F),F}function K(){var e=new Error("");return(e.stack||e.message||"").split("\n").slice(2).join("\n")}function V(e,t){var n,r=this,c=t.Events,l=t.Enum,f=t.Props,h=f[0],d=f[1],p={objId:F};switch(function e(t,n,r){for(var o=n.slice(0),a=function(){var n=o.shift();!n.alias&&~X.indexOf(n.prop)&&o.push(i(i({},n),{alias:n.prop+"Async"})),Object.defineProperty(t,n.alias||n.prop,{get:function(){var o=this,a=1===n.cache,s=a&&this["__"+n.prop+"CacheValue"];if(!s){var c=K(),u=U(a),l=function e(){for(var o,a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return void 0!==n.caller?function e(t,n,r){for(var o=n.slice(0),a=function(){var n=o.shift();!n.alias&&~X.indexOf(n.prop)&&o.push(i(i({},n),{alias:n.prop+"Async"})),Object.defineProperty(t,n.alias||n.prop,{get:function(){var i=this,o=1===n.cache,a=o&&this["__"+n.prop+"CacheValue"];if(!a){var s=K(),c=U(o),u=function i(){for(var o,a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];return void 0!==n.caller?e(o={objId:U()},r[n.caller],r):o={},q(i,o,"api.caller",{obj:i,args:a,parentObjId:t.objId,objId:o.objId,prop:n.prop},s),o};return u.objId=-1,void 0!==n.getter&&(u.objId=c,e(u,r[n.getter],r)),q(t,u,"api.getter",{parentObjId:t.objId,objId:u.objId,prop:n.prop},s,(function(){delete i["__"+n.prop+"CacheValue"]})),o&&(this["__"+n.prop+"CacheValue"]=u),u}return a},set:function(e){var r=K();return q(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:n.prop},r)}})};o.length;)a()}(o={objId:U()},r[n.caller],r):o={},q(e,o,"api.caller",{obj:e,args:a,parentObjId:t.objId,objId:o.objId,prop:n.prop},c),o};return l.objId=-1,void 0!==n.getter&&(l.objId=u,e(l,r[n.getter],r)),q(t,l,"api.getter",{parentObjId:t.objId,objId:l.objId,prop:n.prop},c,(function(){delete o["__"+n.prop+"CacheValue"]})),a&&(this["__"+n.prop+"CacheValue"]=l),l}return s},set:function(e){var r=K();return q(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:n.prop},r)}})};o.length;)a()}(p,h,d),p.Events=c,p.Enum=l,e.Enum=p.Enum,e.Events=p.Events,e.Props=f,j(e.url)){case u.writer:e.WordApplication=e.WpsApplication=function(){return p};break;case u.spreadsheet:e.ExcelApplication=e.EtApplication=function(){return p};break;case u.presentation:e.PPTApplication=e.WppApplication=function(){return p};break;case u.pdf:e.PDFApplication=function(){return p}}e.Application=p,e.Free=function(e){return T("api.free",{objId:e},"")},e.Stack=p.Stack=(n=function(t){e&&e.Free(t)},function(){var e=[],t=function(t){e.push(t)};return W.add(t),{End:function(){n(e),W.delete(t)}}});var v={};s.add((function(e){return o(r,void 0,void 0,(function(){var t,n,r,i,o;return a(this,(function(a){switch(a.label){case 0:return g(e)?[2]:"api.event"===(t=s.parse(e.data)).eventName&&t.data?(n=t.data,r=n.eventName,i=n.data,(o=v[r])?[4,o(i)]:[3,2]):[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}}))}))})),p.Sub={};var y=function(e){var t=c[e];Object.defineProperty(p.Sub,t,{set:function(e){N(),v[t]=e,x({eventName:"api.event.register",data:{eventName:t,register:!!e,objId:F+=1}})}})};for(var m in c)y(m)}var X=["ExportAsFixedFormat","GetOperatorsInfo","ImportDataIntoFields","ReplaceText","ReplaceBookmark","GetBookmarkText","GetComments"];function q(e,t,n,r,i,o){var a,s=(e.done?e.done():Promise.resolve()).then((function(){return a||(a=T(n,r,i,o)),a}));t.done=function(){return s},t.then=function(e,n){return r.objId>=0?(t.then=null,t.catch=null,s.then((function(){e(t)})).catch((function(e){return n(e)}))):s.then(e,n)},t.catch=function(e){return s.catch(e)},t.Destroy=function(){return T("api.free",{objId:t.objId},"")}}var G={},J=null,Y="fileOpen",Z="fileSaved",Q="fileStatus",$="fullscreenChange",ee="error",te="stage",ne="api.getToken",re="event.toast",ie="event.hyperLinkOpen",oe="api.getClipboardData";function ae(e,t,n,r,c,u,l){var f=this;void 0===n&&(n={}),s.add((function(h){return o(f,void 0,void 0,(function(){var o,f,d,p,v,y,m,w,b,_,k,B,S,E,C,A,O,T,P;return a(this,(function(a){switch(a.label){case 0:return g(h)?[2]:(o=s.parse(h.data),f=o.eventName,d=void 0===f?"":f,p=o.data,v=void 0===p?null:p,y=o.url,m=void 0===y?null:y,-1!==["wps.jssdk.api"].indexOf(d)?[2]:"ready"!==d?[3,1]:(c.apiReadySended&&function(e){var t=[];Object.keys(G).forEach((function(n){G[n].forEach((function(r){var i=n;e.off(i,r),t.push({handle:r,eventName:i})})),delete G[n]})),t.forEach((function(e){var t=e.eventName,n=e.handle;null==J||J.ApiEvent.AddApiEventListener(t,n)}))}(t),x({eventName:"setConfig",data:i(i({},n),{version:e.version})}),e.tokenData&&e.setToken(i(i({},e.tokenData),{hasRefreshTokenConfig:!!n.refreshToken})),e.iframeReady=!0,[3,15]));case 1:return"error"!==d?[3,2]:(t.emit(ee,v),[3,15]);case 2:return"open.result"!==d?[3,3]:(void 0!==(null===(O=null==v?void 0:v.fileInfo)||void 0===O?void 0:O.officeVersion)&&(e.mainVersion=v.fileInfo.officeVersion,console.log("WebOfficeSDK Main Version: V"+e.mainVersion)),t.emit(Y,v),[3,15]);case 3:return"api.scroll"!==d?[3,4]:(window.scrollTo(v.x,v.y),[3,15]);case 4:if(d!==ne)return[3,9];w={token:!1},a.label=5;case 5:return a.trys.push([5,7,,8]),[4,c.refreshToken()];case 6:return w=a.sent(),[3,8];case 7:return b=a.sent(),console.error("refreshToken: "+(b||"fail to get")),[3,8];case 8:return x({eventName:ne+".reply",data:w}),[3,15];case 9:if(d!==oe)return[3,14];_={text:"",html:""},a.label=10;case 10:return a.trys.push([10,12,,13]),[4,c.getClipboardData()];case 11:return _=a.sent(),[3,13];case 12:return k=a.sent(),console.error("getClipboardData: "+(k||"fail to get")),[3,13];case 13:return x({eventName:oe+".reply",data:_}),[3,15];case 14:d===re?c.onToast(v):d===ie?c.onHyperLinkOpen(v):"stage"===d?t.emit(te,v):"event.callback"===d?(B=v.eventName,S=v.data,E=B,"fullScreenChange"===B&&(E=$),"file.saved"===B&&(E=Q),((null===(T=n.commonOptions)||void 0===T?void 0:T.isBrowserViewFullscreen)||(null===(P=n.commonOptions)||void 0===P?void 0:P.isParentFullscreen))&&"fullscreenchange"===E&&(C=S.status,A=S.isDispatchEvent,n.commonOptions.isBrowserViewFullscreen?function(e,t,n,r){0===e?t.style="position: static; width: "+n.width+"; height: "+n.height:1===e&&(t.style="position: absolute; width: 100%; height: 100%"),r&&function(e){["fullscreen","fullscreenElement"].forEach((function(t){Object.defineProperty(document,t,{get:function(){return!!e.status},configurable:!0})}));var t=new CustomEvent("fullscreenchange");document.dispatchEvent(t)}({status:e})}(C,u,l,A):n.commonOptions.isParentFullscreen&&function(e,t,n){var r=document.querySelector(n),i=r&&1===r.nodeType?r:t;if(0===e){var o=document;(o.exitFullscreen||o.mozCancelFullScreen||o.msExitFullscreen||o.webkitCancelFullScreen||o.webkitExitFullscreen).call(document)}else if(1===e){(i.requestFullscreen||i.mozRequestFullScreen||i.msRequestFullscreen||i.webkitRequestFullscreen).call(i)}}(C,u,n.commonOptions.isParentFullscreen)),t.emit(E,S)):"api.ready"===d&&V(e,v),a.label=15;case 15:return"function"==typeof r[d]&&r[d](e,m||v),[2]}}))}))}))}function se(e){return new Promise((function(t){s.add((function n(r){g(r)||s.parse(r.data).eventName===e&&(t(),s.remove(n))}))}))}function ce(e){var t,n=this;void 0===e&&(e={}),J&&J.destroy();try{var r=D(e),i=r.subscriptions,u=void 0===i?{}:i,l=r.mount,f=void 0===l?null:l,h=r.url,d=r.refreshToken,p=r.onToast,y=r.onHyperLinkOpen,g=r.getClipboardData;v("origin",(h.match(/https*:\/\/[^\/]+/g)||[])[0]);var m=k(h,f),w=se("ready"),b=se("open.result"),_=se("api.ready"),B=f?{width:f.clientWidth+"px",height:f.clientHeight+"px"}:{width:"100vw",height:"100vh"};delete r.mount,h&&delete r.url,delete r.subscriptions;var C=(t=t||Object.create(null),{on:function(e,n){(t[e]||(t[e]=[])).push(n)},off:function(e,n){t[e]&&t[e].splice(t[e].indexOf(n)>>>0,1)},emit:function(e,n){(t[e]||[]).slice().map((function(e){e(n)})),(t["*"]||[]).slice().map((function(t){t(e,n)}))}}),A={apiReadySended:!1},O=function(e,t,r){return o(n,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return function(e,t,n){if(G[e]){var r=!!G[e].find((function(e){return e===t}));return r&&"off"===n?(C.off(e,t),G[e]=G[e].filter((function(e){return e!==t})),!!G[e].length||(G[e]=void 0,!1)):(r||"on"!==n||(G[e].push(t),C.on(e,t)),!0)}return"on"===n?(G[e]=[],G[e].push(t),!1):"off"===n||void 0}(e,t,r)?[3,2]:[4,w];case 1:n.sent(),function(e,t){var n=e.eventName,r=e.type,i=e.handle;"on"===t?C.on(n,i):C.off(n,i),"base.event"===r&&x({eventName:"basic.event",data:{eventName:n,action:t}}),N()}(function(e,t){var n=e,r="base.event";switch(n){case Z:console.warn("fileSaved事件监听即将弃用， 推荐使用fileStatus进行文件状态的监听"),n="fileStatus";break;case $:n="fullscreenchange";break;case"error":case"fileOpen":r="callback.event"}return{eventName:n,type:r,handle:t}}(e,t),r),n.label=2;case 2:return[2]}}))}))};return J={url:h,iframe:m,version:"1.1.19",iframeReady:!1,tokenData:null,commandBars:null,tabs:{getTabs:function(){return o(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,w];case 1:return e.sent(),[2,E({api:"tab.getTabs"})]}}))}))},switchTab:function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),[2,E({api:"tab.switchTab",args:{tabKey:e}})]}}))}))}},setCooperUserColor:function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),[2,E({api:"setCooperUserColor",args:e})]}}))}))},setToken:function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),J.tokenData=e,x({eventName:"setToken",data:e}),[2]}}))}))},ready:function(){return o(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return A.apiReadySended?[3,2]:[4,b];case 1:e.sent(),A.apiReadySended=!0,x({eventName:"api.ready"}),e.label=2;case 2:return[4,_];case 3:return e.sent(),[2,new Promise((function(e){return setTimeout((function(){return e(null==J?void 0:J.Application)}),0)}))]}}))}))},destroy:function(){m.destroy(),s.empty(),J=null,W=new Set,F=0,document.removeEventListener("fullscreenchange",z),M()},save:function(){return o(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,w];case 1:return e.sent(),[2,S({api:"save"})]}}))}))},setCommandBars:function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),I(e),[2]}}))}))},updateConfig:function(e){return void 0===e&&(e={}),o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),e.commandBars?(console.warn("Deprecated: `updateConfig()` 方法即将废弃，请使用`setCommandBars()`代替`updateConfig()`更新`commandBars`配置。"),[4,I(e.commandBars)]):[3,3];case 2:t.sent(),t.label=3;case 3:return[2]}}))}))},executeCommandBar:function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),I([{cmbId:e,attributes:[{name:"click",value:!0}]}]),[2]}}))}))},on:function(e,t){return o(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.ApiEvent.AddApiEventListener(e,t)]}))}))},off:function(e,t){return o(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.ApiEvent.RemoveApiEventListener(e,t)]}))}))},ApiEvent:{AddApiEventListener:function(e,t){return o(this,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return[4,O(e,t,"on")];case 1:return[2,n.sent()]}}))}))},RemoveApiEventListener:function(e,t){return o(this,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return[4,O(e,t,"off")];case 1:return[2,n.sent()]}}))}))}}},function(e,t,n,r,i,o){t&&c(t)&&(i.refreshToken=t,e.refreshToken={eventName:ne}),o&&c(o)&&(i.getClipboardData=o,e.getClipboardData={eventName:oe}),n&&c(n)&&(i.onToast=n,e.onToast={eventName:re}),r&&c(r)&&(i.onHyperLinkOpen=r,e.onHyperLinkOpen={eventName:ie})}(r,d,p,y,A,g),ae(J,C,r,u,A,m,B),J}catch(e){console.error(e)}}console.log("WebOfficeSDK JS-SDK V1.1.19");var ue=Object.freeze({__proto__:null,listener:ae,config:ce});window.WPS=ue;var le=ce;t.default={config:ce}},620:function(e,t,n){var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)},i=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.CTX=void 0;var a,s,c,u=n(737),l=n(257),f=n(658),h=n(853),d=n(328),p=n(406),v=new Map([[0,"format_text"],[1,"text"],[2,"image"],[3,"combination"],[4,"drop_down"],[6,"data"],[8,"checkbox"]]),y=new Map([[0,"格式文本"],[1,"纯文本"],[2,"图片"],[3,"下拉菜单"],[4,"下拉菜单"],[6,"日期选择器"],[8,"复选框"]]),g=function(){function e(e){var t=this;if(this.executeCommandBar=function(e){return i(t,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return"TabPrintPreview"!==e?[3,2]:[4,this.printExecute()];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},this.litePreviewPrint={CommandBars:function(e){if("TabPrintPreview"===e)return{Execute:function(){return i(t,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return[4,this.printExecute()];case 1:return[2,e.sent()]}}))}))}}}},this.onControlsDelete=function(e){t.instance.ApiEvent.AddApiEventListener("ContentControlBeforeDelete",(function(n){var i=t.getDecryptTag(n.tag);e(r(r({},n),i))}))},this.onControlsInsert=function(e){t.instance.ApiEvent.AddApiEventListener("ContentControlAfterAdd",(function(n){var i=t.getDecryptTag(n.tag);e(r(r({},n),i))}))},this.onControlsChange=function(e){t.instance.ApiEvent.AddApiEventListener("ContentControlBeforeContentUpdate",(function(n){var i=t.getDecryptTag(n.tag);e(r(r({},n),i))}))},this.getDecryptTag=function(e){var n=(0,u.querystring)(t.config.url)._w_appid,r=p.AES.decrypt(e,n).toString(p.enc.Utf8);return(0,u.querystring)(r)},this.getControlsList=function(){return i(t,void 0,void 0,(function(){var e,t,n,i,a,s,c,u,l,f,h,d;return o(this,(function(o){switch(o.label){case 0:return[4,this.instance.ready()];case 1:return o.sent(),[4,this.instance.Application.ActiveDocument.ContentControls.Count];case 2:e=o.sent(),t=[],n=0,o.label=3;case 3:return n<e?[4,this.instance.Application.ActiveDocument.ContentControls.Item(n+1)]:[3,12];case 4:return[4,(i=o.sent()).Title];case 5:return a=o.sent(),[4,i.Tag];case 6:return s=o.sent(),[4,i.ID];case 7:return c=o.sent(),[4,i.Type];case 8:return u=o.sent(),[4,i.Content];case 9:return l=o.sent(),[4,i.PlaceholderText];case 10:f=o.sent(),h=this.getDecryptTag(s),d=r(r({title:a},h),{id:c,type:u,content:l,placeholderText:f}),t.push(d),o.label=11;case 11:return n++,[3,3];case 12:return[2,t]}}))}))},this.pushControlsData=function(){return i(t,void 0,void 0,(function(){var e,t,n,r,i,a,s;return o(this,(function(o){switch(o.label){case 0:return e=this.config.url.split("/weboffice/office/w/"),t=e[1].split("?")[0],n=e[1].split("?")[1],r=e[0]+"/c/docsconnect/api/connect/v1/push/widgets"+"?".concat(n),[4,this.getControlsList()];case 1:return i=o.sent(),a=[],i.forEach((function(e){var t=v.get(e.type),n=y.get(e.type);a.push({widget_id:e.componentId||"",widget_tid:e.id||"",placeholder_text:e.placeholderText||"",widget_name:n||"",widget_tag:e.tag||"",widget_title:e.title||"",widget_type:t||"",widget_value:e.content||""})})),s=JSON.stringify({widget_list:a,file_id:t,token:this.instance.tokenData.token||""}),[4,fetch(r,{method:"post",headers:{"Content-Type":"application/json"},body:s})];case 2:return[4,o.sent().json()];case 3:return[2,o.sent()]}}))}))},console.log("OPEN_JSSDK_VERSION ".concat("0.0.7")),console.log((0,u.isLitePreviewUrl)(null==e?void 0:e.url)?"极速预览":"wo预览"),(0,u.isLitePreviewUrl)(null==e?void 0:e.url))a=f.default;else try{a=WebOfficeSDK,console.log("使用外部的sdk")}catch(e){a=l.default,console.log("使用内部的sdk")}e?e.url&&((0,u.isOpenDocUrl)(e.url)||(0,u.isWebofficeUrl)(e.url))?(e.mount&&e.mount instanceof Node||(e.mount=document.getElementsByTagName("body")[0]),this.config=e,this.addPreviewContainer()):console.error("请设置文档中台预览链接"):console.error("初始化sdk失败，缺失config参数")}return e.prototype.getInstance=function(){var e=this,t=this.instance;return(0,u.isLitePreviewUrl)(this.config.url)&&(t.Application=this.litePreviewPrint,t.executeCommandBar=this.executeCommandBar,t.ready=function(){return i(e,void 0,void 0,(function(){return o(this,(function(e){return[2]}))}))}),(0,u.isWebofficeUrl)(this.config.url)&&(t.onControlsDelete=this.onControlsDelete,t.onControlsInsert=this.onControlsInsert,t.onControlsChange=this.onControlsChange,t.getControlsList=this.getControlsList,t.pushControlsData=this.pushControlsData),t},e.prototype.addPreviewContainer=function(){var e,t=document.createElement("div");t.style.position="relative",t.style.width="100%",t.style.height="100%",null===(e=this.config.mount)||void 0===e||e.appendChild(t),this.config.mount=t,s=(0,u.parseUrl)(this.config.url),this.config.webpath=(0,u.parseWebpath)(s.pathname),this.config.url&&(0,u.isWebofficeUrl)(this.config.url)?this.renderWebOffice():this.renderOpendoc()},e.prototype.renderWebOffice=function(){var e=new a.config(this.config);this.instance=e},e.prototype.renderOpendoc=function(){var e,t=document.createElement("iframe");t.src=this.config.url,t.frameBorder="none",t.style.width="100%",t.style.height="100%",null===(e=this.config.mount)||void 0===e||e.appendChild(t),c=t,this.instance={setToken:this.setToken.bind(this),print:this.openDocPrint.bind(this),download:this.openDocDownload.bind(this)}},e.prototype.setToken=function(e){console.log("this.config",this.config),this.config.setToken=e,(0,h.setToken)(this.config,c)},e.prototype.openDocPrint=function(){(0,d.openDocFunc)(d.EVENT_TYPE.PRINT,c)},e.prototype.openDocDownload=function(){(0,d.openDocFunc)(d.EVENT_TYPE.DOWNLOAD,c)},e.prototype.printExecute=function(){return i(this,void 0,void 0,(function(){var e,t;return o(this,(function(n){switch(n.label){case 0:return[4,this.instance.print()];case 1:return e=n.sent(),(t=document.createElement("iframe")).setAttribute("style","display:none"),fetch(e).then((function(e){return e.blob()})).then((function(e){var n=URL.createObjectURL(e);t.src=n,document.body.appendChild(t),t.onload=function(){t.contentWindow.print()}})),[2]}}))}))},e}();t.CTX=g},328:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.openDocFunc=t.EVENT_TYPE=void 0,function(e){e.PRINT="print",e.DOWNLOAD="download"}(t.EVENT_TYPE||(t.EVENT_TYPE={}));t.openDocFunc=function(e,t){var n,r;n={eventName:e},(r=t.contentWindow)&&r.postMessage(n,"*")}},853:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.setToken=void 0;var n,r=null,i=null,o=!0,a=null;function s(e){var t,n;t={eventName:o?"setToken":"setTokenRefresh",data:e},(n=a.contentWindow)&&n.postMessage(t,"*"),o=!1,r=e,i=(new Date).getTime()}function c(e){return e instanceof Promise||"function"==typeof(null==e?void 0:e.then)}t.setToken=function(e,t){if(!e.setToken||!e.setToken.token)return console.error("请按照文档规范设置token格式");n=e,window.addEventListener("message",(function(u){"wpsPreviewDidMount"===u.data&&(r=null,i=null,o=!0,a=t,s(e.setToken),"function"==typeof e.refreshToken&&function(e){window.document.addEventListener("visibilitychange",(function(){if("hidden"!==document.visibilityState){var e=(new Date).getTime();if(r&&e-i>r.timeout){var t=n.refreshToken();c(t)?t.then((function(e){s(e)})):s(t)}}}));var t=function(e){s(e),e.timeout&&o(e.timeout)},o=function(e){var r,i=e-3e5;setTimeout((function(){var o=(new Date).getTime(),a=n.refreshToken();if(c(a))a.then((function(n){r=n;var a=(new Date).getTime();setTimeout((function(){t(r)}),i>0?3e5-(a-o):e-(a-o))}));else{r=a;var s=(new Date).getTime();setTimeout((function(){t(r)}),i>0?3e5-(s-o):e-(s-o))}}),i)};o(e)}(e.setToken.timeout))}))}},737:function(e,t){function n(e){return new RegExp(/\/weboffice\/office\//).test(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.querystring=t.isLitePreviewUrl=t.isWebofficeUrl=t.isOpenDocUrl=t.parseWebpath=t.parseUrl=t.isJSON=void 0,t.isJSON=function(e){if("string"!=typeof e)return!1;try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return console.log("error："+e+"!!!"+t),!1}},t.parseUrl=function(e){var t=document.createElement("a");t.href=e;var n=t.hostname,r="80"===t.port||"443"===t.port?"":t.port,i=n+(r?":".concat(r):"");return{href:e,protocol:t.protocol||"",host:i,hostname:n,port:r,search:t.search.replace(t.hash,"")||"",hash:t.hash||"",pathname:0===(t.pathname||"").indexOf("/")?t.pathname||"":"/"+(t.pathname||""),relative:(e.match(/tps?:\/\/[^\/]+(.+)/)||[,""])[1]||"",segments:t.pathname.replace(/^\//,"").split("/")||[],origin:t.protocol+"//"+i||""}},t.parseWebpath=function(e){var t=e||window.location.pathname||"";return t.substring(0,t.lastIndexOf("/docs/viewweb/"))||""},t.isOpenDocUrl=function(e){return new RegExp(/\/micsweb\/viewweb\/reader\//).test(e)},t.isWebofficeUrl=n,t.isLitePreviewUrl=function(e){return n(e)&&e.includes("wpsCachePreview")},t.querystring=function(e){var t=(e||window.location.search).replace(/^(\S*)\?/,"").split("&"),n={};return t.forEach((function(e){var t=e.split("=");n[t[0]]=t[1]})),n}},480:function(){}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){var e=r;Object.defineProperty(e,"X",{value:!0});var t=n(620);e.Z={config:function(e){return new t.CTX(e).getInstance()}}}();var i=r.X,o=r.Z;export{i as __esModule,o as default};
import { axios } from '@/utils/request'

class ApprovalService {
  /**
   * 获取系统角色列表
   * @param {*} params
   */
  deployments(params) {
    return axios({
      url: '/roleInfo/listRoleInfo',
      method: 'post',
      data: params
    })
  }

  /**
   * 获取所有审批流程
   * @param {*} params
   */
  listProcess(params) {
    return axios({
      url: '/approval/processList',
      method: 'post',
      data: params
    })
  }

  /**
   * 获取所有审批流程
   * @param {*} params
   */
  getFirstPerson(processId) {
    return axios({
      url: '/ApprovalPerson/getFirstPerson/' + processId,
      method: 'get'
    })
  }

  deleteDeployments(params) {
    return axios({
      url: '/approval/deleteDeployments/' + params,
      method: 'delete'
    })
  }

  getTaskByUserId(params) {
    return axios({
      url: '/approval/getTaskByUserId',
      method: 'get',
      params: params
    })
  }

  getTaskHistoryByUserId(params) {
    return axios({
      url: '/approval/getTaskHistoryByUserId',
      method: 'post',
      data: params
    })
  }

  countMyTask(params) {
    return axios({
      url: '/approval/getTaskByUcountMyTaskerId',
      method: 'get'
    })
  }

  completeTask(params) {
    return axios({
      url: '/approval/completeTask/' + params.id,
      method: 'post',
      data: params
    })
  }

  startApproval(params) {
    return axios({
      url: '/approval/startApproval/',
      method: 'post',
      data: params
    })
  }

  getDiagram(params) {
    return axios({
      url: '/approval/getDiagram/' + params,
      method: 'get',
      responseType: 'arraybuffer'
    })
  }

  queryCommentsByProcessId(params) {
    return axios({
      url: '/approval/queryCommentsByProcessId/' + params,
      method: 'get'
    })
  }
}

export default new ApprovalService()

import { axios } from '@/utils/request'

/**
 * 查询执行地变更_跨省迁出
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
export function correctionPlacechangeEmigrationPage(parameter) {
  return axios({
    url: '/correctionPlacechangeEmigration/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 执行地变更_跨省迁出列表
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
export function correctionPlacechangeEmigrationList(parameter) {
  return axios({
    url: '/correctionPlacechangeEmigration/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加执行地变更_跨省迁出
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
export function correctionPlacechangeEmigrationAdd(parameter) {
  return axios({
    url: '/correctionPlacechangeEmigration/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑执行地变更_跨省迁出
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
export function correctionPlacechangeEmigrationEdit(parameter) {
  return axios({
    url: '/correctionPlacechangeEmigration/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除执行地变更_跨省迁出
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
export function correctionPlacechangeEmigrationDelete(parameter) {
  return axios({
    url: '/correctionPlacechangeEmigration/delete',
    method: 'post',
    data: parameter
  })
}

export function correctionPlacechangeEmigrationDetail(id) {
  return axios({
    url: '/correctionPlacechangeEmigration/detail?id=' + id,
    method: 'get',
    data: {}
  })
}

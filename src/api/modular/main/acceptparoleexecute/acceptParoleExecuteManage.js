import { axios } from '@/utils/request'

/**
 * 查询假释执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
export function acceptParoleExecutePage (parameter) {
  return axios({
    url: '/acceptParoleExecute/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 假释执行接收表列表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
export function acceptParoleExecuteList (parameter) {
  return axios({
    url: '/acceptParoleExecute/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加假释执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
export function acceptParoleExecuteAdd (parameter) {
  return axios({
    url: '/acceptParoleExecute/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑假释执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
export function acceptParoleExecuteEdit (parameter) {
  return axios({
    url: '/acceptParoleExecute/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除假释执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
export function acceptParoleExecuteDelete (parameter) {
  return axios({
    url: '/acceptParoleExecute/delete',
    method: 'post',
    data: parameter
  })
}

export function acceptParoleExecuteDetail (id) {
  return axios({
    url: '/acceptParoleExecute/detail?id=' + id,
    method: 'get',
    data: {}
  })
}

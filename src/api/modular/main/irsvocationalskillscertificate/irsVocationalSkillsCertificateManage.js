import { axios } from '@/utils/request'

/**
 * 查询职业技能证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:30
 */
export function irsVocationalSkillsCertificatePage (parameter) {
  return axios({
    url: '/irsVocationalSkillsCertificate/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 职业技能证书列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:30
 */
export function irsVocationalSkillsCertificateList (parameter) {
  return axios({
    url: '/irsVocationalSkillsCertificate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加职业技能证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:30
 */
export function irsVocationalSkillsCertificateAdd (parameter) {
  return axios({
    url: '/irsVocationalSkillsCertificate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑职业技能证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:30
 */
export function irsVocationalSkillsCertificateEdit (parameter) {
  return axios({
    url: '/irsVocationalSkillsCertificate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除职业技能证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:30
 */
export function irsVocationalSkillsCertificateDelete (parameter) {
  return axios({
    url: '/irsVocationalSkillsCertificate/delete',
    method: 'post',
    data: parameter
  })
}

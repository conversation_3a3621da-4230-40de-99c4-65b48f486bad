import { axios } from '@/utils/request'

/**
 * 查询矫正对象文书信息表
 *
 * <AUTHOR>
 * @date 2025-04-24 20:36:16
 */
export function correctionDocPage (parameter) {
  return axios({
    url: '/correctionDoc/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象文书信息表列表
 *
 * <AUTHOR>
 * @date 2025-04-24 20:36:16
 */
export function correctionDocList (parameter) {
  return axios({
    url: '/correctionDoc/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象文书基本信息
 */
export function correctionBaseInfo (parameter) {
  return axios({
    url: '/correctionDoc/baseInfo',
    method: 'get',
    params: parameter
  })
}

/**
 * 包含目录的文件树
 */
export function correctionDocTree (parameter) {
  return axios({
    url: '/correctionDoc/tree',
    method: 'get',
    params: parameter
  })
}

/**
 * 更新包含目录的文件树
 */
export function correctionDocUpdateDocTree (parameter) {
  return axios({
    url: '/correctionDoc/updateDocTree',
    method: 'post',
    data: parameter
  })
}

export function correctionDocDetail (parameter) {
  return axios({
    url: '/correctionDoc/detail',
    method: 'get',
    params: parameter
  })
}
/**
 * 添加矫正对象文书信息表
 *
 * <AUTHOR>
 * @date 2025-04-24 20:36:16
 */
export function correctionDocAdd (parameter) {
  return axios({
    url: '/correctionDoc/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象文书信息表
 *
 * <AUTHOR>
 * @date 2025-04-24 20:36:16
 */
export function correctionDocEdit (parameter) {
  return axios({
    url: '/correctionDoc/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象文书信息表
 *
 * <AUTHOR>
 * @date 2025-04-24 20:36:16
 */
export function correctionDocDelete (parameter) {
  return axios({
    url: '/correctionDoc/delete',
    method: 'post',
    data: parameter
  })
}

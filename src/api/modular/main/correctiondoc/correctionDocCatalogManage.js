import { axios } from '@/utils/request'

/**
 * 查询文书目录
 *
 * <AUTHOR>
 * @date 2025-04-24 15:52:17
 */
export function correctionDocCatalogTree (parameter) {
  return axios({
    url: '/correctionDocCatalog/tree',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加文书目录
 *
 * <AUTHOR>
 * @date 2025-04-24 15:52:17
 */
export function correctionDocCatalogAdd (parameter) {
  return axios({
    url: '/correctionDocCatalog/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑文书目录
 *
 * <AUTHOR>
 * @date 2025-04-24 15:52:17
 */
export function correctionDocCatalogEdit (parameter) {
  return axios({
    url: '/correctionDocCatalog/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除文书目录
 *
 * <AUTHOR>
 * @date 2025-04-24 15:52:17
 */
export function correctionDocCatalogDelete (parameter) {
  return axios({
    url: '/correctionDocCatalog/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询特困对象基本信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:19
 */
export function irsExtremePoorBasicInfoPage (parameter) {
  return axios({
    url: '/irsExtremePoorBasicInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 特困对象基本信息列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:19
 */
export function irsExtremePoorBasicInfoList (parameter) {
  return axios({
    url: '/irsExtremePoorBasicInfo/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加特困对象基本信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:19
 */
export function irsExtremePoorBasicInfoAdd (parameter) {
  return axios({
    url: '/irsExtremePoorBasicInfo/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑特困对象基本信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:19
 */
export function irsExtremePoorBasicInfoEdit (parameter) {
  return axios({
    url: '/irsExtremePoorBasicInfo/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除特困对象基本信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:19
 */
export function irsExtremePoorBasicInfoDelete (parameter) {
  return axios({
    url: '/irsExtremePoorBasicInfo/delete',
    method: 'post',
    data: parameter
  })
}

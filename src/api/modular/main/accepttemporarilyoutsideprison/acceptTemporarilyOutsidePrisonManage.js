import { axios } from '@/utils/request'

/**
 * 查询暂予监外接收表
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
 */
export function acceptTemporarilyOutsidePrisonTerminate(parameter) {
  return axios({
    url: '/acceptTemporarilyOutsidePrison/terminate',
    method: 'post',
    data: parameter
  })
}

export function addTopPsnPage(parameter) {
  return axios({
    url: '/acceptTemporarilyOutsidePrison/psn/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 暂予监外反馈
 * @param parameter
 * @returns {AxiosPromise}
 * {
 *    rjjg: '1'//入矫结果
 *    ,wnrjyy: ''//未入矫原因
 *    ,zxtzshzwh: ''//执行通知书回执文号
 *    ,hzcl: ''//回执材料 附件id
 *    ,fkr: ''//反馈人
 *    ,fksj: ''//反馈时间
 *    ,fkbz: ''//反馈备注
 * }
 */
export function acceptTemporarilyOutsidePrisonFeedback(parameter) {
  return axios({
    url: '/acceptTemporarilyOutsidePrison/feedback',
    method: 'post',
    data: parameter
  })
}

export function acceptTemporarilyOutsidePrisonDetail(parameter) {
  return axios({
    url: '/acceptTemporarilyOutsidePrison/detail',
    method: 'get',
    params: parameter
  })
}

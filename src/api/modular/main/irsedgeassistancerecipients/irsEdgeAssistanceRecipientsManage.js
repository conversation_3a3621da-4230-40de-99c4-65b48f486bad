import { axios } from '@/utils/request'

/**
 * 查询边缘救助对象信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:17
 */
export function irsEdgeAssistanceRecipientsPage (parameter) {
  return axios({
    url: '/irsEdgeAssistanceRecipients/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 边缘救助对象信息列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:17
 */
export function irsEdgeAssistanceRecipientsList (parameter) {
  return axios({
    url: '/irsEdgeAssistanceRecipients/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加边缘救助对象信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:17
 */
export function irsEdgeAssistanceRecipientsAdd (parameter) {
  return axios({
    url: '/irsEdgeAssistanceRecipients/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑边缘救助对象信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:17
 */
export function irsEdgeAssistanceRecipientsEdit (parameter) {
  return axios({
    url: '/irsEdgeAssistanceRecipients/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除边缘救助对象信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:17
 */
export function irsEdgeAssistanceRecipientsDelete (parameter) {
  return axios({
    url: '/irsEdgeAssistanceRecipients/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询婚姻登记信息查询（新国标）
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:36
 */
export function irsMarriageRegistrationInformationPage (parameter) {
  return axios({
    url: '/irsMarriageRegistrationInformation/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 婚姻登记信息查询（新国标）列表
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:36
 */
export function irsMarriageRegistrationInformationList (parameter) {
  return axios({
    url: '/irsMarriageRegistrationInformation/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加婚姻登记信息查询（新国标）
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:36
 */
export function irsMarriageRegistrationInformationAdd (parameter) {
  return axios({
    url: '/irsMarriageRegistrationInformation/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑婚姻登记信息查询（新国标）
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:36
 */
export function irsMarriageRegistrationInformationEdit (parameter) {
  return axios({
    url: '/irsMarriageRegistrationInformation/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除婚姻登记信息查询（新国标）
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:36
 */
export function irsMarriageRegistrationInformationDelete (parameter) {
  return axios({
    url: '/irsMarriageRegistrationInformation/delete',
    method: 'post',
    data: parameter
  })
}

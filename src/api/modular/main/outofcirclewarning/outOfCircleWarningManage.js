import { axios } from '@/utils/request'

/**
 * 查询出圈预警信息
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningPage (parameter) {
  return axios({
    url: '/outOfCircleWarning/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 出圈预警信息列表
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningList (parameter) {
  return axios({
    url: '/outOfCircleWarning/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加出圈预警信息
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningAdd (parameter) {
  return axios({
    url: '/outOfCircleWarning/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑出圈预警信息
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningEdit (parameter) {
  return axios({
    url: '/outOfCircleWarning/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除出圈预警信息
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningDelete (parameter) {
  return axios({
    url: '/outOfCircleWarning/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
export function outOfCircleWarningExport (parameter) {
  return axios({
    url: '/outOfCircleWarning/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

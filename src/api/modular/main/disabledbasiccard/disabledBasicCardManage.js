import { axios } from '@/utils/request'

/**
 * 查询残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
export function disabledBasicCardPage (parameter) {
  return axios({
    url: '/disabledBasicCard/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 残疾人信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
export function disabledBasicCardList (parameter) {
  return axios({
    url: '/disabledBasicCard/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
export function disabledBasicCardAdd (parameter) {
  return axios({
    url: '/disabledBasicCard/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
export function disabledBasicCardEdit (parameter) {
  return axios({
    url: '/disabledBasicCard/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
export function disabledBasicCardDelete (parameter) {
  return axios({
    url: '/disabledBasicCard/delete',
    method: 'post',
    data: parameter
  })
}

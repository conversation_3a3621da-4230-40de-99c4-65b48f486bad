import { axios } from '@/utils/request'

/**
 * 矫正对象法律文书信息信息接收表列表
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
export function acceptCorrectionDocList (parameter) {
  return axios({
    url: '/acceptCorrectionDoc/list',
    method: 'get',
    params: parameter
  })
}

export function reLoadFileFromOss (parameter) {
  return axios({
    url: '/acceptCorrectionDoc/reLoadFileFromOss',
    method: 'get',
    params: parameter
  })
}

export function docBatchDownLoad (parameter) {
  return axios({
    url: '/acceptCorrectionDoc/batchDownLoad',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

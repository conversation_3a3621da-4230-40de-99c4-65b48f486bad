import { axios } from '@/utils/request'

/**
 * 查询离退休人员类别信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:54
 */
export function sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldPage (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOld/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 离退休人员类别信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:54
 */
export function sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldList (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOld/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加离退休人员类别信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:54
 */
export function sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldAdd (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOld/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑离退休人员类别信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:54
 */
export function sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldEdit (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOld/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除离退休人员类别信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:54
 */
export function sjzxDm029Biz029SjjhDsjjAc60DeltaGtOldDelete (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc60DeltaGtOld/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询一体化消息日志
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
 */
export function ywxtMessageLogPage (parameter) {
  return axios({
    url: '/ywxtMessageLog/page',
    method: 'get',
    params: parameter
  })
}
export function ywxtWsLog (parameter) {
  return axios({
    url: '/ywxtWsLog/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 删除一体化消息日志
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
 */
export function ywxtMessageLogDelete (parameter) {
  return axios({
    url: '/ywxtMessageLog/delete',
    method: 'post',
    data: parameter
  })
}

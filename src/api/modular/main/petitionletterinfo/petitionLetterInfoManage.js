import { axios } from '@/utils/request'

/**
 * 查询信访信息
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
 */
export function petitionLetterInfoPage (parameter) {
  return axios({
    url: '/petitionLetterInfo/page',
    method: 'get',
    params: parameter
  })
}

export function petitionLetterInfoExport (parameter) {
  return axios({
    url: '/petitionLetterInfo/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

// 调查评估导出
export function investigationInfoExport(parameter) {
  return axios({
    url: '/investigationInfo/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

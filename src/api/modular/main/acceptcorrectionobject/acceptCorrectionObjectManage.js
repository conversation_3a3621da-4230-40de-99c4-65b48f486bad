import { axios } from '@/utils/request'

export function acceptBasePage(parameter) {
  return axios({
    url: '/acceptBaseInfo/page',
    method: 'get',
    params: parameter
  })
}

export function acceptBaseEdit(parameter) {
  return axios({
    url: '/acceptBaseInfo/edit',
    method: 'post',
    data: parameter
  })
}

export function acceptBaseSetStatus(parameter) {
  return axios({
    url: '/acceptBaseInfo/setStatus',
    method: 'post',
    data: parameter
  })
}

export function acceptBaseDetail(parameter) {
  return axios({
    url: '/acceptBaseInfo/detail',
    method: 'get',
    params: parameter
  })
}

export function acceptBaseStep(parameter) {
  return axios({
    url: '/acceptBaseInfo/step',
    method: 'get',
    params: parameter
  })
}

export function acceptBaseDelete(parameter) {
  return axios({
    url: '/acceptBaseInfo/delete',
    method: 'post',
    data: parameter
  })
}

export function checkPsn(parameter) {
  return axios({
    url: '/acceptBaseInfo/check',
    method: 'get',
    params: parameter
  })
}

/**
 * 编辑矫正对象信息接收表
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:05
 */
export function acceptCorrectionObjectFeedback(parameter) {
  return axios({
    url: '/acceptCorrectionObject/feedback',
    method: 'post',
    data: parameter
  })
}

export function acceptCorrectionObjectTerminate(parameter) {
  return axios({
    url: '/acceptCorrectionObject/terminate',
    method: 'post',
    data: parameter
  })
}

export function acceptCorrectionObjectDetail(parameter) {
  return axios({
    url: '/acceptCorrectionObject/detail',
    method: 'get',
    params: parameter
  })
}

export function getHzcl(parameter) {
  return axios({
    url: '/acceptCorrectionObject/gethzcl',
    method: 'get',
    params: parameter
  })
}

export function getNoSignPdf4003 (parameter) {
  return axios({
    url: '/acceptCorrectionObject/getNoSignPdf',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

export function getSignedPdf4003 (parameter) {
  return axios({
    url: '/acceptCorrectionObject/getSignedPdf',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

export function acceptBaseInfoStatistics(parameter) {
  return axios({
    url: '/acceptBaseInfo/statistics',
    method: 'get',
    params: parameter
  })
}

export function acceptBaseInfoStatisticsExport (parameter) {
  return axios({
    url: '/acceptBaseInfo/statistics/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

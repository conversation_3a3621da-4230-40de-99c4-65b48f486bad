import { axios } from '@/utils/request'

/**
 * 查询释放信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
export function acceptDischargedInfoPage (parameter) {
  return axios({
    url: '/acceptDischargedInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 释放信息接收表列表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
export function acceptDischargedInfoList (parameter) {
  return axios({
    url: '/acceptDischargedInfo/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加释放信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
export function acceptDischargedInfoAdd (parameter) {
  return axios({
    url: '/acceptDischargedInfo/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑释放信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
export function acceptDischargedInfoEdit (parameter) {
  return axios({
    url: '/acceptDischargedInfo/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除释放信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
export function acceptDischargedInfoDelete (parameter) {
  return axios({
    url: '/acceptDischargedInfo/delete',
    method: 'post',
    data: parameter
  })
}

export function acceptDischargedInfoDetail(parameter) {
  return axios({
    url: '/acceptDischargedInfo/detail',
    method: 'get',
    params: parameter
  })
}

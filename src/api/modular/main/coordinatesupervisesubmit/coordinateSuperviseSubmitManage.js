import { axios } from '@/utils/request'

/**
 * 查询提请外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
export function coordinateSuperviseSubmitPage (parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 提请外出监管协同列表
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
export function coordinateSuperviseSubmitList (parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加提请外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
export function coordinateSuperviseSubmitAdd (parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑提请外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
export function coordinateSuperviseSubmitEdit (parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除提请外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
export function coordinateSuperviseSubmitDelete (parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/delete',
    method: 'post',
    data: parameter
  })
}

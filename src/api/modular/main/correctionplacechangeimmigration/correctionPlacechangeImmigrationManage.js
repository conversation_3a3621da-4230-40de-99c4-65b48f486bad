import { axios } from '@/utils/request'

/**
 * 查询执行地变更_外省迁入
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
export function correctionPlacechangeImmigrationPage (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 执行地变更_外省迁入列表
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
export function correctionPlacechangeImmigrationList (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加执行地变更_外省迁入
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
export function correctionPlacechangeImmigrationAdd (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑执行地变更_外省迁入
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
export function correctionPlacechangeImmigrationEdit (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/edit',
    method: 'post',
    data: parameter
  })
}
export function correctionPlacechangeImmigrationAccept (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/accept',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除执行地变更_外省迁入
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
export function correctionPlacechangeImmigrationDelete (parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 添加调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function correctionImmigrationInvestigateAdd(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/add',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatep1(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p1',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatep2(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p2',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatep3(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p3',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatep4(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p4',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatelastP3(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/lastP3',
    method: 'get',
    params: parameter
  })
}

export function correctionImmigrationInvestigatep5(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p5',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigatep6(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/p6',
    method: 'post',
    data: parameter
  })
}

export function correctionImmigrationInvestigateGetProcess(parameter) {
  return axios({
    url: '/correctionImmigrationInvestigate/getProcess',
    method: 'get',
    params: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询题库
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
export function paperQuestionBankPage(parameter) {
  return axios({
    url: '/paperQuestionBank/page',
    method: 'get',
    params: parameter
  })
}

// 题库列表
export function paperQuestionBankPageDetail(parameter) {
  return axios({
    url: '/paperQuestionBank/pageDetail',
    method: 'get',
    params: parameter
  })
}

/**
 * 题库列表
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
export function paperQuestionBankList(parameter) {
  return axios({
    url: '/paperQuestionBank/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加题库
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
export function paperQuestionBankAdd(parameter) {
  return axios({
    url: '/paperQuestionBank/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑题库
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
export function paperQuestionBankEdit(parameter) {
  return axios({
    url: '/paperQuestionBank/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除题库
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
export function paperQuestionBankDelete(parameter) {
  return axios({
    url: '/paperQuestionBank/delete',
    method: 'post',
    data: parameter
  })
}
// 查看详情
export function paperQuestionBankDetail(parameter) {
  return axios({
    url: '/paperQuestionBank/detail',
    method: 'get',
    params: parameter
  })
}
// 导出
export function paperQuestionBankExport(parameter) {
  return axios({
    url: '/paperQuestionBank/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

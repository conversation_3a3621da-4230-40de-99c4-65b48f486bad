import { axios } from '@/utils/request'

/**
 * 查询人社厅高级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:25
 */
export function irsSeniorProfessionalTitleCertificatePage (parameter) {
  return axios({
    url: '/irsSeniorProfessionalTitleCertificate/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 人社厅高级职称证书列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:25
 */
export function irsSeniorProfessionalTitleCertificateList (parameter) {
  return axios({
    url: '/irsSeniorProfessionalTitleCertificate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加人社厅高级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:25
 */
export function irsSeniorProfessionalTitleCertificateAdd (parameter) {
  return axios({
    url: '/irsSeniorProfessionalTitleCertificate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑人社厅高级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:25
 */
export function irsSeniorProfessionalTitleCertificateEdit (parameter) {
  return axios({
    url: '/irsSeniorProfessionalTitleCertificate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除人社厅高级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:25
 */
export function irsSeniorProfessionalTitleCertificateDelete (parameter) {
  return axios({
    url: '/irsSeniorProfessionalTitleCertificate/delete',
    method: 'post',
    data: parameter
  })
}

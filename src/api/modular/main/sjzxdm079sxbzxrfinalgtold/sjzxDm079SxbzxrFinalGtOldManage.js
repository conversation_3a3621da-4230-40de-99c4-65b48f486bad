import { axios } from '@/utils/request'

/**
 * 查询社区矫正失信被执行人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:58
 */
export function sjzxDm079SxbzxrFinalGtOldPage (parameter) {
  return axios({
    url: '/sjzxDm079SxbzxrFinalGtOld/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 社区矫正失信被执行人信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:58
 */
export function sjzxDm079SxbzxrFinalGtOldList (parameter) {
  return axios({
    url: '/sjzxDm079SxbzxrFinalGtOld/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加社区矫正失信被执行人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:58
 */
export function sjzxDm079SxbzxrFinalGtOldAdd (parameter) {
  return axios({
    url: '/sjzxDm079SxbzxrFinalGtOld/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑社区矫正失信被执行人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:58
 */
export function sjzxDm079SxbzxrFinalGtOldEdit (parameter) {
  return axios({
    url: '/sjzxDm079SxbzxrFinalGtOld/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除社区矫正失信被执行人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:58
 */
export function sjzxDm079SxbzxrFinalGtOldDelete (parameter) {
  return axios({
    url: '/sjzxDm079SxbzxrFinalGtOld/delete',
    method: 'post',
    data: parameter
  })
}

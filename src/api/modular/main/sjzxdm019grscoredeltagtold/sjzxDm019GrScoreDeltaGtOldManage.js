import { axios } from '@/utils/request'

/**
 * 查询矫正对象个人公共信用评价信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:25:57
 */
export function sjzxDm019GrScoreDeltaGtOldPage (parameter) {
  return axios({
    url: '/sjzxDm019GrScoreDeltaGtOld/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象个人公共信用评价信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:25:57
 */
export function sjzxDm019GrScoreDeltaGtOldList (parameter) {
  return axios({
    url: '/sjzxDm019GrScoreDeltaGtOld/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正对象个人公共信用评价信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:25:57
 */
export function sjzxDm019GrScoreDeltaGtOldAdd (parameter) {
  return axios({
    url: '/sjzxDm019GrScoreDeltaGtOld/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象个人公共信用评价信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:25:57
 */
export function sjzxDm019GrScoreDeltaGtOldEdit (parameter) {
  return axios({
    url: '/sjzxDm019GrScoreDeltaGtOld/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象个人公共信用评价信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:25:57
 */
export function sjzxDm019GrScoreDeltaGtOldDelete (parameter) {
  return axios({
    url: '/sjzxDm019GrScoreDeltaGtOld/delete',
    method: 'post',
    data: parameter
  })
}

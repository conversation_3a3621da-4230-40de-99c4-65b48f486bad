import { axios } from '@/utils/request'

/**
 * 查询职业技能等级证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
export function irsVocationalSkillsLevelCertificatePage (parameter) {
  return axios({
    url: '/irsVocationalSkillsLevelCertificate/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 职业技能等级证书列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
export function irsVocationalSkillsLevelCertificateList (parameter) {
  return axios({
    url: '/irsVocationalSkillsLevelCertificate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加职业技能等级证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
export function irsVocationalSkillsLevelCertificateAdd (parameter) {
  return axios({
    url: '/irsVocationalSkillsLevelCertificate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑职业技能等级证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
export function irsVocationalSkillsLevelCertificateEdit (parameter) {
  return axios({
    url: '/irsVocationalSkillsLevelCertificate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除职业技能等级证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
export function irsVocationalSkillsLevelCertificateDelete (parameter) {
  return axios({
    url: '/irsVocationalSkillsLevelCertificate/delete',
    method: 'post',
    data: parameter
  })
}

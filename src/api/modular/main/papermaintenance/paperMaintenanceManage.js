import { axios } from '@/utils/request'

/**
 * 查询量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
export function paperMaintenancePage(parameter) {
  return axios({
    url: '/paperMaintenance/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 量卷维护列表
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
export function paperMaintenanceList(parameter) {
  return axios({
    url: '/paperMaintenance/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
export function paperMaintenanceAdd(parameter) {
  return axios({
    url: '/paperMaintenance/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
export function paperMaintenanceEdit(parameter) {
  return axios({
    url: '/paperMaintenance/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
export function paperMaintenanceDelete(parameter) {
  return axios({
    url: '/paperMaintenance/delete',
    method: 'post',
    data: parameter
  })
}

// 详情
export function paperMaintenanceDetail(parameter) {
  return axios({
    url: '/paperMaintenance/detail',
    method: 'get',
    params: parameter
  })
}

// 设置状态
export function paperMaintenanceSetStatus(parameter) {
  return axios({
    url: '/paperMaintenance/setStatus',
    method: 'get',
    params: parameter
  })
}

// 导出
export function paperMaintenanceExport(parameter) {
  return axios({
    url: '/paperMaintenance/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

// 预览
export function paperMaintenancePreview(parameter) {
  return axios({
    url: '/paperMaintenance/exportCustomQuestionsAsPdf',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

// 获取类型树
export function paperMaintenanceGetTypeTree(parameter) {
  return axios({
    url: '/sysDictData/tree',
    method: 'get',
    params: parameter
  })
}

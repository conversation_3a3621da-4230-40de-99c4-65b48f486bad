import { axios } from '@/utils/request'

/**
 * 查询走访任务下派
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function interviewJobDistributePage (parameter) {
  return axios({
    url: '/interviewJobDistribute/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 走访任务下派列表
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function interviewJobDistributeList (parameter) {
  return axios({
    url: '/interviewJobDistribute/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加走访任务下派
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function interviewJobDistributeAdd (parameter) {
  return axios({
    url: '/interviewJobDistribute/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑走访任务下派
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function interviewJobDistributeEdit (parameter) {
  return axios({
    url: '/interviewJobDistribute/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除走访任务下派
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function interviewJobDistributeDelete (parameter) {
  return axios({
    url: '/interviewJobDistribute/delete',
    method: 'post',
    data: parameter
  })
}

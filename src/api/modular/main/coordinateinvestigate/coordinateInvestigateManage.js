import { axios } from '@/utils/request'

/**
 * 查询调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function coordinateInvestigatePage(parameter) {
  return axios({
    url: '/coordinateInvestigate/page',
    method: 'get',
    params: parameter
  })
}
/**
 * 调查评估协查列表
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function coordinateInvestigateList(parameter) {
  return axios({
    url: '/coordinateInvestigate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function coordinateInvestigateAdd(parameter) {
  return axios({
    url: '/coordinateInvestigate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function coordinateInvestigateEdit(parameter) {
  return axios({
    url: '/coordinateInvestigate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 受理
 */
export function coordinateInvestigateAccept(parameter) {
  return axios({
    url: '/coordinateInvestigate/accept',
    method: 'post',
    data: parameter
  })
}

export function coordinateSuperviseReceiveAdd(parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/add',
    method: 'post',
    data: parameter
  })
}

export function coordinateSuperviseSubmitAdd(parameter) {
  return axios({
    url: '/coordinateSuperviseSubmit/add',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigateSendPage(parameter) {
  return axios({
    url: '/coordinateInvestigateSend/page',
    method: 'get',
    params: parameter
  })
}

export function coordinateInvestigateSendadd(parameter) {
  return axios({
    url: '/coordinateInvestigateSend/add',
    method: 'post',
    data: parameter
  })
}

export function correctionPlacechangeImmigrationAdd(parameter) {
  return axios({
    url: '/correctionPlacechangeImmigration/edit',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigateFeedback(parameter) {
  return axios({
    url: '/coordinateInvestigate/feedback',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
export function coordinateInvestigateDelete(parameter) {
  return axios({
    url: '/coordinateInvestigate/delete',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigatep2(parameter) {
  return axios({
    url: '/coordinateInvestigate/p2',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigatep3(parameter) {
  return axios({
    url: '/coordinateInvestigate/p3',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigatep4(parameter) {
  return axios({
    url: '/coordinateInvestigate/p4',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigatelastP3(parameter) {
  return axios({
    url: '/coordinateInvestigate/lastP3',
    method: 'get',
    params: parameter
  })
}

export function coordinateInvestigatep5(parameter) {
  return axios({
    url: '/coordinateInvestigate/p5',
    method: 'post',
    data: parameter
  })
}

export function coordinateInvestigatep6(parameter) {
  return axios({
    url: '/coordinateInvestigate/p6',
    method: 'post',
    data: parameter
  })
}
export function coordinateInvestigategetProcess(parameter) {
  return axios({
    url: '/coordinateInvestigate/getProcess',
    method: 'get',
    params: parameter
  })
}

export function CoordinateInvestigatedetail(parameter) {
  return axios({
    url: '/coordinateInvestigate/detail',
    method: 'get',
    params: parameter
  })
}

// 获取迁出对象列表
export function placechangeTransProvPage(parameter) {
  return axios({
    url: '/placeChangeDc/page',
    method: 'get',
    params: parameter
  })
}

// 获取迁出对象详细信息
export function placechangeTransProvInfo(parameter) {
  return axios({
    url: '/placechangeTransProv/fillInfo',
    method: 'get',
    params: parameter
  })
}

// 迁出迁入列表
export function placechangeTransProvList(parameter) {
  return axios({
    url: '/placechangeTransProv/page',
    method: 'get',
    params: parameter
  })
}

// 新增迁出
export function placechangeTransProvAdd(parameter) {
  return axios({
    url: '/placechangeTransProv/add',
    method: 'post',
    data: parameter
  })
}
// 移交提送
export function placechangeTransProvSubmit(parameter) {
  return axios({
    url: '/placechangeTransProv/trans',
    method: 'post',
    data: parameter
  })
}

// 迁出详情
export function placechangeTransProvDetail(parameter) {
  return axios({
    url: '/placechangeTransProv/detail',
    method: 'get',
    params: parameter
  })
}

// 迁入审核
export function placechangeTransProvAudit(parameter) {
  return axios({
    url: '/placechangeTransProv/approval',
    method: 'post',
    data: parameter
  })
}

// 迁入反馈
export function placechangeTransProvFeedback(parameter) {
  return axios({
    url: '/placechangeTransProv/feedback',
    method: 'post',
    data: parameter
  })
}

export function placechangeTransProvExport (parameter) {
  return axios({
    url: '/placechangeTransProv/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

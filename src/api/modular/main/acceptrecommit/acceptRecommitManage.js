import { axios } from '@/utils/request'

/**
 * 查询公安再犯罪协同接收表
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
export function acceptRecommitPage (parameter) {
  return axios({
    url: '/acceptRecommit/page',
    method: 'get',
    params: parameter
  })
}

export function logPage (parameter) {
  return axios({
    url: '/acceptRecommit/logPage',
    method: 'get',
    params: parameter
  })
}

/**
 * 编辑公安再犯罪协同接收表
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
export function acceptRecommitEdit (parameter) {
  return axios({
    url: '/acceptRecommit/edit',
    method: 'post',
    data: parameter
  })
}

export function acceptRecommitDelete (parameter) {
  return axios({
    url: '/acceptRecommit/delete',
    method: 'post',
    data: parameter
  })
}

export function acceptRecommitExport (parameter) {
  return axios({
    url: '/acceptRecommit/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function logExport (parameter) {
  return axios({
    url: '/acceptRecommit/logExport',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function getNoSignPdf44002 (parameter) {
  return axios({
    url: '/acceptRecommit/getNoSignPdf',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function getSignedPdf44002 (parameter) {
  return axios({
    url: '/acceptRecommit/getSignedPdf',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

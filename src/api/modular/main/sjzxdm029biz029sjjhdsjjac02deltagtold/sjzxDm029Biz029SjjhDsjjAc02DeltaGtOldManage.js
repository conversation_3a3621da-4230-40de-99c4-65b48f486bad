import { axios } from '@/utils/request'

/**
 * 查询矫正对象社会保险参保信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:19
 */
export function sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldPage (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOld/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象社会保险参保信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:19
 */
export function sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldList (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOld/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正对象社会保险参保信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:19
 */
export function sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldAdd (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOld/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象社会保险参保信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:19
 */
export function sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldEdit (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOld/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象社会保险参保信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:26:19
 */
export function sjzxDm029Biz029SjjhDsjjAc02DeltaGtOldDelete (parameter) {
  return axios({
    url: '/sjzxDm029Biz029SjjhDsjjAc02DeltaGtOld/delete',
    method: 'post',
    data: parameter
  })
}

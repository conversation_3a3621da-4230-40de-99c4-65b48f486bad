import { axios } from '@/utils/request'

/**
 * 查询收监执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
export function acceptPrisonExecutePage (parameter) {
  return axios({
    url: '/acceptPrisonExecute/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 收监执行接收表列表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
export function acceptPrisonExecuteList (parameter) {
  return axios({
    url: '/acceptPrisonExecute/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加收监执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
export function acceptPrisonExecuteAdd (parameter) {
  return axios({
    url: '/acceptPrisonExecute/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑收监执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
export function acceptPrisonExecuteEdit (parameter) {
  return axios({
    url: '/acceptPrisonExecute/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除收监执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
export function acceptPrisonExecuteDelete (parameter) {
  return axios({
    url: '/acceptPrisonExecute/delete',
    method: 'post',
    data: parameter
  })
}

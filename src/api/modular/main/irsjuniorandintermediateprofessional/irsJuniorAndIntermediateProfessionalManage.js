import { axios } from '@/utils/request'

/**
 * 查询省人社厅初中级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:28:58
 */
export function irsJuniorAndIntermediateProfessionalPage (parameter) {
  return axios({
    url: '/irsJuniorAndIntermediateProfessional/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 省人社厅初中级职称证书列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:28:58
 */
export function irsJuniorAndIntermediateProfessionalList (parameter) {
  return axios({
    url: '/irsJuniorAndIntermediateProfessional/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加省人社厅初中级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:28:58
 */
export function irsJuniorAndIntermediateProfessionalAdd (parameter) {
  return axios({
    url: '/irsJuniorAndIntermediateProfessional/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑省人社厅初中级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:28:58
 */
export function irsJuniorAndIntermediateProfessionalEdit (parameter) {
  return axios({
    url: '/irsJuniorAndIntermediateProfessional/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除省人社厅初中级职称证书
 *
 * <AUTHOR>
 * @date 2024-01-11 09:28:58
 */
export function irsJuniorAndIntermediateProfessionalDelete (parameter) {
  return axios({
    url: '/irsJuniorAndIntermediateProfessional/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询省人社厅专业技术人员资格证书

 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:23
 */
export function irsProfessionalTechnicalCertificatePage (parameter) {
  return axios({
    url: '/irsProfessionalTechnicalCertificate/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 省人社厅专业技术人员资格证书
列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:23
 */
export function irsProfessionalTechnicalCertificateList (parameter) {
  return axios({
    url: '/irsProfessionalTechnicalCertificate/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加省人社厅专业技术人员资格证书

 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:23
 */
export function irsProfessionalTechnicalCertificateAdd (parameter) {
  return axios({
    url: '/irsProfessionalTechnicalCertificate/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑省人社厅专业技术人员资格证书

 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:23
 */
export function irsProfessionalTechnicalCertificateEdit (parameter) {
  return axios({
    url: '/irsProfessionalTechnicalCertificate/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除省人社厅专业技术人员资格证书

 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:23
 */
export function irsProfessionalTechnicalCertificateDelete (parameter) {
  return axios({
    url: '/irsProfessionalTechnicalCertificate/delete',
    method: 'post',
    data: parameter
  })
}

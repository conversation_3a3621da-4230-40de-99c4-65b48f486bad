import { axios } from '@/utils/request'

/**
 * 查询智慧医保-医保个人参保信息查询

 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:37
 */
export function irsIndividualMedicalInsurancePage (parameter) {
  return axios({
    url: '/irsIndividualMedicalInsurance/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 智慧医保-医保个人参保信息查询
列表
 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:37
 */
export function irsIndividualMedicalInsuranceList (parameter) {
  return axios({
    url: '/irsIndividualMedicalInsurance/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加智慧医保-医保个人参保信息查询

 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:37
 */
export function irsIndividualMedicalInsuranceAdd (parameter) {
  return axios({
    url: '/irsIndividualMedicalInsurance/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑智慧医保-医保个人参保信息查询

 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:37
 */
export function irsIndividualMedicalInsuranceEdit (parameter) {
  return axios({
    url: '/irsIndividualMedicalInsurance/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除智慧医保-医保个人参保信息查询

 *
 * <AUTHOR>
 * @date 2024-01-12 14:21:37
 */
export function irsIndividualMedicalInsuranceDelete (parameter) {
  return axios({
    url: '/irsIndividualMedicalInsurance/delete',
    method: 'post',
    data: parameter
  })
}

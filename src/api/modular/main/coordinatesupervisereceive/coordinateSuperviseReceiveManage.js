import { axios } from '@/utils/request'

/**
 * 查询接收外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
export function coordinateSuperviseReceivePage (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 接收外出监管协同列表
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
export function coordinateSuperviseReceiveList (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加接收外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
export function coordinateSuperviseReceiveAdd (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑接收外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
export function coordinateSuperviseReceiveEdit (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/edit',
    method: 'post',
    data: parameter
  })
}
export function coordinateSuperviseReceiveAcept (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/accept',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除接收外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
export function coordinateSuperviseReceiveDelete (parameter) {
  return axios({
    url: '/coordinateSuperviseReceive/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询发送提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
export function sendApplyArrestPage (parameter) {
  return axios({
    url: '/sendApplyArrest/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加发送提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
export function sendApplyArrestAdd (parameter) {
  return axios({
    url: '/sendApplyArrest/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑发送提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
export function sendApplyArrestEdit (parameter) {
  return axios({
    url: '/sendApplyArrest/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除发送提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
export function sendApplyArrestDelete (parameter) {
  return axios({
    url: '/sendApplyArrest/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询提请撤销假释
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
export function revocationParolePage (parameter) {
  return axios({
    url: '/revocationParole/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 提请撤销假释列表
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
export function revocationParoleList (parameter) {
  return axios({
    url: '/revocationParole/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加提请撤销假释
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
export function revocationParoleAdd (parameter) {
  return axios({
    url: '/revocationParole/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑提请撤销假释
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
export function revocationParoleEdit (parameter) {
  return axios({
    url: '/revocationParole/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除提请撤销假释
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
export function revocationParoleDelete (parameter) {
  return axios({
    url: '/revocationParole/delete',
    method: 'post',
    data: parameter
  })
}

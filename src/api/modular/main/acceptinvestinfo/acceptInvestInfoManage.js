import { axios } from '@/utils/request'

/**
 * 查询调查评估信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
export function acceptInvestInfoPage(parameter) {
  return axios({
    url: '/acceptInvestInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 编辑调查评估信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
export function acceptInvestInfoEdit(parameter) {
  return axios({
    url: '/acceptInvestInfo/edit',
    method: 'post',
    data: parameter
  })
}

export function acceptInvestInfoSetStatus(parameter) {
  return axios({
    url: '/acceptInvestInfo/setStatus',
    method: 'post',
    data: parameter
  })
}

export function acceptInvestInfoTerminate(parameter) {
  return axios({
    url: '/acceptInvestInfo/terminate',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除调查评估信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
export function acceptInvestInfoDelete(parameter) {
  return axios({
    url: '/acceptInvestInfo/delete',
    method: 'post',
    data: parameter
  })
}

export function acceptInvestInfoDetail(parameter) {
  return axios({
    url: '/acceptInvestInfo/detail',
    method: 'get',
    params: parameter
  })
}

export function acceptInvestInfoGetWdResult(parameter) {
  return axios({
    url: '/acceptInvestInfo/getWdResult',
    method: 'get',
    params: parameter
  })
}

export function acceptInvestInfoStatistics(parameter) {
  return axios({
    url: '/acceptInvestInfo/statistics',
    method: 'get',
    params: parameter
  })
}

export function acceptInvestInfoStatisticsExport (parameter) {
  return axios({
    url: '/acceptInvestInfo/statistics/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function acceptInvestInfoStep(parameter) {
  return axios({
    url: '/acceptInvestInfo/step',
    method: 'get',
    params: parameter
  })
}

export function getHzcl(parameter) {
  return axios({
    url: '/acceptInvestInfo/gethzcl',
    method: 'get',
    params: parameter
  })
}

export function getNoSignPdf31001 (parameter) {
  return axios({
    url: '/acceptInvestInfo/getNoSignPdf',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

export function getSignedPdf31001 (parameter) {
  return axios({
    url: '/acceptInvestInfo/getSignedPdf',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

import { axios } from '@/utils/request'

/**
 * 查询发送暂外续报
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
export function sendTopExtendDeclarePage(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 发送暂外续报列表
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
export function sendTopExtendDeclareList(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加发送暂外续报
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
export function sendTopExtendDeclareAdd(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/add',
    method: 'post',
    data: parameter
  })
}
/**
 * 编辑发送暂外续报
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
export function sendTopExtendDeclareEdit(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除发送暂外续报
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
export function sendTopExtendDeclareDelete(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/delete',
    method: 'post',
    data: parameter
  })
}

export function sendTopExtendDeclareDetail(parameter) {
  return axios({
    url: '/sendTopExtendDeclare/detail',
    method: 'get',
    params: parameter
  })
}

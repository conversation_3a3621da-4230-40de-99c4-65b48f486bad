import { axios } from '@/utils/request'

// 调查评估_管理评估清单下一步
export function groupInvestigateList(data) {
  return axios({
    url: '/investigationInfo/groupInvestigateList',
    method: 'post',
    data
  })
}
// 调查评估_调查接收
export function groupInvestigateAccept(data) {
  return axios({
    url: '/investigationInfo/groupInvestigateAccept',
    method: 'post',
    data
  })
}
// 调查评估_调查提交
export function groupInvestigateCommit(data) {
  return axios({
    url: '/investigationInfo/groupInvestigateCommit',
    method: 'post',
    data
  })
}

// 调查评估_笔录新增
export function investigationInfoTranscriptAdd(data) {
  return axios({
    url: '/investigationInfo/transcriptAdd',
    method: 'post',
    data
  })
}
// 调查评估_笔录提交
export function investigationInfoCommit(data) {
  return axios({
    url: '/investigationInfo/transcriptCommit',
    method: 'post',
    data
  })
}
// 调查评估_笔录列表
export function investigationInfoTranscriptList(data) {
  return axios({
    url: '/investigationInfo/transcriptList',
    method: 'get',
    params: data
  })
}

// 生成笔录
export function investigationInfoTranscriptGenerate(data) {
  return axios({
    url: '/investigationInfo/preTranscriptList',
    method: 'get',
    params: data
  })
}

// 调查评估_笔录问题详情
export function investigationInfoTranscriptById(data) {
  return axios({
    url: '/investigationInfo/getTranscriptById',
    method: 'get',
    params: data
  })
}

// 调查评估_笔录人员详情
export function investigationInfoTranscriptFillInfoById(data) {
  return axios({
    url: '/investigationInfo/getTranscriptFillInfoById',
    method: 'get',
    params: data
  })
}

// 删除笔录
export function investigationInfoTranscriptDelete(data) {
  return axios({
    url: '/investigationInfo/transcriptDelete',
    method: 'post',
    data
  })
}

// 获取笔录类型
export function investigationInfoTranscriptType(data) {
  return axios({
    url: '/investigationInfo/getTranscriptTypeById',
    method: 'get',
    params: data
  })
}

import {axios} from '@/utils/request'

/**
 * 设备管理
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDevicePage(parameter) {
  return axios({
    url: '/investigationDevice/page',
    method: 'get',
    params: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDeviceList(parameter) {
  return axios({
    url: '/investigationDevice/list',
    method: 'get',
    params: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDeviceAdd(parameter) {
  return axios({
    url: '/investigationDevice/add',
    method: 'post',
    data: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDeviceEdit(parameter) {
  return axios({
    url: '/investigationDevice/edit',
    method: 'post',
    data: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function unbind(parameter) {
  return axios({
    url: '/investigationDevice/unbind',
    method: 'post',
    data: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDeviceDelete(parameter) {
  return axios({
    url: '/investigationDevice/delete',
    method: 'post',
    data: parameter
  })
}

/**
 *
 * <AUTHOR>
 * @date 2022-07-07 10:35:55
 */
export function investigationDeviceExport(parameter) {
  return axios({
    url: '/investigationDevice/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

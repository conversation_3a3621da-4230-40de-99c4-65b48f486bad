import { axios } from '@/utils/request'

/**
 * 查询提请撤销缓刑
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
export function revocationProbationPage (parameter) {
  return axios({
    url: '/revocationProbation/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 提请撤销缓刑列表
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
export function revocationProbationList (parameter) {
  return axios({
    url: '/revocationProbation/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加提请撤销缓刑
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
export function revocationProbationAdd (parameter) {
  return axios({
    url: '/revocationProbation/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑提请撤销缓刑
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
export function revocationProbationEdit (parameter) {
  return axios({
    url: '/revocationProbation/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除提请撤销缓刑
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
export function revocationProbationDelete (parameter) {
  return axios({
    url: '/revocationProbation/delete',
    method: 'post',
    data: parameter
  })
}

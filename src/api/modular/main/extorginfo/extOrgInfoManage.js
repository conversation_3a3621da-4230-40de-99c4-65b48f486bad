import { axios } from '@/utils/request'

/**
 * 查询外部单位信息
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
export function extOrgInfoPage (parameter) {
  return axios({
    url: '/extOrgInfo/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 外部单位信息列表
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
export function extOrgInfoList (parameter) {
  return axios({
    url: '/extOrgInfo/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加外部单位信息
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
export function extOrgInfoAdd (parameter) {
  return axios({
    url: '/extOrgInfo/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑外部单位信息
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
export function extOrgInfoEdit (parameter) {
  return axios({
    url: '/extOrgInfo/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除外部单位信息
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
export function extOrgInfoDelete (parameter) {
  return axios({
    url: '/extOrgInfo/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request';

/**
 * 同案犯信息
 * @param parameter
 * @returns {AxiosPromise}
 */
export function acceptCorrectionAccomplicePage (parameter) {
  return axios({
    url: '/acceptCorrectionAccomplice/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 文书信息
 * @param parameter
 * @returns {AxiosPromise}
 */
export function acceptCorrectionDocPage (parameter) {
  return axios({
    url: '/acceptCorrectionDoc/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 家庭成员信息
 * @param parameter
 * @returns {AxiosPromise}
 */
export function acceptCorrectionFamilyPage (parameter) {
  return axios({
    url: '/acceptCorrectionFamily/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 禁止令信息
 * @param parameter
 * @returns {AxiosPromise}
 */
export function acceptCorrectionForbidPage (parameter) {
  return axios({
    url: '/acceptCorrectionForbid/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 前科信息
 * @param parameter
 * @returns {AxiosPromise}
 */
export function acceptCriminalRecordPage (parameter) {
  return axios({
    url: '/acceptCriminalRecord/page',
    method: 'get',
    params: parameter
  })
}

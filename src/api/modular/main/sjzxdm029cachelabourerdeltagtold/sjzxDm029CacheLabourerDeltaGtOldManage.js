import { axios } from '@/utils/request'

/**
 * 查询矫正对象农民工信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:45
 */
export function sjzxDm029CacheLabourerDeltaGtOldPage (parameter) {
  return axios({
    url: '/sjzxDm029CacheLabourerDeltaGtOld/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 矫正对象农民工信息列表
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:45
 */
export function sjzxDm029CacheLabourerDeltaGtOldList (parameter) {
  return axios({
    url: '/sjzxDm029CacheLabourerDeltaGtOld/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加矫正对象农民工信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:45
 */
export function sjzxDm029CacheLabourerDeltaGtOldAdd (parameter) {
  return axios({
    url: '/sjzxDm029CacheLabourerDeltaGtOld/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑矫正对象农民工信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:45
 */
export function sjzxDm029CacheLabourerDeltaGtOldEdit (parameter) {
  return axios({
    url: '/sjzxDm029CacheLabourerDeltaGtOld/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除矫正对象农民工信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:27:45
 */
export function sjzxDm029CacheLabourerDeltaGtOldDelete (parameter) {
  return axios({
    url: '/sjzxDm029CacheLabourerDeltaGtOld/delete',
    method: 'post',
    data: parameter
  })
}

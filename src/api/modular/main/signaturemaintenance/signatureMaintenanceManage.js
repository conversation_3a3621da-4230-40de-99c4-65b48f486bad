import { axios } from '@/utils/request'

/**
 * 查询签章维护表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
export function signatureMaintenancePage (parameter) {
  return axios({
    url: '/signatureMaintenance/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 签章维护表列表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
export function signatureMaintenanceList (parameter) {
  return axios({
    url: '/signatureMaintenance/list',
    method: 'get',
    params: parameter
  })
}

export function signatureMaintenanceCheck (parameter) {
  return axios({
    url: '/signatureMaintenance/check',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加签章维护表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
export function signatureMaintenanceAdd (parameter) {
  return axios({
    url: '/signatureMaintenance/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑签章维护表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
export function signatureMaintenanceEdit (parameter) {
  return axios({
    url: '/signatureMaintenance/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除签章维护表
 *
 * <AUTHOR>
 * @date 2024-01-19 11:21:29
 */
export function signatureMaintenanceDelete (parameter) {
  return axios({
    url: '/signatureMaintenance/delete',
    method: 'post',
    data: parameter
  })
}

export function signatureMaintenanceExport (parameter) {
  return axios({
    url: '/signatureMaintenance/export',
    method: 'get',
    params: parameter,
    responseType: 'blob'
  })
}

export function signatureMaintenanceChangeStatus (parameter) {
  return axios({
    url: '/signatureMaintenance/changeStatus',
    method: 'post',
    data: parameter
  })
}

export function getSignStatus (parameter) {
  return axios({
    url: '/signatureMaintenance/getSignStatus',
    method: 'get',
    params: parameter
  })
}

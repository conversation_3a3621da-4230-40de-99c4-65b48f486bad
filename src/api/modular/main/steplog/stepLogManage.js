import { axios } from '@/utils/request'

/**
 * 查询流程记录
 *
 * <AUTHOR>
 * @date 2023-06-05 06:03:44
 */
export function stepLogPage (parameter) {
  return axios({
    url: '/stepLog/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 流程记录列表
 *
 * <AUTHOR>
 * @date 2023-06-05 06:03:44
 */
export function stepLogList (parameter) {
  return axios({
    url: '/stepLog/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加流程记录
 *
 * <AUTHOR>
 * @date 2023-06-05 06:03:44
 */
export function stepLogAdd (parameter) {
  return axios({
    url: '/stepLog/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑流程记录
 *
 * <AUTHOR>
 * @date 2023-06-05 06:03:44
 */
export function stepLogEdit (parameter) {
  return axios({
    url: '/stepLog/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除流程记录
 *
 * <AUTHOR>
 * @date 2023-06-05 06:03:44
 */
export function stepLogDelete (parameter) {
  return axios({
    url: '/stepLog/delete',
    method: 'post',
    data: parameter
  })
}

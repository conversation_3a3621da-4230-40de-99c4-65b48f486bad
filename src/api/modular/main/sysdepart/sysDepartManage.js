import { axios } from '@/utils/request'

/**
 * 查询组织机构表
 *
 * <AUTHOR>
 * @date 2022-06-20 11:25:09
 */
export function sysDepartPage (parameter) {
  return axios({
    url: '/sysDepart/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 组织机构表列表
 *
 * <AUTHOR>
 * @date 2022-06-20 11:25:09
 */
export function sysDepartList (parameter) {
  return axios({
    url: '/sysDepart/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加组织机构表
 *
 * <AUTHOR>
 * @date 2022-06-20 11:25:09
 */
export function sysDepartAdd (parameter) {
  return axios({
    url: '/sysDepart/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑组织机构表
 *
 * <AUTHOR>
 * @date 2022-06-20 11:25:09
 */
export function sysDepartEdit (parameter) {
  return axios({
    url: '/sysDepart/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除组织机构表
 *
 * <AUTHOR>
 * @date 2022-06-20 11:25:09
 */
export function sysDepartDelete (parameter) {
  return axios({
    url: '/sysDepart/delete',
    method: 'post',
    data: parameter
  })
}

import { axios } from '@/utils/request'

/**
 * 查询发送变更执行地通知
 *
 * <AUTHOR>
 * @date 2023-09-13 09:30:59
 */
export function sendPlaceChangePage (parameter) {
  return axios({
    url: '/sendPlaceChange/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 发送变更执行地通知列表
 *
 * <AUTHOR>
 * @date 2023-09-13 09:30:59
 */
export function sendPlaceChangeList (parameter) {
  return axios({
    url: '/sendPlaceChange/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加发送变更执行地通知
 *
 * <AUTHOR>
 * @date 2023-09-13 09:30:59
 */
export function sendPlaceChangeAdd (parameter) {
  return axios({
    url: '/sendPlaceChange/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑发送变更执行地通知
 *
 * <AUTHOR>
 * @date 2023-09-13 09:30:59
 */
export function sendPlaceChangeEdit (parameter) {
  return axios({
    url: '/sendPlaceChange/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除发送变更执行地通知
 *
 * <AUTHOR>
 * @date 2023-09-13 09:30:59
 */
export function sendPlaceChangeDelete (parameter) {
  return axios({
    url: '/sendPlaceChange/delete',
    method: 'post',
    data: parameter
  })
}

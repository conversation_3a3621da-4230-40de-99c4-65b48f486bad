import { axios } from '@/utils/request'

/**
 * 查询低保救助信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:26
 */
export function irsSubsistenceAssistanceInformationPage (parameter) {
  return axios({
    url: '/irsSubsistenceAssistanceInformation/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 低保救助信息列表
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:26
 */
export function irsSubsistenceAssistanceInformationList (parameter) {
  return axios({
    url: '/irsSubsistenceAssistanceInformation/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加低保救助信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:26
 */
export function irsSubsistenceAssistanceInformationAdd (parameter) {
  return axios({
    url: '/irsSubsistenceAssistanceInformation/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑低保救助信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:26
 */
export function irsSubsistenceAssistanceInformationEdit (parameter) {
  return axios({
    url: '/irsSubsistenceAssistanceInformation/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除低保救助信息
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:26
 */
export function irsSubsistenceAssistanceInformationDelete (parameter) {
  return axios({
    url: '/irsSubsistenceAssistanceInformation/delete',
    method: 'post',
    data: parameter
  })
}

{"name": "vue-antd-pro", "version": "2.1.2", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "build:preview": "vue-cli-service build --mode preview", "postinstall": "patch-package"}, "dependencies": {"@antv/data-set": "^0.10.2", "@jiamt/component-library": "^1.3.60", "ant-design-vue": "^1.7.8", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.6", "core-js": "^3.1.2", "crypto-js": "^4.0.0", "default-passive-events": "^1.0.10", "docx-preview": "^0.1.20", "echarts": "^5.4.3", "element-ui": "^2.15.14", "enquire.js": "^2.1.6", "font-awesome": "^4.7.0", "jquery": "^3.5.1", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "mockjs2": "1.0.8", "moment": "^2.24.0", "nprogress": "^0.2.0", "patch-package": "^8.0.0", "print-js": "^1.0.63", "raphael": "^2.3.0", "screenfull": "^5.1.0", "sortablejs": "^1.15.6", "unplugin-auto-import": "^0.18.5", "v-distpicker": "^1.3.3", "viser-vue": "^2.4.6", "vue": "^2.7.16", "vue-clipboard2": "^0.2.1", "vue-codemirror-lite": "^1.0.4", "vue-count-to": "^1.0.13", "vue-cropper": "0.4.9", "vue-ls": "^3.2.1", "vue-pdf": "^4.3.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-svg-component-runtime": "^1.0.1", "vuedraggable": "^2.23.2", "vuex": "^3.1.1", "wangeditor": "^3.1.1"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-prettier": "^5.0.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^6.7.2", "eslint-plugin-html": "^5.0.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "prettier": "^1.18.2", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.7.16", "webpack-theme-color-replacer": "^1.5.2"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/ant-design-pro-vue"}, "main": ".eslintrc.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC", "description": ""}
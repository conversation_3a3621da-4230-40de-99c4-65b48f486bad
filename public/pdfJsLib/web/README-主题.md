# PDF.js Viewer 现代化主题

## 概述

这个更新版本的 PDF.js Viewer 包含了现代化的深色和浅色主题，具有以下特性：

## 🎨 主要改进

### 配色方案
- **浅色模式**: 使用现代的蓝灰色调配色方案，提供清爽的视觉体验
- **深色模式**: 采用深蓝色背景，提供舒适的夜间阅读体验
- **统一的强调色**: 使用蓝色 (`rgba(59, 130, 246, 1)`) 作为主要强调色

### 现代化设计元素
- **圆角设计**: 按钮和面板采用现代的圆角设计
- **平滑过渡**: 所有交互元素都有平滑的过渡动画
- **毛玻璃效果**: 弹出面板具有backdrop-filter模糊效果
- **改进的焦点样式**: 更清晰的键盘导航体验

## 🔧 使用方法

### 自动主题切换
系统会自动根据用户的系统偏好设置选择主题：
- 如果系统设置为深色模式，PDF Viewer 将自动使用深色主题
- 如果系统设置为浅色模式，PDF Viewer 将使用浅色主题

### 手动主题切换
如果你想添加手动主题切换功能，可以在 `viewer.html` 中引入主题切换脚本：

```html
<script src="theme-toggle.js"></script>
```

这将在工具栏中添加一个主题切换按钮。

### 强制使用深色模式
如果你想强制使用深色模式，可以在页面的 `<body>` 标签上添加 `dark-mode` 类：

```html
<body class="dark-mode">
```

或者通过 JavaScript 添加：

```javascript
document.body.classList.add('dark-mode');
```

## 🎯 配色详情

### 浅色模式配色
- 主要文字颜色: `rgba(15, 23, 42, 1)` (深蓝灰)
- 背景色: `rgba(248, 250, 252, 1)` (淡蓝灰)
- 工具栏背景: `rgba(255, 255, 255, 1)` (白色)
- 强调色: `rgba(59, 130, 246, 1)` (蓝色)

### 深色模式配色
- 主要文字颜色: `rgba(248, 250, 252, 1)` (淡灰白)
- 背景色: `rgba(26, 32, 44, 1)` (深灰色)
- 工具栏背景: `rgba(45, 55, 72, 1)` (中灰色)
- 强调色: `rgba(59, 130, 246, 1)` (蓝色)

## 🛠️ 自定义

### 修改配色
如果你想自定义配色，可以修改 `viewer.css` 中的 CSS 变量：

```css
:root {
  --main-color: 你的颜色;
  --body-bg-color: 你的颜色;
  --toolbar-bg-color: 你的颜色;
  /* 等等... */
}
```

### 禁用现代化效果
如果你不需要现代化的视觉效果（如圆角、过渡动画等），可以注释掉或删除 CSS 文件末尾的相关样式。

## 📱 响应式支持

主题在所有屏幕尺寸下都能正常工作，包括：
- 桌面电脑
- 平板电脑
- 手机

## 🔄 版本兼容性

这些修改保持了与原始 PDF.js Viewer 的完全兼容性：
- 所有原有功能保持不变
- 布局结构没有改变
- 只是优化了视觉样式

## 💡 提示

- 主题偏好会保存在浏览器的 localStorage 中
- 用户的选择会在下次访问时自动应用
- 如果用户没有手动选择主题，系统会遵循系统偏好设置 
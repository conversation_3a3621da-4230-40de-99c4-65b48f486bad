/**
 * PDF.js Viewer 主题切换器
 * 提供深色和浅色模式的切换功能
 */

class ThemeToggler {
  constructor() {
    this.isDarkMode = this.getStoredTheme() === 'dark' ||
                     (!this.getStoredTheme() && window.matchMedia('(prefers-color-scheme: dark)').matches);
    this.init();
  }

  init() {
    // 应用初始主题
    this.applyTheme(this.isDarkMode);

    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!this.getStoredTheme()) {
        this.isDarkMode = e.matches;
        this.applyTheme(this.isDarkMode);
      }
    });

    // 创建主题切换按钮
    this.createToggleButton();
  }

  getStoredTheme() {
    return localStorage.getItem('pdf-viewer-theme');
  }

  setStoredTheme(theme) {
    localStorage.setItem('pdf-viewer-theme', theme);
  }

  applyTheme(isDark) {
    const body = document.body;
    if (isDark) {
      body.classList.add('dark-mode');
    } else {
      body.classList.remove('dark-mode');
    }
  }

  toggle() {
    this.isDarkMode = !this.isDarkMode;
    this.applyTheme(this.isDarkMode);
    this.setStoredTheme(this.isDarkMode ? 'dark' : 'light');

    // 更新按钮图标
    this.updateToggleButton();
  }

  createToggleButton() {
    // 检查是否已存在按钮
    if (document.getElementById('themeToggle')) return;

    const button = document.createElement('button');
    button.id = 'themeToggle';
    button.className = 'toolbarButton';
    button.title = '切换主题';
    button.setAttribute('tabindex', '50');

    // 创建按钮内容
    const span = document.createElement('span');
    span.textContent = '主题';
    button.appendChild(span);

    // 添加点击事件
    button.addEventListener('click', () => this.toggle());

    // 将按钮添加到工具栏
    const toolbarRight = document.getElementById('toolbarViewerRight');
    if (toolbarRight) {
      // 在下载按钮之后插入
      const downloadButton = document.getElementById('download');
      if (downloadButton) {
        toolbarRight.insertBefore(button, downloadButton.nextSibling);
      } else {
        toolbarRight.appendChild(button);
      }
    }

    this.updateToggleButton();
  }

  updateToggleButton() {
    const button = document.getElementById('themeToggle');
    if (button) {
      button.title = this.isDarkMode ? '切换到浅色模式' : '切换到深色模式';
      // 你可以在这里添加图标切换逻辑
    }
  }
}

// 当 DOM 加载完成后初始化主题切换器
document.addEventListener('DOMContentLoaded', () => {
  window.themeToggler = new ThemeToggler();
});

// 也可以在窗口加载后初始化（以防 DOMContentLoaded 已经触发）
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (!window.themeToggler) {
      window.themeToggler = new ThemeToggler();
    }
  });
} else {
  if (!window.themeToggler) {
    window.themeToggler = new ThemeToggler();
  }
}

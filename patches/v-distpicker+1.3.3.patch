diff --git a/node_modules/v-distpicker/src/Distpicker.vue b/node_modules/v-distpicker/src/Distpicker.vue
index 61baad7..0ff991d 100644
--- a/node_modules/v-distpicker/src/Distpicker.vue
+++ b/node_modules/v-distpicker/src/Distpicker.vue
@@ -1,6 +1,16 @@
 <template>
-  <div :class="wrapper">
+  <div :class="wrapper" style="width: 100%;">
     <template v-if="type !== 'mobile'">
+      <template v-if="disabled">
+        <a-select disabled :value="currentProvince+'/'+currentCity+'/'+currentArea" style="width:100%">
+        </a-select>
+      <!-- {{ currentProvince }}
+      {{ currentCity }}
+      {{ currentArea }} -->
+      </template>
+      <template v-else>
+
+  
       <label>
         <select @change="getCities" v-model="currentProvince" :disabled="disabled || provinceDisabled">
           <option :value="placeholders.province">{{ placeholders.province }}</option>
@@ -42,6 +52,7 @@
         </label>
       </template>
     </template>
+    </template>
     <template v-else>
       <div :class="addressHeader">
         <ul >
@@ -80,6 +91,7 @@
           </ul>
         </template>
       </div>
+    
     </template>
   </div>
 </template>
@@ -428,7 +440,7 @@ export default {
 }
 </script>
 
-<style lang="scss">
+<style lang="less">
 .distpicker-address-wrapper {
   color: #9caebf;
   select {

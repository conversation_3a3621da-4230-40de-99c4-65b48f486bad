const path = require('path')
const webpack = require('webpack')
const createThemeColorReplacerPlugin = require('./config/plugin.config')

function resolve(dir) {
  return path.join(__dirname, dir)
}
// vue.config.js
const vueConfig = {
  configureWebpack: {
    // webpack plugins
    plugins: [
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      require('unplugin-auto-import/webpack').default({
        imports: [
          'vue' // 自动引入 Vue 的 API
          // 'vue-router' // 自动引入 Vue Router 的 API
          // '@vueuse/core',  // 自动引入 VueUse 库的 API
        ],
        eslintrc: {
          enabled: false, //
          globalsPropValue: true
        },
        dts: 'src/auto-imports.d.ts',
        // 自定义模块解析，通过函数方式来定义
        resolvers: [
          (name) => {
            if (name === '$http') {
              return { from: '@/utils/request', name: '$http' }
            }
          },
          (name) => {
            if (name === '$router') {
              return { from: '@/router/index', name: '$router' }
            }
          },
          (name) => {
            if (name === '$route') {
              return { from: '@/router/index', name: '$route' }
            }
          }
        ]
      })
    ],
    // if prod, add externals
    externals: {}
  },

  chainWebpack: (config) => {
    config.resolve.alias
      .set('@$', resolve('src'))

    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .oneOf('inline')
      .resourceQuery(/inline/)
      .use('vue-svg-icon-loader')
      .loader('vue-svg-icon-loader')
      .end()
      .end()
      .oneOf('external')
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'assets/[name].[hash:8].[ext]'
      })

    // if prod is on
    // assets require on cdn
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          'primary-color': '#1890FF',
          'layout-color': '#1890FF',
          'border-radius-base': '2px',
          'disabled-color': 'rgba(0, 0, 0, 0.45)'

        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true
      }
    }
  },

  devServer: {
    port: 81,
    proxy: {

      '/api': {
        // 测试环境
        //  target: 'http://************:6013/api/',
        // target: 'http://************:6013/api/',
        // target: 'http://*************:6042/api',
        target: 'http://localhost:7082/api',

        // 预览环境
        // target: 'http://*************:8095/api/',
        // 生产环境
        // target: 'https://sjxt.zjsft.gov.cn/api/',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '' // 需要rewrite的,
        }
      }
    }
  },

  // disable source map in production
  productionSourceMap: false,
  lintOnSave: false,
  // babel-loader no-ignore node_modules/*
  transpileDependencies: []
}

// preview.pro.loacg.com only do not use in your production;
if (process.env.VUE_APP_PREVIEW === 'true') {
  // eslint-disable-next-line no-labels
  // runtimeCompiler: true,
  // add `ThemeColorReplacer` plugin to webpack plugins
  vueConfig.configureWebpack.plugins.push(createThemeColorReplacerPlugin())
}

module.exports = vueConfig
